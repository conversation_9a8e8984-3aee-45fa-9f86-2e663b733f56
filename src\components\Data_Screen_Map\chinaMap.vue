<template>
  <div class="china-map-container">
      <div id="mapChart" class="china-map-chart"></div>
  </div>
</template>

<script>
import cityMap from '@/common/js/china-main-city-map.js';
import echarts from 'echarts';
import axios from 'axios';

let Path ='';
try{
  Path = window.opener.origin;
}
catch (e) {
  console.log(e);
}
//中国地图（第一级地图）的ID、Name、Json数据
let chinaId = 100000;
let chinaName = "china";
let spot = require('@/assets/images/screen/spot.png')
//记录父级ID、Name
let mapStack = [];
let parentId = null;
let parentName = null;
//Echarts地图全局变量，主要是在返回上级地图的方法中会用到
let myChart = null;
let acRoutes =[];
let currentLevel ='china',currentMapId = 100000, next = true;
let notAllowClick = false; // 避免连续点击
let isBack = false;
export default {
  name: "chinaMap",
  props: {
    mapData:{
      type:Array,
      default:()=> {
        return []
      }
    },
    defaultCity: {
      type: Array,
      default: ()=> {
        return []
      }
    }
  },
  data(){
    return{
      nowCityId:'100000',
      backImg: require('@/assets/images/screen/formatter.png')
    }
  },
  mounted() {
    myChart = echarts.init(document.getElementById('mapChart'));
    this.mapEvent();
    this.$nextTick(() => {
      this.initGenerateMap("mapChart");
    })
    window.addEventListener('resize', () => {
      if(myChart){
        myChart.resize();
      }
    })
  },
  watch:{
    mapData: function (n, o) {
      let newOption= null;
      if (myChart) {
        newOption=myChart.getOption();
        if(currentMapId === 100000) {
          newOption.series[1].data = n;
          myChart.setOption(newOption,true);
        } else {
          newOption.series[1].data = this.formatMapData(n);
          myChart.setOption(newOption,true);
        }
      }
    },
  },
  methods: {
    initShow() {
      let data = JSON.parse(JSON.stringify(this.defaultCity));
      let [first, second] = data;
      let len = data.length;
      for(let item of data) {
        if (item.level === 'province') {
          item.parent = {
            adcode: 100000
          }
        }
        else if (item.level === 'city') {
          item.parent = {
            adcode: first.adcode
          }
        }
        else if (item.level === 'district'){
          if (len === 2) {
            item.parent = {
              adcode: first.adcode
            }
          }else {
            item.parent = {
              adcode: second.adcode
            }
          }

        }
      }
      acRoutes = data || [];
      this.clickEvent({
        componentSubType: 'map',
        data: data[len -1]
      }, this);
      myChart.resize();

    },
    /**
     * 返回上一级地图
     */
    back(self) {
      let len = acRoutes.length
      if (len>0) {
        next = true;
        let backData = acRoutes[len -1];
        if (backData.parent.adcode === 100000) {
          acRoutes = [];
          currentMapId = 100000;
          currentLevel ='china';
          self.initGenerateMap("mapChart");
        } else {
          if(backData.level === 'city') {
            backData = acRoutes[0];
          }else if (backData.level === "district") {
            backData = acRoutes[len -2];
          }else {
            console.log('level: ', backData.level);
          }

          self.$nextTick( async ()=>{
            let nextCode = backData.parent.adcode;
            let _subMapJsonData = await self.getSubMapData(backData, self);
            acRoutes.pop();
            // 按要求显示部分点位
            let _mapData = self.formatMapData(self.mapData);
            registerAndsetOption(myChart, nextCode, backData.name, _subMapJsonData,true, _mapData,self);
          })
        }
      }
    },
    /**
     * Echarts地图
     */
    // 初始化Map Echarts
    initGenerateMap(ele){
      let self = this;
      self.$ajax({
        url:'/static/json/chinaChange.json',
        success:(res)=> {
          // 展示全国地图时，显示全部的点位 mapData
          registerAndsetOption(myChart, chinaId, chinaName, res, false,self.mapData,self);
          myChart.resize();
        }
      })
    },
    // 地图事件
    mapEvent(){
      let self = this;
      //判断点击的区域
      myChart.getZr().on("click", params => {
        if (notAllowClick) {
          return false
        }
        if (params.target) {
          // 点击的地图区域
        } else {
          self.back(self);
        }
      });
      //地图缩放事件
      myChart.on('georoam', params => {
        let option = myChart.getOption();//获得option对象
        if(params.zoom !==null && params.zoom !== undefined){ //捕捉到缩放时
          option.geo[0].zoom = option.series[0].zoom;//下层geo的缩放等级跟着上层的geo一起改变
          option.geo[0].center=option.series[0].center;//下层的geo的中心位置随着上层geo一起改变
        }else{//捕捉到拖曳时
          option.geo[0].center=option.series[0].center;//下层的geo的中心位置随着上层geo一起改变
        }
        myChart.setOption(option);
      })
      // 监听地图的点击事件
      myChart.on("click", function(param) {
        self.clickEvent(param, self);
      })
    },
    clickEvent(param,self) {
      if (notAllowClick) {
        return false;
      }
      if (param.componentSubType === 'scatter') {
        self.$emit('onClickEffectScatter', param.data);
      }
      else if (param.componentSubType === 'map') {
        self.$nextTick( async ()=>{
          if(param.data.level === 'district' && !next) {
            return false;
          } else {
            next = param.data.level !== 'district';
          }
          let nextCode = param.data.adcode;
          let _subMapJsonData = await self.getSubMapData(param.data, self);
          // 按要求显示部分点位
          let _mapData = self.formatMapData(self.mapData);
          registerAndsetOption(myChart, nextCode, param.data.name, _subMapJsonData,true, _mapData, self);
          acRoutes.push(param.data);
          acRoutes = Array.from(new Set([...acRoutes]));
        })
      }
    },
    // 获取地图子数据
    getSubMapData: async (params,self)=> {
      let _subMapJsonData = {};
      currentLevel = params.level;
      currentMapId = params.adcode;
      notAllowClick = true;
      await self.$axios.get(`/dataScreen/getMapJsonFile`, {
        params: {
          "filePath":`/datacenter/maps/${params.level}/${params.adcode}.json`
        }
      }).then(res => {
        if (res.data.state === 1) {
          _subMapJsonData = JSON.parse(res.data.data);
        } else {
          _subMapJsonData = {};
        }
        return _subMapJsonData;
      }).catch(err => {
        console.error("map data get error:", err);
        _subMapJsonData = {};
        return _subMapJsonData;
      })
      notAllowClick = false;
      return _subMapJsonData;
    },
    // 重组地图点位
    /**
     *重组地图点位
     * @params { Object } data 数据源
     * @params { Object } options
     */
    formatMapData(data) {
      let scatterData = [];
      let _level  = '';
      if (currentLevel === "province") {
        _level = 'province';
      } else if (currentLevel === "city") {
        _level = 'city';
      } else if (currentLevel === "district") {
        _level = 'quxian'
      } else {
        _level = '';
      }

      data.map((item,index) => {
        if( item[_level] == currentMapId) {
          scatterData.push(item);
        }
      })
      // console.log('options: ', options);
      return scatterData;
    },
  }
};
/**
 *
 * @param {*} myChart
 * @param {*} id        省市县Id
 * @param {*} name      省市县名称
 * @param {*} mapJson   地图Json数据
 * @param {*} mapData   散点图Json数据
 * @param {*} flag      是否往mapStack里添加parentId，parentName
 */
function registerAndsetOption(myChart, id, name, mapJson, flag,mapData, that) {
  echarts.registerMap(name, mapJson);
  myChart.setOption({ //设置背景颜色
    title: {
      show:true,
      subtext: '',
      left:'center'
    },
    tooltip : {
      trigger: 'item',
      formatter: function (params) {
        let value = '';
        try {
          value = params.data.value;
        }
        catch(err) {
          console.log('err',err)
        }
        if(value){
          let backImg = require('@/assets/images/screen/formatter.png')
          let res = '';
          res = "<div style=\'position: relative;width: 187px;height: 146px;\'><img style='height: 146px;width: 187px' src='"+backImg+"'><div style='position: absolute;left: 28px;top: 19px;font-size: 18px;width: 131px;overflow:hidden;\n" +
            "text-overflow: ellipsis;white-space: nowrap;'>"+params.data.parkName+"</div><div style='position: absolute;top:72px;left:28px;font-size: 16px'>余位：<span style='color:#00DBFF'>"+params.data.value[2]+"个</span></div><div style='position: absolute;top:96px;left:28px;font-size: 16px'>总收入：<span style='color:#EEEC73'>"+params.data.value[3]+"元</span></div></div>"

          return res;
        }
      }
    },
    geo: {
      map: name,
      itemStyle: {					// 定义样式
        normal: {					// 普通状态下的样式
          areaColor: '#323c48',
          borderColor: '#20C6F0',
          borderWidth: 2,
          shadowColor: '#20C6F0',
          shadowOffsetX:'-1',
          shadowOffsetY:'3',
          color:'#fff'
          // shadowBlur: 5,
        },
        emphasis: {					// 高亮状态下的样式
          areaColor: '#2a333d',
          color:'#fff'
        }
      },
      label: {
        normal:{
          color:'#fff'
        },
        emphasis:{  //鼠标移入动态的时候显示的默认样式
          color:'#fff'
        }
      },
    },
    series: [
      {
        data: initMapDatas(mapJson),
        type: "map",
        map: name,
        roam: true,
        scaleLimit: { //滚轮缩放的极限控制
          min: 1,
          max: 3
        },
          itemStyle: {					// 定义样式
          normal: {
            borderColor: '#478DF1',
            borderWidth: 1,// 普通状态下的样式
            areaColor: '#062151',
            textStyle : {
              color: '#fff'
            },
          },
          emphasis: {					// 高亮状态下的样式
            areaColor: '#037FA8',
          }
        },
        label: {
          normal:{
            color:'#fff'
          },
          emphasis:{  //鼠标移入动态的时候显示的默认样式
            color:'#fff'
          }
        }
      },
      {
        type: 'scatter', // series图表类型
        coordinateSystem: 'geo', // series坐标系类型
        symbol: "image://" + spot,
        symbolSize: 18,
        data:mapData,
      }
    ]
  },true);

}
function initMapDatas(mapJson) {
  let mapData = [];
  if (mapJson.features) {
    for (let i = 0; i < mapJson.features.length; i++) {
      mapData.push(mapJson.features[i].properties);
    }
  }
  return mapData;
}

</script>

<style lang="scss" scoped>
.china-map-container {
  position: relative;
  top:8%;
  width: 100%;
  height: 88%;
  overflow: hidden;
}
.china-map-chart{
  position: absolute;
  width: 94%;
  height: 100%;
  left: 3%;
  top:0;
}
</style>
