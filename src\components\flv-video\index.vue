<template>
  <div class="player-container">
    <div id="container" ref="container" class="container playerDom"></div>
  </div>
</template>

<script>
import ElementResize from '../../common/js/erd'

export default {
  name: 'jessibucaPlayer',
  props: {
    url: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      style: {
        height: '100%'
      },
      jessibuca: null,
      vc: 'ff',
      count: 0, // 视频超时重连计数
      maxCount: 5, // 最大重连数
      lagStatus: 0, // 视频卡顿统计
    }
  },
  mounted() {
    let eleResize = new ElementResize(".playerDom");
    eleResize.listen((evt) => {
      if (this.jessibuca) {
        this.jessibuca.resize();
      }
    })
    this.$nextTick(() => {
      this.create();
    })
  },
  unmounted() {
    this.cleanup();
  },
  destroyed() {
    this.cleanup();
  },
  methods: {
    cleanup() {
      if (this.jessibuca) {
        this.jessibuca.destroy();
        this.jessibuca = null;
      }
    },

    handleReconnect() {
      this.cleanup();
      this.count++;
      
      if (this.count >= this.maxCount) {
        this.count = 0;
        // this.$message.error('视频重连失败，请刷新页面重试');
        return;
      }

      console.log(`第 ${this.count} 次重试连接...`);
      setTimeout(() => {
        this.create();
      }, 1000);
    },

    create(options) {
      const self = this;
      options = options || {};
      
      this.jessibuca = new window.Jessibuca(
        Object.assign(
          {
            container: this.$refs.container,
            videoBuffer: 0.2,
            loadingText: "加载中",
            isResize: false,
            debug: true,
            forceNoOffscreen: true,
            isNotMute: false,
            isFlv: true,
            decoder: '/static/jessibuca/decoder.js'
          },
          options
        )
      )

      this.jessibuca.on("load", function () {
        self.count = 0; // 重置重连计数
        self.jessibuca.play(self.url);
      })

      this.jessibuca.on("performance", (performance) => {
        if (performance === 0 && self.lagStatus >= self.maxCount) {
          self.cleanup();
          self.lagStatus = 0;
          self.$nextTick(() => {
            self.create();
          })
          return;
        }
        if (performance === 0) {
          self.lagStatus++;
        }
      })

      this.jessibuca.on("delayTimeout", function () {
        console.log('延迟超时，准备重连...');
        self.handleReconnect();
      })

      this.jessibuca.on("error", function (error) {
        console.log('播放错误类型:', error);
        
        switch (error) {
          case 'playError':
          case 'fetchError':
            console.log(`发生${error}错误，准备重连...`);
            self.handleReconnect();
            break;
            
          case 'websocketError':
            // self.$message.error('WebSocket连接失败');
            self.handleReconnect();
            break;
            
          default:
            self.cleanup();
            // self.$message.error('解码失败，请检查播放器配置');
            break;
        }
      })
    }
  },
  watch: {
    url: {
      handler(newUrl) {
        this.cleanup();
        if (newUrl) {
          this.count = 0; // 重置重连计数
          this.create();
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.player-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgb(0, 0, 0);
  
  .container {
    position: absolute;
    width: 100%;
    height: 100%;
    
    canvas {
      width: 100%;
      height: 100%;
    }
  }
}
</style>