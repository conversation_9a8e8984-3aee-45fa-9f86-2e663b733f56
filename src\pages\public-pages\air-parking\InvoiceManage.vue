<template>
  <section class="right-wrapper-size shop-table-wrapper" id="scrollBarDom">
    <div class="shop-custom-operation">
      <div class="shop-custom-operation">
        <header class="shop-custom-header">
          <p style="float: left">
            机场停车<span style="margin: 2px">-</span>电子发票
          </p>

          <div class="float-right">
            <el-button type="text" size="mini" @click="resetForm" icon="el-icon-refresh" style="font-size: 14px;color: #1E1E1E;">刷新</el-button>
          </div>
        </header>
      </div>

      <super-form
        :form-config="formConfig"
        :value="searchForm"
        @input="handleSearch"
      />
    </div>

    <div class="count-table-wrapper-style">
      <bl-table
        border
        ref="tableRef"
        :api="fetchTableData"
        :request-params="searchForm"
        :showPagination="true"
        :showIndex="true"
      >
        <el-table-column
          prop="operatorName"
          label="车场名称"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="userPhone"
          label="手机号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="applyTime"
          label="开票申请时间"
          align="center"
          width="180"
        ></el-table-column>
        <el-table-column prop="amount" label="金额" align="center">
          <template slot-scope="scope"> {{ scope.row.amount }}元 </template>
        </el-table-column>
        <el-table-column prop="status" label="开票状态" align="center">
          <template slot-scope="scope">
            <el-button :type="getStatusType(scope.row.status)" size="small" @click="scope.row.status < 2 ? statusInvoiceflow(scope.row.id) : ''">
              {{ getStatusText(scope.row.status) }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          prop="title"
          label="抬头"
          align="center"
          width="200"
        ></el-table-column>
        <el-table-column
          prop="taxNo"
          label="税号"
          align="center"
          width="180"
        ></el-table-column>
        <el-table-column
          prop="email"
          label="邮箱"
          align="center"
          width="200"
        ></el-table-column>
        <!-- <el-table-column
          prop="operate"
          label="操作"
          align="center"
          fixed="right"
          width="120"
        >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.status === '0'"
              type="text"
              @click="handleInvoice(scope.row)"
            >
              开票
            </el-button>
            <el-button
              v-if="scope.row.status === '2'"
              type="text"
              @click="handleDownload(scope.row)"
            >
              下载
            </el-button>
            <el-button type="text" @click="handleView(scope.row)">
              查看
            </el-button>
          </template>
        </el-table-column> -->
      </bl-table>
    </div>
  </section>
</template>

<script>
import { fetchInvoiceList, statusInvoiceNext } from "@/api/airParking";
import SuperForm from "@/components/super-form/inline-form";
import BlTable from "@/components/BlTable/index.vue";

export default {
  name: "InvoiceManage",
  components: { SuperForm, BlTable },
  data() {
    return {
      searchForm: {
        userPhone: "",
        status: "",
      },
      current: 1, // 当前页码
      size: 10, // 每页显示多少条数据
      requestUrl: this.$PUBLIC_URL.AIRPORT_API, // 请求接口
      formConfig: {
        first: [
          {
            label: "手机号",
            type: "input",
            prop: "userPhone",
            placeholder: "请输入手机号",
          },
          {
            label: "开票状态",
            type: "select",
            prop: "status",
            placeholder: "请选择开票状态",
            options: [
              { value_name: "全部", value_no: "" },
              { value_name: "已申请", value_no: "0" },
              { value_name: "开票中", value_no: "1" },
              { value_name: "已开票", value_no: "2" }
            ],
          },
        ],
      },
    };
  },

  mounted() {
    this.fetchTableData();
  },

  methods: {
    async fetchTableData() {
      const params = { ...this.searchForm, size: this.size, current: this.current };
      // 生产环境建议关闭或替换为日志系统
      if (process.env.NODE_ENV !== 'production') {
        console.log("请求参数:", params, this.requestUrl, 'this.$PUBLIC_URL.AIRPORT_API');
      }
      console.log("请求接口:", this.requestUrl);
      console.log(params, 'fetchOrderList')
      try {
        const response = await fetchInvoiceList(this.requestUrl, params);
        const dataList = response? response.data.data : [];
        console.log("请求响应:", response);
        return {
          list: dataList.records || [],
          total: dataList.total || 0,
        };
      } catch (error) {
        console.error("InvoiceManage fetchTableData 请求失败:", error); // 记录错误日志
        this.$message.error("加载数据失败，请稍后重试");
        return {
          list: [],
          total: 0,
        };
      }
    },

    handleSearch(form) {
      this.searchForm = form;
      this.$refs.tableRef.loadData();
    },

    resetForm(){
      this.searchForm = {};
      this.current = 1;
      this.$refs.tableRef.loadData();
    },

    getStatusType(status) {
      const statusMap = {
        0: "info",
        1: "warning",
        2: "success",
      };
      return statusMap[status] || "info";
    },

    getStatusText(status) {
      const statusMap = {
        0: "已申请",
        1: "开票中",
        2: "已开票",
      };
      return statusMap[status] || "未知";
    },
    // 发票流程
    async statusInvoiceflow(id) {
      console.log("statusInvoiceflow:", id);
      // 这里可以调用结算API
      try {
        let response = await statusInvoiceNext(this.requestUrl, id);
        console.log(response, 'response===');
        // 校验响应结构
        if (!response || !response.data) {
          this.$message.error('流转失败');
          return;
        }

        const { code } = response.data;
        if (String(code) === '200') {
          this.$refs.tableRef.loadData();
        } else {
          this.$message.error("流转失败");
          return;
        }
    
        
      } catch (error) {
        console.error(error,'InvoiceManage statusInvoiceflow');
        this.$message.error('操作失败');
      }
    },

    handleInvoice(row) {
      this.$confirm(`确认为手机号 ${row.phone} 开具发票？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 这里调用开票API
          this.$message.success("开票成功");
          this.fetchTableData();
        })
        .catch(() => {
          this.$message.info("已取消开票");
        });
    },

    handleDownload(row) {
      // 这里实现发票下载逻辑
      this.$message.success("发票下载中...");
    },

    handleView(row) {
      // 这里实现查看发票详情逻辑
      this.$message.info("查看发票详情");
    },
  },
};
</script>

<!-- <style lang="scss" scoped>
.shop-custom-header {
  padding: 20px;
}
</style> -->