const AUTH_ID = {
    my_account:934,//我的账户
    orderManage: 2, //订单管理
    dataCenter:69,//数据中心
    my_park_vehicle: 945, //新能源车概况
    parkingLot: 10096, // 车位
    Data_SIM_Management:10118,//SIM卡管理
    Data_SIM_Card:10119,//流量卡
    dataCenter_Park: 437,
    dataCenter_RemoteOpening:438,
    dataCenter_4GSentryBox:501,
    Yun_Onduty: 10168,// 云值守
    Onduty_Setting: 10169,// 设置


    orderManage_NowOrders:480,//在场订单
    orderManage_Orders: 83, //订单记录
    orderManage_Poles: 84, //抬杆记录
    orderManage_Income:376,//交易记录
    orderManage_Discount:522,//抵扣记录
    orderManage_Expense:377,//支出记录
    orderManage_Record:378,//减免记录
    orderManage_Abnormal:389,//异常订单
    orderManage_PayAfterDeparture: 10058, // 欠费订单
    monthMember: 7, //月卡会员-大菜单
    relationSet:613,//会员设置
    monthMember_Refill: 297, //月卡续费记录
    monthMember_VIP: 298, //月卡会员-小菜单
    park_white_list:387,//内部车管理
    camera_white_list:521,//下发记录
    park_appoint_manage: 766, // 预约车管理
    prepay_card:336,
    prepay_card_trade:337,
    prepay_card_use:338,
    orderStatistics: 11, //统计分析
    orderStatistics_DailyReport: 12, //统计分析-时租订单统计-日报
    orderStatistics_MonthReport: 314, //统计分析-时租订单统计-日报
    orderStatistics_CollectorReport:317,//统计分析-收费员统计
    orderStatistics_CollectorMonthReport:607,//统计分析-收费员月报
    flowStatistics_DailyReport:625,//统计分析-车流量日报
    flowStatistics_MonthReport: 626, //统计分析-车流量月报
    shopManageA: 71, //商户管理-大菜单
    shopManage_Coupon: 311, //优惠券管理
    shopAnalysis: 444, //商户统计
    newParkAnalysis: 10248, //新商户统计
    shopManage_Shop: 299, //商户管理-小菜单
    shopManage_QueryAccount: 300, //流水查询

    shopManageB:475,
    shopManage_CouponSettings:476,
    shopAnalysis_B: 525, //商户统计
    shopManage_Shop_B:477,
    shopManage_QueryAccount_B:478,
    shopManage_Coupon_B:479,
    shopManage_CouponList: 10230, //发券设置
    shopManage_ShopList: 10231, //商户管理


    employeePermission: 21, //账号管理
    employeePermission_Role: 22, //账号角色
    employeePermission_Manage: 23, //员工管理
    employeePermission_MessageNtification:506,//消息通知
    systemManage_AddedService_Sms:341,//短信服务
    systemManage_AddedService_Screen:342,//数据大屏
    systemManage_AddServices_Public:380,//商户公众号
    systemManage_AddServices_ParkPublic:441, //车场公众号
    systemManage_AddServices_Program:381, //车场小程序
    systemManage_AddServices_Video:502,//语音呼叫
    systemManage_AddServices_FourService:537,//纯云服务
    systemManage_AddedService_Cam_Video:680,//视频VIP服务
    systemManage_AddedService_Car_Distinguish:699,//手动车牌识别
    systemManage_AddedService:340, //平台服务
    Public_AddServices_MarketingAuthorization:10220, //销售授权
    Public_AddServices_ServicePrice:10221,//服务价格


    systemManage: 24, //系统管理
    parkManage: 579, //系统管理
    systemManage_ParkPublic:442,//车场公众号
    parkManage_Set:464,//车场设置
    systemManage_AdvanceSet:440,//高级设置
    systemManage_BlackList: 313, //黑名单管理
    systemManage_Commute: 407, //上下班记录
    systemManage_Account: 25, //账户管理
    systemManage_Params: 26, //参数设置
    systemManage_FreeReason: 648, //免费原因
    systemManage_CarManage_BindType: 260, //绑定车型
    systemManage_CarManage_CarType: 259, //车型设定
    systemManage_CarManage: 27, //车型管理
    systemManage_Price: 28, //时租价格管s理
    systemManage_MonthCard: 29, //月卡套餐管理
    systemManage_Logs: 80, //系统日志
    systemManage_YunLogs:10200, // 云平台日志
    paySystem_log: 10201, // 支付云日志
    parkEventManage:10215, // 事件管理
    eventRecord:10216, // 事件记录
    car_Info_Gather: 651, //车辆信息采集
    equipmentManage: 343, //设备管理
    equipmentManage_Charging: 436, //计费管理
    equipmentManage_ParkCamera: 439, //相机管理
    equipmentManage_Watchhouse:379, //岗亭管理
    equipmentManage_Monitor: 344, //监控管理
    operatorOnDuty: 10164, // 值班人员
    equipmentManage_Intercom: 345, //对讲管理
    equipmentManage_DataUpdate: 935, //数据同步
    equipmentManage_WorkStation: 346,//工作站管理
    equipmentManage_Channel: 347, //通道管理
    equipmentManage_QrCodeManage: 614,//二维码管理
    equipmentManage_Camera: 19, //摄像头管理
    equipmentManage_LED: 20, //LED屏管理
    onlinePay: 8, //电子支付
    onlinePay_Income: 10, //电子收款
    onlinePay_CashManage: 9, //提现管理
    vistorManage_VistorMember:320,
    vistorManage_homeOwner:332,
    vistorManage:319,
    centerMonitor: 339, //中央监控

    faceDetectionManage:510, // 门禁梯控
    faceDetectionManage_FaceProject:772, // 项目管理
    faceDetectionManage_Base: 773, // 基本数据
    faceDetectionManage_Village: 774, // 区域管理
    faceDetectionManage_Building: 775, // 楼宇管理
    faceDetectionManage_Houses: 776, // 房屋管理
    faceDetectionManage_FaceInfomation: 777, // 人员管理
    faceDetectionManage_Caller: 778, // 访客管理
    faceDetectionManage_VisitRegistration: 779, // 来访登记
    faceDetectionManage_DepartureRegistration: 780, // 离开登记
    faceDetectionManage_InoutRecords: 781, // 出入记录
    faceDetectionManage_ElevatorRideRecord:10210, //乘梯记录
    faceDetectionManage_FeeCollectingManagement:10211,//收费管理
    faceDetectionManage_LadderCharge:10212,//乘梯计费
    faceDetectionManage_MonthlyRecord:10213,//包月记录
    faceDetectionManage_AccountRecords:10214,//账户记录
    faceDetectionManage_Service: 782, // 服务管理
    faceDetectionManage_Report: 783, // 报修管理
    faceDetectionManage_Phone: 784, // 服务电话
    faceDetectionManage_Notice: 785, // 公告管理
    faceDetectionManage_Device: 786, // 设备管理
    faceDetectionManage_Devices: 787, // 门禁设备
    faceDetectionManage_DoorsGroup: 788, // 门禁设备组
    faceDetectionManage_TimeGroup: 789, // 门禁时间组
    faceDetectionManage_FaceLaddelEquipment: 790, // 梯控设备
    faceDetectionManage_FaceLaddelPowerGroup: 791, // 梯控权限组
    faceDetectionManage_FaceManageCardIssuer:917,//发卡器
    faceDetectionManage_AccessCtrlPassenger:10226, // 门禁通道
    faceDetectionManage_DownloadRecords: 792, // 门禁同步
    faceDetectionManage_ElevatorCtrlSync: 10218, // 梯控同步
    faceDetectionManage_publicParams: 793, // 参数配置
    faceDetectionManage_appletSet: 794, //  小程序设置
    faceDetectionManage__ConsumptionManage: 795, //人脸消费
    faceDetectionManage_ConsumerPersonnel: 796, //人脸消费-人员管理
    faceDetectionManage_RecordsConsumption: 797, //人脸消费-消费记录
    faceDetectionManage_OperationRecord: 798, //人脸消费-操作记录

    // 汽车运营管理
    Public_CarCharge: 656, // 汽车运营管理
    Public_CarCharge_CalculaterManage: 799, // 计费管理
    Public_CarCharge_Rule: 800, // 充电费
    Public_CarCharge_OrderRule: 801, // 预约费
    Public_CarCharge_SeatRule: 802, // 占位费
    Public_CarCharge_StationManage: 803, // 电站管理
    Public_CarCharge_Station: 804, // 电站
    Public_CarCharge_Area: 805, // 区域
    Public_CarCharge_ParkingLot: 806, // 车位
    Public_CarCharge_Device: 807, // 设备管理
    Public_CarCharge_Pile: 808, // 充电桩
    Public_CarCharge_Camera: 809, // 相机
    Public_CarCharge_GroundLock: 810, // 地锁
    Public_CarCharge_Monitor:10094,//监控
    Public_CarCharge_Order: 811, // 订单管理
    Public_CarCharge_Order_Charge: 812, // 在充订单
    Public_CarCharge_Order_Record: 813, // 订单记录
    Public_CarCharge_Order_Reduction: 903, // 减免订单
    Public_CarCharge_Order_ReductionSummary: 904, // 减免汇总
    Public_CarCharge_Order_Share: 814, // 分账订单
    Public_CarCharge_Order_Order: 815, // 预约订单
    Public_CarCharge_Order_Seat: 816, // 占位订单
    Public_CarCharge_Order_Arrearage:890, // 欠费记录
    Public_CarCharge_Statistics: 817, // 统计分析
    Public_CarCharge_Statistics_Charge: 818, // 充电统计
    Public_CarCharge_Statistics_Share: 819, // 分账统计
    Public_CarCharge_Statistics_ShareArrival: 820, // 分账到账统计
    Public_CarCharge_Store: 821, // 库存管理
    Public_CarCharge_Store_Operate: 822, // 设备出库
    Public_CarCharge_Store_FlowRecord: 823, // 流转记录
    Public_CarCharge_UserManage:951,
    Public_CarCharge_User:952,//用户管理
    Public_CarCharge_ElectronicsCard:948, //电子卡管理
    Public_CarCharge_PhysicalCard:949,//实体卡管理
    Public_CarCharge_PhysicalVIN:10245,//VIN码
    Public_CarCharge_cardRecord:10246,//卡记录
    Public_CarCharge_User_ChargingActivities:950, //充电活动
    Public_CarCharge_User_ActivitiesRecord:951,//活动记录
    //有序能源
    orderlyEnergy:911,  //有序能源
    orderlyEnergy_Home:912,  //首页
    orderlyEnergy_SchedulingModel:913, //调度模型

    // 充电互联
    chargeConnect: 10153, // 充电互联
    Public_connect_auth: 10154, // 互联授权
    connectPark_authorizedStation: 10155, // 授权电站
    connectPark_authPile: 10156, // 授权桩
    connectPark_differenceOrder: 10157, // 异常订单


    // 单车充电
    Public_BicycleCharge: 714, // 两轮车充电
    Public_BicycleCharge_StationManage: 715, // 站点管理
    Public_BicycleCharge_Station: 718, // 充电站
    Public_BicycleCharge_Area: 891, // 充电区
    Public_BicycleCharge_Pile: 719, // 充电桩
    Public_BicycleCharge_Door: 918, // 门禁设备
    Public_BicycleCharge_BillingManage: 716, // 计费管理
    Public_BicycleCharge_ChargingSet: 720, // 计费标准
    Public_BicycleCharge_MonthRuleSet: 892, // 包月计费
    Public_BicycleCharge_Rule_Park: 919, // 停车计费
    Public_BicycleCharge_OrderManage: 717, // 订单管理
    Public_BicycleCharge_ChargeOrder: 721, // 在充订单
    Public_BicycleCharge_OrderRecord: 722, // 订单记录
    Public_BicycleCharge_AccountSplitting:916,//分账订单
    Public_BicycleCharge_Order_Park: 920, // 停车订单
    Public_BicycleCharge_FinancialStatistics:749,//统计分析
    Public_bicycleCharge_chargeDaily: 750,//充电统计
    Public_bicycleCharge_shareDaily:751,//分账统计
    Public_bicycleCharge_shareArrivalDaily:752,//分账到账统计
    Public_BicycleCharge_UserManage: 881,// 用户管理
    Public_BicycleCharge_User: 893,// 用户管理
    Public_BicycleCharge_User_Park: 921,// 车辆管理
    Public_BicycleCharge_MonthCardRecord: 894,// 包月订单
    Public_BicycleCharge_PhysicalCard: 882,// 实体卡管理
    Public_BicycleCharge_ElectronicsCard: 938,// 电子卡管理
    Public_BicycleCharge_User_AccessRecord: 922,// 出入记录
    park_videoPort: 747, // 视频接口
    park_Order_Picture_Storage: 771, //订单图片存储
    Public_BicycleCharge_AlarmInformation: 10198, // 告警消息
    Public_BicycleCharge_deviceMsg: 10199, // 设备消息

    parkCarportManage: 10232,  // 车位管理
    parkCarportManage_carportManage: 10233, // 车位管理
    parkCarportOrder_carportOrder: 10234, // 车位订单
    parkCarportRepport_carportReport: 10235, // 统计报表
    parkCarportUsers_carportUsers: 10236, // 用户管理
};
const showParkItem_const = {
    my_account:false,
    orderManage: false,
    my_park_vehicle: false, //新能源车概况
    parkingLot: false, // 车位
    dataCenter:false,//数据中心
    dataCenter_Park: false,
    dataCenter_RemoteOpening:false,
    dataCenter_4GSentryBox:false,
    Yun_Onduty: false,// 云值守
    Onduty_Setting: false,// 设置
    orderManage_NowOrders:false,
    orderManage_Orders: false,
    orderManage_Poles: false,
    orderManage_Income:false,//交易记录
    orderManage_Expense:false,//支出记录
    orderManage_Record:false,//减免记录
    orderManage_Abnormal:false,//异常订单
    orderManage_PayAfterDeparture: false, // 欠费订单
    orderManage_Discount:false,//抵扣记录
    monthMember: false,
    relationSet:false,//会员设置
    monthMember_Refill: false,
    monthMember_VIP: false,
    park_white_list:false,
    camera_white_list:false,
    park_appoint_manage: false,
    prepay_card:false,
    prepay_card_trade:false,
    prepay_card_use:false,
    orderStatistics: false,
    orderStatistics_DailyReport: false,
    orderStatistics_MonthReport: false,
    orderStatistics_CollectorReport:false,
    orderStatistics_CollectorMonthReport:false,
    flowStatistics_DailyReport:false,//统计分析-车流量日报
    flowStatistics_MonthReport: false, //统计分析-车流量月报
    onlinePay: false,
    onlinePay_Income: false,
    onlinePay_CashManage: false,

    shopManageA: false,
    shopManage_Coupon: false,
    shopAnalysis: false,
    newParkAnalysis: false,
    shopManage_Shop: false,
    shopManage_QueryAccount: false,

    shopManageB:false,
    shopManage_CouponSettings:false,
    shopAnalysis_B: false,
    shopManage_Shop_B:false,
    shopManage_QueryAccount_B:false,
    shopManage_Coupon_B:false,
    shopManage_CouponList:false,
    shopManage_ShopList:false,

    equipmentManage: false,
    equipmentManage_Charging:false,
    equipmentManage_ParkCamera:false,
    equipmentManage_Watchhouse:false,
    equipmentManage_Monitor: false,
    equipmentManage_Intercom: false,
    equipmentManage_DataUpdate: false,
    equipmentManage_WorkStation: false,
    equipmentManage_Channel: false,
    equipmentManage_QrCodeManage: false,//二维码管理
    equipmentManage_Camera: false,
    equipmentManage_LED: false,
    employeePermission: false,
    employeePermission_Role: false,
    employeePermission_Manage: false,
    employeePermission_MessageNtification: false,
    systemManage: false,
    parkManage:false,
    parkManage_Set: false,
    systemManage_AdvanceSet:false,
    systemManage_ParkPublic:false,
    systemManage_BlackList: false,
    systemManage_Commute: false,
    systemManage_Account: false,
    systemManage_Params: false,
    systemManage_FreeReason: false,
    systemManage_CarManage_CarType: false,
    systemManage_CarManage_BindType: false,
    systemManage_CarManage: false,
    systemManage_Price: false,
    systemManage_MonthCard: false,
    systemManage_Logs: false,
    systemManage_YunLogs:false, // 云平台日志
    paySystem_log: false, // 支付云日志
    parkEventManage:false, // 事件管理
    eventRecord:false, // 事件记录
    car_Info_Gather: false, //车辆信息采集
    systemManage_AddServices_Public:false,
    systemManage_AddServices_ParkPublic:false,
    systemManage_AddedService_Sms:false,
    systemManage_AddedService_Screen:false,
    systemManage_AddServices_Program:false,
    systemManage_AddServices_Video:false,
    systemManage_AddServices_FourService:false,
    systemManage_AddedService_Cam_Video:false,//视频VIP服务
    systemManage_AddedService_Car_Distinguish:false,//手动车牌识别
    systemManage_AddedService:false,
    Public_AddServices_MarketingAuthorization:false,//销售授权
    Public_AddServices_ServicePrice:false,//服务价格
    vistorManage_VistorMember:false,
    vistorManage_homeOwner:false,
    vistorManage:false,
    centerMonitor: false,
    Data_SIM_Management:false,//SIM卡管理
    Data_SIM_Card:false,//流量卡
    operatorOnDuty: false, // 值班人员





    faceDetectionManage:false, // 门禁梯控
    faceDetectionManage_FaceProject:false, // 项目管理
    faceDetectionManage_Base: false, // 基本数据
    faceDetectionManage_Village: false, // 区域管理
    faceDetectionManage_Building: false, // 楼宇管理
    faceDetectionManage_Houses: false, // 房屋管理
    faceDetectionManage_FaceInfomation: false, // 人员管理
    faceDetectionManage_Caller: false, // 访客管理
    faceDetectionManage_VisitRegistration: false, // 来访登记
    faceDetectionManage_DepartureRegistration: false, // 离开登记
    faceDetectionManage_InoutRecords: false, // 出入记录
    faceDetectionManage_ElevatorRideRecord:false, //乘梯记录
    faceDetectionManage_FeeCollectingManagement:false,//收费管理
    faceDetectionManage_LadderCharge:false,//乘梯计费
    faceDetectionManage_MonthlyRecord:false,//包月记录
    faceDetectionManage_AccountRecords:false,//账户记录
    faceDetectionManage_Service: false, // 服务管理
    faceDetectionManage_Report: false, // 报修管理
    faceDetectionManage_Phone: false, // 服务电话
    faceDetectionManage_Notice: false, // 公告管理
    faceDetectionManage_Device: false, // 设备管理
    faceDetectionManage_Devices: false, // 门禁设备
    faceDetectionManage_DoorsGroup: false, // 门禁设备组
    faceDetectionManage_TimeGroup: false, // 门禁时间组
    faceDetectionManage_FaceLaddelEquipment: false, // 梯控设备
    faceDetectionManage_FaceLaddelPowerGroup: false, // 梯控权限组
    faceDetectionManage_FaceManageCardIssuer:false,//发卡器
    faceDetectionManage_AccessCtrlPassenger:false, // 门禁通道
    faceDetectionManage_DownloadRecords: false, // 门禁同步
    faceDetectionManage_ElevatorCtrlSync:false, // 梯控同步
    faceDetectionManage_publicParams: false, // 参数配置
    faceDetectionManage_appletSet: false, //  小程序设置
    faceDetectionManage__ConsumptionManage: false, //人脸消费
    faceDetectionManage_ConsumerPersonnel: false, //人脸消费-人员管理
    faceDetectionManage_RecordsConsumption: false, //人脸消费-消费记录
    faceDetectionManage_OperationRecord: false, //人脸消费-操作记录

    // 汽车充电
    Public_CarCharge: false, // 汽车运营管理
    Public_CarCharge_CalculaterManage: false, // 计费管理
    Public_CarCharge_Rule: false, // 充电费
    Public_CarCharge_OrderRule: false, // 预约费
    Public_CarCharge_SeatRule: false, // 占位费
    Public_CarCharge_StationManage: false, // 电站管理
    Public_CarCharge_Station: false, // 电站
    Public_CarCharge_Area: false, // 区域
    Public_CarCharge_ParkingLot: false, // 车位
    Public_CarCharge_Device: false, // 设备管理
    Public_CarCharge_Pile: false, // 充电桩
    Public_CarCharge_Camera: false, // 相机
    Public_CarCharge_GroundLock: false, // 地锁
    Public_CarCharge_Monitor:false,//监控
    Public_CarCharge_Order: false, // 订单管理
    Public_CarCharge_Order_Charge: false, // 在充订单
    Public_CarCharge_Order_Record: false, // 订单记录
    Public_CarCharge_Order_Reduction: false, // 减免订单
    Public_CarCharge_Order_ReductionSummary: false, // 减免汇总
    Public_CarCharge_Order_Share: false, // 分账订单
    Public_CarCharge_Order_Order: false, // 预约订单
    Public_CarCharge_Order_Seat: false, // 占位订单
    Public_CarCharge_Order_Arrearage:false, //欠费记录
    Public_CarCharge_Statistics: false, // 统计分析
    Public_CarCharge_Statistics_Charge: false, // 充电统计
    Public_CarCharge_Statistics_Share: false, // 分账统计
    Public_CarCharge_Statistics_ShareArrival: false, // 分账到账统计
    Public_CarCharge_Store: false, // 库存管理
    Public_CarCharge_Store_Operate: false, // 设备出库
    Public_CarCharge_Store_FlowRecord: false, // 流转记录
    Public_CarCharge_UserManage:false,
    Public_CarCharge_User:false,//用户管理
    Public_CarCharge_ElectronicsCard:false, //电子卡管理
    Public_CarCharge_PhysicalCard:false,//实体卡管理
    Public_CarCharge_PhysicalVIN:false,//VIN码
    Public_CarCharge_cardRecord:false,//卡记录
    Public_CarCharge_User_ChargingActivities:false, //充电活动
    Public_CarCharge_User_ActivitiesRecord:false,//活动记录

    //有序能源
    orderlyEnergy:false, //有序能源
    orderlyEnergy_Home:false, //首页
    orderlyEnergy_SchedulingModel:false,//调度模型

    chargeConnect: false, // 充电互联
    Public_connect_auth: false, // 互联授权
    connectPark_authorizedStation: false, // 授权电站
    connectPark_authPile: false, // 授权桩
    connectPark_differenceOrder: false, // 异常订单

    // 单车充电
    Public_BicycleCharge: false, // 两轮车充电
    Public_BicycleCharge_StationManage: false, // 站点管理
    Public_BicycleCharge_Station: false, // 充电站
    Public_BicycleCharge_Door: false, // 门禁设备
    Public_BicycleCharge_Area: false, // 充电区
    Public_BicycleCharge_Pile: false, // 充电桩
    Public_BicycleCharge_BillingManage: false, // 计费管理
    Public_BicycleCharge_ChargingSet: false, // 计费标准
    Public_BicycleCharge_MonthRuleSet: false, // 包月计费
    Public_BicycleCharge_Rule_Park: false, // 停车计费
    Public_BicycleCharge_OrderManage: false, // 订单管理
    Public_BicycleCharge_ChargeOrder: false, // 在充订单
    Public_BicycleCharge_OrderRecord: false, // 订单记录
    Public_BicycleCharge_AccountSplitting:false,//分账订单
    Public_BicycleCharge_Order_Park: false, // 停车订单
    Public_BicycleCharge_FinancialStatistics:false, //统计分析
    Public_bicycleCharge_chargeDaily:false, //充电统计
    Public_bicycleCharge_shareDaily:false,//分账统计
    Public_bicycleCharge_shareArrivalDaily:false,//分账到账统计
    Public_BicycleCharge_UserManage: false,// 用户管理
    Public_BicycleCharge_User: false,// 用户管理
    Public_BicycleCharge_User_Park: false,// 车辆管理
    Public_BicycleCharge_MonthCardRecord: false,// 包月订单
    Public_BicycleCharge_PhysicalCard: false,// 实体卡管理
    Public_BicycleCharge_ElectronicsCard: false,// 电子卡管理
    Public_BicycleCharge_User_AccessRecord: false,// 出入记录
    park_videoPort: false, // 视频接口
    park_Order_Picture_Storage: false, // 订单图片存储
    Public_BicycleCharge_AlarmInformation: false, // 告警消息
    Public_BicycleCharge_deviceMsg: false, // 设备消息
    
    parkCarportManage: false,  // 车位管理
    parkCarportManage_carportManage: false, // 车位管理
    parkCarportOrder_carportOrder: false, // 车位订单
    parkCarportRepport_carportReport: false, // 统计报表
    parkCarportUsers_carportUsers: false, // 用户管理
};

//集团权限
const AUTH_ID_UNION = {
    my_park_vehicle: 944, //新能源车概况
    businessOrder: 105, //业务订单
    data_Center: 114, //概况-数据中心
    unionDutyCenter:10208,
    unionWatchCenter: 10209,
    data_Center_Child: 723, // 数据中心子菜单
    dataCenter_RemoteOpeningGroup: 650, //车道监控
    businessOrder_Cars: 208, //在场车辆
    businessOrder_Orders: 104, //订单记录
    businessOrder_Poles: 106, //抬杆记录
    businessOrder_Income:383, //交易记录
    businessOrder_Expense:384, //支出记录
    businessOrder_Reduce:386,//减免记录
    businessOrder_Discount:523,//抵扣记录
    member: 212, //会员
    member_MonthVIP: 214, //月卡会员
    member_Refill:536,//月卡续费记录
    member_BlackList: 215, //黑名单管理
    member_WhiteList:385,//内部车管理
    member_PrepayCardVIP:388,//车场储值卡会员
    unionPrepayCard_VIP:619,//集团储值卡
    unionPrepayCard_Trade:620,//集团储值卡余额变更
    unionPrepayCard_Use:621,//集团储值卡使用记录
    systemSetting: 238, //系统设置
    systemSetting_Company: 240, //企业信息
    systemSetting_Account: 247, //账户信息
    systemSetting_Park: 243, //停车场
    systemSetting_Park_Page:10108,//车场管理 二级菜单
    systemParkManage_parkManagePage_4g: 10109, //纯云车场管理
    systemSetting_DevelopmentConfig:611,//开发配置
    systemSetting_HR: 239, //账号管理
    systemSetting_EmployeeManage: 246, //员工管理
    systemSetting_RoleManage: 245, //账号角色
    systemSetting_MessageNtification:507,//消息通知
    systemSetting_LogsManage: 244, //日志管理
    systemSetting_LogsOperates: 284, //操作日志管理
    strategicAnalysis: 219, //决策分析
    strategicAnalysis_DailyReport: 315, //封闭车场订单统计-车场日报
    strategicAnalysis_MonthReport:316,
    strategicAnalysis_CollectorDailyReport:608,//集团收费员日报
    strategicAnalysis_CollectorMonthReport:609,//集团收费员月报
    UnionPrepayCardRenew:633,//储值卡续费统计
    strategicAnalysis_DailyParkReport:318,//车场日报
    strategicAnalysis_ShopReport:616,//商户统计
    strategicAnalysis_MonthParkReport:615,//车场月报
    strategicAnalysis_MonthRenewDetail:617,//月卡续费明细
    flow_DailyReport: 627, // 车场流量日报
    flow_MonthReport: 628, // 车场流量月报
    centerMonitor: 301, //中央监控
    unionValueAddedService:405,//平台服务
    unionValueAddedService_Sms:406, //短信服务
    //todo
    unionValueAddedService_Screen:448, //大屏
    unionValueAddedService_ShopApp:449, //商户公众号
    unionValueAddedService_Program:450, //小程序收费
    unionValueAddedService_ParkApp:451, //车场公众号
    unionValueAddedService_Video:503, //语音
    unionValueAddedService_Cam_Video:681, //视频VIP
    unionValueAddedService_Car_Distinguish:698, //手动车牌识别
    union_videoPort: 746, // 视频接口
    union_Order_Picture_Storage: 770, //订单图片存储
  };
const showUnionItem_const = {
    businessOrder: false,
    data_Center: false, //概况-数据中心
    unionDutyCenter:false,
    unionWatchCenter: false,
    data_Center_Child: true,
    my_park_vehicle: false, //新能源车概况
    dataCenter_RemoteOpeningGroup: false,//车道监控
    businessOrder_Cars: false,
    businessOrder_Orders: false,
    businessOrder_Poles: false,
    businessOrder_Income:false, //交易记录
    businessOrder_Expense:false, //支出记录
    businessOrder_Reduce:false,//减免记录
    businessOrder_Discount:false,//抵扣记录
    member: false,
    member_MonthVIP: false,
    member_Refill:false,//月卡续费记录
    member_BlackList: false,
    member_WhiteList:false,
    member_PrepayCardVIP:false,
    unionPrepayCard_VIP:false,
    unionPrepayCard_Trade:false,//集团储值卡余额变更
    unionPrepayCard_Use:false,//集团储值卡使用记录
    systemSetting: false,
    systemSetting_Account: false,
    systemSetting_Company: false,
    systemSetting_Park: false,
    systemSetting_Park_Page:false,//车场管理 二级菜单
    systemParkManage_parkManagePage_4g: false, //纯云车场管理
    systemSetting_DevelopmentConfig:false,
    systemSetting_EmployeeManage: false,
    systemSetting_RoleManage: false,
    systemSetting_MessageNtification: false,
    systemSetting_HR: false,
    systemSetting_LogsOperates: false,
    systemSetting_LogsManage: false,
    strategicAnalysis: false,
    strategicAnalysis_DailyReport: false,
    strategicAnalysis_MonthReport: false,
    strategicAnalysis_CollectorDailyReport:false,//集团收费员日报
    strategicAnalysis_CollectorMonthReport:false,//集团收费员月报
    UnionPrepayCardRenew:false,//储值卡续费统计
    strategicAnalysis_DailyParkReport:false,//车场日报
    strategicAnalysis_ShopReport:false,//商户统计
    strategicAnalysis_MonthParkReport:false,//车场月报
    strategicAnalysis_MonthRenewDetail:false,//月卡续费明细
    flow_DailyReport: false, // 车场流量日报
    flow_MonthReport: false, // 车场流量月报
    centerMonitor: false,
    unionValueAddedService:false,//平台服务
    unionValueAddedService_Sms:false, //短信服务
    unionValueAddedService_Screen:false, //大屏
    unionValueAddedService_ShopApp:false, //商户公众号
    unionValueAddedService_Program:false, //小程序收费
    unionValueAddedService_ParkApp:false, //车场公众号
    unionValueAddedService_Video:false, //语音呼叫
  unionValueAddedService_Cam_Video:false, //视频VIP
  unionValueAddedService_Car_Distinguish:false, //手动车牌识别
  union_videoPort: false, // 视频接口
  union_Order_Picture_Storage: false, //订单图片存储
};

const showShopItem_const = {
    shop: false,
    fixCode: false,
    ticket:false,
    ticketManage: false,
    shopRecharge: false,
    shopManagementShopAnalysis:false,
    shopMerchantAuthor:false,
    member: false,
    shopRole:false,
    shopMember:false,
    purchaseManage:false,
    shopPrintManage:false,
};

const AUTH_ID_SHOP = {
    shop: 321,
    fixCode: 328,
    ticket:323,
    ticketManage: 330,
    // shopRecharge: 324,
    shopRecharge: 329,
    shopManagementShopAnalysis:498,
    shopMerchantAuthor:10217,
    member: 326,
    shopRole:331,
    shopMember:327,
    purchaseManage:481,
    shopPrintManage:530
};

const showBossItem_const = {
    systemSetting_UnionManage: true,
    systemSetting_ParkManage: true
};

/**
 * @date:********
 * @description:服务商权限
 *
 */
const AUTH_ID_SERVER = {
    my_account:397,
    operator_manage:398,
    park_manage:392,
    serveParkManage_parkManagePage_4g: 667,
    server_park_manage:399,
    server_4gPark_manage:667,
    my_park_vehicle:943, //新能源车概况
    server_dutyCenter:10206,
    server_watchCenter:10207,
    Data_SIM_Management:10116,//SIM卡管理
    Data_SIM_Card:10117,//流量卡
    Public_CarCharge: 669, // 汽车运营管理
    Public_CarCharge_CalculaterManage: 824, // 计费管理
    Public_CarCharge_Rule: 825, // 充电费
    Public_CarCharge_OrderRule: 826, // 预约费
    Public_CarCharge_SeatRule: 827, // 占位费
    Public_CarCharge_StationManage: 828, // 电站管理
    Public_CarCharge_Station: 829, // 电站
    Public_CarCharge_Area: 830, // 区域
    Public_CarCharge_ParkingLot: 831, // 车位
    Public_CarCharge_Device: 832, // 设备管理
    Public_CarCharge_DeviceList: 833, // 设备列表
    Public_CarCharge_Pile: 834, // 充电桩
    Public_CarCharge_Camera: 835, // 相机
    Public_CarCharge_GroundLock: 836, // 地锁
    Public_CarCharge_Monitor:10093,//监控
    Public_CarCharge_Order: 837, // 订单管理
    Public_CarCharge_Order_Charge: 838, // 在充订单
    Public_CarCharge_Order_Record: 839, // 订单记录
    Public_CarCharge_Order_Reduction: 840, // 减免订单
    Public_CarCharge_Order_ReductionSummary: 841, // 减免汇总
    Public_CarCharge_Order_Share: 842, // 分账订单
    Public_CarCharge_Order_Order: 843, // 预约订单
    Public_CarCharge_Order_Seat: 844, // 占位订单
    Public_CarCharge_Order_Arrearage:889, //欠费记录
    Public_CarCharge_Statistics: 845, // 统计分析
    Public_CarCharge_Statistics_Charge: 846, // 充电统计
    Public_CarCharge_Statistics_Share: 847, // 分账统计
    Public_CarCharge_Statistics_ShareArrival: 848, // 分账到账统计
    Public_CarCharge_Store: 849, // 库存管理
    Public_CarCharge_Store_Operate: 850, // 设备出库
    Public_CarCharge_Store_FlowRecord: 851, // 流转记录
    Public_CarCharge_UserManage:953,//用户管理
    Public_CarCharge_User:10041,//用户管理
    Public_CarCharge_ElectronicsCard:10042, //电子卡管理
    Public_CarCharge_PhysicalCard:10043,//实体卡管理
    Public_CarCharge_PhysicalVIN:10243,//VIN码
    Public_CarCharge_cardRecord:10244,//卡记录
    Public_CarCharge_User_ChargingActivities:10044, //充电活动
    Public_CarCharge_User_ActivitiesRecord:10045,//活动记录

    //有序能源
    orderlyEnergy:908, //有序能源
    orderlyEnergy_Home:909, //首页
    orderlyEnergy_SchedulingModel:910,//调度模型

    //充电互联
    chargeConnect: 10148, // 充电互联
    Public_connect_auth: 10149, // 互联授权
    connectServer_authorizedStation: 10150, // 授权电站
    connectServer_authPile: 10151, // 授权充电桩
    connectServer_differenceOrder: 10152, // 异常订单


    // 单车充电
    Public_BicycleCharge: 733, // 两轮车充电
    Public_BicycleCharge_StationManage: 734, // 站点管理
    Public_BicycleCharge_Station: 735, // 充电站
    Public_BicycleCharge_Area: 895, // 充电区
    Public_BicycleCharge_Pile: 736, // 充电桩
    Public_BicycleCharge_Door: 927, // 门禁设备
    Public_BicycleCharge_BillingManage: 737, // 计费管理
    Public_BicycleCharge_ChargingSet: 738, // 计费标准
    Public_BicycleCharge_MonthRuleSet: 896, // 包月计费
    Public_BicycleCharge_Rule_Park: 926, // 停车计费
    Public_BicycleCharge_OrderManage: 739, // 订单管理
    Public_BicycleCharge_ChargeOrder: 740, // 在充订单
    Public_BicycleCharge_OrderRecord: 741, // 订单记录
    Public_BicycleCharge_AccountSplitting:915,//分账订单
    Public_BicycleCharge_Order_Park: 925, // 停车订单
    Public_BicycleCharge_FinancialStatistics:753,//统计分析
    Public_bicycleCharge_chargeDaily:754, //充电统计
    Public_bicycleCharge_shareDaily:755,//分账统计
    Public_bicycleCharge_shareArrivalDaily:756,//分账到账统计
    Public_BicycleCharge_UserManage: 883,// 用户管理
    Public_BicycleCharge_User: 897,// 用户管理
    Public_BicycleCharge_User_Park: 923,// 车辆管理
    Public_BicycleCharge_MonthCardRecord: 898,// 包月订单
    Public_BicycleCharge_PhysicalCard: 884,// 实体卡管理
    Public_BicycleCharge_ElectronicsCard: 939,// 电子卡管理
	  Public_BicycleCharge_User_AccessRecord: 924,// 出入记录
    Public_BicycleCharge_AlarmInformation: 10196, // 告警信息
    Public_BicycleCharge_deviceMsg: 10197, // 设备消息

    subservice_manage:400,
    serverTrade:394,
    serverTrade_MoneyRecord:401,
    serverStatistics:395,
    serverStatistics_NewUnionProfit:402,
    serverResources:396,
    serverResources_RoleManage:404,
    serverResources_EmployeeManage:403,
    serverResources_MessageNtification:508,
    //todo
    serverValueAddedService:452,//平台服务
    Public_AddServices_MarketingAuthorization:10224, //销售授权
    Public_AddServices_ServicePrice:10225,//服务价格
    serverValueAddedService_Sms:453, //短信服务
    serverValueAddedService_Screen:454, //大屏
    serverValueAddedService_ShopApp:455, //商户公众号
    serverValueAddedService_Program:456, //小程序收费
    serverValueAddedService_ParkApp:457, //车场公众号
    Services_Cam_Video:682, //视频VIP
    Services_Car_Distinguish:697, //手动车牌识别
    serverValueAddedService_Video:504, //语音
    serve_videoPort: 748, // 视频接口
    server_Order_Picture_Storage: 769, // 订单图片存储
}
const showServerItems_const = {
  Data_SIM_Management:false,//SIM卡管理
  Data_SIM_Card:false,//流量卡
    my_account:false,
    operator_manage:false,
    park_manage:false,
    serveParkManage_parkManagePage_4g: false,
    server_park_manage:false,
    server_4gPark_manage:false,
    my_park_vehicle: false,  //新能源车概况
    server_dutyCenter:false,
    server_watchCenter:false,
    Public_CarCharge: false, // 汽车运营管理
    Public_CarCharge_CalculaterManage: false, // 计费管理
    Public_CarCharge_Rule: false, // 充电费
    Public_CarCharge_OrderRule: false, // 预约费
    Public_CarCharge_SeatRule: false, // 占位费
    Public_CarCharge_StationManage: false, // 电站管理
    Public_CarCharge_Station: false, // 电站
    Public_CarCharge_Area: false, // 区域
    Public_CarCharge_ParkingLot: false, // 车位
    Public_CarCharge_Device: false, // 设备管理
    Public_CarCharge_DeviceList: false, // 设备列表
    Public_CarCharge_Pile: false, // 充电桩
    Public_CarCharge_Camera: false, // 相机
    Public_CarCharge_GroundLock: false, // 地锁
    Public_CarCharge_Monitor:false,//监控
    Public_CarCharge_Order: false, // 订单管理
    Public_CarCharge_Order_Charge: false, // 在充订单
    Public_CarCharge_Order_Record: false, // 订单记录
    Public_CarCharge_Order_Reduction: false, // 减免订单
    Public_CarCharge_Order_ReductionSummary: false, // 减免汇总
    Public_CarCharge_Order_Share: false, // 分账订单
    Public_CarCharge_Order_Order: false, // 预约订单
    Public_CarCharge_Order_Seat: false, // 占位订单
    Public_CarCharge_Order_Arrearage:false, //欠费记录
    Public_CarCharge_Statistics: false, // 统计分析
    Public_CarCharge_Statistics_Charge: false, // 充电统计
    Public_CarCharge_Statistics_Share: false, // 分账统计
    Public_CarCharge_Statistics_ShareArrival: false, // 分账到账统计
    Public_CarCharge_Store: false, // 库存管理
    Public_CarCharge_Store_Operate: false, // 设备出库
    Public_CarCharge_Store_FlowRecord: false, // 流转记录
    Public_CarCharge_UserManage:false,
    Public_CarCharge_User:false,//用户管理
    Public_CarCharge_ElectronicsCard:false, //电子卡管理
    Public_CarCharge_PhysicalCard:false,//实体卡管理
    Public_CarCharge_PhysicalVIN:false,//VIN码
    Public_CarCharge_cardRecord:false,//卡记录
    Public_CarCharge_User_ChargingActivities:false, //充电活动
    Public_CarCharge_User_ActivitiesRecord:false,//活动记录

    //有序能源
    orderlyEnergy:false, //有序能源
    orderlyEnergy_Home:false, //首页
    orderlyEnergy_SchedulingModel:false,//调度模型

    chargeConnect: false, // 充电互联
    Public_connect_auth: false, // 互联授权
    connectServer_authorizedStation: false, // 授权电站
    connectServer_authPile: false, // 授权充电桩
    connectServer_differenceOrder: false, // 异常订单

    // 单车充电
    Public_BicycleCharge: false, // 两轮车充电
    Public_BicycleCharge_StationManage: false, // 站点管理
    Public_BicycleCharge_Station: false, // 充电站
    Public_BicycleCharge_Area: false, // 充电区
    Public_BicycleCharge_Pile: false, // 充电桩
	  Public_BicycleCharge_Door: false, // 门禁设备
    Public_BicycleCharge_BillingManage: false, // 计费管理
    Public_BicycleCharge_ChargingSet: false, // 计费标准
    Public_BicycleCharge_MonthRuleSet: false, // 包月计费
	  Public_BicycleCharge_Rule_Park: false, // 停车计费
    Public_BicycleCharge_OrderManage: false, // 订单管理
    Public_BicycleCharge_ChargeOrder: false, // 在充订单
    Public_BicycleCharge_OrderRecord: false, // 订单记录
    Public_BicycleCharge_AccountSplitting:false,//分账订单
	  Public_BicycleCharge_Order_Park: false, // 停车订单
    Public_BicycleCharge_FinancialStatistics:false,//统计分析
    Public_bicycleCharge_chargeDaily:false,//充电统计
    Public_bicycleCharge_shareDaily:false,//分账统计
    Public_bicycleCharge_shareArrivalDaily:false,//分账到账统计
    Public_BicycleCharge_UserManage: false,// 用户管理
    Public_BicycleCharge_User: false,// 用户管理
	  Public_BicycleCharge_User_Park: false,// 车辆管理
    Public_BicycleCharge_MonthCardRecord: false,// 包月订单
    Public_BicycleCharge_PhysicalCard: false,// 实体卡管理
    Public_BicycleCharge_ElectronicsCard: false,// 电子卡管理
	  Public_BicycleCharge_User_AccessRecord: false,// 出入记录
    Public_BicycleCharge_AlarmInformation: false, // 告警信息
    Public_BicycleCharge_deviceMsg: false, // 设备消息

    subservice_manage:false,
    serverTrade:false,
    serverTrade_MoneyRecord:false,
    serverStatistics:false,
    serverStatistics_NewUnionProfit:false,
    serverResources:false,
    serverResources_RoleManage:false,
    serverResources_EmployeeManage:false,
    serverResources_MessageNtification:false,
    serverValueAddedService:false,//平台服务
    Public_AddServices_MarketingAuthorization:false,//销售授权
    Public_AddServices_ServicePrice:false,//服务价格
    serverValueAddedService_Sms:false, //短信服务
    serverValueAddedService_Screen:false, //大屏
    serverValueAddedService_ShopApp:false, //商户公众号
    serverValueAddedService_Program:false, //小程序收费
    serverValueAddedService_ParkApp:false, //车场公众号
    Services_Cam_Video:false, //视频VIP
    Services_Car_Distinguish:false, //手动车牌识别
    serverValueAddedService_Video:false, //语音
    serve_videoPort: false, // 视频接口
    server_Order_Picture_Storage: false, //订单图片存储
}
/*----------end-------------*/

/**
 * @date:********
 * @description:厂商权限
 *
 */
const AUTH_ID_CITY = {
    // city_account: 417,
    my_account:417,
    citySerManage:418,
    citySerManage_serManagePage:424,
    cityUnionManage:419,
    my_park_vehicle: 942, //新能源车概况
    city_dutyCenter:10204,
    city_watchCenter:10205,
    cityUnionManage_unionManagePage:425,
    cityParkManage:420,
    cityParkManage_parkManagePage:426,
    cityParkManage_parkManagePage_4g:445,
    cityParkManage_materielPage:427,
    city4GCameraManage_cameraPage:496,
    cityTrade:421,
    cityTrade_MoneyRecord:446,
    cityTrade_MoneyPay:447,
    cityStatistics_NewUnionProfit:428,
    cityResources:429,
    cityResources_RoleManage:432,
    cityResources_EmployeeManage:431,
    cityResources_MessageNtification:509,
    citySettingManage:422,
    citySettingManage_settingPage:430,
    citySettingManage_authPage:434,
    citySettingYunPrivate_Config:618,
    city_systemMange:423,
    //todo
    cityAddService:458,
    cityAddService_sms:459,
    Public_AddServices_MarketingAuthorization:10222, //销售授权
    Public_AddServices_ServicePrice:10223,//服务价格
    cityAddService_screen:460,
    cityAddService_shopApp:461,
    cityAddService_program:462,
    cityAddService_parkApp:463,
    cityAddService_openPlatform:500,
    cityAddService_video:505,
    cityAddService_face:528,
    cityDeviceManage:518,
    city_AddService_Cam_Video:683,
    city_AddService_Car_Distinguish:696,
    cityDeviceManage_deviceManage:519,
    cityfour_serve:535,
    cityDeviceManage_smartScreen:577,
    cityAdManage_http_Manage: 10227,//联网模块
    city_DeviceManage_bicyclePileUpdate:10228,//升级包

    // 汽车充电桩
    Public_CarCharge: 852, // 汽车运营管理
    Public_CarCharge_CalculaterManage: 853, // 计费管理
    Public_CarCharge_Rule: 854, // 充电费
    Public_CarCharge_OrderRule: 855, // 预约费
    Public_CarCharge_SeatRule: 856, // 占位费
    Public_CarCharge_StationManage: 857, // 电站管理
    Public_CarCharge_Station: 859, // 电站
    Public_CarCharge_Area: 860, // 区域
    Public_CarCharge_ParkingLot: 861, // 车位
    Public_CarCharge_Device: 858, // 设备管理
    Public_CarCharge_DeviceList: 862, // 设备列表
    Public_CarCharge_Pile: 863, // 充电桩
    Public_CarCharge_Camera: 864, // 相机
    Public_CarCharge_GroundLock: 865, // 地锁
    Public_CarCharge_Monitor:10092,//监控
    Public_CarCharge_Order: 866, // 订单管理
    Public_CarCharge_Order_Charge: 867, // 在充订单
    Public_CarCharge_Order_Record: 868, // 订单记录
    Public_CarCharge_Order_Reduction: 869, // 减免订单
    Public_CarCharge_Order_ReductionSummary: 870, // 减免汇总
    Public_CarCharge_Order_Share: 871, // 分账订单
    Public_CarCharge_Order_Order: 872, // 预约订单
    Public_CarCharge_Order_Seat: 873, // 占位订单
    Public_CarCharge_Order_Arrearage:887, //欠费记录
    Public_CarCharge_Statistics: 874, // 统计分析
    Public_CarCharge_Statistics_Charge: 875, // 充电统计
    Public_CarCharge_Statistics_Share: 876, // 分账统计
    Public_CarCharge_Statistics_ShareArrival: 877, // 分账到账统计
    Public_CarCharge_Store: 878, // 库存管理
    Public_CarCharge_Store_Operate: 879, // 设备出库
    Public_CarCharge_Store_FlowRecord: 880, // 流转记录
    Public_CarCharge_UserManage:10046,
    Public_CarCharge_User:10047,//用户管理
    Public_CarCharge_ElectronicsCard:10048, //电子卡管理
    Public_CarCharge_PhysicalCard:10049,//实体卡管理
    Public_CarCharge_PhysicalVIN:10241,//VIN码
    Public_CarCharge_cardRecord:10242,//卡记录
    Public_CarCharge_User_ChargingActivities:10050, //充电活动
    Public_CarCharge_User_ActivitiesRecord:10051,//活动记录

    managementOfJointStation: 10097, // 电站投建
    managementOfJointStation_depotManagement: 10098, // 电站投资
    testingOfEquipment:10111,//设备测试
    testingOfEquipment_chargingTest:10112,//充电测试
    testingOfEquipment_testRecord:10113,//测试记录
    Data_SIM_Management:10114,//SIM卡管理
    Data_SIM_Card:10115,//流量卡

    Public_BicycleCharge_AlarmInformation: 10194, // 告警信息
    Public_BicycleCharge_deviceMsg: 10195, // 设备消息

    //有序能源
    orderlyEnergy:905, //有序能源
    orderlyEnergy_Home:906, //首页
    orderlyEnergy_SchedulingModel:907,//调度模型
    //充电互联
    chargeConnect: 10143, //充电互联
    Public_connect_auth: 10144, //互联授权
    connectCity_authorizedStation: 10145, //授权电站
    connectCity_authPile: 10146, //授权充电桩
    connectCity_differenceOrder: 10147, //差异订单

    // 单车充电
    Public_BicycleCharge: 724, // 两轮车充电
    Public_BicycleCharge_StationManage: 725, // 站点管理
    Public_BicycleCharge_Station: 726, // 充电站
    Public_BicycleCharge_Area: 899, // 充电区
    Public_BicycleCharge_Pile: 727, // 充电桩
    Public_BicycleCharge_Door: 928, // 门禁设备
    Public_BicycleCharge_BillingManage: 728, // 计费管理
    Public_BicycleCharge_ChargingSet: 729, // 计费标准
    Public_BicycleCharge_MonthRuleSet: 900, // 包月计费
    Public_BicycleCharge_Rule_Park: 929, // 停车计费
    Public_BicycleCharge_OrderManage: 730, // 订单管理
    Public_BicycleCharge_ChargeOrder: 731, // 在充订单
    Public_BicycleCharge_OrderRecord: 732, // 订单记录
    Public_BicycleCharge_AccountSplitting:914,//分账订单
    Public_BicycleCharge_Order_Park: 930, // 停车订单
    Public_BicycleCharge_FinancialStatistics: 757,//统计分析
    Public_bicycleCharge_chargeDaily:758,//充电统计
    Public_bicycleCharge_shareDaily:759,//分账统计
    Public_bicycleCharge_shareArrivalDaily:760,//分账到账统计
    Public_BicycleCharge_UserManage: 885,// 用户管理
    Public_BicycleCharge_User: 901,// 用户管理
    Public_BicycleCharge_User_Park: 931,// 车辆管理
    Public_BicycleCharge_MonthCardRecord: 902,// 包月订单
    Public_BicycleCharge_PhysicalCard: 886,// 实体卡管理
    Public_BicycleCharge_ElectronicsCard: 940,// 电子卡管理
	  Public_BicycleCharge_User_AccessRecord: 933,// 出入记录
    city_videoPort: 745, // 视频接口
    city_Order_Picture_Storage: 768, // 订单图片存储
    development: 936, // 开发配置
    cityDeviceManage_equipmentModel:10171,//设备型号
    cityDeviceManage_productModel:10170,//产品型号
    city_DeviceManage_carPile:10174,//汽车充电桩
    city_DeviceManage_unbind:10175,//蓝牙桩解绑
};
const showCityItems_const = {
  Data_SIM_Management:false,//SIM卡管理
  Data_SIM_Card:false,//流量卡
    // city_account: false,
    my_account:false,
    citySerManage: false,
    citySerManage_serManagePage: false,
    cityUnionManage: false,
    cityUnionManage_unionManagePage: false,
    cityParkManage: false,
    my_park_vehicle: false, //新能源车概况
    city_dutyCenter:false,
    city_watchCenter:false,
    cityParkManage_parkManagePage: false,
    cityParkManage_parkManagePage_4g:false,
    cityParkManage_materielPage:false,
    city4GCameraManage_cameraPage:false,
    cityTrade:false,
    cityTrade_MoneyRecord:false,
    cityTrade_MoneyPay:false,
    cityStatistics_NewUnionProfit: false,
    cityResources: false,
    cityResources_RoleManage: false,
    cityResources_EmployeeManage: false,
    cityResources_MessageNtification: false,
    citySettingManage: false,
    citySettingManage_settingPage: false,
    citySettingManage_authPage:false,
    citySettingYunPrivate_Config:false,
    city_systemMange: false,
    cityAddService:false,
    cityAddService_sms:false,
    Public_AddServices_MarketingAuthorization:false, //销售授权
    Public_AddServices_ServicePrice:false,//服务价格
    cityAddService_screen:false,
    cityAddService_shopApp:false,
    cityAddService_program:false,
    cityAddService_parkApp:false,
    cityAddService_openPlatform:false,
    cityAddService_video:false,
    cityAddService_face:false,
    cityDeviceManage:false,
    cityDeviceManage_deviceManage:false,
    city_AddService_Cam_Video:false,
    city_AddService_Car_Distinguish:false,
    cityfour_serve:false,
    cityDeviceManage_smartScreen:false,
    cityAdManage_http_Manage:false,
    city_DeviceManage_bicyclePileUpdate:false,

    // 汽车充电桩
    Public_CarCharge: false, // 汽车运营管理
    Public_CarCharge_CalculaterManage: false, // 计费管理
    Public_CarCharge_Rule: false, // 充电费
    Public_CarCharge_OrderRule: false, // 预约费
    Public_CarCharge_SeatRule: false, // 占位费
    Public_CarCharge_StationManage: false, // 电站管理
    Public_CarCharge_Station: false, // 电站
    Public_CarCharge_Area: false, // 区域
    Public_CarCharge_ParkingLot: false, // 车位
    Public_CarCharge_Device: false, // 设备管理
    Public_CarCharge_DeviceList: false, // 设备列表
    Public_CarCharge_Pile: false, // 充电桩
    Public_CarCharge_Camera: false, // 相机
    Public_CarCharge_GroundLock: false, // 地锁
    Public_CarCharge_Monitor:false,//监控
    Public_CarCharge_Order: false, // 订单管理
    Public_CarCharge_Order_Charge: false, // 在充订单
    Public_CarCharge_Order_Record: false, // 订单记录
    Public_CarCharge_Order_Reduction: false, // 减免订单
    Public_CarCharge_Order_ReductionSummary: false, // 减免汇总
    Public_CarCharge_Order_Share: false, // 分账订单
    Public_CarCharge_Order_Order: false, // 预约订单
    Public_CarCharge_Order_Seat: false, // 占位订单
    Public_CarCharge_Order_Arrearage:false, //欠费记录
    Public_CarCharge_Statistics: false, // 统计分析
    Public_CarCharge_Statistics_Charge: false, // 充电统计
    Public_CarCharge_Statistics_Share: false, // 分账统计
    Public_CarCharge_Statistics_ShareArrival: false, // 分账到账统计
    Public_CarCharge_Store: false, // 库存管理
    Public_CarCharge_Store_Operate: false, // 设备出库
    Public_CarCharge_Store_FlowRecord: false, // 流转记录
    Public_CarCharge_UserManage:false,
    Public_CarCharge_User:false,//用户管理
    Public_CarCharge_ElectronicsCard:false, //电子卡管理
    Public_CarCharge_PhysicalCard:false,//实体卡管理
    Public_CarCharge_PhysicalVIN:false,//VIN码
    Public_CarCharge_cardRecord:false,//卡记录
    Public_CarCharge_User_ChargingActivities:false, //充电活动
    Public_CarCharge_User_ActivitiesRecord:false,//活动记录

    //有序能源
    orderlyEnergy:false, //有序能源
    orderlyEnergy_Home:false, //首页
    orderlyEnergy_SchedulingModel:false,//调度模型

    chargeConnect: false, //充电互联
    Public_connect_auth: false, //互联授权
    connectCity_authorizedStation: false, //授权电站
    connectCity_authPile: false, //授权充电桩
    connectCity_differenceOrder: false, //差异订单

    // 单车充电
    Public_BicycleCharge: false, // 两轮车充电
    Public_BicycleCharge_StationManage: false, // 站点管理
    Public_BicycleCharge_Station: false, // 充电站
    Public_BicycleCharge_Area: false, // 充电区
    Public_BicycleCharge_Pile: false, // 充电桩
	  Public_BicycleCharge_Door: false, // 门禁设备
    Public_BicycleCharge_BillingManage: false, // 计费管理
    Public_BicycleCharge_ChargingSet: false, // 计费标准
    Public_BicycleCharge_MonthRuleSet: false, // 包月计费
	  Public_BicycleCharge_Rule_Park: false, // 停车计费
    Public_BicycleCharge_OrderManage: false, // 订单管理
    Public_BicycleCharge_ChargeOrder: false, // 在充订单
    Public_BicycleCharge_OrderRecord: false, // 订单记录
    Public_BicycleCharge_AccountSplitting:false,//分账订单
    Public_BicycleCharge_Order_Park: false, // 停车订单
    Public_BicycleCharge_FinancialStatistics:false, //统计分析
    Public_bicycleCharge_chargeDaily:false,//充电统计
    Public_bicycleCharge_shareDaily:false,//分账统计
    Public_bicycleCharge_shareArrivalDaily:false,//分账到账统计
    Public_BicycleCharge_UserManage: false,// 用户管理
    Public_BicycleCharge_User: false,// 用户管理
	  Public_BicycleCharge_User_Park: false,// 车辆管理
    Public_BicycleCharge_MonthCardRecord: false,// 包月订单
    Public_BicycleCharge_PhysicalCard: false,// 实体卡管理
    Public_BicycleCharge_ElectronicsCard: false,// 电子卡管理
	  Public_BicycleCharge_User_AccessRecord: false,// 出入记录

    managementOfJointStation: false, // 电站投建
    managementOfJointStation_depotManagement: false, // 电站投资
    testingOfEquipment:false,//设备测试
    testingOfEquipment_chargingTest:false,//充电测试
    testingOfEquipment_testRecord:false,//测试记录

    city_videoPort: false, // 视频接口
    city_Order_Picture_Storage: false, // 订单图片存储
    development:false,
    cityDeviceManage_equipmentModel:false,//设备型号
    cityDeviceManage_productModel:false,//产品型号
    city_DeviceManage_carPile:false,//汽车充电桩
    city_DeviceManage_unbind:false,//蓝牙桩解绑
    Public_BicycleCharge_AlarmInformation: false, // 告警信息
    Public_BicycleCharge_deviceMsg: false, // 设备消息
};
/*---------end------------------*/
/**
 * 运维平台权限
 * EOMS
 * oid:12
 */
const AUTH_ID_EOMS = {
    eomsParkManage:534,
    eomsEventManage:532,
    eomsEventManage_choosePark:533,
    eomsEmployeeManage:623,
    eomsRoleManage: 10099,
};
const showEomsItems_const = {
    eomsParkManage:false,
    eomsEventManage:false,
    eomsEventManage_choosePark:false,
    eomsEmployeeManage:false,
    eomsRoleManage: false,
}


/**
 * 总后台下属的权限
 * ADMIN
 * oid:15
 */
const AUTH_ID_ADMIN = {
  adminMyAccount: 937, // 首页
  adminCityManage:538,//厂商管理
  adminCityManage_cityManage:539,//厂商管理
  adminServiceProviderManage: 711,
  adminServiceProviderManage_ServiceProviderManage: 712,
  cityGroupManage:632,//运营集团
  upgradeExplain:647,//升级说明
  adminCityManage_privateCloudManage:540,//私有云管理
  Data_SIM_Management:10105,//SIM卡管理
  Data_SIM_Card:10106,//流量卡
  SIM_Statistics:10120,//流量卡统计
  adminParkManage:543,//车场管理
  adminParkManage_parkManage:544,//车场管理
  adminParkManage_ActivationCode:713,// 车场激活码
  adminParkManage_blacklist:545,//黑名单管理
  adminParkManage_whitelist:546,//白名单
  adminParkManage_depotEmployee:547,//车场员工

  adminEomsManage: 10183, // 云值守中心
  adminEomsManage_eomsManage: 10184, // 服务中心
  adminEomsManage_miniProgramManage: 10185, // 小程序代运维平台
  SecretKeyConfig: 10186, // 秘钥配置
  Provisioning: 10187, // 服务开通

  adminAddServices:548,//平台服务
  adminAddService_sms:549,//短信服务
  adminAddService_screen:550,//大屏服务
  adminAddService_shopApp:551,//商户公众号
  adminAddService_program:553,//小程序收费
  adminAddService_parkApp:552,//车场公众号
  adminAddService_openPlatform:555,//开放接口
  adminAddService_video:556,//语音购买
  adminAddService_fourG:554,//纯云服务
  adminAddService_faceAccess:557,//人脸门禁
  adminCamVideoSever:684,//视频VIP服务
  adminCarDistinguish:695,//手动车牌识别
  adminAuthSet:558,//权限设置
  adminAuthSet_editAuth:559,//权限设置
  adminAuthSet_authManage:560,//权限管理
  adminAuthSet_authManage_city:561,//权限管理-厂商
  adminAuthSet_authManage_service:562,//权限管理-服务商
  adminAuthSet_authManage_union:563,//权限管理-集团
  adminAuthSet_authManage_park:564,//权限管理-车场
  adminAuthSet_authManage_shop:565,//权限管理-商户
  adminAuthSet_authManage_eoms:566,//权限管理-运维
  adminDeviceManage:567,//设备管理
  adminDeviceManage_deviceManage:568,//
  admin_DeviceManage_productType:10160,//产品类型
  admin_DeviceManage_productModel:10158,//产品型号
  admin_DeviceManage_equipmentModel:10159,//设备型号
  admin_DeviceManage_carPile:10161,//汽车充电桩
  admin_DeviceManage_bicyclePile:10162,//单车充电桩
    adminAdManage_smartScreen:578,//智慧屏管理
    device_manage_admin:569,//人脸设备管理
  adminAbnormalWatch:570,//异常监控
  adminAbnormalWatch_park:571,//设备管理-车场
  adminAbnormalWatch_timer:572,//设备管理-定时任务
  adminAbnormalWatch_log:573,//设备管理-日志
  adminAbnormalWatch_androidLog: 612,// 异常监控-安卓日志
  adminAbnormalWatch_shieldCarnumber: 10247,// 异常监控-屏蔽车牌查询
  statisticalAnalysis: 582,// 统计分析
  statisticalAnalysis_parkAlive: 583,// 统计分析-车场日活
    statisticalAnalysis_parkUse: 649,// 统计分析-车场使用日活
  statisticalAnalysis_merchantsAlive: 585,// 统计分析-商户日活
  statisticalAnalysis_parkOrder: 709, // 统计分析 - 车场订单统计
  admin_Order_Picture_Storage: 767, // 订单图片存储

  Public_CarCharge_Manage:954, // 新能源充电
  Public_CarCharge_ChargeData:1005,//数据仪表
  Public_CarCharge_CalculaterManage: 957,// 计费管理
  Public_CarCharge_ChargeRule: 964, // 充电费
  Public_CarCharge_OrderRule: 965, // 预约费
  Public_CarCharge_SeatRule: 966, //站位费
  Public_CarCharge_StationManage:958, //电站管理
  Public_Station: 967, //电站
  Public_CarCharge_Area: 968, // 区域
  Public_CarCharge_ParkingLot: 969, //车位
  Public_CarCharge_Device: 959, // 设备管理
  Admin_CarCharge_EuquipmentType: 970, // 设备类型
  Public_CarCharge_DeviceList: 971, // 设备列表
  Public_CarCharge_Pile: 972, //充电桩
  Public_CarCharge_Camera: 973, //相机
  Public_CarCharge_GroundLock: 974, //地锁
  Public_CarCharge_Monitor:10095,//监控
  Public_CarCharge_Order: 960, // 订单管理
  Public_CarCharge_Order_Charge: 975, // 在充订单
  Public_CarCharge_Order_Record: 976, // 订单记录
  Public_CarCharge_Order_Reduction: 977, // 减免记录
  Public_CarCharge_Order_ReductionSummary: 978, // 减免汇总
  Public_CarCharge_Order_Share: 979, // 分账订单
  Public_CarCharge_Order_Order: 980, // 预约订单
  Public_CarCharge_Order_Seat: 981, //占位订单
  Public_CarCharge_Order_Arrearage: 1006, //欠费记录
  Public_CarCharge_Statistics: 961, // 统计分析
  Public_CarCharge_Statistics_Charge: 982, // 充电统计
  Public_CarCharge_Statistics_Share: 983, // 分账统计
  Public_CarCharge_Statistics_ShareArrival: 984, // 分账到账统计
  Public_CarCharge_Store: 962, // 库存管理
  Public_CarCharge_Store_Operate: 985, // 库存运维
  Public_CarCharge_Store_FlowRecord: 986, // 流转记录
  Public_CarCharge_euquipment: 963, // 设备运维
  Public_CarCharge_Store_FirmwareManagement:10182,//固件管理
  Public_CarCharge_pilePrivManage: 10163, // 私桩管理
  Public_CarCharge_UserManage:10052,
  Public_CarCharge_User:10053,//用户管理
  Public_CarCharge_ElectronicsCard:10054, //电子卡管理
  Public_CarCharge_PhysicalCard:10055,//实体卡管理
  Public_CarCharge_PhysicalVIN:10239,//VIN码
  Public_CarCharge_cardRecord:10240,//卡记录
  Public_CarCharge_User_ChargingActivities:10056, //充电活动
  Public_CarCharge_User_ActivitiesRecord:10057,//活动记录

  Public_BicycleCharge: 956, // 两轮车充电
  Public_BicycleCharge_StationManage: 991, // 站点管理
  Public_BicycleCharge_Station: 997, // 充电站
  Public_BicycleCharge_Area: 1007, // 充电区
  Public_BicycleCharge_Pile: 998, // 充电桩
  Public_BicycleCharge_Door: 1008, // 门禁设备
  Public_BicycleCharge_BillingManage: 992, // 计费管理
  Public_BicycleCharge_ChargingSet: 999, // 计费标准
  Public_BicycleCharge_MonthRuleSet: 1009, // 包月计费
  Public_BicycleCharge_Rule_Park: 1010, // 停车计费
  Public_BicycleCharge_OrderManage: 993, // 订单管理
  Public_BicycleCharge_ChargeOrder: 1000, // 在充订单
  Public_BicycleCharge_OrderRecord: 1001, // 订单记录
  Public_BicycleCharge_AccountSplitting: 1011, // 分账订单
  Public_BicycleCharge_Order_Park: 1012, // 停车订单
  Public_BicycleCharge_FinancialStatistics: 994, // 统计分析
  Public_bicycleCharge_chargeDaily: 1002, // 充电统计
  Public_bicycleCharge_shareDaily: 1003, // 分账统计
  Public_bicycleCharge_shareArrivalDaily: 1004, // 分账到账统计
  Public_BicycleCharge_UserManage: 1013, // 用户管理
  Public_BicycleCharge_User: 1014, // 用户管理 - 用户管理
  Public_BicycleCharge_User_Park: 1015, //车辆管理
  Public_BicycleCharge_PhysicalCard: 1016, // 实体卡管理
  Public_BicycleCharge_ElectronicsCard: 941,// 电子卡管理
  Public_BicycleCharge_MonthCardRecord: 1017, // 包月订单
  Public_BicycleCharge_User_AccessRecord: 1018, // 出入记录
  Public_BicycleCharge_AlarmInformation: 10192, // 告警消息
  Public_BicycleCharge_deviceMsg: 10193, // 设备消息
  admin_interconnection: 955, // 充电互联
  admin_interconnection_Company: 987, //互联企业
  admin_interconnection_auth: 10121, // 互联授权
  admin_interconnection_authorizedStation: 10135, // 授权电站
  admin_interconnection_authPile: 10136, // 授权桩
  admin_interconnection_differenceOrder: 10137, // 差异订单
  admin_interconnection_connectOrderManage: 998, // 互联接入
  admin_interconnection_stationData: 10138, // 互联电站
  admin_interconnection_pileData: 10142, // 互联充电桩
  admin_interconnection_chargeOrder: 10140, // 在充订单
  admin_interconnection_orderRecord: 10141, // 订单记录

  managementOfJointStation: 1019, // 电站投建
  managementOfJointStation_depotManagement: 1020, // 场站管理
  managementOfJointStation_contractManagement: 1021, // 合同管理
  managementOfJointStation_outboundDeliveryOrder: 1022, // 出库单
  managementOfJointStation_checkAndAccept: 10100, // 场站验收
  managementOfJointStation_deliveryStatistics: 10101, // 发货统计

  orderlyEnergy:1027, // 有序能源
  orderlyEnergy_Home: 1028, // 首页
  orderlyEnergy_SchedulingModel: 1029, //调度模型
  

  adminResourceManagement: 10102, // 资源管理
  adminResourceManagement_downloadCenter: 10103, // 下载中心
  
  OwingCenter:10179, // 欠费中心
  OwingSignPage:10180, // 欠费标签
  OwingOrderPage:10181, // 欠费订单

  healthBoard: 10237, //健康看板
  healthBoard_serveList: 10238, //服务列表
};
const showADMINItems_const = {
  adminMyAccount: false, // 首页
  adminCityManage:false,//厂商管理
  adminCityManage_cityManage:false,//厂商管理
  adminServiceProviderManage: false,
  adminServiceProviderManage_ServiceProviderManage: false,
  cityGroupManage:false,//运营集团
  upgradeExplain:false,//升级说明
  adminCityManage_privateCloudManage:false,//私有云管理
  adminEomsManage:false,//运维平台
  adminEomsManage_eomsManage:false,//运维平台
  Data_SIM_Management:false,//SIM卡管理
  Data_SIM_Card:false,//流量卡
  adminParkManage:false,//车场管理
  adminParkManage_parkManage:false,//车场管理
  adminParkManage_ActivationCode:false,// 车场激活码
  adminParkManage_blacklist:false,//黑名单管理
  adminParkManage_whitelist:false,//白名单
  adminParkManage_depotEmployee:false,//车场员工
  adminEomsManage_miniProgramManage: false, // 小程序代运维平台
  SecretKeyConfig: false, // 秘钥配置
  Provisioning: false, // 服务开通

  adminAddServices:false,//平台服务
  adminAddService_sms:false,//短信服务
  adminAddService_screen:false,//大屏服务
  adminAddService_shopApp:false,//商户公众号
  adminAddService_program:false,//小程序收费
  adminAddService_parkApp:false,//车场公众号
  adminAddService_openPlatform:false,//开放接口
  adminAddService_video:false,//语音购买
  adminAddService_fourG:false,//纯云服务
  adminAddService_faceAccess:false,//人脸门禁
  adminCamVideoSever:false,//视频VIP
  adminCarDistinguish:false,//手动车牌识别
  adminAuthSet:false,//权限设置
  adminAuthSet_editAuth:false,//权限设置
  adminAuthSet_authManage:false,//权限管理
  adminAuthSet_authManage_city:false,//权限管理-厂商
  adminAuthSet_authManage_service:false,//权限管理-服务商
  adminAuthSet_authManage_union:false,//权限管理-集团
  adminAuthSet_authManage_park:false,//权限管理-车场
  adminAuthSet_authManage_shop:false,//权限管理-商户
  adminAuthSet_authManage_eoms:false,//权限管理-运维
  adminDeviceManage:false,//设备管理
  admin_DeviceManage_productType:false,//产品类型
  admin_DeviceManage_productModel:false,//产品型号
  admin_DeviceManage_equipmentModel:false,//设备型号
  admin_DeviceManage_carPile:false,//汽车充电桩
  admin_DeviceManage_bicyclePile:false,//单车充电桩
  adminDeviceManage_deviceManage:false,//设备管理
    adminAdManage_smartScreen:false,//广告屏
    device_manage_admin:false,//人脸设备管理
  adminAbnormalWatch:false,//异常监控
  adminAbnormalWatch_park:false,//设备管理-车场
  adminAbnormalWatch_timer:false,//设备管理-定时任务
  adminAbnormalWatch_log:false,//设备管理-日志
  adminAbnormalWatch_androidLog: false,// 异常监控-安卓日志
  adminAbnormalWatch_shieldCarnumber: false,
  statisticalAnalysis: false,// 统计分析
    statisticalAnalysis_parkUse: false,// 统计分析-车场使用日活
  statisticalAnalysis_merchantsAlive: false,// 统计分析-商户日活
  statisticalAnalysis_parkOrder: false, // 统计分析 - 车场订单统计
  admin_Order_Picture_Storage: false, // 订单图片存储

  Public_CarCharge_Manage:false, // 新能源充电
  Public_CarCharge_ChargeData:false,//数据仪表
  Public_CarCharge_CalculaterManage: false,// 计费管理
  Public_CarCharge_ChargeRule: false, // 充电费
  Public_CarCharge_OrderRule: false, // 预约费
  Public_CarCharge_SeatRule: false, //站位费
  Public_CarCharge_StationManage:false, //电站管理
  Public_Station: false, //电站
  Public_CarCharge_Area: false, // 区域
  Public_CarCharge_ParkingLot: false, //车位
  Public_CarCharge_Device: false, // 设备管理
  Admin_CarCharge_EuquipmentType: false, // 设备类型
  Public_CarCharge_DeviceList: false, // 设备列表
  Public_CarCharge_Pile: false, //充电桩
  Public_CarCharge_Camera: false, //相机
  Public_CarCharge_GroundLock: false, //地锁
  Public_CarCharge_Monitor:false, //监控
  Public_CarCharge_Order: false, // 订单管理
  Public_CarCharge_Order_Charge: false, // 在充订单
  Public_CarCharge_Order_Record: false, // 订单记录
  Public_CarCharge_Order_Reduction: false, // 减免记录
  Public_CarCharge_Order_ReductionSummary: false, // 减免汇总
  Public_CarCharge_Order_Share: false, // 分账订单
  Public_CarCharge_Order_Order: false, // 预约订单
  Public_CarCharge_Order_Seat: false, //占位订单
  Public_CarCharge_Order_Arrearage: false, //欠费记录
  Public_CarCharge_Statistics: false, // 统计分析
  Public_CarCharge_Statistics_Charge: false, // 充电统计
  Public_CarCharge_Statistics_Share: false, // 分账统计
  Public_CarCharge_Statistics_ShareArrival: false, // 分账到账统计
  Public_CarCharge_Store: false, // 库存管理
  Public_CarCharge_Store_Operate: false, // 库存运维
  Public_CarCharge_Store_FlowRecord: false, // 流转记录
  Public_CarCharge_euquipment: false, // 设备运维
  Public_CarCharge_Store_FirmwareManagement:false,//固件管理
  Public_CarCharge_pilePrivManage: false, // 私桩管理
  Public_CarCharge_UserManage:false,
  Public_CarCharge_User:false,//用户管理
  Public_CarCharge_ElectronicsCard:false, //电子卡管理
  Public_CarCharge_PhysicalCard:false,//实体卡管理
  Public_CarCharge_PhysicalVIN:false,//VIN码
  Public_CarCharge_cardRecord:false,//卡记录
  Public_CarCharge_User_ChargingActivities:false, //充电活动
  Public_CarCharge_User_ActivitiesRecord:false,//活动记录

  Public_BicycleCharge: false, // 两轮车充电
  Public_BicycleCharge_StationManage: false, // 站点管理
  Public_BicycleCharge_Station: false, // 充电站
  Public_BicycleCharge_Area: false, // 充电区
  Public_BicycleCharge_Pile: false, // 充电桩
  Public_BicycleCharge_Door: false, // 门禁设备
  Public_BicycleCharge_BillingManage: false, // 计费管理
  Public_BicycleCharge_ChargingSet: false, // 计费标准
  Public_BicycleCharge_MonthRuleSet: false, // 包月计费
  Public_BicycleCharge_Rule_Park: false, // 停车计费
  Public_BicycleCharge_OrderManage: false, // 订单管理
  Public_BicycleCharge_ChargeOrder: false, // 在充订单
  Public_BicycleCharge_OrderRecord: false, // 订单记录
  Public_BicycleCharge_AccountSplitting: false, // 分账订单
  Public_BicycleCharge_Order_Park: false, // 停车订单
  Public_BicycleCharge_FinancialStatistics: false, // 统计分析
  Public_bicycleCharge_chargeDaily: false, // 充电统计
  Public_bicycleCharge_shareDaily: false, // 分账统计
  Public_bicycleCharge_shareArrivalDaily: false, // 分账到账统计
  Public_BicycleCharge_UserManage: false, // 用户管理
  Public_BicycleCharge_User: false, // 用户管理 - 用户管理
  Public_BicycleCharge_User_Park: false, //车辆管理
  Public_BicycleCharge_PhysicalCard: false, // 实体卡管理
  Public_BicycleCharge_ElectronicsCard: false,// 电子卡管理
  Public_BicycleCharge_MonthCardRecord: false, // 包月订单
  Public_BicycleCharge_User_AccessRecord: false, // 出入记录
  Public_BicycleCharge_AlarmInformation: false, // 告警消息
  Public_BicycleCharge_deviceMsg: false, // 设备消息
  admin_interconnection: false, // 充电互联
  admin_interconnection_Company: false, //互联企业
  admin_interconnection_auth: false, // 互联授权
  admin_interconnection_authorizedStation: false, // 授权电站
  admin_interconnection_authPile: false, // 授权桩
  admin_interconnection_differenceOrder: false, // 差异订单
  admin_interconnection_connectOrderManage: false, // 互联接入
  admin_interconnection_stationData: false, // 互联电站
  admin_interconnection_pileData: false, // 互联充电桩
  admin_interconnection_chargeOrder: false, // 在充订单
  admin_interconnection_orderRecord: false, // 订单记录

  managementOfJointStation: false, // 电站投建
  managementOfJointStation_depotManagement: false, // 场站管理
  managementOfJointStation_contractManagement: false, // 合同管理
  managementOfJointStation_outboundDeliveryOrder: false, // 出库单
  managementOfJointStation_checkAndAccept: false, // 场站验收
  managementOfJointStation_deliveryStatistics: false, // 发货统计

  orderlyEnergy:false, // 有序能源
  orderlyEnergy_Home: false, // 首页
  orderlyEnergy_SchedulingModel: false, //调度模型

  adminResourceManagement: false, // 资源管理
  adminResourceManagement_downloadCenter: false, // 下载中心
  OwingCenter:false, // 欠费中心
  OwingSignPage:false, // 欠费标签
  OwingOrderPage:false, // 欠费订单

  healthBoard: false, //健康看板
  healthBoard_serveList: false, //服务列表
}


const ROLE_ID = {
    // 30 车场,26集团,,,27渠道,,28联盟,,,29城市
    //2018.2.6修改 2 集团...............8 车场 ........   7城市
    GROUP: 222,
    CHANNEL: 27,
    UNION: 2,
    CITY: 7,
    PARK: 8,
    BOSS:5,
    CITYREGIS:1001,
    SHOP:10,
    SERVER:11,
    EOMS:12,
    SUBADMIN:15
};


module.exports = {
    AUTH_ID,AUTH_ID_ADMIN,showADMINItems_const, showParkItem_const,
    AUTH_ID_UNION,showUnionItem_const,
    showShopItem_const,AUTH_ID_SHOP,
    showBossItem_const,
    AUTH_ID_SERVER,showServerItems_const,
    AUTH_ID_CITY,showCityItems_const,
    AUTH_ID_EOMS,showEomsItems_const,
    ROLE_ID
}
