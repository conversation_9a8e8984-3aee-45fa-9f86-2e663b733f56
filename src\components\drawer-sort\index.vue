<template>
  <div class="drawer-wrapper">
<!--    <header style="padding: 20px 0 10px 0;text-align: center">车道排序</header>-->
    <p class="drawer-tips">*按顺序从上到下显示，可拖拽调整顺序</p>
    <div class="drawer-list">
      <div
        class="drawer-item"
        v-for="item in sortData" v-dragging="{ item: item, list: sortData, group: 'item' }"
        :key="item.id">
        <span class="drawer-item__text">{{item.passname}}</span>
        <span class="drawer-icon"></span>
      </div>
    </div>
    <div class="drawer-footer">
      <el-button round type="primary" :loading="submitLoading" @click="onSubmit" style="width: 100%">确认</el-button>
    </div>

  </div>
</template>

<script>
export default {
  name: "drawer-sort",
  props:{
    sortData:{
      type:Array,
      default:()=>{
        return []
      }
    },
    editapi:{
      type:String,
      default:()=>{
          return ''
      }
    },
  },
  data () {
    return {
      submitLoading:false,
      submitData:[]
    }
  },
  mounted () {
    this.$dragging.$on('dragged', ({ value }) => {
      this.submitData = value.list;
    })
    this.$dragging.$on('dragend', () => {

    })
  },
  methods:{
    filterData(data){
      let arr = [];
      if(data.length>0){
        data.map((item,index)=>{
          arr.push({
            id:item.id,
            index:index
          })
        })
      }else{
        arr = [];
      }
      return arr;
    },
    onSubmit(){
        let sortData;
        if(this.submitData.length > 0){
            sortData = this.filterData(this.submitData);
        }else{
            sortData = this.filterData(this.sortData);
        }
      // let sortData = this.filterData(this.submitData);
      let laneSortData = {
        comid:sessionStorage.getItem('comid'),
        sortData:JSON.stringify(sortData)
      }
      this.submitLoading = true;
      this.$axios.post(this.editapi,this.$qs.stringify(laneSortData)).then(res=>{
        let data = res.data;
        if(data.state === "1"){
          this.$message({
            message: data.message,
            type: 'success'
          });
        }else{
          this.$message({
            showClose: true,
            message: data.message,
            type: 'error'
          });
        }
        this.submitLoading = false;
      }).catch(err=>{
        this.submitLoading = false;
      })
    }
  },
}
</script>

<style lang="scss" scoped>
  .drawer-wrapper{
    padding: 0 10px;
    .drawer-tips{
      font-size: 16px;
      color: #5e5e5e;
      padding-bottom: 20px;
    }
    .drawer-list{
      height: calc(100vh - 190px);
      overflow: auto;
      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    }
    .drawer-item{
      position: relative;
      padding: 4px 6px;
      margin-bottom: 10px;
      border:1px solid #eee;
      cursor: move;
      font-size: 16px;
      height: 20px;
      line-height: 20px;
    }
    .drawer-item:active{
      background: rgba(30,159,225,0.6);
      color: #fff;
    }
    .drawer-item__text{
      display: inline-block;
      width: calc(100% - 70px);
      white-space:nowrap;
      overflow:hidden;
      text-overflow:ellipsis;
    }
    .drawer-icon{
      position: absolute;
      top:6px;
      right: 19px;
      display: inline-block;
      width: 20px;
      height: 12px;
      margin: 0;
      padding: 0;
      border-top: 2px solid #5e5e5e;
      border-bottom: 2px solid #5e5e5e;
    }
    .drawer-icon:after{
      position: absolute;
      top:50%;
      left: 0;
      transform: translateY(-50%);
      width: 100%;
      height: 2px;
      background: #5e5e5e;
      content: '';
    }
    .drawer-footer{
      padding: 10px 20px;

    }
  }
</style>
