import request from '@/api/axiosService'
import { head } from 'shelljs'

// 机场查询
export const fetchAirportList = (requestUrl,params) => {
  return request({
    url: `${requestUrl}/admin/airport`,
    method: 'get',
    params
  })
}

// 机场上下架
export const deleteAirport = (requestUrl,id) => {
  return request({
    url: `${requestUrl}/admin/airport/${id}/status`,
    headers: {
      'Content-Type': 'application/json'
    },
    method: 'patch'
  })
}

// 机场下拉列表
export const dropdownAirport = (requestUrl,params) => {
  return request({
    url: `${requestUrl}/admin/airport/dropdown`,
    method: 'get',
    params
  })
}

// 新增机场
export const createAirport = (requestUrl, data) => {
  return request({
    url: `${requestUrl}/admin/airport`,
    method: 'post',
    data
  })
}

// 更新机场
export const updateAirport = (requestUrl, id, data) => {
  return request({
    url: `${requestUrl}/admin/airport/${id}`,
    method: 'put',
    data
  })
}

// 机场车场查询
export const fetchParkingList = (requestUrl, params) => {
  return request({
    url: `${requestUrl}/admin/parking-lot`,
    method: 'get',
    params
  })
}

// 更新机场车场状态
export const updateParkingStatus = (requestUrl, id, data,params) => {
  return request({
    url: `${requestUrl}/admin/parking-lot/${id}/status`,
    method: 'patch',
    data,
    params
  })
}

//根据 id 获取老系统车场名称
export const fetchParkSettlement = (requestUrl, id,params) => {
  return request({
    url: `${requestUrl}/admin/settlement-subject/com-info/${id}`,
    method: 'get',
    params
  })
}

// 新增车场
export const addParking = (requestUrl,data) => {
  return request({
    url: `${requestUrl}/admin/parking-lot`,
    method: 'post',
    data
  })
}
//车场详情
export const detailsParking = (requestUrl, id,params) => {
  return request({
    url: `${requestUrl}/admin/parking-lot/${id}`,
    method: 'get',
    params
  })
}

//停车详情
export const detailsParkConfig = (requestUrl, id,params) => {
  return request({
    url: `${requestUrl}/admin/parking-config/${id}`,
    method: 'get',
    params
  })
}

//保存停车配置
export const saveParkConfig = (requestUrl,data) => {
  return request({
    url: `${requestUrl}/admin/parking-config`,
    method: 'post',
    data
  })
}

// 编辑车场
export const updateParking = (requestUrl, id, data) => {
  return request({
    url: `${requestUrl}/admin/parking-lot/${id}`,
    method: 'put',
    data
  })
}

// 上下架车场
export const deleteParking = (requestUrl,id) => {
  return request({
    url: `${requestUrl}/admin/parking-lot/${id}/status`,
    method: 'delete'
  })
}

// 新增机场（修正方法名）
export const addAirport = (requestUrl,data) => {
  return request({
    url: `${requestUrl}/admin/airport`,
    method: 'post',
    data
  })
}


// 预约订单管理
export const reserveOrderList = (requestUrl,params) => {
  console.log(requestUrl,'requestUrl')
  return request({
    url: `${requestUrl}/admin/reserve-order`,
    method: 'get',
    params
  })
}

//人工结算
export const settleOrderList = (requestUrl, id, data) => {
  return request({
    url: `${requestUrl}/admin/reserve-order/${id}/settle`,
    method: 'put',
    data
  })
}

// 电子发票管理
export const fetchInvoiceList = (requestUrl,params) => {
  return request({
    url: `${requestUrl}/admin/invoice`,
    method: 'get',
    params
  })
}

//电子发票状态流转
export const statusInvoiceNext = (requestUrl, id, data) => {
  return request({
    url: `${requestUrl}/admin/invoice/${id}/next-status`,
    method: 'put',
    data
  })
}

// 分账统计
export const fetchsharestatisticList = (requestUrl,params) => {
  return request({
    url: `${requestUrl}/stat/share`,
    method: 'get',
    params
  })
}

// 订单统计
export const fetchOrderstatisticList = (requestUrl,params) => {
  return request({
    url: `${requestUrl}/admin/reserve-order/statistic`,
    method: 'get',
    params
  })
}

// 提现明细
export const fetchWithdrawalList = (requestUrl,params) => {
  return request({
    url: `${requestUrl}/stat/account-detail`,
    method: 'get',
    params
  })
}

