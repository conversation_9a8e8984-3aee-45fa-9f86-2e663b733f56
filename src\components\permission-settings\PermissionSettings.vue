<template>
    <div>
        <el-dialog
                width="650px"
                :visible.sync="isShowPermission"
                custom-class="custom-dialog"
                :show-close="false"
                :close-on-click-modal="false"
                @close="()=>{this.$emit('close')}"
        >
            <header class="dialog-header" slot="title">
                权限设置<i class="el-icon-close dialog-header-iconfont" @click="isShowPermission = false"></i>
            </header>
            <div class="tab-wrapper" v-if="getOrigin">
                <div class="custom-tab">
                    <p class="custom-tab-item" :class="activeIndex ===0?'custom-tab__active':''" @click="handleToggle(0)">平台页面</p>
<!--                    <p class="custom-tab-item" :class="activeIndex ===1?'custom-tab__active':''" @click="handleToggle(1)">消息通知</p>-->
                </div>
            </div>
            <el-scrollbar class="set-jurisdiction">
                <div  style="padding: 10px" v-show="activeIndex === 0">
                    <div v-for="(sub,subindex) of permissions" :key="subindex" style="padding-bottom: 10px">
                        <div class="dividing-wrapper">
                            <el-checkbox @change="subchange(sub)" v-model="sub.ischeck">{{sub.subname}}</el-checkbox><p class="dividing-line"></p>
                        </div>
                        <div style="margin-left: 40px;" v-for="(sub_,sub_index) of sub.subpermission" :key="sub_index">
                            <el-checkbox @change="sub_change(sub,sub_)" v-model="sub_.ischeck">{{sub_.subname}}</el-checkbox>
                            <div style="margin-left: 20px;display: flex;flex-wrap:wrap;">
                                <div style="margin-left: 20px;"
                                     v-for="(sub__,sub__index) of sub_.subpermission" :key="sub__index">
                                    <el-checkbox @change="sub__change(sub,sub_,sub__)"
                                                 v-model="sub__.ischeck">{{sub__.subname}}
                                    </el-checkbox>
                                    <div style="margin-left: 20px;display: flex;flex-direction: row;"><div style="margin-left: 20px">
                                        <el-checkbox v-for="(sub___,sub___index) of sub__.subpermission"
                                                     @change="sub___change(sub,sub_,sub__,sub___)"
                                                     :key="sub___index"
                                                     v-model="sub___.ischeck">{{sub___.subname}}
                                        </el-checkbox>
                                    </div></div>

                                    <!--</el-checkbox>-->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-show="activeIndex === 1">
                    <el-checkbox-group v-model="checkNotifyList">
                        <template v-for="item in notifyData">
                            <p class="notify-item"><el-checkbox :label="item.value">{{item.name}}</el-checkbox></p>
                        </template>
                    </el-checkbox-group>
                </div>
            </el-scrollbar>
            <footer slot="footer" class="dialog-footer">
                <el-button size="small" style="width: 90px;" @click="isShowPermission = false">取 消</el-button>
                <el-button type="primary" v-if="showSubMit" :loading="dialogloading" size="small" style="width: 90px;margin-left: 60px" @click="handleSavePermission">确 定</el-button>
            </footer>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: 'PermissionSettings',
        props:{
            pageApi:String,
            pageEditApi:String,
            notifyApi:String,
            notifyEditApi:String,
            showSubMit:{
                type:Boolean,
                default:true
            },
            toggleVisible:{
              type:Boolean,
              default:false
            },
            currentRow:{
                type:Object,
                default:()=>{
                    return {}
                }
            },
        },
        data(){
            return {
                activeIndex:0,
                dialogloading:false,
                isShowPermission:false,
                getOrigin:false,
                permissions:[],
                notifyData:[],
                checkNotifyList:[],
                authlist:{},
                nickname:'',
            }
        },
        watch:{
            toggleVisible:function (n,o) {
                if(n){
                    this.$store.commit('getOrigin')
                    this.getOrigin = this.$store.state.app.getOrigin;
                    this.showRolePermission(this.currentRow.id)
                }else{
                    this.isShowPermission = false;
                }
            }
        },
        methods:{
            getNotifyAuth(){
                this.$axios.get(this.notifyApi+'?id='+this.currentRow.id)
                    .then(res=>{
                        let data = res.data;
                        if(data.state === 1){
                            this.notifyData = data.notifyData;
                            this.checkNotifyList = data.checkNotifyList;
                        }else{
                            this.notifyData = [];
                            this.checkNotifyList = [];
                        }
                    }).catch(err=>{
                    this.notifyData = [];
                    this.checkNotifyList = [];
                })
            },
            handleToggle(val){
                if(val !== this.activeIndex){
                    this.activeIndex = val;
                    if(this.activeIndex == 1){
                        this.getNotifyAuth();
                    }
                }
            },
            //打开权限编辑
            showRolePermission: function (id) {
                this.activeIndex = 0;
                this.isShowPermission = true;
                let _this = this;
                _this.$axios.get(_this.pageApi + '?loginroleid=' + sessionStorage.getItem('loginroleid') + '&id=' + id+'&t='+Date.now())
                    .then(function (response) {
                        let ret = response.data;
                        _this.authlist = response.data;
                        _this.permissions = _this.authlist.allAuth;
                        _this.nickname = _this.authlist.nickname;
                    })
                    .catch(function (error) {
                        console.log(error)
                    })
            },
            handleSavePermission: function () {
                let _this = this;
                if(_this.activeIndex === 1){
                    _this.$axios.post(_this.notifyEditApi,_this.$qs.stringify({
                        id:_this.currentRow.id,
                        checkNotifyList:JSON.stringify(_this.checkNotifyList)
                    })).then(response=>{
                        let ret = response.data;
                        if (ret.state == 1) {
                            _this.$message({
                                message: '更新成功!',
                                type: 'success',
                                duration: 600
                            });
                            _this.isShowPermission = false;
                        } else {
                            _this.$message({
                                message: '更新失败!' + ret.msg,
                                type: 'error',
                                duration: 600
                            });
                        }
                    }).catch(err=>{
                        console.log('err--->',err)
                    })
                }else{
                    _this.$axios.post(_this.pageEditApi, _this.$qs.stringify({
                        id: _this.currentRow.id,
                        auths: JSON.stringify(_this.authlist)
                    }), {
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
                        }
                    }).then(function (response) {
                        // console.log(ret)
                        let ret = response.data;
                        if (ret.state == 1) {
                            _this.$message({
                                message: '更新成功!',
                                type: 'success',
                                duration: 600
                            });
                            _this.isShowPermission = false;
                        } else {
                            _this.$message({
                                message: '更新失败!' + ret.msg,
                                type: 'error',
                                duration: 600
                            });
                        }
                    }).catch(function (error) {
                        setTimeout(() => {
                            _this.alertInfo('请求失败!' + error)
                        }, 150)
                    })
                }


            },
            alertInfo(msg) {
                this.$alert(msg, '提示', {
                    confirmButtonText: '确定',
                    type: 'warning',
                    callback: action => {
                        sessionStorage.removeItem('user');
                        sessionStorage.removeItem('token');
                        this.$router.push('/login');
                    }
                });
            },
            vertifyArray: function (array) {
                //确认是合法数组
                if (array != null && array != '' && array.length > 0) {
                    return true;
                }
                return false;
            },
            subchange: function (sub) {
                if (!this.vertifyArray(sub.subpermission))
                    return;
                for (let item of sub.subpermission) {
                    item.ischeck = sub.ischeck;
                    if (!this.vertifyArray(item.subpermission))
                        continue;
                    for (let item_ of item.subpermission) {
                        item_.ischeck = sub.ischeck;
                        if (!this.vertifyArray(item_.subpermission))
                            continue;
                        for (let item__ of item_.subpermission) {
                            item__.ischeck = item_.ischeck;
                        }
                    }
                }
            },
            sub_change: function (sub, sub1) {
                if (sub1.ischeck) {
                    sub.ischeck = true;
                }
                this.subchange(sub1)

            },
            sub__change: function (sub, sub1, sub2) {
                if (sub2.ischeck) {
                    sub1.ischeck = true;
                    sub.ischeck = true;
                }
                this.sub_change(sub1, sub2)
            },
            sub___change: function (sub, sub1, sub2, sub3) {
                if (sub3.ischeck) {
                    sub2.ischeck = true;
                    sub1.ischeck = true;
                    sub.ischeck = true;
                }
            },
        }
    };
</script>

<style lang="scss" scoped>
    .tab-wrapper{
        .custom-tab{
            display: flex;
            margin-bottom: 20px;
            .custom-tab-item{
                flex: 1;
                padding-bottom: 10px;
                border-bottom: 1px solid #eee;
                text-align: center;
                font-size: 16px;
                color: #000;
                cursor: pointer;
            }
            .custom-tab__active{
                font-weight: bold;
                border-bottom: 2px solid #0E5FF6;
                color: #0E5FF6;
            }
        }
    }
    .notify-item{
        padding-top: 15px;
        margin-left: 20px;
    }
</style>
