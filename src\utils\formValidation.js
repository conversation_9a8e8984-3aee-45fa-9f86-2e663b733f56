/**
 * 表单验证工具类
 * 提供统一的表单验证规则和错误处理机制
 */

// 常用正则表达式
export const REGEX_PATTERNS = {
  // 手机号码
  MOBILE: /^1[3-9]\d{9}$/,
  // 固定电话
  PHONE: /^(\d{3,4}-?)?\d{7,8}$/,
  // 邮箱
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  // 身份证号
  ID_CARD: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  // 经度 (-180 到 180)
  LONGITUDE: /^-?((1[0-7]\d)|(\d{1,2}))(\.\d+)?$/,
  // 纬度 (-90 到 90)
  LATITUDE: /^-?([1-8]?\d(\.\d+)?|90(\.0+)?)$/,
  // 车牌号
  LICENSE_PLATE: /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/,
  // 中文姓名
  CHINESE_NAME: /^[\u4e00-\u9fa5]{2,8}$/,
  // 数字和小数
  NUMBER: /^\d+(\.\d+)?$/,
  // 正整数
  POSITIVE_INTEGER: /^[1-9]\d*$/
};

// 基础验证规则
export const VALIDATION_RULES = {
  // 必填项验证
  required: (message = '此项为必填项') => ({
    required: true,
    message,
    trigger: 'blur'
  }),

  // 手机号验证
  mobile: (message = '请输入正确的手机号码') => ({
    pattern: REGEX_PATTERNS.MOBILE,
    message,
    trigger: 'blur'
  }),

  // 邮箱验证
  email: (message = '请输入正确的邮箱地址') => ({
    pattern: REGEX_PATTERNS.EMAIL,
    message,
    trigger: 'blur'
  }),

  // 经度验证
  longitude: (message = '请输入有效的经度值(-180到180)') => ({
    pattern: REGEX_PATTERNS.LONGITUDE,
    message,
    trigger: 'blur'
  }),

  // 纬度验证
  latitude: (message = '请输入有效的纬度值(-90到90)') => ({
    pattern: REGEX_PATTERNS.LATITUDE,
    message,
    trigger: 'blur'
  }),

  // 长度验证
  length: (min, max, message) => ({
    min,
    max,
    message: message || `长度应在${min}到${max}个字符之间`,
    trigger: 'blur'
  }),

  // 数值范围验证
  range: (min, max, message) => ({
    type: 'number',
    min,
    max,
    message: message || `数值应在${min}到${max}之间`,
    trigger: 'blur'
  }),

  // 自定义验证
  custom: (validator, message = '验证失败') => ({
    validator: (rule, value, callback) => {
      if (validator(value)) {
        callback();
      } else {
        callback(new Error(message));
      }
    },
    trigger: 'blur'
  })
};

// 车场配置专用验证规则
export const PARKING_VALIDATION_RULES = {
  // 车场基础信息验证
  basicInfo: {
    lotName: [
      VALIDATION_RULES.required('请输入车场名称'),
      VALIDATION_RULES.length(2, 50, '车场名称长度应在2-50个字符之间')
    ],
    airportId: [
      VALIDATION_RULES.required('请选择配对机场')
    ],
    region: [
      VALIDATION_RULES.required('请选择省市区域')
    ],
    address: [
      VALIDATION_RULES.required('请输入详细地址'),
      VALIDATION_RULES.length(5, 200, '地址长度应在5-200个字符之间')
    ],
    longitude: [
      VALIDATION_RULES.required('请输入经度'),
      VALIDATION_RULES.longitude()
    ],
    latitude: [
      VALIDATION_RULES.required('请输入纬度'),
      VALIDATION_RULES.latitude()
    ],
    phone: [
      VALIDATION_RULES.required('请输入联系手机'),
      VALIDATION_RULES.mobile()
    ],
    feeStandard: [
      VALIDATION_RULES.required('请输入停车收费标准'),
      VALIDATION_RULES.length(5, 100, '收费标准描述长度应在5-100个字符之间')
    ],
    positionType: [
      VALIDATION_RULES.required('请选择停车位置')
    ]
  },

  // 停车费率验证
  vehicleRate: {
    dailyRate: [
      VALIDATION_RULES.required('请输入预约停车费率'),
      VALIDATION_RULES.range(1, 1000, '费率应在1-1000元之间')
    ],
    minDays: [
      VALIDATION_RULES.required('请输入最少停车天数'),
      VALIDATION_RULES.range(1, 30, '最少停车天数应在1-30天之间')
    ],
    freeOvertime: [
      VALIDATION_RULES.required('请输入超时免费时长'),
      VALIDATION_RULES.range(0, 1440, '免费时长应在0-1440分钟之间')
    ]
  },

  // 接送服务验证
  shuttleService: {
    shuttlePhone: [
      VALIDATION_RULES.required('请输入接驳电话'),
      VALIDATION_RULES.mobile('请输入正确的接驳电话')
    ],
    region: [
      VALIDATION_RULES.required('请选择省市区域')
    ],
    airportId: [
      VALIDATION_RULES.required('请选择枢纽名称')
    ],
    distance: [
      VALIDATION_RULES.required('请输入直线距离'),
      VALIDATION_RULES.custom(
        value => /^\d+(\.\d{1,2})?$/.test(value) && parseFloat(value) > 0 && parseFloat(value) <= 100,
        '距离应为0-100公里之间的数字，最多保留2位小数'
      )
    ],
    timeSlot: [
      VALIDATION_RULES.required('请输入接驳时段'),
      VALIDATION_RULES.length(2, 50, '接驳时段描述长度应在2-50个字符之间')
    ],
    duration: [
      VALIDATION_RULES.required('请输入接驳时长'),
      VALIDATION_RULES.length(2, 30, '接驳时长描述长度应在2-30个字符之间')
    ],
    positionType: [
      VALIDATION_RULES.required('请选择停车位置')
    ],
    dropoffLocation: [
      VALIDATION_RULES.required('请输入送机地点'),
      VALIDATION_RULES.length(2, 100, '送机地点描述长度应在2-100个字符之间')
    ],
    pickupLocation: [
      VALIDATION_RULES.required('请输入接机地点'),
      VALIDATION_RULES.length(2, 100, '接机地点描述长度应在2-100个字符之间')
    ],
    description: [
      VALIDATION_RULES.required('请输入载客说明'),
      VALIDATION_RULES.length(10, 200, '载客说明长度应在10-200个字符之间')
    ],
    notice: [
      VALIDATION_RULES.required('请输入温馨提示'),
      VALIDATION_RULES.length(10, 200, '温馨提示长度应在10-200个字符之间')
    ]
  }
};

// 表单验证工具类
export class FormValidator {
  constructor(formRef) {
    this.formRef = formRef;
  }

  /**
   * 验证整个表单
   * @returns {Promise<boolean>}
   */
  async validateForm() {
    try {
      await this.formRef.validate();
      return true;
    } catch (error) {
      console.warn('表单验证失败:', error);
      return false;
    }
  }

  /**
   * 验证指定字段
   * @param {string|Array} fields - 字段名或字段名数组
   * @returns {Promise<boolean>}
   */
  async validateFields(fields) {
    try {
      await this.formRef.validateField(fields);
      return true;
    } catch (error) {
      console.warn('字段验证失败:', error);
      return false;
    }
  }

  /**
   * 清除验证结果
   * @param {string|Array} fields - 字段名或字段名数组，不传则清除所有
   */
  clearValidation(fields) {
    if (fields) {
      this.formRef.clearValidate(fields);
    } else {
      this.formRef.clearValidate();
    }
  }

  /**
   * 重置表单
   */
  resetForm() {
    this.formRef.resetFields();
  }
}

// 错误处理工具
export class ErrorHandler {
  /**
   * 处理API错误
   * @param {Error} error - 错误对象
   * @param {Object} context - Vue组件实例
   * @param {string} defaultMessage - 默认错误消息
   */
  static handleApiError(error, context, defaultMessage = '操作失败') {
    let message = defaultMessage;
    
    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response;
      
      switch (status) {
        case 400:
          message = data.message || '请求参数错误';
          break;
        case 401:
          message = '登录已过期，请重新登录';
          // 可以在这里处理登录跳转
          break;
        case 403:
          message = '没有权限执行此操作';
          break;
        case 404:
          message = '请求的资源不存在';
          break;
        case 500:
          message = '服务器内部错误';
          break;
        default:
          message = data.message || defaultMessage;
      }
    } else if (error.request) {
      // 网络错误
      message = '网络连接失败，请检查网络设置';
    } else {
      // 其他错误
      message = error.message || defaultMessage;
    }
    
    context.$message.error(message);
    console.error('API Error:', error);
  }

  /**
   * 处理表单验证错误
   * @param {Object} errors - 验证错误对象
   * @param {Object} context - Vue组件实例
   */
  static handleValidationError(errors, context) {
    const firstError = Object.values(errors)[0];
    if (firstError && firstError[0]) {
      context.$message.error(firstError[0].message);
    }
  }

  /**
   * 显示成功消息
   * @param {string} message - 成功消息
   * @param {Object} context - Vue组件实例
   */
  static showSuccess(message, context) {
    context.$message.success(message);
  }

  /**
   * 显示警告消息
   * @param {string} message - 警告消息
   * @param {Object} context - Vue组件实例
   */
  static showWarning(message, context) {
    context.$message.warning(message);
  }
}

// 数据验证工具
export const DataValidator = {
  /**
   * 验证手机号
   * @param {string} mobile - 手机号
   * @returns {boolean}
   */
  isMobile(mobile) {
    return REGEX_PATTERNS.MOBILE.test(mobile);
  },

  /**
   * 验证邮箱
   * @param {string} email - 邮箱
   * @returns {boolean}
   */
  isEmail(email) {
    return REGEX_PATTERNS.EMAIL.test(email);
  },

  /**
   * 验证经纬度
   * @param {number} longitude - 经度
   * @param {number} latitude - 纬度
   * @returns {boolean}
   */
  isValidCoordinate(longitude, latitude) {
    return REGEX_PATTERNS.LONGITUDE.test(longitude) && 
           REGEX_PATTERNS.LATITUDE.test(latitude);
  },

  /**
   * 验证数值范围
   * @param {number} value - 值
   * @param {number} min - 最小值
   * @param {number} max - 最大值
   * @returns {boolean}
   */
  isInRange(value, min, max) {
    return value >= min && value <= max;
  },

  /**
   * 验证字符串长度
   * @param {string} str - 字符串
   * @param {number} min - 最小长度
   * @param {number} max - 最大长度
   * @returns {boolean}
   */
  isValidLength(str, min, max) {
    return str && str.length >= min && str.length <= max;
  }
};

export default {
  REGEX_PATTERNS,
  VALIDATION_RULES,
  PARKING_VALIDATION_RULES,
  FormValidator,
  ErrorHandler,
  DataValidator
};
