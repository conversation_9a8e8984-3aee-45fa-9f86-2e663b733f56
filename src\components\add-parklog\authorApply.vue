<template>
    <div class="parking-spaceNew">
      <el-drawer
            :visible="authorApplyShow"
            direction="rtl"
            @close="closeDialog"
            size="680px"
            custom-class="custom-drawer">
            <div slot="title" class="header-title">申请授权</div>
            <div class="applicant-info">
              <div class="level-title">申请方</div>
              <el-form class="shop-custom-form-search">
                <el-form-item label="电站名称" class="clear-style">
                  <el-select v-model="orgId" placeholder="请选择电站名称" class="shop-custom-input" clearable filterable>
                    <el-option v-for="item in stationList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>  
              </el-form>
            </div>
            <div class="custom-drawer-info">
              <div class="level-title">被申请方</div>
              <div class="tips"><svg-tips-one theme="outline" size="16"/>搜索系统内已存在的车场，选择确认发起授权</div>
              <div class="search-info">
                <el-input v-model="searchInfo" type="text" placeholder="车场ID或者名称"></el-input>
                <el-button type="primary" @click="parkingSearch">查询</el-button>
              </div>
              <div class="resultsQuery" v-if="searchDataShow">
                 <div class="results-title">查询结果</div>
                 <div class="results-table">
                  <el-table
                    ref="multipleTable"
                    :data="tableData"
                    tooltip-effect="dark"
                    style="width: 100%">
                      <el-table-column
                        label="操作"
                        width="55">
                        <template slot-scope="scope">
                          <el-checkbox class="from-checkbox" v-model="scope.row.checked" @change="handleSelectionChange(scope.row)" v-if="scope.row.status == 1"></el-checkbox>
                        </template>
                      </el-table-column>
                      <el-table-column
                        type="index"
                        label="序号"
                        width="50">
                      </el-table-column>
                      <el-table-column
                        align="left"
                        prop="id"
                        label="ID"
                        width="120">
                      </el-table-column>
                      <el-table-column
                        align="left"
                        prop="name"
                        label="车场名称">
                      </el-table-column>
                      <el-table-column
                        align="left"
                        prop="out_park_count"
                        label="授权类型"
                        width="80">
                        <template slot-scope="scope">
                            车位
                        </template>
                      </el-table-column>
                      <el-table-column
                        align="left"
                        prop="out_park_count"
                        label="状态"
                        width="80">
                        <template slot-scope="scope">
                            {{ scope.row.status == 1 ? '可申请':'已申请' }}
                        </template>
                      </el-table-column>
                    </el-table>
                    
                    <div class="submit-btn">
                      <el-button type="primary" @click="defineSubmit()">确定</el-button>
                    </div>
                    
                 </div>
              </div>
              
            </div>
      </el-drawer>
    </div>
</template>
<script>

export default {
  props: {
    authorApplyShow: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      top:'0vh', 
      isBolink: true,
      searchDataShow: false,
      searchInfo:'',
      pageSize: 20,
      orgId: '', //电站ID
      stationList: [], //电站列表
      page: 1,
      selectRow: {},
      tableData: [
        
      ]
    }
  },
  mounted() {
    this.initData();
  },
  methods: {
    closeDialog(){
      this.tableData = [];
      this.searchDataShow = false;
      this.searchInfo = '';
      this.orgId = '';
      this.$emit("closeDialog",false)
    },

    /* 初始化 */
    initData() {
        let baseParams = {},
          loginuin = sessionStorage.getItem("loginuin"),
          cityid = sessionStorage.getItem("cityid"),
          serverId = sessionStorage.getItem("serverid"),
          comid = sessionStorage.getItem("comid")
        if (comid != '0' && comid != 'undefined') {
          baseParams.comid = comid
          this.roleType=1
        } else if (serverId != 'undefined'&& serverId!==null) {
          baseParams.serverId = serverId
          this.roleType=2
        } else if (cityid != 'undefined') {
          baseParams.cityid = cityid
          this.roleType=3
        } else {
          baseParams.backstageId = loginuin
          this.roleType=4
        }
        this.getStation(baseParams)
      },

    //车场查询
    parkingSearch(){
      if(this.orgId == ''){
        this.$message({
          message: '请选择电站名称',
          type: 'warning'
        });
      }else if(this.searchInfo == ''){
        this.$message({
          message: '请输入车场ID或者名称',
          type: 'warning'
        });
      }else{
        this.getAuthorList()
      }
    },

    //获取车场查询列表
    getAuthorList(){
      let data = {
        name: this.searchInfo,
        path:'/cloud/parkingLot/com/find',
        powerStationId: this.orgId
      }
      this.$axios.post('/parkingSpaceInfo/transferRequest', data).then(res => {
        if(res.data.code == 200){
          let dataInfo = res.data.data;
          dataInfo.forEach(item =>{
            item.checked = false;
          })
          this.tableData = dataInfo;
          this.searchDataShow = true;
        }else{
          this.tableData = [];
        }
      })
    },

    //分页器相关
    handleSizeChange(val){
        this.pageSize = val;
        this.page = 1;
    },

    handleCurrentChange(val){
        this.page = val;
    },

    handleSelectionChange(row) {
      this.tableData.forEach(item => {
        if (item.id !== row.id) {
          item.checked = false
        }
      })
      this.selectRow = row;
    },

    getStation(data) {
      let api ='/powerStation/queryList'
      this.$axios.post(api, this.$qs.stringify(data), {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        }
      }).then(res => {
        if (res.data.code === 200) {
          this.stationList = res.data.data
        }
      });
    },

    defineSubmit(){
      let transmissInfo = {}
      if(this.orgId){
        transmissInfo.orgId = this.orgId;
        transmissInfo.orgType = 6;
        this.stationList.forEach((item)=>{
          if(item.value == this.orgId){
            transmissInfo.orgName = item.label
          }
        })
      }
      let data = {
        comid: this.selectRow.id,
        path:'/cloud/parkingLot/apply/add',
        ...transmissInfo
      }
      this.$axios.post('/parkingSpaceInfo/transferRequest', data).then(res => {
        if(res.data.code == 200){
          this.$message({
            message: '授权成功',
            type: 'success'
          });
          this.getAuthorList();
          this.$emit("searchUpData")
        }else{
          this.$message({
          message: res.data.message,
          type: 'error'
        });
        }
      })
    }
  }
}
</script>
<style  lang="scss" scoped>
.header-title{
  color: #333;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
/deep/.el-drawer__header{
  margin-bottom: 10px;
  padding: 20px 30px 0 30px;
}

.resultsQuery{
  margin: 14px 0;
  .results-title{
    color: #999;
    margin-bottom: 10px;
  }
  .results-table{

  }
}
.custom-drawer-info{
  margin: 10px 30px;
}
.level-title{
  font-weight: bold;
  margin-bottom: 10px;
}

.headerInfo .el-button{
  width: 120px;
  height: 40px;
}

.tips{
  color: #999;
  font-size: 14px;
  background: #fff;
  line-height: 20px;
  margin: 6px 0;
  span{
    vertical-align: middle;
    margin-right: 6px;
  }
}

.from-checkbox{
  /deep/.el-checkbox__inner{
    border: 1px solid #333;
  } 
}

.header-tips{
  color: #B8741A;
  margin-left: 20px;
  margin: 0 10px;
}

/deep/.el-input{
  width: 200px;
}

.pagination-info{
  margin: 30px 0;
  text-align: right;
}

.submit-btn{
  margin: 30px 0;
  text-align: center;
  .el-button{
    width: 120px;
  }
}

.houseInfo{
  display: flex;
  span{
    display: inline-block;
    width: 30px;
    margin: 0 10px;
  }
  .el-input{
    width: 90px;
    margin-right: 10px;
  }
}

.ellipsis{
  white-space:nowrap;
  overflow:hidden;
  text-overflow:ellipsis;
}
.applicant-info{
  width: 284px;
  margin: 0 30px;
  padding-bottom: 30px;
}

.headerInfo{
  display: flex;
  margin: 20px 0;
  span{
    margin-left: 10px;
    color: #B8741A;
  }
}

/deep/.custom-dialog .el-dialog__body{
  padding: 30px 20px;
}

/deep/.custom-dialog .el-dialog__header .el-dialog__headerbtn{
  top: 20px;
}
.import-input-wrapper{
    position: relative;
    height: 32px;
    border: 1px solid #3C75CF;
    border-radius: 6px;
    width: 350px;
    .upload-demo{
      position: absolute;
      top: 0;
      right: 0;
    }
}

/deep/.el-form-item__content{
  line-height: 24px;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}

/deep/.el-upload-list{
  position: absolute;
  top: -5px;
  left: -224px;
}

</style>
