/**
 * ../../srcdate 20190828
 * ../../srcdescription:车场权限对外输出的
 *
 */


//数据中心
import DataCenterPark from '../../src/pages/park/Data_Center_Park';
import DataScreen from '../../src/pages/park/Data_Screen';
import RemoteOpening from '../../src/pages/park/lane-monitoring/Remote_Opening';
//订单管理
import OrderManageOrders from '../../src/pages/park/OrderManage_Orders';
import OrderManageOrderDetail from '../../src/pages/park/OrderManage_OrderDetail';
import OrderManagePoles from '../../src/pages/park/OrderManage_Poles';
import OrderManageIncome from '../../src/pages/park/Bolink_Income';
import OrderManageExpense from '../../src/pages/park/Bolink_Expense';
import OrderManageAbnormal from '../../src/pages/park/Abnormal_Order';
import OrderManageRecord from '../../src/pages/park/Reduce_Record';
//会员管理
import MemberManageMonthVIP from '../../src/pages/park/MonthMember_VIP';
import MemberManageRefill from '../../src/pages/park/MonthMember_Refill';
import MemberManagePrepayCard from '../../src/pages/park/PrepayCard_VIP';
import MemberManageRechargeRecord from '../../src/pages/park/PrepayCard_Trade';
import MemberManageUseRecord from '../../src/pages/park/PrepayCard_Use';
import MemberManageWhiteList from '../../src/pages/park/Park_WhiteList';
//访客管理
import VistorManage from '../../src/pages/park/visitor-manage/VistorManage_VistorMember';
import OwnerManage from '../../src/pages/park/visitor-manage/VistorManage_HomeOwner';
//统计分析
import OrderStatisticsTollCollector from '../../src/pages/park/OrderStatistics_CollectorReport';

import OrderStatisticsDailyReport from '../../src/pages/park/OrderStatistics_DailyReport';
import OrderStatisticsMonthReport from '../../src/pages/park/OrderStatistics_MonthReport';
import OrderStatisticsCommute from '../../src/pages/park/SystemManage_Commute';
//商户管理classA
import shopManageShop from '../../src/pages/park/shop-manage/classA/ShopManage_Shop';
import shopManageShopStaff from '../../src/pages/park/shop-manage/classA/shopMange_Shop_Staff';
import shopManageQueryAccount from '../../src/pages/park/shop-manage/classA/ShopManage_QueryAccount';
import shopManageCoupon from '../../src/pages/park/shop-manage/classA/ShopManage_Coupon';
import shopManageShopAnalysis from '../../src/pages/park/shop-manage/classA/ShopAnalysis';
//商户管理classB
import shopManageShopClassB from '../../src/pages/park/shop-manage/classB/ShopManage_Shop';
import shopManageShopStaffClassB from '../../src/pages/park/shop-manage/classB/shopMange_Shop_Staff';
import shopManageQueryAccountClassB from '../../src/pages/park/shop-manage/classB/ShopManage_QueryAccount';
import shopManageCouponClassB from '../../src/pages/park/shop-manage/classB/ShopManage_Coupon';
import ShopManageCouponSetClassB from '../../src/pages/park/shop-manage/classB/ShopManage_CouponSettings';
import shopManageShopAnalysisB from '../../src/pages/park/shop-manage/classB/ShopAnalysis';
//账号管理
import ParkRoleManage from '../../src/pages/park/EmployeePermission_RoleManage';
import ParkEmployeeManage from '../../src/pages/park/EmployeePermission_EmployeeManage';
//平台服务
import ValueAddedSMS from '../../src/pages/park/SystemManage_Sms';
import ValueAddedDataScreen from '../../src/pages/park/SystemManage_Screen';
import ValueAddedSubscription from '../../src/pages/park/AddServices_Public';
import ValueAddedParkSubscription from '../../src/pages/park/AddServices_ParkPublic';
import ValueAddedApplet from '../../src/pages/park/AddServices_Program';
//系统设置
import EquipmentManageWatchhouse from '../../src/pages/park/EquipmentManage_Watchhouse';
import EquipmentManageMonitor from '../../src/pages/park/EquipmentManage_Monitor';
import EquipmentManageChannel from '../../src/pages/park/EquipmentManage_Channel';
import EquipmentManageCharging from '../../src/pages/park/SystemManage_Params';
import EquipmentManageParkCamera from '../../src/pages/park/SystemManage_Params';
import SystemManageAdvanceSet from '../../src/pages/park/SystemManage_AdvanceSet';
import SystemManageParkPublic from '../../src/pages/park/park-public/SystemManage_ParkPublic';
import SystemManageBlackList from '../../src/pages/park/SystemManage_BlackList';
import SystemManageAccount from '../../src/pages/park/SystemManage_Account';
import SystemManageFreeReason from '../../src/pages/park/SystemManage_FreeReason';
import SystemManageCarType from '../../src/pages/park/SystemManage_CarManage_CarType';
import SystemManagePrice from '../../src/pages/park/SystemManage_Price';
import SystemManageMonthCard from '../../src/pages/park/SystemManage_MonthCard';
import SystemManageLogs from '../../src/pages/park/SystemManage_Logs';

/* *********************park end   ********/

export const parkComponents = [
    //park
    DataCenterPark,
    DataScreen,
    RemoteOpening,
    OrderManageOrders,
    OrderManageOrderDetail,
    OrderManagePoles,
    OrderManageIncome,
    OrderManageExpense,
    OrderManageAbnormal,
    OrderManageRecord,
    MemberManageMonthVIP,
    MemberManageRefill,
    MemberManagePrepayCard,
    MemberManageRechargeRecord,
    MemberManageUseRecord,
    MemberManageWhiteList,
    VistorManage,
    OwnerManage,
    OrderStatisticsTollCollector,
   
    OrderStatisticsDailyReport,
    OrderStatisticsMonthReport,
    OrderStatisticsCommute,
    shopManageShop,
    shopManageShopStaff,
    shopManageQueryAccount,
    shopManageCoupon,
    shopManageShopAnalysis,
    shopManageShopClassB,
    shopManageShopStaffClassB,
    shopManageQueryAccountClassB,
    shopManageCouponClassB,
    ShopManageCouponSetClassB,
    shopManageShopAnalysisB,
    ParkRoleManage,
    ParkEmployeeManage,
    ValueAddedSMS,
    ValueAddedDataScreen,
    ValueAddedSubscription,
    ValueAddedApplet,
    ValueAddedParkSubscription,
    EquipmentManageWatchhouse,
    EquipmentManageMonitor,
    EquipmentManageChannel,
    EquipmentManageCharging,
    EquipmentManageParkCamera,
    SystemManageAdvanceSet,
    SystemManageParkPublic,
    SystemManageBlackList,
    SystemManageAccount,
    SystemManageCarType,
    SystemManageFreeReason,
    SystemManageLogs,
    SystemManageMonthCard,
    SystemManagePrice,

];

export const parkComponentsObj = {
    //park
    DataCenterPark, //数据中心
    DataScreen,//数据大屏
    RemoteOpening, //车道监控
    OrderManageOrders, //订单记录
    OrderManageOrderDetail, //订单记录详情
    OrderManagePoles, //抬杆记录
    OrderManageIncome, //交易记录
    OrderManageExpense, //支出记录
    OrderManageAbnormal,//异常订单
    OrderManageRecord,//减免记录
    MemberManageMonthVIP,//月卡会员
    MemberManageRefill,//月卡会员续费记录
    MemberManagePrepayCard,//储值卡会员
    MemberManageRechargeRecord,//储值卡充值记录
    MemberManageUseRecord,//储值卡使用记录
    MemberManageWhiteList,//内部车管理
    VistorManage,//访客人员管理
    OwnerManage,//业主管理
    OrderStatisticsTollCollector,//收费员日报
    OrderStatisticsDailyReport,//车场日报
    OrderStatisticsMonthReport,//车场月报
    OrderStatisticsCommute,//上下班记录
    shopManageShop,//商户管理
    shopManageShopStaff,//商户管理下的员工管理
    shopManageQueryAccount,//流水查询
    shopManageCoupon,//优惠券管理
    shopManageShopAnalysis,
    shopManageShopClassB,
    shopManageShopStaffClassB,
    shopManageQueryAccountClassB,
    shopManageCouponClassB,
    ShopManageCouponSetClassB,
    shopManageShopAnalysisB,
    ParkRoleManage,//车场角色管理
    ParkEmployeeManage,//车场员工管理
    ValueAddedSMS,//短信服务
    ValueAddedDataScreen,//数据大屏
    ValueAddedSubscription,//商户公众号
    ValueAddedApplet,//小程序收费
    ValueAddedParkSubscription,//车场公众号
    EquipmentManageWatchhouse,//岗亭管理
    EquipmentManageMonitor,//监控管理
    EquipmentManageChannel,//通道管理
    EquipmentManageCharging,
    EquipmentManageParkCamera,
    SystemManageAdvanceSet,//高级设置
    SystemManageParkPublic,//车场公众号
    SystemManageBlackList,//黑名单管理
    SystemManageAccount,//账号管理
    SystemManageFreeReason,//免费原因
    SystemManageCarType,//车型设定
    SystemManagePrice, //时租价格
    SystemManageMonthCard,//月卡套餐
    SystemManageLogs,//系统日志

}

export default {
    parkComponents,
    parkComponentsObj
};
