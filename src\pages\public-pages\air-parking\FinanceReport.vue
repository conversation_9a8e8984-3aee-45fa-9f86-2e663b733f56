<template>
  <section class="right-wrapper-size shop-table-wrapper" id="scrollBarDom">
    <!-- 页面标题 -->
    <div class="shop-custom-operation">
      <header class="shop-custom-header">
        <p style="float: left">机场停车<span style="margin: 2px">-</span>财务统计</p>
        <!-- <div class="float-right">
          <el-button type="text" size="mini" @click="resetForm" icon="el-icon-refresh" style="font-size: 14px;color: #1E1E1E;">刷新</el-button>
        </div> -->
      </header>
    </div>

    <!-- Tab组件 -->
    <el-tabs
      class="borderAppoint-card"
      v-model="activeTab"
      type="border-card"
      @tab-click="handleTabChange"
    >
      <!-- 订单统计Tab -->
      <el-tab-pane label="订单统计" name="orderStats">
        <!-- 时间搜索条件 -->
        <div class="search-container">
          <super-form
            :form-config="oneFormConfig"
            :value="oneSearchForm"
            @input="handleDateChange"
          />
        </div>

        <!-- 订单统计表格 -->
        <div class="table-wrapper-pre">
          <bl-table
            ref="orderTable"
            :api="fetchOrderData"
            :show-pagination="true"
            :show-summary="true"
            :summary-fields="['income']"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handlePageChange"
            border>
            <el-table-column
              prop="parkingLotName"
              label="车场名称"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="income"
              label="收入"
              align="center"
            ></el-table-column>
          </bl-table>
          <div class="moneyNum-pos">订单总金额: {{  orderTotalAmount }}</div>
        </div>
      </el-tab-pane>

      <!-- 分账统计Tab -->
      <el-tab-pane label="分账统计" name="profitShare">
        <!-- 时间搜索条件 -->
        <div class="search-container">
          <super-form
            :form-config="twoFormConfig"
            :value="twoSearchForm"
            @input="handleDateChange"
          />
        </div>
        <!-- 分账统计表格 -->
        <bl-table
          ref="profitTable"
          :api="fetchProfitData"
          :show-pagination="false"
          border
        >
          <el-table-column
            prop="comName"
            label="车场名称"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="orderCount"
            label="订单数量"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="orderAmount"
            label="订单金额"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="subjectName"
            label="分账主体"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="arrivedAmount"
            label="结算金额"
            align="center"
          ></el-table-column>

          <el-table-column
            prop="arrivedTime"
            label="结算时间"
            align="center"
          ></el-table-column>
        </bl-table>
      </el-tab-pane>

      <!-- 提现明细Tab -->
      <el-tab-pane label="提现明细" name="WithdrawalShare" v-if="comId > 0">
        <!-- 时间搜索条件 -->
        <!-- <div class="search-container">
          <super-form
            :form-config="threeFormConfig"
            :value="threeSearchForm"
            @input="handleDateChange"
          />
        </div> -->
        <!-- 提现明细表格 -->
        <bl-table
          ref="withdrawalTable"
          :api="fetchWithdrawalData"
          :show-pagination="false"
          border
        >
          <el-table-column
            prop="comId"
            label="编号"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="comName"
            label="分账主体"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="arrivedAmount"
            label="到账金额"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="arrivedTime"
            label="转账时间"
            align="center"
          ></el-table-column>
        </bl-table>
      </el-tab-pane>

      
    </el-tabs>
  </section>
</template>

<script lang="ts">
import { fetchsharestatisticList, fetchOrderstatisticList, fetchWithdrawalList, fetchOrderSummary } from "@/api/airParking";
import SuperForm from "@/components/super-form/inline-form";
import BlTable from "@/components/BlTable/index.vue";
export default {
  name: "FinanceReport",
  components: { SuperForm, BlTable },
  data() {
    return {
      activeTab: "orderStats",
      dateRange: [],
      comId: sessionStorage.getItem('comid') || '',
      orderTotalAmount:'',
      current: 1, // 当前页码
      size: 10, // 每页显示多少条数据
      requestUrl: this.$PUBLIC_URL.AIRPORT_API, // 请求接口
      oneFormConfig: {
        showMore: false,
        first: [
          {
            label: "车场名称",
            type: "input",
            prop: "parkName",
          },
          {
            label: "选择时间",
            type:'date',
            subtype:'daterange',
            prop:'datetime',
            subprop: 'createTime',
            valueFormat:'yyyy-MM-dd HH:mm:ss',
            defaultTime: ['00:00:00', '23:59:59']
          },
        ],
      },
      twoFormConfig: {
        showMore: false,
        first: [
          {
            label: "车场名称",
            type: "input",
            prop: "parkName",
          },
          {
            label: "选择时间",
            type:'date',
            subtype:'daterange',
            prop:'datetime',
            subprop: 'createTime',
            valueFormat:'yyyy-MM-dd',
          },
        ],
      },
      onloadType: 0, //首次加载
      oneSearchForm: {
        parkingLotName:'',
        datetime:[]
      },
      twoSearchForm: {
        parkName:'',
        datetime:[]
      },
      threeSearchForm: {
        parkName:'',
        datetime:[]
      },
    };
  },
  methods: {
    // Tab切换处理
    handleTabChange(tab) {
      this.activeTab = tab.name;
      // 切换时重置日期范围
      // 根据当前激活的tab查询对应数据
      if (this.activeTab == "orderStats") {
        let currentTime =  this.common.currentDateArray(30);
        this.$set(this.oneSearchForm, 'datetime', currentTime);
        setTimeout(() => {
          this.onloadType = 0;
          this.fetchOrderData()
        }, 200);
      } else if(this.activeTab == "profitShare"){
        let currentTime =  this.common.currentDatetimeArray(30);
        console.log(currentTime, 'currentDatetimeArray');
        this.$set(this.twoSearchForm, 'datetime', currentTime);
        setTimeout(() => {
          this.$refs.profitTable.loadData();
        }, 500);
        
      } else if(this.activeTab == "WithdrawalShare"){
        setTimeout(() => {
          this.$refs.withdrawalTable.loadData();
        }, 200);
      }
    },
    // 查询变化处理
    handleDateChange() {
      // 根据当前激活的tab查询对应数据
      if (this.activeTab == "orderStats") {
        setTimeout(() => {
          this.$refs.orderTable.loadData();
        }, 300);
      } else if(this.activeTab == "profitShare"){
        setTimeout(() => {
          this.$refs.profitTable.loadData();
        }, 300);
      } else if(this.activeTab == "WithdrawalShare"){
        setTimeout(() => {
          this.$refs.WithdrawalShare.loadData();
        }, 300);
      }
    },
    // 订单统计数据接口
    async fetchOrderData() {
      // 格式化日期参数
      if(this.onloadType == 0){
        let currentTime =  this.common.currentDateArray(30);
        this.$set(this.oneSearchForm, 'datetime', currentTime);
        this.onloadType = 1;
        let oneSearchForm= this.oneSearchForm;
        if (oneSearchForm.datetime && oneSearchForm.datetime.length) {
          oneSearchForm.startTime = oneSearchForm.datetime[0];
          oneSearchForm.endTime = oneSearchForm.datetime[1];
        }
        const params = { parkName:oneSearchForm.parkName, endTime:oneSearchForm.endTime || '', startTime:oneSearchForm.startTime || '', size: this.size, current: this.current };
        const paramSummary = { parkName:oneSearchForm.parkName, endTime:oneSearchForm.endTime || '', startTime:oneSearchForm.startTime || '' };

        
        // 生产环境建议关闭或替换为日志系统
        if (process.env.NODE_ENV !== 'production') {
          console.log("请求参数:", params, this.requestUrl, 'this.$PUBLIC_URL.AIRPORT_API');
        }

        try {
          const responses = await fetchOrderSummary(this.requestUrl, paramSummary);
          const { income } = responses.data.data;
          if(income){
            this.$set(this,'orderTotalAmount', income)
          }
        } catch (error) {
          console.error("fetchOrderSummary 请求失败:", error); // 记录错误日志
        }
      
        try {
          const response = await fetchOrderstatisticList(this.requestUrl, params);
          const dataList = response? response.data.data : [];
          return {
            list: dataList.records || [],
            total: dataList.total || 0,
          };
        } catch (error) {
          console.error("fetchTableData 请求失败:", error); // 记录错误日志
          this.$message.error("加载数据失败，请稍后重试");
          return {
            list: [],
            total: 0,
          };
        }
        
      }
      
    },
    // 分账统计数据接口
    async fetchProfitData() {
      // 格式化日期参数
      if(this.activeTab == "profitShare"){
        let oneSearchForm= this.twoSearchForm;
        if (oneSearchForm.datetime && oneSearchForm.datetime.length) {
          oneSearchForm.startTime = oneSearchForm.datetime[0];
          oneSearchForm.endTime = oneSearchForm.datetime[1];
        }
        
        const params = { parkName:oneSearchForm.parkName, endTime:oneSearchForm.endTime || '', startTime:oneSearchForm.startTime || '', size: this.size, current: this.current };
        
        if(this.comId > 0){
          params.subjectId = this.comId;
        }
        console.log('params',params)
        try {
          const response = await fetchsharestatisticList(this.requestUrl, params);
          const dataList = response? response.data.data : [];
          console.log("请求响应:", dataList);
          return {
            list: dataList || []
          };
        } catch (error) {
          console.error("fetchsharestatisticList 请求失败:", error); // 记录错误日志
          this.$message.error("加载数据失败，请稍后重试");
          return {
            list: [],
            total: 0,
          };
        }
      }  
    },
    // 提现明细数据接口
    async fetchWithdrawalData() {
      // 格式化日期参数
      // let threeSearchForm= this.threeSearchForm;
      // if (threeSearchForm.datetime && threeSearchForm.datetime.length) {
      //   threeSearchForm.startTime = threeSearchForm.datetime[0];
      //   threeSearchForm.endTime = threeSearchForm.datetime[1];
      // }
      // const params = { size: this.size, current: this.current };
      // console.log(params);
      if(this.activeTab == "WithdrawalShare"){
        try {
          const response = await fetchWithdrawalList(this.requestUrl,{comId:this.comId});
          const dataList = response? response.data.data : [];
          console.log("请求响应:", response);
          return {
            list: dataList || []
          };
        } catch (error) {
          console.error("fetchsharestatisticList 请求失败:", error); // 记录错误日志
          this.$message.error("加载数据失败，请稍后重试");
          return {
            list: [],
            total: 0,
          };
        }
      }
    },

    handleSizeChange(size) {
      this.size = size;
      this.$refs.tableRef.loadData();
    },

    handlePageChange(page) {
      console.log("handlePageChange", page);
      this.current = page;
      this.$refs.tableRef.loadData();
    },

    // 导出订单统计
    exportOrderStats() {
      // 实现导出逻辑
      this.$message.success("订单统计数据导出成功");
    },
    // 导出分账统计
    exportProfitStats() {
      // 实现导出逻辑
      this.$message.success("分账统计数据导出成功");
    },
  },
};
</script>

<style scoped>
.page-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}
.table-wrapper-pre{
  position: relative;
}
.table-wrapper-pre .moneyNum-pos{
  position: absolute;
  bottom: 10px;
}
.search-container {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}
::v-deep .el-tabs__content {
  padding-top: 15px;
}
</style>