<template>
  <section class="right-wrapper-size shop-table-wrapper" id="scrollBarDom">
    <!-- 页面标题 -->
    <div class="shop-custom-operation">
      <header class="shop-custom-header">
        <p style="float: left">机场停车<span style="margin: 2px">-</span>财务统计</p>
        <!-- <div class="float-right">
          <el-button type="text" size="mini" @click="resetForm" icon="el-icon-refresh" style="font-size: 14px;color: #1E1E1E;">刷新</el-button>
        </div> -->
      </header>
    </div>

    <!-- Tab组件 -->
    <el-tabs
      class="borderAppoint-card"
      v-model="activeTab"
      type="border-card"
      @tab-click="handleTabChange"
    >
      <!-- 订单统计Tab -->
      <el-tab-pane label="订单统计" name="orderStats">
        <!-- 时间搜索条件 -->
        <div class="search-container">
          <super-form
            :form-config="oneFormConfig"
            :value="oneSearchForm"
            @input="handleDateChange"
          />
        </div>

        <!-- 订单统计表格 -->
        <div class="table-wrapper-pre">
          <bl-table
            ref="orderTable"
            :api="fetchOrderData"
            :show-pagination="true"
            :show-summary="true"
            :summary-fields="['income']"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handlePageChange"
            border>
            <el-table-column
              prop="parkingLotName"
              label="车场名称"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="income"
              label="收入"
              align="center"
            ></el-table-column>
          </bl-table>
          <div class="moneyNum-pos">订单总金额: {{  orderTotalAmount }}</div>
        </div>
      </el-tab-pane>

      <!-- 分账统计Tab -->
      <el-tab-pane label="分账统计" name="profitShare">
        <!-- 时间搜索条件 -->
        <div class="search-container">
          <super-form
            :form-config="twoFormConfig"
            :value="twoSearchForm"
            @input="handleDateChange"
          />
        </div>
        <!-- 分账统计表格 -->
        <bl-table
          ref="profitTable"
          :api="fetchProfitData"
          :show-pagination="false"
          border
        >
          <el-table-column
            prop="comName"
            label="车场名称"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="orderCount"
            label="订单数量"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="orderAmount"
            label="订单金额"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="subjectName"
            label="分账主体"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="arrivedAmount"
            label="结算金额"
            align="center"
          ></el-table-column>

          <el-table-column
            prop="arrivedTime"
            label="结算时间"
            align="center"
          ></el-table-column>
        </bl-table>
      </el-tab-pane>

      <!-- 提现明细Tab -->
      <el-tab-pane label="提现明细" name="WithdrawalShare" v-if="comId > 0">
        <!-- 时间搜索条件 -->
        <!-- <div class="search-container">
          <super-form
            :form-config="threeFormConfig"
            :value="threeSearchForm"
            @input="handleDateChange"
          />
        </div> -->
        <!-- 提现明细表格 -->
        <bl-table
          ref="withdrawalTable"
          :api="fetchWithdrawalData"
          :show-pagination="false"
          border
        >
          <el-table-column
            prop="comId"
            label="编号"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="comName"
            label="分账主体"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="arrivedAmount"
            label="到账金额"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="arrivedTime"
            label="转账时间"
            align="center"
          ></el-table-column>
        </bl-table>
      </el-tab-pane>

      
    </el-tabs>
  </section>
</template>
<script>
import { fetchsharestatisticList, fetchOrderstatisticList, fetchWithdrawalList, fetchOrderSummary } from "@/api/airParking";
import SuperForm from "@/components/super-form/inline-form";
import BlTable from "@/components/BlTable/index.vue";
export default {
  name: "FinanceReport",
  components: { SuperForm, BlTable },
  data() {
    return {
      activeTab: "orderStats",
      dateRange: [],
      comId: sessionStorage.getItem('comid') || '',
      orderTotalAmount:'',
      current: 1, // 当前页码
      size: 10, // 每页显示多少条数据
      requestUrl: this.$PUBLIC_URL.AIRPORT_API, // 请求接口
      oneFormConfig: {
        showMore: false,
        first: [
          {
            label: "车场名称",
            type: "input",
            prop: "parkName",
          },
          {
            label: "选择时间",
            type:'date',
            subtype:'daterange',
            prop:'datetime',
            subprop: 'createTime',
            valueFormat:'yyyy-MM-dd HH:mm:ss',
            defaultTime: ['00:00:00', '23:59:59']
          },
        ],
      },
      twoFormConfig: {
        showMore: false,
        first: [
          {
            label: "车场名称",
            type: "input",
            prop: "parkName",
          },
          {
            label: "选择时间",
            type:'date',
            subtype:'daterange',
            prop:'datetime',
            subprop: 'createTime',
            valueFormat:'yyyy-MM-dd',
          },
        ],
      },
      onloadType: 0, //首次加载
      oneSearchForm: {
        parkName:'', // 修复字段名匹配问题
        datetime:[]
      },
      twoSearchForm: {
        parkName:'',
        datetime:[]
      },
      threeSearchForm: {
        parkName:'',
        datetime:[]
      },
    };
  },
  methods: {
    // Tab切换处理 - 优化逻辑
    handleTabChange(tab) {
      this.activeTab = tab.name;

      // 根据不同Tab初始化数据和加载
      switch (this.activeTab) {
        case "orderStats":
          this.initOrderStatsTab();
          break;
        case "profitShare":
          this.initProfitShareTab();
          break;
        case "WithdrawalShare":
          this.initWithdrawalTab();
          break;
      }
    },

    // 初始化订单统计Tab
    initOrderStatsTab() {
      const currentTime = this.common.currentDateArray(30);
      this.$set(this.oneSearchForm, 'datetime', currentTime);
      this.onloadType = 0; // 重置首次加载标志

      this.$nextTick(() => {
        this.$refs.orderTable && this.$refs.orderTable.loadData();
      });
    },

    // 初始化分账统计Tab
    initProfitShareTab() {
      const currentTime = this.common.currentDatetimeArray(30);
      this.$set(this.twoSearchForm, 'datetime', currentTime);

      this.$nextTick(() => {
        this.$refs.profitTable && this.$refs.profitTable.loadData();
      });
    },

    // 初始化提现明细Tab
    initWithdrawalTab() {
      this.$nextTick(() => {
        this.$refs.withdrawalTable && this.$refs.withdrawalTable.loadData();
      });
    },
    // 查询变化处理 - 优化逻辑，统一处理
    handleDateChange(formData) {
      // 更新对应的搜索表单数据
      if (this.activeTab === "orderStats") {
        this.oneSearchForm = { ...this.oneSearchForm, ...formData };
        this.$nextTick(() => {
          this.$refs.orderTable && this.$refs.orderTable.loadData();
        });
      } else if (this.activeTab === "profitShare") {
        this.twoSearchForm = { ...this.twoSearchForm, ...formData };
        this.$nextTick(() => {
          this.$refs.profitTable && this.$refs.profitTable.loadData();
        });
      } else if (this.activeTab === "WithdrawalShare") {
        this.threeSearchForm = { ...this.threeSearchForm, ...formData };
        this.$nextTick(() => {
          this.$refs.withdrawalTable && this.$refs.withdrawalTable.loadData();
        });
      }
    },
    // 订单统计数据接口 - 优化逻辑
    async fetchOrderData() {
      try {
        // 初始化默认时间范围（首次加载）
        if (this.onloadType === 0) {
          const currentTime = this.common.currentDateArray(30);
          this.$set(this.oneSearchForm, 'datetime', currentTime);
          this.onloadType = 1;
        }

        // 格式化搜索参数
        const searchForm = { ...this.oneSearchForm };
        if (searchForm.datetime && searchForm.datetime.length === 2) {
          searchForm.startTime = searchForm.datetime[0];
          searchForm.endTime = searchForm.datetime[1];
        }

        // 构建请求参数
        const params = {
          parkName: searchForm.parkName || '',
          startTime: searchForm.startTime || '',
          endTime: searchForm.endTime || '',
          size: this.size,
          current: this.current
        };

        // 如果有公司ID，添加到参数中
        if (this.comId && this.comId > 0) {
          params.subjectId = this.comId;
        }
        const summaryParams = {
          parkName: searchForm.parkName || '',
          startTime: searchForm.startTime || '',
          endTime: searchForm.endTime || ''
        };

        // 如果有公司ID，添加到参数中
        if (this.comId && this.comId > 0) {
          summaryParams.subjectId = this.comId;
        }

        // 开发环境日志
        if (process.env.NODE_ENV !== 'production') {
          console.log("订单统计请求参数:", params);
        }

        // 先获取汇总数据
        try {
          const summaryResponse = await fetchOrderSummary(this.requestUrl, summaryParams);
          if (summaryResponse && summaryResponse.data && summaryResponse.data.data && summaryResponse.data.data.income) {
            this.orderTotalAmount = summaryResponse.data.data.income;
          } else {
            this.orderTotalAmount = '0';
          }
        } catch (error) {
          console.error("获取汇总数据失败:", error);
          this.orderTotalAmount = '0';
        }

        // 获取列表数据
        const listResponse = await fetchOrderstatisticList(this.requestUrl, params);
        const dataList = (listResponse && listResponse.data && listResponse.data.data) || {};
        return {
          list: dataList.records || [],
          total: dataList.total || 0,
        };

      } catch (error) {
        console.error("fetchOrderData 请求失败:", error);
        this.$message.error("加载订单统计数据失败，请稍后重试");
        return {
          list: [],
          total: 0,
        };
      }
    },
    // 分账统计数据接口 - 优化逻辑
    async fetchProfitData() {
      if (this.activeTab !== "profitShare") {
        return { list: [] };
      }

      try {
        // 格式化搜索参数
        const searchForm = { ...this.twoSearchForm };
        if (searchForm.datetime && searchForm.datetime.length === 2) {
          searchForm.startTime = searchForm.datetime[0];
          searchForm.endTime = searchForm.datetime[1];
        }

        // 构建请求参数
        const params = {
          parkName: searchForm.parkName || '',
          startTime: searchForm.startTime || '',
          endTime: searchForm.endTime || '',
          size: this.size,
          current: this.current
        };

        // 如果有公司ID，添加到参数中
        if (this.comId && this.comId > 0) {
          params.subjectId = this.comId;
        }

        console.log('分账统计请求参数:', params);

        const response = await fetchsharestatisticList(this.requestUrl, params);
        const dataList = (response && response.data && response.data.data) || [];

        console.log("分账统计响应:", dataList);

        return {
          list: Array.isArray(dataList) ? dataList : []
        };

      } catch (error) {
        console.error("fetchProfitData 请求失败:", error);
        this.$message.error("加载分账统计数据失败，请稍后重试");
        return {
          list: []
        };
      }
    },
    // 提现明细数据接口
    async fetchWithdrawalData() {
      // 格式化日期参数
      // let threeSearchForm= this.threeSearchForm;
      // if (threeSearchForm.datetime && threeSearchForm.datetime.length) {
      //   threeSearchForm.startTime = threeSearchForm.datetime[0];
      //   threeSearchForm.endTime = threeSearchForm.datetime[1];
      // }
      // const params = { size: this.size, current: this.current };
      // console.log(params);
      if(this.activeTab == "WithdrawalShare"){
        try {
          const response = await fetchWithdrawalList(this.requestUrl,{comId:this.comId});
          const dataList = response? response.data.data : [];
          console.log("请求响应:", response);
          return {
            list: dataList || []
          };
        } catch (error) {
          console.error("fetchsharestatisticList 请求失败:", error); // 记录错误日志
          this.$message.error("加载数据失败，请稍后重试");
          return {
            list: [],
            total: 0,
          };
        }
      }
    },

    // 分页大小变化处理
    handleSizeChange(size) {
      this.size = size;
      this.current = 1; // 重置到第一页

      // 根据当前Tab刷新对应表格
      if (this.activeTab === "orderStats") {
        this.$refs.orderTable && this.$refs.orderTable.loadData();
      }
    },

    // 页码变化处理
    handlePageChange(page) {
      console.log("handlePageChange", page);
      this.current = page;

      // 根据当前Tab刷新对应表格
      if (this.activeTab === "orderStats") {
        this.$refs.orderTable && this.$refs.orderTable.loadData();
      }
    },

    // 导出订单统计
    exportOrderStats() {
      // 实现导出逻辑
      this.$message.success("订单统计数据导出成功");
    },
    // 导出分账统计
    exportProfitStats() {
      // 实现导出逻辑
      this.$message.success("分账统计数据导出成功");
    },
  },
};
</script>

<style scoped>
.page-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}
.table-wrapper-pre{
  position: relative;
}
.table-wrapper-pre .moneyNum-pos{
  position: absolute;
  bottom: 10px;
}
.search-container {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}
::v-deep .el-tabs__content {
  padding-top: 15px;
}
</style>