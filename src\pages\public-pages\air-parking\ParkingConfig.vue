<template>
  <section class="right-wrapper-size shop-table-wrapper" id="scrollBarDom">
    <div class="shop-custom-operation">
      <header class="shop-custom-header">
        <p style="float: left">
          机场停车<span style="margin: 2px">-</span>车场配置
          <span v-if="currentParkingName" style="margin-left: 10px; color: #409EFF;">（{{ currentParkingName }}）</span>
        </p>
        <div style="float: right">
          <el-button type="text" @click="goBack" icon="el-icon-back">返回车场管理</el-button>
        </div>
      </header>
    </div>

    <div class="config-container">
      <!-- 标签页 -->
      <el-tabs v-model="activeTab" type="border-card" @tab-click="handleTabChange">
        <!-- 基础信息 -->
        <el-tab-pane label="车场信息" name="basic">
          <div class="tab-content">
            <ParkingBasicInfo
              :airportList="airportList"
              :regionOptions="regionOptions"
              v-model="basicForm"
              @save="handleBasicInfoSave"
              :loading="loading.basic"
            />
          </div>
        </el-tab-pane>

        <!-- 停车费率 -->
        <el-tab-pane label="停车费率" name="vehicle">
          <div class="tab-content">
            <div class="section-header">
              <h3><i class="el-icon-money"></i> 停车费率设置</h3>
              <p class="section-desc">设置预约停车的费率标准和相关规则</p>
            </div>
            <div class="info-section">
              <el-form
                ref="vehicleForm"
                :model="vehicleForm"
                :rules="vehicleRules"
                label-width="140px"
                size="medium"
                class="rate-form"
              >
                <el-card class="form-card" shadow="never">
                  <div slot="header" class="card-header">
                    <span><i class="el-icon-tickets"></i> 费率配置</span>
                  </div>
                  <el-row :gutter="24">
                    <el-col :span="8">
                      <el-form-item label="预约停车费率" prop="dailyRate">
                        <el-input-number
                          v-model="vehicleForm.dailyRate"
                          :min="0"
                          :precision="2"
                          :step="1"
                          placeholder="请输入费率"
                          style="width: 100%"
                        />
                        <span class="unit">元/天</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="最少停车天数" prop="minDays">
                        <el-input-number
                          v-model="vehicleForm.minDays"
                          :min="1"
                          :max="30"
                          placeholder="请输入天数"
                          style="width: 100%"
                        />
                        <span class="unit">天</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="超时免费时长" prop="freeOvertime">
                        <el-input-number
                          v-model="vehicleForm.freeOvertime"
                          :min="0"
                          :max="1440"
                          placeholder="请输入时长"
                          style="width: 100%"
                        />
                        <span class="unit">分钟</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-card>
              </el-form>
              <div class="form-actions">
                <el-button
                  type="primary"
                  :loading="loading.vehicle"
                  :disabled="isVehicleFormValid"
                  @click="saveVehicleInfo"
                  size="medium"
                >
                  <i class="el-icon-check"></i>
                  保存费率配置
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 接送服务 -->
        <el-tab-pane label="接送服务" name="shuttle">
          <div class="tab-content">
            <div class="section-header">
              <h3><i class="el-icon-truck"></i> 接送服务配置</h3>
              <p class="section-desc">配置车场的接送服务信息，包括接驳时间、地点等</p>
            </div>
            <div class="info-section">
              <el-form
                ref="shuttleForm"
                :model="shuttleForm"
                :rules="shuttleRules"
                label-width="140px"
                size="medium"
                class="shuttle-form"
              >
                <!-- 基础信息卡片 -->
                <el-card class="form-card" shadow="never">
                  <div slot="header" class="card-header">
                    <span><i class="el-icon-phone"></i> 联系信息</span>
                  </div>
                  <el-row :gutter="24">
                    <el-col :span="12">
                      <el-form-item label="接驳电话" prop="shuttlePhone">
                        <el-input
                          v-model="shuttleForm.shuttlePhone"
                          placeholder="请输入接驳电话"
                          prefix-icon="el-icon-phone"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="省市区域" prop="region">
                        <el-cascader
                          v-model="shuttleForm.region"
                          :options="regionOptions"
                          :props="{ value: 'code', label: 'name', children: 'children' }"
                          placeholder="请选择省市区域"
                          style="width: 100%;"
                          @change="handleRegionChange"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="24">
                    <el-col :span="12">
                      <el-form-item label="枢纽名称" prop="airportId">
                        <el-select
                          v-model="shuttleForm.airportId"
                          placeholder="请选择枢纽名称"
                          filterable
                          style="width: 100%"
                        >
                          <el-option
                            v-for="airport in airportList"
                            :key="airport.id"
                            :label="airport.name"
                            :value="airport.id"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="停车位置" prop="positionType">
                        <el-select
                          v-model="shuttleForm.positionType"
                          placeholder="请选择停车位置"
                          style="width: 100%"
                        >
                          <el-option
                            v-for="position in positionList"
                            :key="position.value"
                            :label="position.label"
                            :value="position.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-card>

                <!-- 服务信息卡片 -->
                <el-card class="form-card" shadow="never">
                  <div slot="header" class="card-header">
                    <span><i class="el-icon-time"></i> 服务信息</span>
                  </div>
                  <el-row :gutter="24">
                    <el-col :span="8">
                      <el-form-item label="直线距离" prop="distance">
                        <el-input
                          v-model="shuttleForm.distance"
                          placeholder="如：5.6"
                          suffix-icon="el-icon-location"
                        >
                          <template slot="append">KM</template>
                        </el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="接驳时段" prop="timeSlot">
                        <el-input
                          v-model="shuttleForm.timeSlot"
                          placeholder="如：24小时"
                          prefix-icon="el-icon-time"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="接驳时长" prop="duration">
                        <el-input
                          v-model="shuttleForm.duration"
                          placeholder="如：15分钟"
                          prefix-icon="el-icon-timer"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="24">
                    <el-col :span="12">
                      <el-form-item label="送机地点" prop="dropoffLocation">
                        <el-input
                          v-model="shuttleForm.dropoffLocation"
                          placeholder="请输入送机地点"
                          prefix-icon="el-icon-upload2"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="接机地点" prop="pickupLocation">
                        <el-input
                          v-model="shuttleForm.pickupLocation"
                          placeholder="请输入接机地点"
                          prefix-icon="el-icon-download"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-card>

                <!-- 服务说明卡片 -->
                <el-card class="form-card" shadow="never">
                  <div slot="header" class="card-header">
                    <span><i class="el-icon-document"></i> 服务说明</span>
                  </div>
                  <el-row :gutter="24">
                    <el-col :span="12">
                      <el-form-item label="载客说明" prop="description">
                        <el-input
                          type="textarea"
                          v-model="shuttleForm.description"
                          placeholder="请输入载客说明，如：单次停车最高25人，建议5人"
                          :rows="4"
                          maxlength="200"
                          show-word-limit
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="温馨提示" prop="notice">
                        <el-input
                          type="textarea"
                          v-model="shuttleForm.notice"
                          placeholder="请输入温馨提示信息"
                          :rows="4"
                          maxlength="200"
                          show-word-limit
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-card>

                <!-- 图片上传卡片 -->
                <el-card class="form-card" shadow="never">
                  <div slot="header" class="card-header">
                    <span><i class="el-icon-picture"></i> 接送图片</span>
                  </div>
                  <el-form-item>
                    <div class="image-upload-section">
                      <el-upload
                        class="shuttle-image-upload"
                        action="#"
                        :before-upload="beforeUpload"
                        :on-success="handleShuttleImageSuccess"
                        :on-remove="handleShuttleImageRemove"
                        :file-list="shuttleImageList"
                        list-type="picture-card"
                        :limit="6"
                      >
                        <i class="el-icon-plus"></i>
                      </el-upload>
                      <div class="upload-tips">
                        <p><i class="el-icon-info"></i> 上传提示：</p>
                        <ul>
                          <li>最多上传6张接送服务相关图片</li>
                          <li>支持jpg、png格式，单张图片不超过2MB</li>
                          <li>建议上传车辆、服务场景等相关图片</li>
                        </ul>
                      </div>
                    </div>
                  </el-form-item>
                </el-card>
              </el-form>

              <div class="form-actions">
                <el-button
                  type="primary"
                  :loading="loading.shuttle"
                  @click="saveShuttleInfo"
                  size="medium"
                >
                  <i class="el-icon-check"></i>
                  保存接送配置
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>


        <!-- 分账信息 -->
        <el-tab-pane label="分账信息" name="account">
          <div class="tab-content">
            <AccountInfoTable 
              v-model="accountInfo" 
              @save="handleAccountSave"
            />
          </div>
        </el-tab-pane>
        
      </el-tabs>
    </div>
  </section>
</template>

<script lang="ts">
import {
  detailsParking,
  detailsParkConfig,
  updateParking,
  dropdownAirport,
  saveParkConfig
} from "@/api/airParking";
import AccountInfoTable from './components/AccountInfoTable.vue'
import ParkingBasicInfo from './components/ParkingBasicInfo.vue'

export default {
  name: 'ParkingConfig',
  components: {
    AccountInfoTable,
    ParkingBasicInfo
  },
  data() {
    return {
      activeTab: 'basic',
      currentParkingId: null,
      currentParkingName: '',
      isVehicleFormValid: false,
      airportList: [],
      positionList: [
        {value: 1, label: '室外'},
        {value: 0, label: '室内'}
      ],
      // loading 状态新增在这里
      loading: {
        basic: false,
        vehicle: false,
        shuttle: false,
        account: false
      },
      regionOptions: [
        {
          code: '110000',
          name: '北京市',
          children: [
            {
              code: '110100',
              name: '北京市'
            }
          ]
        },
        {
          code: '310000',
          name: '上海市',
          children: [
            {
              code: '310100',
              name: '上海市'
            }
          ]
        },
        {
          code: '440000',
          name: '广东省',
          children: [
            {
              code: '440100',
              name: '广州市'
            },
            {
              code: '440300',
              name: '深圳市'
            }
          ]
        }
      ],
      shuttleImageList:[],  
      vehicleRules: {
        dailyRate: [{ required: true, message: '请输入预约停车费率', trigger: 'blur' }],
        minDays: [{ required: true, message: '请输入最少停车天数', trigger: 'blur' }],
        freeOvertime: [{ required: true, message: '请输入超时免费时长', trigger: 'blur' }]
      },
      shuttleRules: {
        description: [{ required: true, message: '请输入载客说明', trigger: 'blur' }],
        notice: [{ required: true, message: '请输入温馨提示', trigger: 'blur' }],
        shuttlePhone: [{ required: true, message: '请输入接驳电话', trigger: 'blur' }],
        pickupLocation: [{ required: true, message: '请输入接机地点', trigger: 'blur' }],
        dropoffLocation: [{ required: true, message: '请输入送机地点', trigger: 'blur' }],
        positionType: [{ required: true, message: '请选择停车地点', trigger: 'blur' }],
        duration: [{ required: true, message: '请输入接驳时长', trigger: 'blur' }],
        timeSlot: [{ required: true, message: '请输入接驳时段', trigger: 'blur' }],
        distance: [{ required: true, message: '请输入直线距离', trigger: 'blur' }],
        airportId: [{ required: true, message: '请选择枢纽名称', trigger: 'blur' }],
        region: [{ required: true, message: '请选择省市', trigger: 'blur' }]
      },
      requestUrl: this.$PUBLIC_URL.AIRPORT_API, // 请求接口
      basicForm: {
        name: 'XXXX车场',
        airportId: '1',
        province: 'guangdong',
        city: 'shenzhen',
        district: 'baoan',
        address: '广东省深圳市宝安区XXXXXXX',
        longitude: '113.810665',
        latitude: '22.639925',
        phone: '***********',
        servicePhone: '0755-********',
        parkingSpaces: '高/室外',
        feeStandard: '一小时5元，单次50元封顶XXXX',
        feeScreenPhoto: '',
        parkingPhotos: []
      },
      accountInfo: {
        globalSettings: {
          withdrawalDay: 'friday',
          waitingHours: '24'
        },
        accountList: [
          {
            id: 1,
            name: '宝安机场',
            type: 'platform',
            ratio: '60',
            settlementCycle: 'weekly',
            accountInfo: '招商银行 6225***1234',
            status: true,
            editing: false
          },
          {
            id: 2,
            name: '车场运营方',
            type: 'parking',
            ratio: '40',
            settlementCycle: 'weekly',
            accountInfo: '工商银行 6222***5678',
            status: true,
            editing: false
          }
        ]
      },
      parkingForm: {
        reservedVehicles: '30',
        minParkingDays: '3',
        overtimeFeeRate: '120',
        totalSpaces: '地下车库共计1楼-150车位',
        serviceDescription: '单次停车最高25人，建议5人，请多关注，',
        contactInfo: 'XXXXXXX',
        images: []
      },
      vehicleForm: {
        dailyRate: '',
        minDays: '',
        freeOvertime: ''
      },
      shuttleForm: {
        contactPhone: '***********',
        province: 'guangdong',
        city: 'shenzhen',
        district: 'baoan',
        collectionName: '宝安机场',
        distanceToAirport: '5.6 KM',
        shuttleDuration: '24 小时',
        shuttleTime: '15 分钟',
        parkingSpaces: '地下车库共计1楼-150车位',
        serviceNote: '单次停车最高25人，建议5人，请多关注，',
        contactMethod: 'XXXXXXX',
        images: []
      }
    }
  },
  mounted() {
    // 获取路由参数
    this.currentParkingId = this.$route.query.id;
    this.currentParkingName = this.$route.query.name || '';
    
    // 如果有车场ID，加载车场配置信息
    if (this.currentParkingId) {
      this.loadParkingConfig();
      this.loadAirportList();
    }
  },
  methods: {
    goBack() {
      this.$router.push({ name: 'ParkingManage' });
    },

    // 加载机场列表
    async loadAirportList() {
      try {
        // 这里应该调用实际的API
        const response = await dropdownAirport(this.requestUrl);
    
        // 防止 response 或 response.data 不存在导致解构失败
        if (!response || !response.data) {
          throw new Error('响应数据为空');
        }
    
        const { code, data } = response.data;
        if (code == 200) {
          this.airportList = data;
        } else {
          this.airportList = [];
        }
      } catch (error) {
        console.error('dropdownAirport 加载机场列表失败:', error)
      }
    },

    // Tab切换处理
    handleTabChange(tab) {
      this.activeTab = tab.name;
      if(tab.name == 'vehicle'){
        this.getParkConfig();
      }
    },

    getLabelsByValue(value) {
      if (!value) return [];
      const labels = [];
      let currentOptions = this.regionOptions;
      value.forEach((val, index) => {
        const option = currentOptions.find(item => item.code === val);
        if (option) {
          labels.push(option.name);
          // 如果不是最后一级，继续查找下一级的选项
          if (index < value.length - 1 && option.children) {
            currentOptions = option.children;
          }
        }
      });
      
      return labels;
    },

    handleRegionChange(value) {
      // 省市区变化处理
      console.log('选择的省市区:', value)
      let lableValue = this.getLabelsByValue(value);
      this.$set(this.shuttleForm, 'provinceName', lableValue[0]);
      this.$set(this.shuttleForm, 'cityName', lableValue[1]);
      this.$set(this.shuttleForm, 'provinceCode', value[0]);
      this.$set(this.shuttleForm, 'cityCode', value[1]);
    },
    
    //获取车场信息
    async loadParkingConfig() {
      try {
        const response = await detailsParking(this.requestUrl, this.currentParkingId);
        console.log("请求响应:", response);
    
        // 增加对 response.data 的空值判断
        if (!response.data) {
          this.$message.error(`加载车场配置信息失败！`);
          return;
        }
    
        const { code, data } = response.data;
        console.log("请求响应:", data);
        if (code === 200) {
          this.basicForm = data;
        } else {
          this.$message.error(`加载车场配置信息失败，错误码：${code}`);
        }
      } catch (error) {
        console.error('加载车场配置信息时发生错误:', error);
      }
    },

    //获取停车费率
    async getParkConfig() {
      try {
        const response = await detailsParkConfig(this.requestUrl, this.currentParkingId);
        const { code, data } = response.data;
        console.log("请求响应:", data);
        if (code == 200 && data !== null) {
          this.vehicleForm = data;
          this.isVehicleFormValid = true;
        }
      } catch (error) {
        console.log(`加载获取停车费率失败`);
        
      }
    },

    // 停车费率保存
    async saveVehicleInfo() {
      const params = { ...this.vehicleForm, parkingLotId: this.currentParkingId}
      try {
        const response = await saveParkConfig(this.requestUrl, params);
        const { code, data } = response.data;
        console.log("请求响应:", data);
        if (code == 200) {
          this.$message.success('停车费存保存成功');
        }
      } catch (error) {
        
      }
    },

    showErrorMessage(message) {
      this.$message.error(message);
    },
  
    //保存车场信息
    async handleBasicInfoSave(formData) {
      // this.basicForm = formData;
      this.loading.basic = true;
      console.log("保存车场信息", formData);
      try {
        let response = await updateParking(this.requestUrl, formData.id, formData);

        // 校验响应结构
        if (!response || !response.data) {
          this.showErrorMessage('编辑失败');
          this.loading.basic = false;
          return;
        }
    
        const { code } = response.data;
    
        if (code === 200 || code === '200') {
          this.$message.success('编辑成功');
          this.loading.basic = false;
        } else {
          this.showErrorMessage('编辑失败');
          this.loading.basic = false;
          return;
        }

      } catch (error) {
        this.$message.error(error.message || "操作失败");
      }
    },

    handleAccountSave(accountData) {
      this.accountInfo = accountData;
      // 这里可以调用API保存分账配置
      // await saveAccountConfig(this.currentParkingId, accountData);
      this.$message.success('分账信息保存成功');
    },
    saveParkingInfo() {
      this.$message.success('车场信息保存成功');
    },
    
    saveShuttleInfo() {
      this.$message.success('接送服务信息保存成功');
    },
    // 删除图片
    handleShuttleImageRemove(){
      this.$message.success('接送服务图片保存成功');
    },
    // 上传成功处理
    handleShuttleImageSuccess(){

    },
    beforeUpload(file) {
      const isImage = file.type.indexOf('image/') === 0;
      if (!isImage) {
        this.$message.error('只能上传图片文件!');
        return false;
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
        return false;
      }
      return true;
    }
  }
}
</script>

<style lang="scss" scoped>
// 全局变量定义
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

$bg-color: #f8f9fa;
$card-bg: #ffffff;
$border-color: #e4e7ed;
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;

$border-radius: 8px;
$box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
$transition: all 0.3s ease;

// 主容器样式
.parking-config-wrapper {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  // 页面头部
  .page-header {
    background: $card-bg;
    box-shadow: $box-shadow;
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px 32px;
      max-width: 1400px;
      margin: 0 auto;

      .header-left {
        flex: 1;

        .page-title {
          font-size: 24px;
          font-weight: 600;
          color: $text-primary;
          margin: 0 0 8px 0;
          display: flex;
          align-items: center;

          i {
            margin-right: 12px;
            color: $primary-color;
            font-size: 28px;
          }
        }

        .breadcrumb {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: $text-secondary;
          margin-bottom: 12px;

          .breadcrumb-item {
            &.current {
              color: $primary-color;
              font-weight: 500;
            }
          }

          i {
            margin: 0 8px;
            font-size: 12px;
          }
        }

        .parking-name-tag {
          .el-tag {
            font-size: 14px;
            padding: 8px 16px;
            border-radius: 20px;

            i {
              margin-right: 6px;
            }
          }
        }
      }

      .header-right {
        .el-button {
          padding: 12px 24px;
          border-radius: 6px;
          font-weight: 500;
          transition: $transition;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          }
        }
      }
    }
  }

  // 配置容器
  .config-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 32px 32px;

    // 进度指示器
    .progress-indicator {
      background: $card-bg;
      border-radius: $border-radius;
      padding: 32px;
      margin-bottom: 24px;
      box-shadow: $box-shadow;

      .el-steps {
        ::v-deep .el-step__title {
          font-size: 16px;
          font-weight: 500;
        }

        ::v-deep .el-step__description {
          font-size: 14px;
          color: $text-secondary;
        }

        ::v-deep .el-step__icon {
          width: 40px;
          height: 40px;

          &.is-text {
            border-width: 2px;
          }
        }
      }
    }

    // 标签页样式
    .config-tabs {
      background: $card-bg;
      border-radius: $border-radius;
      box-shadow: $box-shadow;
      overflow: hidden;

      ::v-deep .el-tabs__header {
        margin: 0;
        background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);

        .el-tabs__nav-wrap {
          padding: 0 32px;
        }

        .el-tabs__item {
          padding: 20px 32px;
          font-size: 16px;
          font-weight: 500;
          color: $text-regular;
          border: none;
          transition: $transition;

          &:hover {
            color: $primary-color;
          }

          &.is-active {
            color: $primary-color;
            background: $card-bg;
            border-radius: 8px 8px 0 0;
            position: relative;

            &::after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              height: 3px;
              background: linear-gradient(90deg, $primary-color, lighten($primary-color, 20%));
            }
          }
        }
      }

      ::v-deep .el-tabs__content {
        padding: 0;
      }
    }
  }
}

// 标签页内容
.tab-content {
  padding: 32px;

  // 区域头部
  .section-header {
    margin-bottom: 32px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f2f5;

    h3 {
      font-size: 20px;
      font-weight: 600;
      color: $text-primary;
      margin: 0 0 8px 0;
      display: flex;
      align-items: center;

      i {
        margin-right: 12px;
        color: $primary-color;
        font-size: 22px;
      }
    }

    .section-desc {
      font-size: 14px;
      color: $text-secondary;
      margin: 0;
      line-height: 1.6;
    }
  }

  // 信息区域
  .info-section {
    // 表单卡片
    .form-card {
      margin-bottom: 24px;
      border-radius: $border-radius;
      border: 1px solid #f0f2f5;
      transition: $transition;

      &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transform: translateY(-2px);
      }

      .card-header {
        font-size: 16px;
        font-weight: 600;
        color: $text-primary;
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          color: $primary-color;
        }
      }

      ::v-deep .el-card__header {
        background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
        border-bottom: 1px solid #f0f2f5;
        padding: 20px 24px;
      }

      ::v-deep .el-card__body {
        padding: 24px;
      }
    }

    // 费率表单特殊样式
    .rate-form {
      .unit {
        margin-left: 12px;
        color: $text-secondary;
        font-size: 14px;
        font-weight: 500;
        padding: 4px 8px;
        background: #f5f7fa;
        border-radius: 4px;
      }
    }

    // 接送服务表单
    .shuttle-form {
      // 图片上传区域
      .image-upload-section {
        .shuttle-image-upload {
          ::v-deep .el-upload {
            border: 2px dashed #d9d9d9;
            border-radius: $border-radius;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: $transition;

            &:hover {
              border-color: $primary-color;
              background: rgba(64, 158, 255, 0.05);
            }

            i {
              font-size: 28px;
              color: #c0c4cc;
              width: 148px;
              height: 148px;
              line-height: 148px;
              text-align: center;
            }
          }

          ::v-deep .el-upload-list__item {
            transition: $transition;
            border-radius: $border-radius;

            &:hover {
              transform: scale(1.05);
            }
          }
        }

        .upload-tips {
          margin-top: 16px;
          padding: 16px;
          background: #f8f9fa;
          border-radius: $border-radius;
          border-left: 4px solid $primary-color;

          p {
            margin: 0 0 8px 0;
            font-weight: 600;
            color: $text-primary;
            display: flex;
            align-items: center;

            i {
              margin-right: 6px;
              color: $primary-color;
            }
          }

          ul {
            margin: 0;
            padding-left: 20px;

            li {
              color: $text-secondary;
              font-size: 14px;
              line-height: 1.6;
              margin-bottom: 4px;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }
}

// 表单操作区域
.form-actions {
  text-align: center;
  margin-top: 40px;
  padding: 24px;
  background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: $border-radius;
  border: 1px solid #f0f2f5;

  .el-button {
    padding: 12px 32px;
    border-radius: 6px;
    font-weight: 500;
    margin: 0 8px;
    transition: $transition;

    &.el-button--primary {
      background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
      border: none;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
      }

      &:disabled {
        background: #c0c4cc;
        transform: none;
        box-shadow: none;
      }
    }

    &:not(.el-button--primary) {
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .parking-config-wrapper {
    .page-header .header-content {
      padding: 20px 24px;
    }

    .config-container {
      padding: 0 24px 24px;
    }
  }
}

@media (max-width: 768px) {
  .parking-config-wrapper {
    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-right {
        align-self: stretch;

        .el-button {
          width: 100%;
        }
      }
    }

    .config-container {
      padding: 0 16px 16px;

      .progress-indicator {
        padding: 20px;

        .el-steps {
          ::v-deep .el-step__title {
            font-size: 14px;
          }
        }
      }
    }
  }

  .tab-content {
    padding: 20px;

    .section-header h3 {
      font-size: 18px;
    }
  }
}

// Element UI 组件样式覆盖
::v-deep .el-form-item {
  margin-bottom: 24px;

  .el-form-item__label {
    font-weight: 500;
    color: $text-primary;
    line-height: 1.6;
  }

  .el-input__inner,
  .el-textarea__inner {
    border-radius: 6px;
    border: 1px solid #e4e7ed;
    transition: $transition;

    &:focus {
      border-color: $primary-color;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }

  .el-input-number {
    width: 100%;

    .el-input__inner {
      text-align: left;
    }
  }

  .el-select {
    width: 100%;
  }

  .el-cascader {
    width: 100%;
  }
}

::v-deep .el-card {
  border-radius: $border-radius;

  &.is-never-shadow {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }
}

::v-deep .el-tag {
  border-radius: 20px;
  padding: 6px 12px;
}
</style>