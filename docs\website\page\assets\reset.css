html{ font-size:62.5%; height: 100%;}
 body{
   margin:0;
   padding:0;
   color:#222222;
   margin:0 auto;
   background: #F7F7F7;
   height: 100%;
   position: relative;
 }
 body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td {margin:0;padding:0;}
 table{border-collapse:collapse; border-spacing:0;}
 fieldset,img{border:0;}
 address,caption,cite,code,dfn,em,th,var,i{font-style:normal;font-weight:normal;}
 ol,ul{list-style:none;}
 caption,th{text-align:left;}
 h1,h2,h3,h4,h5,h6{font-size:100%;font-weight:normal;}
 q:before,q:after{content:'';}
 a:focus{outline-style:none;}
 abbr,acronym{border:0;font-variant:normal;}
 sup{vertical-align:text-top;}
 sub{vertical-align:text-bottom;}
 input,select{font-family:inherit;font-size:inherit;font-weight:inherit;vertical-align: middle; }
 textarea{resize:none;font-family:inherit;font-size:inherit;font-weight:inherit;outline-style:none;}

 html{font-size: 100%;-webkit-tap-highlight-color: rgba(0, 0, 0, 0); }
 body{overflow-x: hidden;color: #404040;}

 a {
  text-decoration: none;
  color: #000;
}

 .clearfix:after { content: "."; display: block; height: 0; clear: both; visibility: hidden; }
