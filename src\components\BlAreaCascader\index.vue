<template>
  <el-cascader
    v-model="value"
    :options="options"
    :props="props"
    clearable
    placeholder="请选择省市区"
    @change="handleChange"
    style="width: 100%"
  ></el-cascader>
</template>
<script>
export default {
  name: "BlAreaCascader",
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    value: {
      type: [Array, String], // 适配单选或多选的值类型
      default: () => [],
    },
  },
  data() {
    return {
      props: {
        lazy: true,
        lazyLoad: this.lazyLoad,
      },
      options: [],
    };
  },
  
  methods: {
    async lazyLoad(node, resolve) {
      const { level, value } = node;
      resolve(await this.getAddressList(level, value));
    },

    /* 根据类型获取下一级所有地址信息 areaType:2-省，3-市，4-区*/
    async getAddressList(level, areaCode) {
      const areaType = level === 0 ? 2 : level === 1 ? 3 : 4;
      const data = {
        areaType,
        areaCode,
      };
      if (areaType != 2 && !areaCode) return;

      return new Promise(async (resolve) => {
        const res = await this.$axios.post("/facearea/queryNext", data);
        const resData = res.data;
        const { code } = resData;
        if (code === 200) {
          const nodes = resData.data.map((item) => ({
            value: item.areaCode,
            label: item.areaName,
            leaf: level >= 2,
          }));
          if (level === 0) {
            this.options = nodes;
          }
          resolve(nodes);
        } else {
          resolve([]);
        }
      });
    },

    // // 动态加载完整路径数据
    // async loadSelectedNodes() {
    //   const loadPath = async (ids, options) => {
    //     let parentNode = null;
    //     for (const id of ids) {
    //       // 查找当前节点
    //       let currentNode = options.find((item) => item.value === id);
    //       if (!currentNode) {
    //         // 如果节点不存在，触发懒加载
    //         await new Promise((resolve) => {
    //           this.lazyLoad(parentNode || {}, (children) => {
    //             parentNode.children = children;
    //             resolve();
    //           });
    //         });
    //         currentNode = parentNode.children.find((item) => item.value === id);
    //       }
    //       // 为下一级做准备
    //       parentNode = currentNode;
    //       options = currentNode
    //         ? currentNode.children || (currentNode.children = [])
    //         : [];
    //     }
    //   };
    //   await loadPath(this.value, this.options);
    // },

    handleChange(value) {
      // 触发 change 事件
      this.$emit("change", value);
    },

    // 父组件触发：this.$refs.[name].showValue()
    showValue(regions) {
      Promise.all(
        this.options.map(async (province) => {
          if (province.value == regions[0]) {
            const provChildren = await this.getAddressList(3, regions[0]);
            province.children = provChildren;

            await Promise.all(
              province.children.map(async (city) => {
                if (city.value == regions[1]) {
                  const cityChildren = await this.getAddressList(4, regions[1]);
                  city.children = cityChildren;
                }
                return city;
              })
            );

          }
          return province;
        })
      ).then((data) => {
        this.options = data;
      });
    },
  },
};
</script>