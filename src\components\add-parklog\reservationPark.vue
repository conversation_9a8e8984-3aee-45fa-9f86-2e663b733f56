<template>
  <div class="parkShareSet-container">
    <div class="parkShareSet-item">
      <!-- <div class="title">车场预约</div> -->
      <div class="parkShareSetFrom-info">
        <el-form v-loading="loading" ref="rateForm" label-width="100px" :model="rateForm" :rules="rateFormFormRules" class="share-setting-form">
          <el-form-item prop="money" label="收费标准">
            <el-table :data="feeStandardsList" border>
                <el-table-column property="id" label="时长(分钟)" align="center">
                    <template slot-scope="scope">
                      <!-- <el-input v-model="rateForm.time" style="width: 100px"></el-input> -->
                      <el-select v-model="rateForm.time"  style="width: 100px">
                        <el-option
                            v-for="item in timeyList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                      </el-select>
                    </template>
                </el-table-column>
                <el-table-column property="name" label="费用(元)" align="center">
                    <template slot-scope="scope">
                      <el-input-number v-model="rateForm.money" :precision="2" :min="0" :max="100000"></el-input-number>
                    </template>
                </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item prop="freeTime" label="免费时长">
            <el-input-number v-model="rateForm.freeTime" :min="0" :max="100000"></el-input-number>
          </el-form-item>
          <el-form-item prop="reserveStartTime" label="预约起始">
              <el-radio-group v-model="rateForm.reserveStartTime">
                <el-radio :label="1">当前时间</el-radio>
                <!-- <el-radio :label="0">未来任意时间</el-radio> -->
              </el-radio-group>
          </el-form-item>
          <el-form-item prop="reserveLimit" label="预约上限">
              <el-radio-group v-model="rateForm.reserveLimit">
                <el-radio :label="1"><el-input-number v-model="rateForm.reserveLimitNum" :min="0" :max="10000"></el-input-number> 小时</el-radio>
                <!-- <el-radio :label="0">无限制</el-radio> -->
              </el-radio-group>
          </el-form-item>
          <el-form-item prop="reserveTimes" label="预约次数">
            <el-input-number v-model="rateForm.reserveTimes" :min="0" :max="100000"></el-input-number>
          </el-form-item>

          <el-form-item label="">
            <el-button type="primary" @click="rateConfirm" :loading="rateLoading" style="width: 90px;">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
  
</template>
<script>
export default {
  name: 'reservationPark',
  props: {
    bolinkId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      loading: false,
      rateForm:{
        time: '',
        money: '',
        freeTime: '',
        reserveStartTime: 1,
        reserveLimit: 1,
        reserveTimes: ''
      },
      rateLoading:false,
      rateFormFormRules: {
        time: [
          { required: true, message: '请输入时长', trigger: 'blur' }
        ],
        money: [
          { required: true, message: '请输入费用', trigger: 'blur' }
        ],
        reserveStartTime: [
          { required: true, message: '请选择预约起始', trigger: 'blur' }
        ],
        freeTime:  [
          { required: true, message: '请输入免费时长', trigger: 'blur' }
        ],
        reserveLimit: [
          { required: true, message: '请选择预约上限', trigger: 'blur' }
        ],
        reserveTimes: [
          { required: true, message: '请输入预约次数', trigger: 'blur' }
        ],
      },
      timeyList: [
        {
          label: '15',
          value: 15
        },
        {
          label: '20',
          value: 20
        },
        {
          label: '30',
          value: 30
        },
      ],
      feeStandardsList:[
        {
          minutesNum:'15',
          costNum:'1'
        }
      ],
      showNumber: false,
      comid: '',
      unionId: '',
      parkId: '',
      parkingFrom:{},
      is4G: 0 //判断是否是纯云车场
    }
  },
  methods: {
    //车位预约费率查询
    rateInquiry(parkingFrom) {
      console.log(parkingFrom)
      if(parkingFrom){
        this.parkingFrom = parkingFrom;
      }
      let para = { comid: parkingFrom.comid, shareLotId: parkingFrom.id}
      this.$axios
        .post(`/parking/lot/share/reserveRate/query`, para)
        .then(res => {
          let data = res.data
          if (data.code === 200) {
            var dataInfo = data.data;
            
            if(dataInfo){
              if(dataInfo.reserveLimit > 0){
                dataInfo.reserveLimitNum = dataInfo.reserveLimit;
                dataInfo.reserveLimit = 1;
              }
              this.rateForm = dataInfo
            }else{
              this.rateForm = {
                time: '',
                money: '',
                freeTime: '',
                reserveStartTime: 1,
                reserveLimit: 1,
                reserveTimes: ''
              }
            }
            
          }else{
            this.rateForm = {
              time: '',
              money: '',
              freeTime: '',
              reserveStartTime: 1,
              reserveLimit: 1,
              reserveTimes: ''
            }
          }
        })
        .catch(err => {
          console.log(err)
          this.loading = false
        })
    },

    

    rateConfirm() {
      // 添加校验
      this.$refs.rateForm.validate(valid => {
        if (valid) {
          this.rateLoading = true;
          if(this.rateForm.time == ''){
            this.rateLoading = false;
            this.$message.error('请填写时长');
            return;
          }

          let para = {
            ...this.rateForm,
            comid: this.parkingFrom.comid,
            shareLotId: this.bolinkId,
          }
          if(this.rateForm.reserveLimit == 1){
            para.reserveLimit = this.rateForm.reserveLimitNum
          }
          console.log(para)
          this.$axios
            .post('/parking/lot/share/reserveRate/set', para)
            .then(res => {
              let data = res.data
              console.log(data)
              this.rateLoading = false;
              if (data.code === 200) {
                this.$message.success(data.message);
              } else {
                this.$message.error(data.message);
              }
            })

            .catch(err => {
              console.log(err)
            })
        }
      });
    },

  },
  activated() {
    this.rateInquiry()
  },
}
</script>
<style lang="scss" scoped>
.parkShareSet-container{
  margin: 0 20px;
  .parkShareSet-item{
    margin-bottom: 20px;
  }
  .title{
    font-size: 18px;
    color: #000;
  }
}
.share-setting-form {
  width: 600px;
}

</style>