<template>
  <div>
    <el-dialog
      custom-class="add-server-dialog width-520"
      :show-close="false"
      :visible.sync="dialogVisible"
      @close="handleClose"
    >
      <header
        class="fixed-code__title"
        slot="title"
        style="font-size: 18px; font-weight: bold"
      >
        纯云服务购买<i
          class="el-icon-close dialog-header-iconfont"
          @click="handleClose"
        ></i>
      </header>

      <el-steps :active="activeIndex" simple style="padding: 18px 20%">
        <el-step title="数量选择" icon="iconfont icon-icon-test1"></el-step>
        <el-step title="确认支付" icon="iconfont icon-icon-test"></el-step>
      </el-steps>

      <el-form
        ref="cloudForm"
        v-if="dialogVisible"
        label-width="80px"
        :model="cloudForm"
        class="custom-form-style fiexd-code-form"
        :rules="rules"
      >
        <div v-show="activeIndex == 1" class="first-form">
          <el-form-item label="所属车场" prop="comid">
            <el-select
              v-model="cloudForm.comid"
              filterable
              style="width: 100%"
              :disabled="dialogType === 2"
              @change="changeComidBuy"
            >
              <el-option
                v-for="item in cityParks"
                :label="item.value_name"
                :value="item.value_no"
                :key="item.value_no"
              >
                <span>{{ item.value_name }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="购买时长" prop="number">
            <el-select
              v-model="cloudForm.number"
              filterable
              style="width: 100%"
              @change="changeComidBuy"
            >
              <el-option
                v-for="item in buyTimes"
                :label="item.value_name"
                :value="item.value_no"
                :key="item.value_no"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="应收金额" prop="money">
            <p :class="String(cloudForm.money) === '0' ? 'curx-color' : ''">
              {{ cloudForm.money }} 元
            </p>
          </el-form-item>

          <div class="fixed-code-btn">
            <el-button
              type="primary"
              style="width: 144px"
              @click="paySumbitFn"
              :disabled="nextStepDisabled"
              :loading="submitLoad"
              >下一步</el-button
            >
          </div>
        </div>
        <div v-show="activeIndex == 2 && !noMoney">
          <div class="pay-method-wrapper">
            <span>支付方式：</span>
            <el-radio-group v-model="payMethod" @change="paymethodChangeFn">
              <!-- 服务商，集团只有扫码支付 -->
              <el-radio :label="1" v-show="roleType == 1"
                >扣除分润余额</el-radio
              >
              <el-radio :label="2" v-show="!(noMoney && roleType == 1)"
                >扫码支付</el-radio
              >
              <el-radio :label="3" v-show="!noMoney && roleType == 1"
                >人工调价</el-radio
              >
            </el-radio-group>
          </div>
          <div class="code-wrapper" v-show="payMethod !== 2">
            <p v-show="payMethod == 1" class="payTip">
              扣除厂商分润余额支付该平台服务费用
            </p>
            <p v-show="payMethod == 3" class="payTip">
              如有特殊原因需要调价的,提交后联系泊链商务人员处理
            </p>
          </div>
          <div
            class="code-wrapper"
            v-show="payMethod == 2"
            v-loading="nextLoad"
          >
            <img :src="qrsrc" alt="支付二维码" />
            <div class="pay-state" v-show="[0, 1].includes(payState)">
              <img
                class="pay-state-img"
                :src="payState == 1 ? paySuccessImg : payErrorImg"
                :alt="payState == 1 ? '支付成功' : '支付失败'"
              />
            </div>
            <p v-show="payState === 2">
              扫码支付 <b class="curx-color">{{ cloudForm.money }}</b> 元
            </p>
            <p class="pay-success" v-show="payState == 1">
              支付成功<span style="margin: 0 3px">{{ readSecondSuccess }}</span
              >秒后<a class="back" @click="backList">返回列表</a>
            </p>
            <p class="pay-error" v-show="payState == 0">
              支付失败<span style="margin: 0 3px">{{ readSecondError }}</span
              >秒后<a class="back" @click="getCode">重新获取二维码</a>
            </p>
          </div>

          <div class="fixed-code-btn">
            <el-button style="width: 144px" @click="upperStep"
              >上一步</el-button
            >
            <el-button
              type="primary"
              style="width: 144px; margin-left: 60px"
              :loading="submitLoad"
              @click="handleComfire"
              >{{ submitLoad ? "提交中" : "确定" }}</el-button
            >
          </div>
        </div>

        <div v-show="activeIndex == 2 && noMoney">
          <div class="no-money">
            当前需支付金额为：<b class="curx-color">{{ cloudForm.money }}</b>
            元，点击确定即可下单。
          </div>
          <div class="fixed-code-btn">
            <el-button style="width: 144px" @click="upperStep"
              >上一步</el-button
            >
            <el-button
              type="primary"
              style="width: 144px; margin-left: 60px"
              :loading="nextLoad"
              @click="handleNomoney"
              >{{ nextLoad ? "提交中" : "确定" }}</el-button
            >
          </div>
        </div>
      </el-form>
    </el-dialog>

    <!--canvas 容器隐藏-->
    <canvas id="canvas" style="display: none"></canvas>
    <canvas id="img" style="display: none"></canvas>
  </div>
</template>

<script>
import { Order } from '@icon-park/vue';

export default {
  name: "BlBuyCloud",
  props:['isOpen'],
  data() {
    return {
      isFour:false,
      dialogVisible: false,
      submitLoad: false,
      payMethodLoad: false,
      nextLoad: false,
      nextStepDisabled: true,
      cloudForm: {
        serviceId:5,
        comid: "",
        number: "",
        money: 0,
        orderId:""
      },
      rules: {
        comid: [
          { required: true, message: "请选择所属车场", trigger: "change" },
        ],
        number: [
          { required: true, message: "请选择购买时长", trigger: "change" },
        ],
      },
      cityParks: [],
      cloudList: [],
      buyTimes: [
        { value_name: "1年", value_no: 1 },
        { value_name: "2年", value_no: 2 },
        { value_name: "3年", value_no: 3 },
        { value_name: "4年", value_no: 4 },
        { value_name: "5年", value_no: 5 },
        { value_name: "6年", value_no: 6 },
        { value_name: "7年", value_no: 7 },
        { value_name: "8年", value_no: 8 },
        { value_name: "9年", value_no: 9 },
        { value_name: "10年", value_no: 10 },
      ],
      is_authorize: 0,
      ischecked: false,
      payMethod: 1,
      camera_nums: 0,
      activeIndex: 1,
      noMoney: false,
      payState: 0,
      paySuccessImg: require("@/assets/images/pay-success.png"),
      payErrorImg: require("@/assets/images/pay-error.png"),
      readSecondSuccess: 5,
      readSecondError: 5,
      qrsrc: "",
      roleType: 1,
      paySuccess: false,
      payError: false,
      dialogType: 1, //1-新增；2-编辑
    };
  },
  methods: {
    open(type, data,isFour) {
      this.isFour = isFour;
      // type:1-厂商；2-服务商；3-车场；4-总后台
      this.dialogVisible = true;
      this.roleType = type;
      this.dialogType = data ? 2 : 1; //2-纯云车场管理购买；1-新增
      if (data) {
        const { comid, name } = data;
        this.cityParks = [{ value_name: name, value_no: comid }];
        this.cloudForm.comid = comid;
        this.cloudForm.number = 1;
        this.changeComidBuy();
      } else {
        this.getParks();
      }
    },
    changeComidBuy(){
        if(this.cloudForm && this.cloudForm.comid && this.cloudForm.number && this.cloudForm.number > 0){
          this.cloudForm.serviceId = 5;
          this.$axios.post("/valueServicePrice/queryByAuthorizer", this.cloudForm)
          .then((res) => {
            if (res.data.code === 200) {
              this.cloudForm.money = res.data.data.price;
              this.cloudForm.tradeNo = res.data.data.tradeNo;
              this.nextStepDisabled = false;
            } else {
                this.cloudForm.money = 0;
                this.$message.error(res.data.message);
                this.nextStepDisabled = true;
            }
          })
          .catch((err) => {
            this.nextStepDisabled = true;
            this.cloudForm.money = 0;
            console.log(err);
          });
        }
    },
    handleClose() {
      this.dialogVisible = false;
      this.cloudForm = {
        serverId:5,
        comid: "",
        number: "",
        money: 0,
      };
      this.cityParks = [];
      this.camera_nums = 0;
      this.is_authorize = 0;
      this.qrsrc = "";
      this.payMethod = 1;
      this.dialogType = 1;
      this.activeIndex = 1;
      this.readSecondSuccess = 5;
      this.readSecondError = 5;
      clearInterval(this.timer);
      clearInterval(this.paySuccess);
      clearInterval(this.payError);
    },

    // 获取角色下的车场
    async getParks() {
      let cityid = sessionStorage.getItem("cityid");
      let serverId = sessionStorage.getItem("bolink_serverid");
      let groupid = sessionStorage.getItem("groupid");
      if(this.isFour){
        let parks = await this.$axios.post("/getdata/getAll5GParks");
        this.cityParks = parks && parks.status == 200 && parks.data.data.length ? parks.data.data : [];
      } else {
        let parks = await this.$axios.post(`/getdata/unionAlllNon4GDepotParks?cityid=${cityid}&serverId=${serverId}&groupid=${groupid}`);
        this.cityParks = parks && parks.status == 200 && parks.data.length ? parks.data : [];
      }
    },
    // 车场切换
    changeComid(e) {
      this.searchPrice(e);
    },
    // 下一步前期校验
    paySumbitFn() {
      this.$refs.cloudForm.validate((valid, obj) => {
        let errKeys = Object.keys(obj);
        let isAuthorize = this.is_authorize;
        if (
          (isAuthorize === 0 && valid) ||
          (isAuthorize === 1 && errKeys.length === 1)
        ) {
          this.nextStep();
        } else {
          return false;
        }
      });
    },

    // 进入下一步
    nextStep() {
      const noMoney = this.noMoney,
        roleType = this.roleType;

      if (roleType === 1 && noMoney) {
        this.payMethod = 1;
      } else {
        this.payMethod = 2;
        // 支付金额为0
        if (!noMoney) {
          this.getCode();
        }
      }
      this.activeIndex = 2;
    },

    // 切换支付方式
    paymethodChangeFn(val) {
      if (val === 2) {
        this.getQrCode();
      }
    },

    // 获取支付码key值
    getQrCode() {
      clearInterval(this.timer);
      this.payState = 2;
      let timestamp = Math.random().toString();
      let params = {
        serviceId: 5,
        buyType: 2,
        comid: this.cloudForm.comid,
        amount: this.cloudForm.money,
        number: this.cloudForm.number,
        tradeNo: this.cloudForm.tradeNo,
        tmp: timestamp,
        nickname1: sessionStorage.getItem("nickname1"),
        loginuin: sessionStorage.getItem("loginuin"),
        cityid: sessionStorage.getItem("cityid"),
      };
      this.toQrcodeBug(params);
    },
    // 获取二维码流水号 生成二维码图片地址
    toQrcodeBug(params) {
      this.nextLoad = true;
      this.$axios
        .post("/servicePrice/toQrcodeBugV2", params)
        .then((res) => {
          this.nextLoad = false;
          const apiResData = res.data;
          const resData = apiResData && apiResData.data;
          const { comid } = this.cloudForm;
          const noMoney = this.noMoney;

          if (apiResData.code !== 200 || !resData) {
            this.qrsrc = "";
            this.activeIndex = 1;
            this.changeComid(comid);
            this.$message.warning(apiResData.message);
          } else {
            if (resData.state == 1) {
              if (noMoney) {
                this.$message.success("操作成功！");
                this.handleClose();
                this.$emit("successPay");
              } else {
                const { trade_no } = resData;
                let _url = this.$PUBLIC_URL.SERVER_API + "/zld/buymessage?trade_no=" + trade_no;
                this.timer = setInterval(() => {
                  this.getPayStateFn(trade_no);
                }, 2500);
                this.getqr(_url);
              }
            } else {
              this.qrsrc = "";
              this.$message.error("获取失败，请稍后重试");
            }
          }
        })
        .catch((err) => {
          this.qrsrc = "";
          this.nextLoad = false;
          this.$message.error(err);
        });
    },
    // 获取二维码图片
    getqr(url) {
      let canvas = document.getElementById("canvas");
      this.QRCode.toCanvas(
        canvas,
        url,
        { errorCorrectionLevel: "H" },
        (err) => {
          console.log("QRCode.toCanvas.err", err);
        }
      );
      let context = canvas.getContext("2d");
      let imageData = context.getImageData(0, 0, canvas.width, canvas.height);
      let img = document.getElementById("img");
      img.width = canvas.width;
      img.height = canvas.height;
      let context2 = img.getContext("2d");
      context2.fillStyle = "white";
      context2.fillRect(0, 0, canvas.width, canvas.height);
      context2.putImageData(imageData, 0, 0);
      context2.font = "bold 10px 微软雅黑";
      context2.fillStyle = "black";

      this.qrsrc = img.toDataURL("image/png");
      this.activeIndex = 2;
      this.nextLoad = false;
    },

    //轮询获取支付状态
    getPayStateFn(trade_no) {
      let params = { trade_no };
      this.$axios
        .post("/message/getcodestate", this.$qs.stringify(params))
        .then((res) => {
          let state = res.data.state;
          this.payState = state;
          if (state == 2) {
            //  支付中
          } else if (state == 1) {
            //  支付成功
            clearInterval(this.timer);
            this.paySuccess = setInterval(() => {
              this.readSecondSuccess--;
              if (this.readSecondSuccess <= 0) {
                clearInterval(this.paySuccess);
                this.handleClose();
                this.$emit("successPay");
              }
            }, 1000);
          } else {
            //支付失败
            clearInterval(this.timer);
            this.payError = setInterval(() => {
              this.readSecondError--;
              if (this.readSecondError <= 0) {
                clearInterval(this.payError);
                this.getCode();
              }
            }, 1000);
          }
        })
        .catch((err) => {
          console.log("payError", err);
        });
    },

    // 返回列表
    backList() {
      this.activeIndex = 1;
    },

    // 分润支付接口请求
    payRequest(params) {
      this.submitLoad = true;
      // "/unionpaycom4gService/unionpay" 原接口
      this.$axios
        .post("/servicePrice/toBug", params)
        .then((res) => {
          this.submitLoad = false;
          const apiResData = res.data;
          const resData = apiResData && apiResData.data;
          if (apiResData.code !== 200 || !resData) {
            const errMsg=resData?resData.msg:apiResData.message
            this.errSubmitTip(errMsg);
          } else {
            if (resData.state === 1) {
              this.$message.success("购买成功");
              this.handleClose();
              this.$emit("successPay");
            } else {
              this.errSubmitTip(resData.msg);
            }
          }
        })
        .catch((err) => {
          this.submitLoad = false;
        });
    },

    // 分润支付失败提示
    errSubmitTip(msg) {
      this.$confirm(`<strong>扣款失败！</strong><p>${msg}</p>`, "提示", {
        dangerouslyUseHTMLString: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {})
        .catch(() => {});
    },

    // 分润确定提交
    handleComfire() {
      const buyType = this.payMethod;

      if ([1, 3].includes(buyType)) {
        const {
          comid,
          autopay,
          buy_time: number,
          money: amount,
        } = this.cloudForm;
        const autopays = autopay.split("-");

        let params = {
          comid,
          number,
          amount,
          buyType,
          servicePriceId: Number(autopays[0]),
        };

        this.payRequest(params);
      }
    },

    handleNomoney() {
      this.getCode();
    },

    // 获取二维码条件判断
    getCode() {
      let { comid } = this.cloudForm;
      if (comid) {
        clearInterval(this.timer);
        this.getQrCode();
      } else {
        this.$message({
          message: "所属车场必须选择一个进行购买",
          type: "warning",
        });
        return false;
      }
    },

    // 上一步
    upperStep() {
      this.activeIndex = 1;
    },
  },
};
</script>

<style lang="scss" scoped>
.fixed-code__title {
  text-align: center;
}
.code-wrapper {
  position: relative;
  padding-top: 20px;
  width: 100%;
  text-align: center;
  min-height: 250px;
  .payTip {
    padding: 50px 0;
  }
  img {
    display: inline-block;
    width: 196px;
    height: 196px;
  }
  .pay-state {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 196px;
    height: 196px;
    background: rgba(255, 255, 255, 0.9);
    z-index: 3;
    .pay-state-img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 112px;
      height: 112px;
    }
  }
  p {
    font-size: 16px;
    line-height: 30px;
    color: rgba(54, 54, 54, 1);
  }
  .back {
    margin-left: 7px;
    color: #3c75cf;
    cursor: pointer;
  }
}
.fixed-code-btn {
  display: block;
  text-align: center;
}
.first-form {
  min-height: 250px;
  padding-top: 20px;
}
.no-money {
  min-height: 250px;
  min-width: 250px;
  text-align: center;
  line-height: 250px;
}
</style>