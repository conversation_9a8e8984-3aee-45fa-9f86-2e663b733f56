<template>
  <div class="parking-spaceNew">
    <el-drawer :visible="parkingShow" direction="rtl" @close="closeDialog" size="710px" custom-class="custom-drawer">
      <div slot="title" class="parkInfoTab">
        <!-- @tab-click="handleClick" -->
        <el-tabs v-model="activeName">
          <el-tab-pane label="车位信息" name="parkInfo">
            <div class="custom-drawer-info">
              <el-form ref="refillAddForm" label-width="68px" :rules="parkingFromRules" :model="parkingFrom">
                <div class="level-title">基础信息</div>
                <el-form-item label="车场名称" v-if="parkingFrom.applyName">
                  <el-input v-model.trim="parkingFrom.applyName" placeholder="" :disabled="true"></el-input>
                  <div class="tips">
                    <svg-tips-one theme="outline" size="16" />
                    必填，名称字符数最大6
                  </div>
                </el-form-item>
                <el-form-item label="车场编号" v-if="parkingFrom.comid">
                  <el-input v-model.trim="parkingFrom.comid" placeholder="" :disabled="true"></el-input>
                  <div class="tips">
                    <svg-tips-one theme="outline" size="16" />
                    必填，名称字符数最大6
                  </div>
                </el-form-item>
                <el-form-item label="车位编号">
                  <el-input
                    v-model.trim="parkingFrom.parkingLotNumber"
                    placeholder=""
                    :disabled="(parkingFrom.occupyState > 0 && parkType == '0') || (parkingFrom.applyName !== null && parkType == '0')"
                    maxlength="8"
                  ></el-input>
                  <div class="tips">
                    <svg-tips-one theme="outline" size="16" />
                    必填，名称字符数最大8
                  </div>
                </el-form-item>
                <el-form-item label="车位备注">
                  <el-input
                    type="textarea"
                    v-model.trim="parkingFrom.remark"
                    placeholder="选填，最大30字符"
                    :disabled="(parkingFrom.occupyState > 0 && parkType == '0') || (parkingFrom.applyName !== null && parkType == '0')"
                    maxlength="30"
                  ></el-input>
                </el-form-item>
                <el-form-item label="所属区域">
                  <el-select v-model="parkingFrom.comAreaInfoId" style="width:100%" :disabled="parkingFrom.occupyState > 0 || parkingFrom.applyName !== null">
                    <el-option v-for="item in regionList" :key="item.value_no" :label="item.value_name" :value="item.value_no"></el-option>
                  </el-select>
                  <div class="tips">
                    <svg-tips-one theme="outline" size="16" />
                    必填，选择本次车位的所属区域
                  </div>
                </el-form-item>
                <el-form-item label="车位类型">
                  <el-select v-model="parkingLotList" multiple :multiple-limit="1" placeholder="请选择" :disabled="parkingFrom.occupyState > 0 || parkingFrom.applyName !== null">
                    <el-option v-for="item in getParkingList" :key="item.value_no" :label="item.value_name" :value="item.value_no"></el-option>
                  </el-select>
                  <div class="tips">
                    <svg-tips-one theme="outline" size="16" />
                    必填，选择本次车位的类型，（若无类型，请前往本页“设置”中创建）
                  </div>
                </el-form-item>
                <div class="level-title">车位预约</div>
                <el-form-item label="允许预约">
                  <el-switch
                    v-model="parkingFrom.appointState"
                    :val="parkingFrom.appointState"
                    active-color="#2878ff"
                    inactive-color="#999"
                    :active-value="1"
                    :inactive-value="0"
                    :disabled="(parkingFrom.occupyState > 0 && parkType == '0') || (parkingFrom.applyName !== null && parkType == '0')"
                  ></el-switch>
                  <div class="tips">
                    <svg-tips-one theme="outline" size="16" />
                    选填，是否为当前车位开启/关闭预约
                  </div>
                </el-form-item>
                <el-form-item label="车位共享" v-if="shareParkType == 1">
                  <el-switch
                    v-model="parkingFrom.shareState"
                    :val="parkingFrom.shareState"
                    active-color="#2878ff"
                    inactive-color="#999"
                    :active-value="1"
                    :inactive-value="0"
                    :disabled="(parkingFrom.occupyState > 0 && parkType == '0') || (parkingFrom.applyName !== null && parkType == '0')"
                  ></el-switch>
                </el-form-item>
                <!-- <div class="level-title">车位归属</div>
                <el-form-item label="产权方">
                  <div class="belong-input">
                    <el-input
                      class="beName-info"
                      v-model.trim="parkingFrom.belongTo"
                      placeholder="名称"
                      maxlength="20"
                      :disabled="parkingFrom.occupyState > 0 || parkingFrom.applyName !== null"
                    ></el-input>
                    <el-input v-model.trim="parkingFrom.belongToContact" placeholder="联系人" maxlength="4" :disabled="parkingFrom.occupyState > 0 || parkingFrom.applyName !== null"></el-input>
                    <el-input v-model.trim="parkingFrom.belongToMobile" placeholder="电话" maxlength="11" :disabled="parkingFrom.occupyState > 0 || parkingFrom.applyName !== null"></el-input>
                  </div>
                  <div class="tips">
                    <svg-tips-one theme="outline" size="16" />
                    选填，记录此车位的产权方信息（名称最大20字符，联系人名称最大4字符，电话11位）
                  </div>
                </el-form-item>
                <el-form-item label="运营方">
                  <div class="belong-input">
                    <el-input
                      class="beName-info"
                      v-model.trim="parkingFrom.operate"
                      placeholder="名称"
                      maxlength="20"
                      :disabled="parkingFrom.occupyState > 0 || parkingFrom.applyName !== null"
                    ></el-input>
                    <el-input v-model.trim="parkingFrom.operateContact" placeholder="姓名" maxlength="4" :disabled="parkingFrom.occupyState > 0 || parkingFrom.applyName !== null"></el-input>
                    <el-input v-model.trim="parkingFrom.operateMobile" placeholder="电话" maxlength="11" :disabled="parkingFrom.occupyState > 0 || parkingFrom.applyName !== null"></el-input>
                  </div>
                  <div class="tips">
                    <svg-tips-one theme="outline" size="16" />
                    选填，记录此车位的产权方信息（名称最大20字符，联系人名称最大4字符，电话11位）
                  </div>
                </el-form-item> -->
                <template v-if="shareParkType == 1">

                  <div class="level-title">业主信息</div>
                  
                  <el-form-item label="手机号">
                    <!-- <el-input v-model.trim="parkingFrom.ownerMobile" placeholder="" :disabled="parkingFrom.occupyState > 0 || parkingFrom.applyName !== null" maxlength="11"></el-input> -->
                    <el-select
                        class="mobile"
                        v-model.trim="parkingFrom.owner_info"
                        filterable
                        reserve-keyword
                        :disabled="parkingFrom.occupyState > 0 || parkingFrom.applyName !== null"
                        placeholder="请输入手机号"
                        @change="ownerMobileChange">
                        <el-option
                          v-for="item in ownerMobileList"
                          :key="`${item.userPhone}（${item.userName} - ${item.userIdentityName}）`"
                          :label="`${item.userPhone}（${item.userName} - ${item.userIdentityName}）`"
                          :value="item">
                        </el-option>
                      </el-select>
                    <div class="tips">
                      <svg-tips-one theme="outline" size="16" />
                      选填，号码仅限数字，字符数最大11
                    </div>
                  </el-form-item>
                  <!-- <el-form-item label="业主身份">
                    <el-select v-model="parkingFrom.ownerIdentity" placeholder="请选择" :disabled="parkingFrom.occupyState > 0 || parkingFrom.applyName !== null">
                      <el-option v-for="item in identityList" :key="item.value_no" :label="item.value_name" :value="item.value_no"></el-option>
                    </el-select>
                  </el-form-item> -->
                </template>

                <el-form-item label="房号">
                  <div class="houseInfo">
                    <span>楼区</span>
                    <el-input v-model.trim="parkingFrom.buildingArea" placeholder="" :disabled="parkingFrom.occupyState > 0 || parkingFrom.applyName !== null"></el-input>
                    <span>楼栋</span>
                    <el-input v-model.trim="parkingFrom.building" placeholder="" :disabled="parkingFrom.occupyState > 0 || parkingFrom.applyName !== null"></el-input>
                    <span>单元</span>
                    <el-input v-model.trim="parkingFrom.buildingUnit" placeholder="" :disabled="parkingFrom.occupyState > 0 || parkingFrom.applyName !== null"></el-input>
                    <span>房号</span>
                    <el-input v-model.trim="parkingFrom.roomNumber" placeholder="" :disabled="parkingFrom.occupyState > 0 || parkingFrom.applyName !== null"></el-input>
                  </div>
                  <div class="tips">
                    <svg-tips-one theme="outline" size="16" />
                    选填，（示例；A区--2栋--1单元---502）
                  </div>
                </el-form-item>
              </el-form>

              <div style="height: 80px;"></div>

              <footer slot="footer" class="dialog-footer">
                <el-button type="primary" @click="saveParklot" :disabled="(parkingFrom.occupyState > 0 && parkType == '0') || (parkingFrom.applyName !== null && parkType == '0')">提交</el-button>
              </footer>
            </div>
          </el-tab-pane>
          <el-tab-pane label="预约信息" name="reservatInfor">
            <reservat-infor ref="reservatInfo" :bolinkId="parkingFrom.id" :applyName="parkingFrom.applyName" :empowerType="parkingFrom.empowerType"></reservat-infor>
          </el-tab-pane>
          <el-tab-pane label="车位预约" name="reservationPark" v-if="shareParkType == 1">
            <reservation-park ref="reservationPark" :bolinkId="parkingFrom.id"></reservation-park>
          </el-tab-pane>
          <el-tab-pane label="车位共享" name="spaceSharePark" v-if="shareParkType == 1">
            <parkspace-share ref="spaceSharePark" :bolinkId="parkingFrom.id"></parkspace-share>
          </el-tab-pane>
          <el-tab-pane label="占位信息" name="placeholderInfor">
            <placeholder-infor ref="seatInfo" :bolinkId="parkingFrom.id" :applyName="parkingFrom.applyName" :empowerType="parkingFrom.empowerType" :updateType="1"></placeholder-infor>
          </el-tab-pane>
          <el-tab-pane label="车位准入" name="parkAccess">
            <placeholder-infor ref="parkAccessInfo" :bolinkId="parkingFrom.id" :applyName="parkingFrom.applyName" :empowerType="parkingFrom.empowerType" :updateType="2"></placeholder-infor>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import reservatInfor from '@/components/add-parklog/reservation-infor.vue'
import reservationPark from '@/components/add-parklog/reservationPark.vue'
import parkspaceShare from '@/components/add-parklog/parkspaceShare.vue'
import placeholderInfor from '@/components/add-parklog/placeholder-infor.vue'


export default {
  components: {
    reservatInfor,
    reservationPark,
    parkspaceShare,
    placeholderInfor
  },
  props: {
    // value: {
    //   type: Number,
    // },
    parkType: {
      type: String,
      default: '0'
    },
    shareParkType: {
      type: Number,
      default: 0
    },
    parkingFrom: {
      type: Object,
      default: {}
    },
    parkingShow: {
      type: Boolean,
      default: false
    },
    regionList: {
      type: Array,
      default: [] //区域列表
    },
    getParkingList: {
      type: Array,
      default: [] //类型列表
    },
    identityList: {
      type: Array,
      default: [] //类型列表
    }
  },
  data() {
    return {
      top: '0vh',
      parkingLotList: [], //车位类型列表
      activeName: 'parkInfo',
      ownerMobileList: [],
      parkingFromRules: {
        total: [{ required: true, message: '应收金额不能为空', trigger: 'blur' }],
        add_money: [{ required: true, message: '充值金额不能为空', trigger: 'blur' }]
      }
    }
  },
  mounted() {},
  watch: {
    'parkingFrom.ownerMobile': {
      handler(val) {
        let name = ''
        if(this.parkingFrom.ownerIdentity == 1){
          name = '商户'
        }else if(this.parkingFrom.ownerIdentity == 2){
          name = '租户'
        }else if(this.parkingFrom.ownerIdentity == 0){
          name = '业主'
        }else{
          name = ''
        }
        if(!val){
          this.$set(this.parkingFrom, 'owner_info', '')
          return
        }
        this.$set(this.parkingFrom, 'owner_info', `${this.parkingFrom.ownerMobile}（${this.parkingFrom.ownerName} - ${name}）`)
      },
      deep: true
    },
    parkingShow: {
      handler(val) {
        if (!val) {
          this.ownerMobileList = []
        }else{
          this.getUserPageInfoList()
        }
      },
      immediate: false
    }
  },
  methods: {
    closeDialog() {
      this.parkingLotList = []
      this.activeName = 'parkInfo'
      this.$refs.seatInfo.clearData()
      this.$refs.parkAccessInfo.clearData()
      this.$refs.reservatInfo.clearData()
      this.$emit('closeDialog', false)
    },

    ownerMobileChange(item){
        // this.parkingFrom.ownerName = item.userName
        this.$set(this.parkingFrom,'ownerName',item.userName)
        this.$set(this.parkingFrom,'ownerIdentity',item.userIdentity)
        this.$set(this.parkingFrom,'ownerMobile',item.userPhone)
        // this.parkingFrom.ownerIdentity = item.userIdentity
        // this.parkingFrom.ownerMobile = item.userPhone
        console.log("🚀 ~ ownerMobileChange ~ val:", this.parkingFrom)
    },

    // 获取业主列表
    getUserPageInfoList(query){

      let comid = sessionStorage.getItem('comid')
      let data = {
        comid: comid == 'undefined' ? '' : comid,
        companyName: '',
        page: 1,
        rp: 1000,
        userPhone: null
      }

      this.$axios.post(`parking/lot/share/user/pageInfo`, data).then(res => {
        if (res.data.code == 200) {

          const list = res.data.data.rows
          list.forEach(item=>{
            if(item.userIdentity == 1){
              item.userIdentityName = '商户'
            }else if(item.userIdentity == 2){
              item.userIdentityName = '租户'
            }else if(item.userIdentity == 0){
              item.userIdentityName = '业主'
            }else{
              item.userIdentityName = ''
            }
          })
          
          this.ownerMobileList = list
        } else {
          this.ownerMobileList = []
        }
      })

    },

    // 数据编辑处理
    processEcho() {
      setTimeout(() => {
        const parkingLotTypes = this.parkingFrom.parkingLotTypes
        const comAreaInfoId = this.parkingFrom.comAreaInfoId
        this.$set(this.parkingFrom, 'comAreaInfoId', comAreaInfoId.toString())
        parkingLotTypes.forEach(item => {
          const parkLength = this.getParkingList.filter(parkItem => parkItem.value_no == item.parkingLotTypeId).length
          if (parkLength > 0) {
            this.parkingLotList.push(item.parkingLotTypeId)
          }
        })
        this.$refs.reservatInfo.getReservationInfor(this.parkingFrom)
        this.$refs.seatInfo.getPlaceholderInfor(this.parkingFrom)
        this.$refs.parkAccessInfo.getPlaceholderInfor(this.parkingFrom)
        this.$refs.reservationPark.rateInquiry(this.parkingFrom)
        this.$refs.spaceSharePark.shareFeeInquiry(this.parkingFrom)
        this.$refs.spaceSharePark.feesSharedQuiry(this.parkingFrom)
      }, 100)
    },

    saveParklot() {
      var parkingFromInfo = JSON.parse(JSON.stringify(this.parkingFrom))
      const { parkingLotNumber, comAreaInfoId, appointState } = parkingFromInfo
      const parkingLotList = this.parkingLotList
      const regionList = this.regionList //区域列表
      if (parkingLotNumber == '') {
        this.$message({
          message: '车位编号不能为空',
          type: 'error'
        })
        return
      } else if (comAreaInfoId == '') {
        this.$message({
          message: '请选择所属区域',
          type: 'error'
        })
        return
      } else if (parkingLotList.length == 0) {
        this.$message({
          message: '请选择车位类型',
          type: 'error'
        })
        return
      }

      if(this.shareParkType == 1 && !parkingFromInfo.owner_info){
        this.$message({
          message: '请选择业主信息',
          type: 'error'
        })
        return
      }

      regionList.forEach(item => {
        if (item.value_no == comAreaInfoId) {
          parkingFromInfo.comAreaInfoName = item.value_name
        }
      })
      parkingFromInfo.parkingLotTypes = []
      parkingLotList.forEach(item => {
        this.getParkingList.forEach(Idex => {
          if (item == Idex.value_no) {
            parkingFromInfo.parkingLotTypes.push({
              parkingLotTypeId: Idex.value_no, //车位类型ID
              parkingLotType: Idex.value_name //车位类型名称
            })
          }
        })
      })
      this.parkingLotList = []
      this.$emit('addParkingFrom', parkingFromInfo)
    }
  }
}
</script>
<style lang="scss" scoped>
.header-title {
  color: #333;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.parkInfoTab {
}
/deep/.el-drawer__close-btn {
  position: absolute;
  right: 20px;
  top: 30px;
}

.belong-input {
  display: flex;
  /deep/.el-input {
    width: 130px;
    margin-right: 10px;
  }
  .beName-info {
    width: 180px;
  }
}

/deep/.el-drawer__header {
  margin-bottom: 10px;
  padding: 20px 30px 0 30px;
}
.custom-drawer-info {
  height: 86vh;
  overflow: auto;


  .mobile{
    width: 240px !important;
    /deep/.el-input{
      width: 100% !important;
    }
  }
}

/deep/.el-select .el-input {
  max-width: 300px !important;
}
.level-title {
  font-weight: bold;
  margin-bottom: 10px;
}
.tips {
  color: #999;
  font-size: 14px;
  background: #fff;
  line-height: 20px;
  margin: 6px 0;
  span {
    vertical-align: middle;
    margin-right: 6px;
  }
}

.el-button {
  width: 120px;
}
/deep/.el-tabs__nav-wrap::after {
  display: none;
}

.header-tips {
  color: #b8741a;
  margin-left: 20px;
  margin: 0 10px;
}

/deep/.el-input {
  max-width: 210px;
}

.houseInfo {
  display: flex;
  span {
    display: inline-block;
    width: 30px;
    margin: 0 10px;
  }
  .el-input {
    width: 70px;
  }
}

.switch-info {
  .el-input {
    width: 70px;
  }
}
</style>
