import { Message } from 'element-ui';
/**
 * 公共跳转
 */
class PublicSkip {
  constructor(params) {
    this.Qs = require('qs')
    this.params = params || {}
  }
  // 更新token后跳转到进件页面
  jumpCommonNav() {
    const userInfo = JSON.parse(sessionStorage.getItem("user") || '{}');
    const path = process.env.BOLINK_API;
    const { id } = this.params
    fetch(`${path}/parkingos/gettoken`, {
      method: 'post',
      body: this.Qs.stringify(userInfo.token_params),
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
      }
    }).then(data => {
      return data.text();
    }).then(data => {
      data = JSON.parse(data);
      if(data.state === 1){
        sessionStorage.setItem('token',data.token);
        if(path.indexOf("beta") !== -1){
          // 从paas进入的
          if (window.__POWERED_BY_QIANKUN__) {
            const { protocol, host } = window.location
            window.open(`${protocol}//${host}/pay-os/commonNav?token=${data.token}&comid=${id}`, '_blank');
          } else {
            window.open(`https://beta.bolink.club/commonNav?token=${data.token}&comid=${id}`, '_blank');
          }

        }else{
          // 从paas进入的
          if (window.__POWERED_BY_QIANKUN__) {
            const { protocol, host } = window.location
            window.open(`${protocol}//${host}/pay-os/commonNav?token=${data.token}&comid=${id}`, '_blank');
          } else {
            window.open(`https://s.bolinkpaas.com/commonNav?token=${data.token}&comid=${id}`, '_blank');
          }

        }
      }else{
        Message({
          message: '请重新登录后，再尝试跳转',
          type: 'error',
          duration: 600
        });
      }
    })
  }
}
export default PublicSkip
