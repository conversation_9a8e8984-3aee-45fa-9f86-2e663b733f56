<template>
  <el-scrollbar class="bl-scrollbar" :style="{height: height}">
    <div class="basic-container--block" :class="{'is-background': isBackground}" :style="styleName">
      <slot />
    </div>
  </el-scrollbar>
</template>

<script>
/**
 * @description 页面主要容器，el-scrollbar包裹,为兼容浏览器滚动条
 * @props { string } height 高度默认100%
 * @props { object } styleName block样式
 */
export default {
  name: 'BasicContainer',
  props: {
    height: {
      type: String,
      default: '100%'
    },
    styleName: {
      type: Object,
      default: () => {
        return {
          padding: '16px'
        }
      }
    },
    background: Boolean
  },
  computed: {
    isBackground() {
      return this.background
    }
  }
}
</script>

<style scoped>
  .is-background {
    background: #fff;
  }
</style>
