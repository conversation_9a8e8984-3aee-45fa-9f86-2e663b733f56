<template>
    <div class="parking-spaceNew">
      <el-drawer
            :visible="parkingShow"
            direction="rtl"
            @close="closeDialog"
            size="680px"
            custom-class="custom-drawer">
            <div slot="title" class="parkInfoTab">
              <el-tabs v-model="activeName">
                <el-tab-pane label="车位准入" name="allowInInfo">
                  <!-- 加入车位准入子组件 -->
                  <allowIn-info  ref="parkAccessInfo" v-bind="$attrs" v-on="$listeners" :bolinkId="parkingForm.id" :comName="parkingForm.comName" :empowerType="parkingForm.empowerType" :updateType="2" :setType="2"></allowIn-info>
                </el-tab-pane>
                <el-tab-pane label="车位预约" name="reservatInfor">
                  <preorder-info ref="reservatInfo" v-bind="$attrs" v-on="$listeners" :bolinkId="parkingForm.id"></preorder-info>
                </el-tab-pane>
                <el-tab-pane label="车位占位" name="placeholderInfor">
                  <placeholder-infor ref="seatInfo" v-bind="$attrs" v-on="$listeners" :bolinkId="parkingForm.id" ></placeholder-infor>
                </el-tab-pane>
              </el-tabs>
            </div>
        </el-drawer>
    </div>
</template>
<script>
import placeholderInfor from '@/components/add-parklog/placeholder-infor.vue';
import preorderInfo from '@/components/add-parklog/preorderInfo.vue';
import allowInInfo from '@/components/add-parklog/allowInInfo.vue'

export default {
  components: {
    placeholderInfor, // 占位
    preorderInfo, // 预约
    allowInInfo, // 准入
  },
  props: {
    parkingForm:  {
      type: Object,
      default: {}
    },
    parkingShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      top:'0vh',
      activeName: 'allowInInfo'
    }
  },
  mounted() {
    
  },
  methods: {
    closeDialog(){
      this.activeName = 'allowInInfo';
      this.$refs.parkAccessInfo.clearData();
      this.$refs.reservatInfo.clearData();
      this.$refs.seatInfo.clearData();
      this.$emit("closeDialog",false)
    },

    //获取车位列表
    getParkinglotList(parkData){
      this.$refs.reservatInfo.getParkList(parkData);
      this.$refs.seatInfo.getParkList(parkData);
      this.$refs.parkAccessInfo.getParkList(parkData);
    },

    // 数据编辑处理
    processEcho(){
      setTimeout(() => {
        this.$refs.reservatInfo.getReservationInfor(this.parkinglotList); //车位预约
        this.$refs.seatInfo.getPlaceholderInfor(this.parkinglotList); // 车位占位
        this.$refs.parkAccessInfo.getAllowInInfo(this.parkinglotList); // 车位准入
      }, 100); 
    },
  },
}
</script>
<style  lang="scss" scoped>
.header-title{
  color: #333;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.parkInfoTab{
  
}
/deep/.el-drawer__close-btn{
  position: absolute;
  right: 20px;
  top: 30px;
}

.belong-input{
  display: flex;
  /deep/.el-input{
    width: 130px;
    margin-right: 10px;
  }
  .beName-info{
    width: 180px;
  }
}

/deep/.el-drawer__header{
  margin-bottom: 10px;
  padding: 20px 30px 0 30px;
}
.custom-drawer-info{
  height: 86vh;
  overflow: auto;
}

/deep/.el-select .el-input{
  width: 300px !important;
}
.level-title{
  font-weight: bold;
  margin-bottom: 10px;
}
.tips{
  color: #999;
  font-size: 14px;
  background: #fff;
  line-height: 20px;
  margin: 6px 0;
  span{
    vertical-align: middle;
    margin-right: 6px;
  }
}

.el-button{
  width: 120px;
}
/deep/.el-tabs__nav-wrap::after{
  display: none;
}

.header-tips{
  color: #B8741A;
  margin-left: 20px;
  margin: 0 10px;
}

/deep/.el-input{
  width: 210px;
}

.houseInfo{
  display: flex;
  span{
    display: inline-block;
    width: 30px;
    margin: 0 10px;
  }
  .el-input{
    width: 70px;
  }
}

.switch-info{
  .el-input{
    width: 70px;
  }
}
</style>
