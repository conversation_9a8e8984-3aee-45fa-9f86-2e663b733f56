import axios from 'axios';
// import axios from '@/api/axiosService.js'
const RoleFuncion = [
    {'value_no': 0, 'value_name': '无'},
    {'value_no': 1, 'value_name': '收费功能'},
    {'value_no': 2, 'value_name': '巡查功能'},
    {'value_no': 3, 'value_name': '开卡功能'}

];
var path = process.env.BASE_API;
export default {
    setBaseUrl(url){
        path = url;
        return url;
    },
    /**
     * 某些状态、名称等需要格式化显示的数据
     * @param arr,state
     * @returns {str}
     */
    formatCommonSateFn(arr,state){
        let stateText = '';
        if((typeof arr) != 'object'){
            return stateText;
        }
        arr.forEach(item=>{
            if(item.value_no == state){
                stateText = item.value_name;
                return stateText;
            }
        });
        return stateText;
    },
    timestampFormat:function (d) {
        var date = d;
        date = date.substring(0,19);
        date = date.replace(/-/g,'/');
        var timestamp = new Date(date).getTime();
        return timestamp
    },
    dateformat: function (longtime) {
        if (longtime == '' || longtime == null) {
            return '';
        }
        let nTime = new Date(longtime * 1000);
        let month = nTime.getMonth() + 1;
        let day = nTime.getDate();
        let hour = nTime.getHours();
        let minute = nTime.getMinutes();
        let second = nTime.getSeconds();
        return nTime.getFullYear() + '-' + (month < 10 ? ('0' + month) : month) + '-' + (day < 10 ? ('0' + day) : day) + ' ' + (hour < 10 ? ('0' + hour) : hour) + ':' + (minute < 10 ? ('0' + minute) : minute) + ':' + (second < 10 ? ('0' + second) : second);
    },
    dateformatName: function (longtime) {
      if (longtime == '' || longtime == null) {
          return '';
      }
      let nTime = new Date(longtime * 1000);
      let month = nTime.getMonth() + 1;
      let day = nTime.getDate();
      let hour = nTime.getHours();
      let minute = nTime.getMinutes();
      let second = nTime.getSeconds();
      return nTime.getFullYear() + '年' + (month < 10 ? ('0' + month) : month) + '月' + (day < 10 ? ('0' + day) : day) + '日 ' + (hour < 10 ? ('0' + hour) : hour) + ':' + (minute < 10 ? ('0' + minute) : minute) + ':' + (second < 10 ? ('0' + second) : second);
  },
    typeformat: function (carCostType,is4gPark) {
        if (carCostType === '' || carCostType == null) {
            return '临时车';
        }
        if (carCostType === '1' ){
            return "临时车";
        }else if (carCostType === '2' ){
            return "月卡车";
        }else if (carCostType === '3' && is4gPark ===1 ){
            return "内部车";
        }else if (carCostType === '3' ){
            return "白名单车";
        }else if (carCostType === '4' ){
            return "临时车";
        }else if (carCostType === '5' ){
            return "贵宾车";
        }else if (carCostType === '6' ){
            return "VIP";
        }else if (carCostType === '7' ){
            return "内部车";
        }else if (carCostType === '8' ){
            return "月卡多位多车";
        }else if (carCostType === '9' ){
            return "储值车";
        }
    },

    /* 处理时间差 返回xx小时xx分钟 */
    dateFormatBetween: function (time1,time2){
      let time = time2 - time1
      let str = ''
      let hour = Math.floor(time/(3600))
      console.log(hour)
      if(hour > 0){
        time -= hour * 3600
        str = hour + '小时'
      }
      let min = Math.ceil(time/60)
      if(min == 60){
        min = 0
        str = hour + 1 + '小时'
      }

      if(!hour && !min && time%60 == 0){
        str += '0分钟'
      }else{
        str += min + '分钟'
      }
      return str;
    },
    /* 处理时间差 返回xx小时xx分钟 */
    dateFormatSecond: function (time) {
        let str = ''
        let hour = Math.floor(time / (3600))
        console.log(hour)
        if (hour > 0) {
            time -= hour * 3600
            str = hour + '小时'
        }
        let min = Math.ceil(time / 60)
        if (min == 60) {
            min = 0
            str = hour + 1 + '小时'
        }

        if (!hour && !min && time % 60 == 0) {
            str += '0分钟'
        } else {
            str += min + '分钟'
        }
        return str;
    },
    dateyymm: function(time){
        let year = time.getFullYear() + '';
        let month = time.getMonth() + 1;
        if (month < 10){
            month = '0' + month;
        }
        let day = time.getDate() ;
        if (day < 10){
            day = '0' + day;
        }
        let hour = time.getHours();
        if (hour < 10){
            hour = '0' + hour;
        }
        let minute = time.getMinutes();
        if (minute < 10){
            minute = '0' + minute;
        }
        let second = time.getSeconds();
        if (second < 10){
            second = '0' + second;
        }
        return year + month + day + hour + minute + second;
    },
    //预计回本周期
    returnPeriod: function (costValue,periodInfo) {
        if(costValue == 0){
            return '无数据';
        }else{
            if(periodInfo == '-9999'){
                return '无营收';
            }else if(periodInfo <= 0){
                return '已回本';
            }else{
                return periodInfo;
            }
        }

    },
    costTypeormat: function (carcosttype) {
        var type = '';
        if (carcosttype != null && carcosttype!=undefined && carcosttype !='undefined'){
            if (carcosttype == 1 || carcosttype ==4){
                type = '临停车';
            }else if (carcosttype == 2){
                type = '月卡车';
            } else if (carcosttype == 3){
                type = '内部车';
            }else if(carcosttype == 5){
                type = '贵宾车';
            }else if(carcosttype == 6){
                type = 'VIP';
            }else if(carcosttype == 7){
                type = '白名单车';
            }else if(carcosttype == 9){
                type = '储值车';
            }
        }
        return type;
    },
    dateformatonly: function (longtime) {
        if (longtime == '' || longtime == null) {
            return '';
        }
        longtime = (longtime + '').length < 13 ? longtime * 1000 : longtime
        var nTime = new Date(longtime);
        var month = nTime.getMonth() + 1;
        var day = nTime.getDate();
        return nTime.getFullYear() + '-' + (month < 10 ? ('0' + month) : month) + '-' + (day < 10 ? ('0' + day) : day);
    },
    datetosecond: function (d) {
        return Math.round((d.getTime()) / 1000);
    },
    secondtodate: function (d) {
        return Math.round((d.getTime()) * 1000);
    },
    getordertype: function (order) {
        if (order.substring(0, 1) == 'a') {
            return order.substring(0, 3);
        } else {
            return order.substring(0, 4);
        }
    },
    gww: function (_w) {
        let w = 0;
        if (window.innerWidth) {
            w = window.innerWidth;
        } else {
            w = document.documentElement.offsetWidth || document.body.clientWidth || 0;
        }

        w = w < _w ? _w : w;
        return parseInt(w);
    },
    gwh: function (_h) {
        let h = 0;
        if (window.innerHeight) {
            h = window.innerHeight;
        } else {
            h = document.documentElement.offsetHeight || document.body.clientHeight || 0;
        }

        h = h < _h ? _h : h;
        return parseInt(h);
    },
    clone: function (obj) {
        let o = '', i = '', j = '';
        if (typeof(obj) != 'object' || obj === null) return obj;
        if (obj instanceof (Array)) {
            o = [];
            i = 0;
            j = obj.length;
            for (; i < j; i++) {
                if (typeof(obj[i]) == 'object' && obj[i] != null) {
                    o[i] = arguments.callee(obj[i]);
                }
                else {
                    o[i] = obj[i];
                }
            }
        }
        else {
            o = {};
            for (i in obj) {
                if (typeof(obj[i]) == 'object' && obj[i] != null) {
                    o[i] = arguments.callee(obj[i]);
                }
                else {
                    o[i] = obj[i];
                }
            }
        }

        return o;
    },
    intervalchange: function (vm, val, field) {
        var start = field + 'start';
        var end = field + 'end';

        if (val == 'between') {
            vm.searchShow[start] = true;
            vm.searchShow[end] = true;
        } else if (val == 'null') {
            vm.searchShow[start] = false;
            vm.searchShow[end] = false;
        } else {
            vm.searchShow[start] = true;
            vm.searchShow[end] = false;
        }
    },
    stateformat: function (state) {
        if (state == 0) return '未审核';
        if (state == 1) return '已审核';
        if (state == 2) return '禁用';
        if (state == 3) return '已锁定';
    },
    ynformat: function (state) {
        if (state == 0) return '否';
        if (state == 1) return '是';
    },
    balanceformat: function (balance, digit) {
        if (balance == null) {
            return '-';
        } else {
            return balance.toFixed(digit) + ' 元';
        }
    },
    /* 保留两位小数 */
    numformat2: function(num){
      return Number(num).toFixed(2)
    },
    /* 保留三位小数 */
    numformat3: function(num){
        return num?Number(num).toFixed(3):'0.000'
      },
    /* 最低为0.00 */
    moneyFormat: function(num){
      return num ? Number(num).toFixed(2) : '0.00'
    },
    /* 两数相加,保留两位小数 */
    numaddformat2: function(num1, num2){
      return (Number(num1) + Number(num2)).toFixed(2)
    },
    /* 两数相减,保留两位小数 */
    numsubformat2: function(num1, num2){
      return Number(num1-num2).toFixed(2)
    },
    nameformat: function (row, list, col) {
        // console.log(row)
        for (let x in list) {
            // console.log('for '+x)
            // console.log('value_no '+list[x].value_no)
            // console.log('row '+row[col])
            if (row[col] == null) {
                if (col == 'com_id') {
                    return '(未知车场)';
                } else if (col == 'car_union_id') {
                    return '-';
                } else if (col == 'server_id') {
                    return '(无服务商)';
                } else {
                    return '';
                }
            }
            // console.log(list)
            if ((list[x].value_no == row[col])||(list[x].value_no+'' == row[col])) {
                return list[x].value_name;
            }
        }
    },
    getMoneyData:function(){


    },
    funcformat: function (is_collector, is_inspect, is_opencard) {
        if (is_collector == 1) {
            return RoleFuncion[1].value_name;
        }
        if (is_inspect == 1) {
            return RoleFuncion[2].value_name;
        }
        if (is_opencard == 1) {
            return RoleFuncion[3].value_name;
        }
        return RoleFuncion[0].value_name;
    },
    getWorkSite_id() {
        let param = '?token=' + sessionStorage.getItem('token')
            + this.attachParams('comid');
        return axios.get(path+'/getdata/getWorkSiteId' + param);
    },

    getChannelType() {
        let param = '?token=' + sessionStorage.getItem('token')
            + this.attachParams('comid');
        return axios.get(path+'/getdata/getChannelType' + param);
    },
    getChannelTypeByGroupid(groupid) {
        let param = '?groupid=' + groupid
        return axios.get(path+'/getdata/getgroupchannels' + param);
    },
    getChannelTypeByComid(comid) {
        let param = '?comid=' + comid
        return axios.get(path+'/getdata/getChannelType' + param);
    },
    getSuperimposed() {
        if(sessionStorage.getItem('comid')){
          let param = '?token=' + sessionStorage.getItem('token')
              + this.attachParams('comid');
          return axios.get(path+'/getdata/getSuperimposed' + param);
        }
        else{
          return {};
        }
    },
    getTicketSkipPay() {
        if(sessionStorage.getItem('comid')){
            let param = '?token=' + sessionStorage.getItem('token')
                + this.attachParams('comid');
            return axios.get(path+'/getdata/getTicketSkipPay' + param);
        }
        else{
            return {};
        }
    },
    getMonitorName() {
        let param = '?token=' + sessionStorage.getItem('token')
            + this.attachParams('comid');
        return axios.get(path+'/getdata/getMonitorName' + param);

    },
    // getUnionList: function (params) {
    //     let param = '?token=' + sessionStorage.getItem('token');
    //     if (typeof(params) != 'undefined') {
    //         param += params;
    //     }
    //     return axios.get('/getdata/unionlist' + param);
    // },
    getServerList: function (params) {
        let param = '?token=' + sessionStorage.getItem('token');
        if (typeof(params) != 'undefined') {
            param += params;
        }
        return axios.get(path+'/getdata/serverlist' + param);
    },
    getParkList: function (params) {
        let param = '?token=' + sessionStorage.getItem('token');
        if (typeof(params) != 'undefined') {
            param += params;
        }
        return axios.get(path+'/getdata/parklist' + param);
    },
    getParkLists: function () {
        let param = '?token=' + sessionStorage.getItem('token');
        return axios.get(path+'/getdata/parklists' + param);
    },
    getBaPayUnionList: function () {
        let param = '?token=' + sessionStorage.getItem('token');
        return axios.get(path+'/getdata/payunionlist' + param);
    },
    getBankInfo: function () {
        let param = '?token=' + sessionStorage.getItem('token');
        return axios.get(path+'/getdata/getbankinfo' + param);
    },
    getCentralPaymentList: function (params) {
        let param = '?token=' + sessionStorage.getItem('token');
        if (typeof(params) != 'undefined') {
            param += params;
        }
        return axios.get(path+'/getdata/getcentralpaymentlist' + param);
    },
    getEventLists() {
        let param = '?token=' + sessionStorage.getItem('token');
        return axios.get(path+'/getdata/geteventlist' + param);
    },
    /**
     * 集团接口
     * @returns {AxiosPromise}
     */
    getAllParks() {
        // 获得集团和城市下的所有车场
        let param = '?token=' + sessionStorage.getItem('token')
            + this.commonParams();
        return axios.get(path+'/getdata/cityparks' + param);
    },
    getAllShops() {
        // 获得集团和城市下的所有车场
        let param = '?token=' + sessionStorage.getItem('token')
            + this.commonParams();
        return axios.get(path+'/getdata/getshops' + param);
    },
    getAShops() {
        // 获得集团和城市下的所有车场
        let param = '?token=' + sessionStorage.getItem('token')
            // + "&isNewType=0"
            + this.commonParams();
        return axios.get(path+'/getdata/getshops' + param);
    },
    getBShops() {
        // 获得集团和城市下的所有车场
        let param = '?token=' + sessionStorage.getItem('token')
            + "&isNewType=1"
            + this.commonParams();
        return axios.get(path+'/getdata/getshops' + param);
    },
    getNewShops() {
        // 获得集团和城市下的所有车场
        let param = '?token=' + sessionStorage.getItem('token')
            + "&isNewType=2"
            + this.commonParams();
        return axios.get(path+'/getdata/getshops' + param);
    },
    getAllCollector() {
        // 获得集团和城市下面所有的收费员
        let param = '?token=' + sessionStorage.getItem('token')
            + this.commonParams();
        return axios.get(path+'/getdata/allcollectors' + param);
    },
    getAllPName() {
        let param = '?token=' + sessionStorage.getItem('token')
            + this.commonParams();
            path = this.getItemDomain()
        return axios.get(path+'/getdata/getAllPackage' + param);
    },
    getItemDomain() {
        if(sessionStorage.getItem('businessDomain')){
            path = sessionStorage.getItem('businessDomain')
        }
        return path;
    },
    getServerDomain() {
        var serverPath = 'https:' + process.env.SERVER_API;
        if(sessionStorage.getItem('serverDomain')){
            serverPath = sessionStorage.getItem('serverDomain')
        }
        return serverPath;
    },
    
    getSocketDomain() {
        var socketPath = process.env.SOCKET_API;
        if(sessionStorage.getItem('businessWebSocket')){
            socketPath = sessionStorage.getItem('businessWebSocket')
        }
        return socketPath;
    },
    getAllEmployeeRole() {
        //获取员工角色
        let param = '?token=' + sessionStorage.getItem('token')
            + this.commonParams();
        return axios.get(path+'/groupmember/getrole' + param);
    },
    getCityEmployeeRole() {
        //获取员工角色
        let param = '?token=' + sessionStorage.getItem('token')
            + this.commonParams();
        return axios.get(path+'/citymember/getrole' + param);
    },
    /**
     * 车场接口
     * @returns {AxiosPromise}
     */
    getCollector() {
        //获取收费员
        let param = '?token=' + sessionStorage.getItem('token')
            + this.commonParams();
        return axios.get(path+'/getdata/getalluser' + param);
    },
    getgroupCollector(){
        //集团获取收费员
        let param = '?token=' + sessionStorage.getItem('token')
            + this.commonParams();
        return axios.get(path+'/getdata/getallgroupuser' + param);
    },
    getLiftReason() {
        //获取收费员
        let param = '?token=' + sessionStorage.getItem('token');
        return axios.get(path+'/liftRod/getLiftReason' + param);
    },
    getEmployeeRole() {
        //获取员工角色
        //alert('111')
        let param = '?token=' + sessionStorage.getItem('token')
            + this.commonParams()+ this.attachParams('loginroleid');
        return axios.get(path+'/member/getrole' + param);
    },
    getShopRole() {
        //获取员工角色

        let param = '?token=' + sessionStorage.getItem('token')
            + this.commonParams() + this.attachParams('shopid') + this.attachParams('loginroleid');
        return axios.get(path+'/shopmember/getrole' + param);
    },

    getShopUsers(){
        let param = '?token=' + sessionStorage.getItem('token')
             + this.attachParams('shopid') ;
        return axios.get(this.getItemDomain() +'/shopmember/getshopusers' + param);
    },
    getPName() {
        //获得月卡套餐
        let param = '?token=' + sessionStorage.getItem('token')
            + this.commonParams();
        return axios.get(this.getItemDomain() +'/getdata/getpname' + param);
    },
    getPNameByCar(carId){
        //获得月卡套餐
        let param = '?token=' + sessionStorage.getItem('token')
            +'&carId='+carId
            + this.commonParams();
        return axios.get(this.getItemDomain() + '/getdata/getpnamebycar' + param);
    },
    getUnionList() {
        //获取城市下面所有的集团
        let param = '?token=' + sessionStorage.getItem('token')
            + this.commonParams();
        return axios.get(path+'/getdata/getallunion' + param);
    },
    getCarType() {
        //获得车型类型
        let param = '?token=' + sessionStorage.getItem('token')
            + this.commonParams();
        return axios.get(this.getItemDomain() +'/getdata/getcartype' + param);
    },
    getCardType(params) {
      // 获取车场储值卡类型
      return axios.post(`${this.getItemDomain()}/getdata/getPrepareCardTypes`,params);
    },
    editCarNum(carnumber, id,carNumber) {
        //更改车牌号
        let param = '?token=' + sessionStorage.getItem('token')
            + this.commonParams()
            + '&id=' + id
            + '&carnumber=' + encodeURI(encodeURI(carnumber))
            + '&old_carnumber=' + encodeURI(encodeURI(carNumber));
        return axios.get(path+'/vip/editCarNum' + param);
    },
    getProdSum(p_name, month, b_time,e_time) {
        //通过续费月数和月卡套餐获取金额
        let param = '?token=' + sessionStorage.getItem('token')
            + this.commonParams()
            + '&p_name=' + p_name
            + '&months=' + month
            + '&b_time=' + b_time
            + '&e_time=' + e_time;
        return axios.get(this.getItemDomain() +'/getdata/getprodsum' + param);
    },
    getEndTime(begin, month,pid,comid) {
        //通过续费月数和月卡套餐获取金额
        let param = '?token=' + sessionStorage.getItem('token')
            + '&beginTime=' + begin
            + '&pid=' + pid
            + '&months=' + month
            + '&comid=' + comid + this.commonParams();
        return axios.get( this.getItemDomain() +'/getdata/getEndTime' + param);
    },
    reNewProduct(p_name, month, name, b_time, id, remark, act_total,total, nickname,card_id,e_time) {
        // reNewProduct(this.pnameno,this.refillcount,this.currentRow.name,this.Btime,this.currentRow.pid,this.currentRow.remark,this.RefillTotalact,roleid==30?'车场':roleid){
        //月卡续费
        let param = '?token=' + sessionStorage.getItem('token')
            + this.commonParams()
            + '&p_name=' + p_name
            + '&months=' + month
            + '&name=' + name
            + '&b_time=' + b_time
            + '&id=' + id
            + '&remark=' + remark
            + '&act_total=' + act_total
            + '&total=' + total
            + '&card_id=' + card_id
            + '&nickname=' + nickname
            + '&e_time=' + e_time;
        path = this.getItemDomain()
        return axios.get(path+'/vip/renewproduct' + param);
    },


    /**
     *
     * @returns {AxiosPromise<any>}
     */


    getSmsInfo() {
        //短信续费
        let param = '?token=' + sessionStorage.getItem('token');
        return axios.get(path+'/getdata/getmessageprice' + param);
    },
  /**
   * @desc 获取车辆种类： 月卡车、储值车、内部车等
   * @return {AxiosPromise<any>}
   */
  getCarcostTypeByComid() {
      return axios.post(`${path}/getdata/getCarcostTypeByComid?comid=${sessionStorage.getItem('comid')}`);
    },
    getMonthlyCardList() {
        return axios.post(`${path}/monthGroup/getAllGroup?comid=${sessionStorage.getItem('comid')}`);
    },
    getLength: function (obj) {
        var count = 0;
        for (var key in obj) {
            count++;
        }
        return count;
    },
    paytypeformat: function (paytype) {
        if (paytype == 0) return '主扫';
        if (paytype == 1) return '被扫';
        if (paytype == 2) return '免密';
        if (paytype == 3) return '现金';
    },
    paychannelformat: function (paychennel) {
        if (paychennel == 0) return '微信';
        if (paychennel == 1) return '支付宝';
        if (paychennel == 2) return '余额';
        if (paychennel == 3) return '银联';
    },
    pageShow: function (user, pageName) {
        for (var item of user.authlist) {
            if (pageName == item.auth_id) {
                // console.log(item.nname)
                return true;
            }
        }

        return false;
    },
    showSettlement:function (sub_auth) {
    //    0元结算
        if (sub_auth.indexOf('手动结算') > -1) {
            return true;
        }
        return false;
    },
    showSubSearch: function (sub_auth) {
        //显示高级查询
        if (sub_auth.indexOf('查看') > -1) {
            return true;
        }
        return false;
    },
    showSubExportCount: function (sub_auth) {
        //显示导出
        if (sub_auth.indexOf('出入次数统计') > -1) {
            return true;
        }
        return false;
    },
    showSubExport: function (sub_auth) {
      //显示导出
      if (sub_auth.indexOf('导出') > -1) {
          return true;
      }
      return false;
    },
    showSubImport: function (sub_auth) {
        //显示导入
        if (sub_auth.indexOf('导入') > -1) {
            return true;
        }
        return false;
    },
    showQrBtn:function(sub_auth){
        if (sub_auth.indexOf('二维码') > -1) {
            return true;
        }
        return false;
    },
    showChargeGun:function(sub_auth){
      if (sub_auth.indexOf('枪信息') > -1) {
          return true;
      }
      return false;
  },
    show4GBtn:function(sub_auth){
        if (sub_auth.indexOf('纯云服务') > -1) {
            return true;
        }
        return false;
    },
    downLoadBtn:function(sub_auth){
        if (sub_auth.indexOf('下载') > -1) {
            return true;
        }
        return false;
    },
    validTimeBtn:function(sub_auth){
        if (sub_auth.indexOf('有效期') > -1) {
            return true;
        }
        return false;
    },
    channelBtn:function(sub_auth){
        if (sub_auth.indexOf('通道管理') > -1) {
            return true;
        }
        return false;
    },
    showControl:function(sub_auth){
        if (sub_auth.indexOf('管理') > -1) {
            return true;
        }
        return false;

    },
    showSubAdd: function (sub_auth) {
        //显示新增
        if (sub_auth.indexOf('注册') > -1) {
            return true;
        }
        if (sub_auth.indexOf('添加') > -1) {
            return true;
        }
        if (sub_auth.indexOf('新增') > -1) {
          return true;
        }
        if (sub_auth.indexOf('发卡') > -1) {
            return true;
        }
        return false;
    },
    showCardSetting(sub_auth){
         if (sub_auth.indexOf('发卡配置') > -1) {
            return true;
        }
        return false;
    },
    showChargeActivity(sub_auth){
        if (sub_auth.indexOf('充值活动') > -1) {
           return true;
       }
       return false;
    },
    showChargeSetting(sub_auth){
        if (sub_auth.indexOf('充值配置') > -1) {
           return true;
       }
       return false;
    },
    showClass: function (sub_auth) {
        //显示新增
        if (sub_auth.indexOf('分类管理') > -1) {
            return true;
        }
        return false;
    },
    showOneCodeGO:function(sub_auth) {
        if (sub_auth.indexOf('车主小程序') > -1) {
            return true;
        }
        return false;
    },
    showSetSMS: function (sub_auth) {
        //显示新增
        if (sub_auth.indexOf('短信设置') > -1) {
            return true;
        }
        return false;
    },
    showPrepayCardSetting: function (sub_auth) {
        if (sub_auth.indexOf('公众号注册') > -1) {
            return true;
        }
        return false;
    },
    showWxUnbind: function (sub_auth) {
        //显示微信解绑
        if (sub_auth.indexOf('微信解绑') > -1) {
            return true;
        }
        return false;
    },
    showSubBatch: function (sub_auth) {
        if (sub_auth.indexOf('批量延期') > -1) {
            return true;
        }
        return false;
    },
    showSubRecord: function (sub_auth) {
        if (sub_auth.indexOf('识别记录') > -1) {
            return true;
        }
        return false;
    },
    showSubMessage: function (sub_auth) {
        if (sub_auth.indexOf('下发记录') > -1) {
            return true;
        }
        return false;
    },
    showSubRemote: function (sub_auth) {
        if (sub_auth.indexOf('远程') > -1) {
            return true;
        }
        return false;
    },
    showSubSend: function (sub_auth) {
        if (sub_auth.indexOf('下发') > -1) {
            return true;
        }
        return false;
    },
    showBatchSend: function (sub_auth) {
        if (sub_auth.indexOf('批量下发') > -1) {
            return true;
        }
        return false;
    },
    showSendWhiteMonth: function (sub_auth) {
        if (sub_auth.indexOf('下发到设备') > -1) {
            return true;
        }
        return false;
    },
    showBind: function (sub_auth) {
        if (sub_auth.indexOf('绑定设备') > -1) {
            return true;
        }
        return false;
    },
    showProfitEdit: function (sub_auth) {
        if (sub_auth.indexOf('充电分账设置') > -1) {
            return true;
        }
        if (sub_auth.indexOf('分账设置') > -1) {
            return true;
        }
        return false;
    },
    showEquipBack: function (sub_auth) {
      if (sub_auth.indexOf('设备回退') > -1) {
          return true;
      }
      return false;
    },
    showReduceEdit: function (sub_auth) {
      if (sub_auth.indexOf('停车费减免设置') > -1) {
          return true;
      }
      return false;
    },
    showSubAddSlave: function (sub_auth) {
        if (sub_auth.indexOf('添加辅助') > -1) {
            return true;
        }
        return false;
    },
    showSubDown: function (sub_auth) {
        if (sub_auth.indexOf('下架') > -1 ) {
            return true;
        }
        return false;
    },
    showSubProductType: function (sub_auth) {
        if (sub_auth.indexOf('产品类型') > -1 ) {
            return true;
        }
        return false;
    },
    showConfiguration: function (sub_auth) {
        if (sub_auth.indexOf('配置') > -1 ) {
            return true;
        }
        return false;
    },
    showSubEdit: function (sub_auth) {
        if (sub_auth.indexOf('编辑') > -1 || sub_auth.indexOf('漂白') > -1) {
            return true;
        }
        return false;
    },
    showSubPurchasePrice: function (sub_auth) {
        if (sub_auth.indexOf('采购价') > -1) {
            return true;
        }
        return false;
    },
    showSubSalesPrice: function (sub_auth) {
        if (sub_auth.indexOf('销售价') > -1) {
            return true;
        }
        return false;
    },
    showCaptivePrice: function (sub_auth) {
        if (sub_auth.indexOf('专属价') > -1) {
            return true;
        }
        return false;
    },
    showCarType: function (sub_auth) {
        if (sub_auth.indexOf('车辆类型') > -1 ) {
            return true;
        }
        return false;
    },
    showRepairType: function (sub_auth) {
        if (sub_auth.indexOf('报修类型') > -1) {
            return true;
        }
        return false;
    },
    showHistory: function (sub_auth) {
        if (sub_auth.indexOf('历史计费标准') > -1) {
            return true;
        }
        return false;
    },
    showSubWatch: function (sub_auth) {
        if (sub_auth.indexOf('查看') > -1 ) {
            return true;
        }
        return false;
    },
    showPublic: function (sub_auth) {
        if (sub_auth.indexOf('公众号设置') > -1 ) {
            return true;
        }
        return false;
    },
    payAndOpen:function(sub_auth){
        if (sub_auth.indexOf('支付开通') > -1 ) {
            return true;
        }
        return false;
    },
    showDepartmentManagement:function(sub_auth){
        if (sub_auth.indexOf('部门管理') > -1 ) {
            return true;
        }
        return false;
    },
    showDeviceSet:function(sub_auth){
        if (sub_auth.indexOf('访客机设置') > -1 ) {
            return true;
        }
        return false;
    },
    showSubCamera:function(sub_auth){
        if (sub_auth.indexOf('相机记录') > -1 ) {
            return true;
        }
        return false;
    },
    showClearing:function(sub_auth){
        if (sub_auth.indexOf('手动结算') > -1 ) {
            return true;
        }
        return false;
    },
    showAbnormalOrder:function(sub_auth){
        if (sub_auth.indexOf('异常订单') > -1 ) {
            return true;
        }
        return false;
    },
    showSubDel: function (sub_auth) {
        if (sub_auth.indexOf('删除') > -1||sub_auth.indexOf('销户')>-1 ||sub_auth.indexOf('设备出库')>-1 ||sub_auth.indexOf('移除')>-1) {
            return true;
        }
        return false;
    },
    showBatchDelete: function (sub_auth) {
        if (sub_auth.indexOf('批量移除') > -1 || sub_auth.indexOf('批量出库') > -1 || sub_auth.indexOf('批量删除') > -1) {
            return true;
        }
        return false;
    },
    showBatchDown: function (sub_auth) {
        if (sub_auth.indexOf('批量下发') > -1) {
            return true;
        }
        return false;
    },
    showCancelDown: function (sub_auth) {
        if (sub_auth.indexOf('一键取消') > -1) {
            return true;
        }
        return false;
    },

    showRefund: function (sub_auth) {
        if (sub_auth.indexOf('退款') > -1) {
            return true;
        }
        return false;
    },
    showSubUpdate: function (sub_auth) {
        if (sub_auth.indexOf('修改') > -1) {
            return true;
        }
        return false;
    },
    showSubConsumptionRules: function (sub_auth) {
        if (sub_auth.indexOf('消费规则') > -1) {
            return true;
        }
        return false;
    },

    showSubReFill: function (sub_auth) {
        if (sub_auth.indexOf('续费') > -1) {
            return true;
        }
        return false;
    },
    showAudit: function (sub_auth) {
        if (sub_auth.indexOf('月卡审核') > -1) {
            return true;
        }
        return false;
    },
    showTicket: function(sub_auth){
        if (sub_auth.indexOf('查看券码') > -1) {
            return true;
        }
        return false;
    },
    showTicketSet: function (sub_auth) {
        if (sub_auth.indexOf('优惠券设置') > -1) {
            return true;
        }
        return false;
    },
    showSubPermission: function (sub_auth) {
        if (sub_auth.indexOf('权限') > -1 || sub_auth.indexOf('修改权限') > -1  || sub_auth.indexOf('角色权限') > -1) {
            return true;
        }
        return false;
    },
    showSetting: function (sub_auth) {
        if (sub_auth.indexOf('设置') > -1) {
            return true;
        }
        return false;
    },
    showStaffSetting: function (sub_auth) {
        if (sub_auth.indexOf('员工设置') > -1) {
            return true;
        }
        return false;
    },
    showParkSetting: function (sub_auth) {
        if (sub_auth.indexOf('车场设置') > -1) {
            return true;
        }
        return false;
    },
    showPay: function (sub_auth) {
        if (sub_auth.indexOf('续费') > -1) {
            return true;
        }
        return false;
    },
    showSubSetFee: function (sub_auth) {
        if (sub_auth.indexOf('收费设置') > -1) {
            return true;
        }
        return false;
    },
    showSubReset: function (sub_auth) {
        if (sub_auth.indexOf('修改密码') > -1) {
            return true;
        }
        return false;
    },
    showResources:function(sub_auth) {
        if (sub_auth.indexOf('禁用') > -1) {
            return true;
        }
        return false;
    },
    //face
    distribution:function (sub_auth) {
        if (sub_auth.indexOf('分配') > -1) {
            return true;
        }
        return false;
    },
    rechargeIndexOf:function (sub_auth) {
        if (sub_auth&&sub_auth.split(',').includes('充值')) {
            return true;
        }
        return false;
    },
    showFaceShenhe:function(sub_auth) {
        if (sub_auth.indexOf('审核') > -1) {
            return true;
        }
        return false;
    },
    showRemotelySetting:function(sub_auth) {
        if (sub_auth.indexOf('远程开门') > -1) {
            return true;
        }
        return false;
    },
    showFaceDownLoad:function(sub_auth) {
        if (sub_auth.indexOf('下发') > -1) {
            return true;
        }
        return false;
    },
    showFaceSubAdd: function (sub_auth) {
        //显示导入
        if (sub_auth.indexOf('新增') > -1) {
            return true;
        }
        return false;
    },
    showDecryption: function (sub_auth) {
        if (sub_auth.indexOf('加解密') > -1) {
            return true;
        }
        return false;
    },
    showSynchronizationStatus: function (sub_auth) {
        if (sub_auth.indexOf('同步状态') > -1) {
            return true;
        }
        return false;
    },
    showFaceSubHouseAdmin: function (sub_auth) {
        //房屋管理员
        if (sub_auth.indexOf('房屋管理员') > -1) {
            return true;
        }
        return false;
    },
    showFaceSubBuildingAdmin: function (sub_auth) {
        //楼栋管理员
        if (sub_auth.indexOf('楼栋管理员') > -1) {
            return true;
        }
        return false;
    },
    showFloorManage: function (sub_auth) {
        //楼栋管理员
        if (sub_auth.indexOf('楼层管理') > -1) {
            return true;
        }
        return false;
    },
    showCharge: function (sub_auth) {
      //显示导入
      if (sub_auth&&sub_auth.split(',').includes('充值')) {
          return true;
      }
      return false;
    },
    showFaceSubControl: function (sub_auth) {
        if (sub_auth.indexOf('控制') > -1) {
            return true;
        }
        return false;
    },
    showPersonnelSync: function (sub_auth) {
        if (sub_auth.indexOf('同步记录') > -1) {
            return true;
        }
        return false;
    },
    showFaceDownLoadToDevice: function (sub_auth) {
        if (sub_auth.indexOf('一键下发设备') > -1 || sub_auth.indexOf('下载到设备') > -1) {
            return true;
        }
        return false;
    },
    showFaceOpenDoor: function (sub_auth) {
        if (sub_auth.indexOf('远程开门') > -1) {
            return true;
        }
        return false;
    },
    showFaceTakePicture: function (sub_auth) {
        if (sub_auth.indexOf('远程拍照') > -1) {
            return true;
        }
        return false;
    },
    showbatchAdd:function (sub_auth) {
        if (sub_auth.indexOf('批量生成') > -1) {
            return true;
        }
        return false;
    },
    showBatchSetting: function (sub_auth) {
        if (sub_auth.indexOf('批量设置') > -1) {
            return true;
        }
        return false;
    },
    capacitySetting:function (sub_auth) {
        if (sub_auth.indexOf('房屋容纳人数') > -1) {
            return true;
        }
        return false;
    },
    showMiniProgramSetting: function (sub_auth) {
        if (sub_auth.indexOf('小程序录入设置') > -1) {
            return true;
        }
        return false;
    },
    showFaceEnterSetting: function (sub_auth) {
        if (sub_auth.indexOf('人脸录入设置') > -1) {
            return true;
        }
        return false;
    },
    showInoutSetting: function (sub_auth) {
        if (sub_auth.indexOf('进出推送') > -1) {
            return true;
        }
        return false;
    },
    showCallereason: function (sub_auth) {
        if (sub_auth.indexOf('访客设置') > -1) {
            return true;
        }
        return false;
    },
    showCallerSetting: function (sub_auth) {
        if (sub_auth.indexOf('访客认证') > -1) {
            return true;
        }
        return false;
    },
    showCallerReasonAdd: function (sub_auth) {
        if (sub_auth.indexOf('访客设置新增') > -1) {
            return true;
        }
        return false;
    },
    showCallerReasonEdit: function (sub_auth) {
        if (sub_auth.indexOf('访客设置编辑') > -1) {
            return true;
        }
        return false;
    },
    showCallerReasonDelete: function (sub_auth) {
        if (sub_auth.indexOf('访客设置删除') > -1) {
            return true;
        }
        return false;
    },
    showFaceAddGroup: function (sub_auth) {
        if (sub_auth.indexOf('添加组') > -1) {
            return true;
        }
        return false;
    },
    showFaceService: function (sub_auth) {
        if (sub_auth.indexOf('人脸门禁') > -1) {
            return true;
        }
        return false;
    },
    showDeviceSetting: function (sub_auth) {
        if (sub_auth.indexOf('设备参数配置') > -1) {
            return true;
        }
        return false;
    },
    showDeviceMessage: function (sub_auth) {
        if (sub_auth.indexOf('设备信息') > -1) {
            return true;
        }
        return false;
    },
    showEndCharging: function (sub_auth) {
        if (sub_auth.indexOf('结束充电') > -1) {
            return true;
        }
        if (sub_auth.indexOf('结束') > -1) {
            return true;
        }
        return false;
    },
    //----------------face end-------------------
    showStartUsing:function(sub_auth) {
        if (sub_auth.indexOf('启用') > -1) {
            return true;
        }
        return false;
    },
    showCommit:function(sub_auth) {
        if (sub_auth.indexOf('提交') > -1) {
            return true;
        }
        return false;
    },
    showChargeTest:function(sub_auth) {
        if (sub_auth.indexOf('计费测试') > -1) {
            return true;
        }
        return false;
    },
    showBatchOperation:function(sub_auth) {
      if (sub_auth.indexOf('批量操作') > -1) {
          return true;
      }
      return false;
    },
    showViewPeople:function(sub_auth) {
      if (sub_auth.indexOf('查看人员') > -1) {
          return true;
      }
      return false;
    },
    showRemoteControl:function(sub_auth) {
      if (sub_auth.indexOf('远程控制') > -1) {
          return true;
      }
      return false;
    },
    showTimePeriodSetting:function(sub_auth) {
      if (sub_auth.indexOf('时间段设置') > -1) {
          return true;
      }
      return false;
    },
    showFirmwareUpdate:function(sub_auth) {
      if (sub_auth.indexOf('固件升级') > -1) {
          return true;
      }
      return false;
    },
    getBaseParamsUniteNew(){
        let base = {},
            unite = {},
            cityid = sessionStorage.getItem("cityid"),
            serverId = sessionStorage.getItem("serverid"),
            comid = sessionStorage.getItem("comid"),
            usernamne = sessionStorage.getItem("nickname1"),
            loginuin = sessionStorage.getItem("loginuin"),
            atuhGroup = ""
        if (comid != '0' && comid != 'undefined') {
            atuhGroup = "AUTH_ID"
            base.comid = comid
            unite.mainId = comid
            unite.type = 3
        } else if (serverId != 'undefined') {
            atuhGroup = "AUTH_ID_SERVER"
            base.serverId = serverId
            unite.mainId = serverId
            unite.type = 2
        } else if (cityid != 'undefined') {
            atuhGroup = ""
            base.cityid = cityid
            unite.mainId = cityid
            unite.type = 1
        } else {
            atuhGroup = ""
            base.backstageId = loginuin
            unite.mainId = loginuin
            unite.type = 4
        }
        let baseParams = {
            base,
            unite,
            atuhGroup,
			userName: usernamne,
			userId: loginuin,
            fullName: `${usernamne}(${loginuin})`
        }
        return baseParams
    },
    getShopMemberList(obj) {
        return axios.get(path+'/shopmember/quickquery' + '?token=' + sessionStorage.getItem('token') + '&shop_id=' + obj.shop_id + '&page=' + obj.page);
    },
    saveShopMember(obj) {
        return axios.get(path+'/shopmember/create' + '?token=' + sessionStorage.getItem('token')
            + '&shop_id=' + obj.shop_id
            + '&nickname=' + encodeURI(encodeURI(obj.nickname)) + '&phone=' + obj.phone
            + '&mobile=' + obj.mobile + '&auth_flag=' + obj.auth_flag
            + '&userId=' + obj.userId + this.commonParams());
    },
    addMoney(obj) {
        return axios.get(path+'/shop/addmoney' + '?token=' + sessionStorage.getItem('token')
            + '&shop_id=' + obj.shop_id + '&addmoney=' + obj.addmoney
            + '&operator=' + obj.operator + '&parkid=' + obj.parkid
            + '&ticket_time=' + obj.ticket_time + '&ticket_money=' + obj.ticket_money
            + '&ticketfree_limit=' + obj.ticketfree_limit+ this.commonParams());
    },
    editPass(obj) {
        return axios.get(path+'/shopmember/editpass' + '?token=' + sessionStorage.getItem('token')
            + '&newpass=' + obj.newpass + '&confirmpass=' + obj.confirmpass
            + '&id=' + obj.id+ this.commonParams());
    },
    deleteShopMember(id) {
        return axios.get(path+'/shopmember/delete' + '?token=' + sessionStorage.getItem('token') + '&id=' + id+ this.commonParams());
    },
    generateForm(sform) {
        //用来构建相同的参数-表单中添加这几个属性
        sform.token = this.attachParams('token',1);
        sform.oid = sform.oid || this.attachParams('oid', 1);
        sform.comid = sform.comid || this.attachParams('comid', 1);
        sform.groupid =sform.groupid ||this.attachParams('groupid', 1);
        sform.cityid =sform.cityid || this.attachParams('cityid', 1);
        sform.serverid = sform.serverid || this.attachParams('serverid', 1);
        sform.eoms_id = sform.eoms_id || this.attachParams('eomsid', 1);
        sform.unionid = this.attachParams('unionid', 1);
        sform.channelid = this.attachParams('channelid', 1);
        sform.loginuin = this.attachParams('loginuin', 1);
        sform.ishdorder = this.attachParams('ishdorder', 1);
        sform.roleid = this.attachParams('loginroleid', 1);
        sform.shopid = sform.shopid || this.attachParams('shopid', 1);
        sform.nickname1 = this.attachParams('nickname1', 1);
        return sform;
    },

    commonParams() {
        //返回通用的一些参数
        return this.attachParams('comid')
            + this.attachParams('groupid')
            + this.attachParams('cityid')
            + this.attachParams('loginuin')
            + this.attachParams('supperadmin')
            + this.attachParams('nickname1');
    },
    attachParams(key,type) {
        //判断是否是undifined，如果是，则不添加该参数
        let params = '';
        try{
          let p = sessionStorage.getItem(key);
          //alert(p);
          //   alert(p);
           if(key=='nickname1'&&type!==1) {
               p=encodeURI(encodeURI(p));
           }
          if (p !== undefined && p !== 'undefined') {
              if(type!==1){
                  //如果不是1，则说明是默认的拼接参数；否则是获得key对应的值
                  params += '&' + key + '=';
              }
              params += p;
          }
        }
        catch(e){
            console.log(e);
        }

        return params;
    },
    formatNumber(num) {
        //数字格式化成两位 xx
        if (num > 9)
            return num;
        else
            return '0' + num;
    },
    currentDate() {
        //返回当前日期年月日 2018-03-20
        let start = new Date();
        return start.getFullYear() + '-' + this.formatNumber(start.getMonth() + 1) + '-' + this.formatNumber(start.getDate());
    },
    lastWeek() {
        //返回上一周(前七天)的日期年月日 2018-03-14
        let today = new Date();
        let timestamp = today.getTime() + 1000*60*60*24*(-7);
        today.setTime(timestamp);
        return today.getFullYear() + '-' + this.formatNumber(today.getMonth() + 1) + '-' + this.formatNumber(today.getDate());
    },
    nextDate(date) {
        //返回当前日期下一天年月日 2018-03-20
        let start = new Date(date*1000);
        return start.getFullYear() + '-' + this.formatNumber(start.getMonth() + 1) + '-' + this.formatNumber(start.getDate() + 1);
    },
    // yesterdayDate() {
    //     //返回昨天日期年月日 2018-03-20
    //     let start = new Date();
    //     return start.getFullYear() + '-' + this.formatNumber(start.getMonth() + 1) + '-' + this.formatNumber(start.getDate()-1);
    // },
  /**
   * 昨天的日期
   * @param splicing 拼接符
   * @return {*}
   */
    yesterdayDate(splicing) {
      splicing = splicing !== undefined?splicing:'-'
      //返回昨天日期年月日 2018-03-20
      let start = new Date();
      start = new Date(start.setDate(start.getDate()-1));
      return start.getFullYear() + splicing + this.formatNumber(start.getMonth() + 1) + splicing + this.formatNumber(start.getDate());
    },
    getFirstDayOfWeek() {
        //返回当前日期年月日 2018-03-20
        let start = new Date();
        var weekday = start.getDay()||7;
        start.setDate(start.getDate()-weekday+1);
        return start.getFullYear() + '-' + this.formatNumber(start.getMonth() + 1) + '-' + this.formatNumber(start.getDate());
    },
    currentMonth() {
        //返回当前年月 2018-03
        let start = new Date();
        return start.getFullYear() + '-' + this.formatNumber(start.getMonth() + 1);
    },
    yearStart() {
        //返回当前年月 2018-03
        let start = new Date();
        return start.getFullYear() + '-01' ;
    },
    currentFormatDate() {
        //返回时间区间 2018-03-20 00:00:00至 2018-03-20 23:59:59
        return this.currentDate() + ' 00:00:00至' + this.currentDate() + ' 23:59:59';
    },
    currentDateArray(delay){
        let threeDaysAgo = new Date(new Date().getTime()-(delay-1)*24*60*60*1000);
        let tda = threeDaysAgo.getFullYear() + '-' + this.formatNumber(threeDaysAgo.getMonth() + 1) + '-' + this.formatNumber(threeDaysAgo.getDate());
        return [tda+' 00:00:00',this.currentDate()+' 23:59:59'];
    },
    currentDatetimeArray(delay){
        let threeDaysAgo = new Date(new Date().getTime()-(delay-1)*24*60*60*1000);
        let tda = threeDaysAgo.getFullYear() + '-' + this.formatNumber(threeDaysAgo.getMonth() + 1) + '-' + this.formatNumber(threeDaysAgo.getDate());
        return [tda,this.currentDate()];
    },
    timestampToTime(timestamp){
        var date,Y,M,D,h,m,s;
        var date = new Date(timestamp*1000 );//时间戳为10位需*1000，时间戳为13位的话不需乘1000
        Y = date.getFullYear() + '-';
        M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
        D = date.getDate() + ' ';
        h = date.getHours() + ':';
        m = date.getMinutes() + ':';
        s = date.getSeconds();
        return Y+M+D+h+m+s;
    },
    timestampToTime2(timestamp) {
        if(timestamp){
           var date = new Date(timestamp * 1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
           var Y = date.getFullYear() + '-';
           var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1):date.getMonth()+1) + '-';
           var D = (date.getDate()< 10 ? '0'+date.getDate():date.getDate())+ ' ';
           var h = (date.getHours() < 10 ? '0'+date.getHours():date.getHours())+ ':';
           var m = (date.getMinutes() < 10 ? '0'+date.getMinutes():date.getMinutes()) + ':';
           var s = date.getSeconds() < 10 ? '0'+date.getSeconds():date.getSeconds();
           return Y+M+D+h+m+s;
        }
        return '';
    },
    //////新加的检测后台数据时10位还是13位的，转化为年月日
    timestampToTime1(timestamp1){
        var Y,M,D,date;
        if(timestamp1===undefined || timestamp1===null || timestamp1===''){
            return ''
        }else if(String(timestamp1).length === 10){
            date = new Date(timestamp1*1000 )
            Y = date.getFullYear() + '-';
            M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
            D = date.getDate();
            return Y+M+D;
        }else if(String(timestamp1).length===13){
             date = new Date(timestamp1)
            Y = date.getFullYear() + '-';
            M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
            D = date.getDate();
            return Y+M+D;
        }else{
            return ''
        }
    },
    //js+运算
    accAdd(arg1,arg2){
        var r1,r2,m;
        try{r1=arg1.toString().split(".")[1].length}catch(e){r1=0}
        try{r2=arg2.toString().split(".")[1].length}catch(e){r2=0}
        m=Math.pow(10,Math.max(r1,r2))
        return (arg1*m+arg2*m)/m
    },
// 根据秒转换 时分秒
    getHMS (time){
        const hour = parseInt(time / 3600) < 10 ? '0' + parseInt(time / 3600) : parseInt(time / 3600)
        let min = parseInt(time % 3600 / 60)
        // const sec = parseInt(time % 3600 % 60)
        // console.log(sec)
        // if (sec > 0) {
        // min += 1
        // }
        if (hour > 0) {
        return hour + '小时' + min + '分钟'
        } else {
        // min<10?min='0'+min:''
        return min + '分钟'
        }
    },

    // 根据秒转换小时
    getHour (time){
        const hour = (time / 3600).toFixed(2)
        return hour + ' h'
    },

    /**
     *
     * @description:字符串时间转时间戳
     * @value:date
     * @case: 2019-05-22 00:00:00 ===> 1558454400000
     */
    strTimeToTimestamp(date){
        var ndate = date.substring(0,19);
            ndate = ndate.replace(/-/g,'/');
        var timestamp = new Date(ndate).getTime();
        return timestamp
    },

    /**
     *
     * @description:时间格式转时间戳(10|13)
     * @value:date,typ==1?10位时间戳，type==0?13位时间戳
     */
     timeformatInt(date,type=1){
      date = Date.parse(new Date(date))
      date = isNaN(date) ? '' : type ? date / 1000 : date
      return date
    },
  /**
   * @description 获取时间格式化
   * @param datetime
   * @param startType
   * @return {string}
   */
  getDate(datetime, startType) {
    let date = new Date(datetime); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
    let year = date.getFullYear(),
      month = ("0" + (date.getMonth() + 1)).slice(-2),
      sdate = ("0" + date.getDate()).slice(-2),
      hour = ("0" + date.getHours()).slice(-2),
      minute = ("0" + date.getMinutes()).slice(-2),
      second = ("0" + date.getSeconds()).slice(-2);
    let resStr = "";
    if (startType === "year")
      resStr =
        year +
        "-" +
        month +
        "-" +
        sdate +
        " " +
        hour +
        ":" +
        minute +
        ":" +
        second;
    if (startType === "day") resStr = year + "-" + month + "-" + sdate;
    if (startType === "month") resStr = month + "-" + sdate;
    if (startType === "hour") resStr = hour + ":" + minute + ":" + second;
    return resStr;
  },

    /**
     *
     * @description:校验是不是数字
     * @value:num
     * @case:
     */
    CheckNum:function (num) {
        let check = /^[0-9]+.?[0-9]*$/;
        return (check.test(num)||num==0);
    },
    /**
     *
     * @description 日期格式化
     * @params num
     */
    formatDate(num) {
        let formatNum = num?num:0;
        let date = new Date(new Date().getTime() - (24*60*60*1000*formatNum))
        return date.getFullYear() + '-' + this.formatNumber(date.getMonth() + 1) + '-' + this.formatNumber(date.getDate());
    },

    /**
     *
     * @description:非零正整数校验
     * @param [number] n
     */

    isPositiveInteger(n){
        let reg = /^\+?[1-9]\d*$/;
        if(n){
            if(reg.test(n)){
                return true
            }else{
                return false;
            }
        }else{
            return false;
        }
    },

    /**
     *
     * @description：非零正数校验
     * @param [number] n
     */
    isPositiveNumber(n){
        let reg = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/;
        if(n){
            if(reg.test(n)){
                return true
            }else{
                return false;
            }
        }else{
            return false;
        }
    },
    /**
     * 按钮权限方法通用配置
     * @param sub_auth { String } 支持的全部权限
     * @param text { String } 要校验的文字
     * @returns {boolean}
     */
    authorityControl(sub_auth,text){
        var stringResult,status= false;
        if(sub_auth && text){
            stringResult = sub_auth.split(',');
            if(stringResult.length>0){
                for(var i=0;i < stringResult.length;i++){
                    if(stringResult[i] == text){
                        status = true;
                        break;
                    }
                }
            }
        }
        return status
    },
  /**
   * @description 格式化提交的数据，去掉Undefined
   * @param { Object } options
   */
    removeUndefined(options) {
    console.log("_key: ", options);
      let _data = new FormData();
      Object.keys(options).forEach((_key) => {

        if(options[_key]) {
          _data.append(_key, options[_key]);
        } else if(options[_key] === 0) {
          _data.append(_key, options[_key]);
        } else {

        }
      })
      return _data;
  },
  /**
   * @description 需要在页面中添加两个id为canvas和img的元素
   * @description 根据得到的url,返回图片地址
   */
  generateQr(url, self) {
    let canvas = document.getElementById('canvas')
    self.QRCode.toCanvas(canvas, url,{ errorCorrectionLevel: 'H' }, function (error) {
      if (error){
      } else{

      }
    });
    let context=canvas.getContext('2d');
    let imageData = context.getImageData(0,0,canvas.width,canvas.height);

    let img = document.getElementById("img");
    img.width=canvas.width
    img.height=canvas.height
    let context2 = img.getContext('2d');
    context2.fillStyle="white";
    context2.fillRect(0,0,canvas.width,(canvas.height));
    context2.putImageData(imageData,0,0);
    context2.font="bold 10px 微软雅黑"
    context2.fillStyle="black"

    return img.toDataURL("image/png");
  },
  /**
   * 根据url绘制二维码
   */
  getQRCode(url) {
    let qrCodeCanvas = document.getElementById("canvas");
    this.QRCode.toCanvas(qrCodeCanvas, url, { errorCorrectionLevel: "H" });
    let context = qrCodeCanvas.getContext("2d");
    context.fillStyle = "white";
    context.font = "bold 10px 微软雅黑";
    context.fillStyle = "black";
    const canvasUrl = qrCodeCanvas.toDataURL("image/png");
    return canvasUrl;
  },
  /**
   * @description 根据ID获取下拉实际值
   * @param list：参数列表，id：查询id, key: 参数键，value：参数值
   */
  getValueById(list, id, key='value_no', value='value_name') {
    for (let item of list) {
      if (item[key] == id) return item[value]
    }
  },
  /**
   * @description 根据type 判断组织类型
   */
   getOrganizationType(type) {
    if(type == 1){
        return '厂商'
    }else if(type == 2){
        return '服务商'
    }else if(type == 3){
        return '车场'
    }else{
        return '总后台'
    }
  },
  /**
   * @desc 根据传入的prop，返回index
   * @desc 纯云车场 state：true
   * @param data tableItem
   * @param prop prop
   * @return {Object}
   */
  getIndex(data, prop) {
    const _data = data;
    let index = 0;
    for(let i=0;i<data.length;i++) {
      if (_data[i].subs && _data[i].subs[0] && _data[i].subs[0].prop === prop) {
        index = i;
        break;
      }
    }
    const is_4g_park = String(sessionStorage.getItem('is4GPark')) === '1';
    if (is_4g_park) {
      return {
        index: index,
        state: false
      };
    }
    else {
      return {
        index: index,
        state: true
      };
    }
  },
  /**
   * @desc 根据相机类型，判断按钮是否可操作
   * @param button_type { String } ['add', 'remote']
   * @param camera_type { String | Number } [1~6]
   * 1 - 华夏V85 添加辅助
   * 2 - 臻识R3 添加辅助 远程
   * 3 - 千熠 添加辅助 远程
   * 4 - 臻识RF 远程
   * 5 - 臻识C3 添加辅助 远程
   * 6 - 宇视 添加辅助
   * @return {boolean}
   */
  getCameraButtonPermission(button_type, camera_type) {
    let boo = false
    if (button_type === 'add') {
      boo = String(camera_type) !== '4'
    } else if (button_type === 'remote') {
      boo = String(camera_type) !== '1' && String(camera_type) !== '6'
    } else if (button_type === 'method') {
      boo = true
    }
    return boo
  },
  /**
   * @describe 格式化字符串/数字 最多保留两位小数并且去掉多余的0
   */
    unifyNumber(num) {
        if (num === '') {
        return 0
        } else {
        let handleNum = parseFloat(num)
        let isToFixed = handleNum.toString().includes('.') && handleNum.toString().split('.')[1].length > 2
        if (isToFixed) {
            return handleNum.toFixed(2)
        } else {
            return handleNum
        }
        }
    },
  /**
   * @describe 获取设备指令ID
   */
    getCommand(params,api,method="get", type="formData"){
        if(method == 'get' && type == 'formData'){
            params = {
                params
            }
        }
        return new Promise((resolve,reject)=>{
        axios[method](`${path}${api}`,params)
            .then(async res => {
                if(res.data.code == 200){
                    await this.recursionFetch(res.data.data.id).then(result=>{
                        return resolve(result.value)
                    }).catch(err=>{
                        return reject({ message: '操作失败！' || err.message})
                    })
                }else{
                    // return reject({message:res.data.message || '操作失败！'})
                    return resolve(res.data)
                }
            })
            .catch((err) => {
                return reject({ message: '操作失败！'})
            });
        })
    },
  /**
   * @describe 根据指令循环查结果
   */
    recursionFetch(id,time=0){
        return new Promise((resolve, reject) => {
            this.getReslut(id,time)
            .then((res) => {
                // 超过次数返回失败
                if (res.time > 13) {
                    return reject({ message: '操作失败！'})
                } else {
                    // 1: 未查到结果，2: 成功, 3： 失败
                    if(res.value.orderStatus===1){
                        return resolve({ done: false, value: res });
                    }else if(res.value.orderStatus===2){
                        return resolve({ done: true, value: res})
                    }else{
                        return reject({ message: res.data.message || '操作失败！'})
                    }
                }
            })
            .catch((err) => {
                return reject({ message: '操作失败！'})
            });
        }).then((res) => {
            // 满足条件，返回
            if (res.done) {
            return Promise.resolve(res.value);
            } else {
            // 不满足条件，进入递归
            return this.recursionFetch(id,res.value.time);
            }
        })
        .catch((err) => {
            return Promise.reject(err);
        });
    },
    /**
     * @describe 根据指令查结果
     */
    getReslut(id,time=0){
        let resultApi = `${path}/deviceInstructionIssuance/queryById?id=${id}`
        return new Promise((resolve,reject)=>{
        setTimeout(() => {
            axios.post(resultApi).then(res => {
                time+=1
                return resolve({time,value:res.data.data})
            }).catch(err=>{
                return resolve({time:15});

            })
        }, 2000);
        })
    },
    searchCriteria(data){
        let searchApi = `${path}/meiLiSearch/search`
        return new Promise((resolve,reject)=>{
            axios.post(searchApi, data)
             .then(res => {
            let data = res.data
             if (data.code === 200) {
                 let list=data.hits.map((item)=>{
                    return {
                    value_no:item.id,
                     value_name:item.name,
                    orgType:item.orgType
                    }

                 })

                 resolve(list)

             } else {
                reject(data.message)

             }

             })
             .catch(err => {
                reject(err)
             })

        })
    },
        // 查询商户开通状态
    queryMerchantBalance(data) {
        let api = `/parkshare/queryMerchantBalance?unionId=${data.unionId}&type=${data.type}`
        return new Promise((resolve, reject) => {
            axios.get(api).then(res => {
            let data = res.data
            resolve(data)

          }).catch((err) => {
            reject(false)

          })
        })

    },

    isJSON(str) {
        if (typeof str == 'string') {
          try {
              var obj=JSON.parse(str);
              if(typeof obj == 'object' && obj ){
                  return true;
              }else{
                  return false;
              }

          } catch(e) {
              console.log('error：'+str+'!'+e);
              return false;
          }
        } else {
          console.log(str+'不是字符串!');
          return false;
        }
    },
    format: function(fmt, date) {
        var o = {
          "M+": date.getMonth() + 1, //月份
          "d+": date.getDate(), //日
          "h+": date.getHours(), //小时
          "m+": date.getMinutes(), //分
          "s+": date.getSeconds(), //秒
          "q+": Math.floor((date.getMonth() + 3) / 3), //季度
          S: date.getMilliseconds(), //毫秒
        };
        if (/(y+)/.test(fmt))
          fmt = fmt.replace(
            RegExp.$1,
            (date.getFullYear() + "").substr(4 - RegExp.$1.length)
          );
        for (var k in o)
          if (new RegExp("(" + k + ")").test(fmt))
            fmt = fmt.replace(
              RegExp.$1,
              RegExp.$1.length == 1
                ? o[k]
                : ("00" + o[k]).substr(("" + o[k]).length)
            );
        return fmt;
      },
    isMobile() {
        let flag = navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)
        return flag
    },
    // 校验手机号是否合法
    isValidPhoneNumber(phoneNumber) {
        const regex = /^1[3-9]\d{9}$/;
        return regex.test(phoneNumber);
    }
};
