<template>
    <div class="parking-spaceNew">
      <el-drawer
            :visible="parkImportShow"
            direction="rtl"
            @close="closeDialog"
            size="680px"
            custom-class="custom-drawer">
            <div slot="title" class="header-title">导入车位</div>
            <div class="custom-drawer-info">
              <el-form ref="refillAddForm" label-width="68px" >
                <div class="tips"><svg-tips-one theme="outline" size="16"/>可选生成批量车位编号模板，或者选择本地已有文件导入</div>
                <el-form-item label="模板文件">
                  <div class="houseInfo">
                    <el-input type="text" v-model.trim="parkingLotNumberPrefix" placeholder="车位号"></el-input>
                    <el-input type="number" v-model.trim="number" placeholder="数量"></el-input>
                    <el-button @click="generateTemplate">生成模板</el-button>
                  </div>
                  <div class="tips"><svg-tips-one theme="outline" size="16"/>选填，编辑一个首车位，字母+数字的结尾（如；A12），系统将根据结尾数字+设置数量，自动递增A13，A14...等</div>
                </el-form-item>
                <el-form-item label="导入">
                  <div class="import-input-wrapper">
                    <el-upload class="upload-demo" ref="upload" :action="uploadapi" :auto-upload="false"
                              :on-success="uploadSuccess" :on-remove="handleRemove" :on-change="handleChange">
                      <el-button slot="trigger" size="small" type="primary" @click="handleSelect">选取文件</el-button>
                    </el-upload>
                  </div>
                  <div class="headerInfo">
                    <el-button type="primary" @click="importConfirm()">确定导入</el-button>
                    <span  v-html="uploadMsg"></span>
                  </div>
                </el-form-item>
              </el-form>
            </div>
      </el-drawer>
    </div>
</template>
<script>

export default {
  props: {
    parkImportShow: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      top:'0vh', 
      importName: '', //导入文件名称
      uploadMsg:'',
      isBolink: true,
      // 导入
      uploadapi:this.$PUBLIC_URL.BASE_API + '/parkingLot/importExcel?comid=' + sessionStorage.getItem("comid"),
      parkingLotNumberPrefix: '', //车位号
      number:'', //数量
    }
  },
  methods: {
    closeDialog(){
      this.$emit("closeDialog",false)
    },

    //重新打开清空数据
    openPop(){
      this.parkingLotNumberPrefix = ''; //车位号
      this.number = ''; //数量
      this.uploadMsg = '';
      // this.$refs.upload.clearFiles();
    },

    //生成模板
    generateTemplate(){
      const comid = sessionStorage.getItem("comid");
      var params ={
        comid,
        parkingLotNumberPrefix:this.parkingLotNumberPrefix || null,
        number: this.number || null
      }

      this.handleExport(params)
    },

    //导出表格数据
		handleExport(paramsInfo) {
			let api = '/parkingLot/exportExcel'
			let params = ""
			params +=
					"comid="+ paramsInfo.comid + "&parkingLotNumberPrefix="
          + paramsInfo.parkingLotNumberPrefix +"&number="+ paramsInfo.number
          
			if (this.isBolink) {
				window.open(this.$PUBLIC_URL.BASE_API  + api + "?" + params)
			}
		},

    //确定导入
    importConfirm(){
      //上传文件
      this.$refs.upload.submit();
    },

    //选择文件
    handleSelect() {
      //点击选择文件，清空当前文件列表和上传信息
      this.$refs.upload.clearFiles();
      this.uploadMsg = '';
    },

    handleRemove(file, fileList) {
      this.uploadMsg = '';
    },
    uploadSuccess(response, file, filelist) {
      if(response.code === 200) {
        setTimeout(() => {
          this.$refs.upload.clearFiles();
        }, 1000);
        this.uploadMsg = `${response.message}`;
        // this.$emit("successUpload")
      } else {
        this.$message({
          message: '上传失败!',
          type: 'error',
          duration: 1200
        });
        setTimeout(() => {
          this.$refs.upload.clearFiles();
        }, 1000);
        this.uploadMsg = `${response.message}`;
      }

    },

    handleChange(file, fileList) {
      //校验文件
      const that = this;
      if (!(file.name.endsWith('.xls') || file.name.endsWith('.xlsx'))) {
        this.$alert('请选择正确的Excel文件', '提示', {
          confirmButtonText: '确定',
          type: 'warning',
          callback: action => {
            that.$refs.upload.clearFiles();
          }
        });
      }

    },

    handleClose(tag) {
      this.parkTagsList.splice(this.parkTagsList.indexOf(tag), 1);
    },

    showInput() {
      this.inputVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
  }
}
</script>
<style  lang="scss" scoped>
.header-title{
  color: #333;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
/deep/.el-drawer__header{
  margin-bottom: 10px;
  padding: 20px 30px 0 30px;
}
.custom-drawer-info{
  margin: 10px 30px;
}
.level-title{
  font-weight: bold;
  margin-bottom: 10px;
}

.headerInfo .el-button{
  width: 120px;
  height: 40px;
}

.tips{
  color: #999;
  font-size: 14px;
  background: #fff;
  line-height: 20px;
  margin: 6px 0;
  span{
    vertical-align: middle;
    margin-right: 6px;
  }
}

.header-tips{
  color: #B8741A;
  margin-left: 20px;
  margin: 0 10px;
}

/deep/.el-input{
  width: 200px;
}

.houseInfo{
  display: flex;
  span{
    display: inline-block;
    width: 30px;
    margin: 0 10px;
  }
  .el-input{
    width: 90px;
    margin-right: 10px;
  }
}

.headerInfo{
  display: flex;
  margin: 20px 0;
  span{
    margin-left: 10px;
    color: #B8741A;
  }
}

/deep/.custom-dialog .el-dialog__body{
  padding: 30px 20px;
}

/deep/.custom-dialog .el-dialog__header .el-dialog__headerbtn{
  top: 20px;
}
.import-input-wrapper{
    position: relative;
    height: 32px;
    border: 1px solid #3C75CF;
    border-radius: 6px;
    width: 350px;
    .upload-demo{
      position: absolute;
      top: 0;
      right: 0;
    }
}

/deep/.el-form-item__content{
  line-height: 24px;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}

/deep/.el-upload-list{
  position: absolute;
  top: -5px;
  left: -224px;
}

</style>
