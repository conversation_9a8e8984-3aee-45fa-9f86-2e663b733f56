<template>
  <div class="custom-tab-wrapper">
    <div class="tab-item-wrapper">
      <div
        v-for="item in tabList"
        :key="item.prop"
        :class="
          curActiveName === item.prop ? 'tab-item tab-item-active' : 'tab-item'
        "
        @click="tabToggle(item)"
      >
        {{ item.name }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "BlTabs",
  props: {
    tabList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      curActiveName: "",
    };
  },
  created() {
    if (this.tabList.length) {
      const firstTab = this.tabList[0];
      this.setCurTab(firstTab.prop);
    }
  },
  methods: {
    setCurTab(val) {
      this.curActiveName = val;
      this.$emit("input", val);
    },

    tabToggle(item) {
      if(this.curActiveName===item.prop) return false
      this.setCurTab(item.prop);
      this.$emit("change", item.prop);
    },
  },
};
</script>

<style lang="scss" scoped>
.custom-tab-wrapper {
  position: relative;
  height: 54px;
  margin: 0 12px;
  background: rgba(245, 247, 250, 1);
  .tab-item-wrapper {
    position: absolute;
    left: 23px;
    bottom: 0;
    width: 212px;
    height: 48px;
    display: flex;
    .tab-item {
      flex: 1;
      text-align: center;
      height: 48px;
      line-height: 48px;
      box-sizing: border-box;
      color: #909399;
      font-size: 16px;
      cursor: pointer;
    }
    .tab-item-active {
      border-top: 1px solid #193c7b;
      background: #ffffff;
      color: #3c75cf;
    }
  }
}
</style>
