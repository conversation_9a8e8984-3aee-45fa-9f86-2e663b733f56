import $store from '@/store'
export default {
	/**
	 * @description 获取项目列表
	 */
	async getProject() {
		let projectList = $store.state.accessLadder.projectList
		if (!projectList)
			projectList = await $store.dispatch('accessLadder/goGetProject')
		return projectList
	},

	/**
	 * @description 获取区域列表
	 */
	async getVillage() {
		let villageList = $store.state.accessLadder.villageList
		if (!villageList)
			villageList = await $store.dispatch('accessLadder/goGetVillage')
		return villageList
	},

	/**
	 * @description 获取住宅地址列表
	 */
	async getAllAddress() {
		let allAddressList = $store.state.accessLadder.allAddressList
		if (!allAddressList)
			allAddressList = await $store.dispatch(
				'accessLadder/goGetAllAddress'
			)
		return allAddressList
	},

	/**
	 * @description 获取所有区域
	 */
	getAllVillageList() {
		return $store.state.accessLadder.allVillageList
	},

	/**
	 * @description 获取所有楼宇
	 */
	getAllBuildingList() {
		return $store.state.accessLadder.allBuildingList
	},

	/**
	 * @description 获取所有楼层
	 */
	 getAllFloorList() {
		return $store.state.accessLadder.allFloorList
	},

	/**
	 * @description 获取所有房间
	 */
	getAllHouseList() {
		return $store.state.accessLadder.allHouseList
	},

	/**
	 * @description 获取需要操作的项目信息
	 */
	getProjectData() {
		let projectData = $store.state.accessLadder.projectData
		if (!projectData) {
			projectData =
				sessionStorage.getItem('projectData') &&
				JSON.parse(sessionStorage.getItem('projectData'))
			$store.commit('accessLadder/SET_PROJECTDATA', projectData)
		}
		return projectData
	},

  /**
	 * @description 获取需要操作的人员信息
	 */
	getFaceUserData() {
		let faceUserData = $store.state.accessLadder.faceUserData
		if (!faceUserData) {
			faceUserData =
				sessionStorage.getItem('faceUserData') &&
				JSON.parse(sessionStorage.getItem('faceUserData'))
			$store.commit('accessLadder/SET_FACEUSERDATA', faceUserData)
		}
		return faceUserData
	},

	/**
	 * @description 获取设备类型(梯控)
	 */
	async getAccessDeviceType() {
		let deviceType = $store.state.accessLadder.accessDeviceType
		if (!deviceType)
			deviceType = await $store.dispatch(
				'accessLadder/goGetAccessDeviceType'
			)
		return deviceType
	},

	/**
	 * @description 获取设备类型(门禁)
	 */
	async getDoorDeviceType() {
		let deviceType = $store.state.accessLadder.doorDeviceType
		if (!deviceType)
			deviceType = await $store.dispatch(
				'accessLadder/goGetDoorDeviceType'
			)
		return deviceType
	},
	/**
	 * @description 获取设备类别(门禁)
	 */
	async getDoorDeviceCategory() {
		let deviceCategory = $store.state.accessLadder.doorDeviceCategory
		if (!deviceCategory)
			deviceCategory = await $store.dispatch(
				'accessLadder/goGetDoorDeviceCategory'
			)
		return deviceCategory
	},
	/**
	 * @description 获取门禁设备
	 */
   async getDoorDeviceList() {
		let doorDeviceList = $store.state.accessLadder.doorDeviceList
		if (!doorDeviceList)
    doorDeviceList = await $store.dispatch(
				'accessLadder/goGetDoorDeviceList'
			)
		return doorDeviceList
	},
  /**
	 * @description 获取公共参数
	 */
   async getPublicConfig() {
		let publicConfig = $store.state.accessLadder.publicConfig
		if (!publicConfig)
    publicConfig = await $store.dispatch(
				'accessLadder/goGetPublicConfig'
			)
		return publicConfig
	},
}
