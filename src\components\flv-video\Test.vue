<template>
  <div class="player-container">
    <div id="container" ref="container" class="container playerDom"></div>
  </div>
</template>

<script>
import ElementResize from '../../common/js/erd'

export default {
  name: 'jessibucaPlayer',
  props: {
    url: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      style: {
        height: '100%'
      },
      jessibuca: null,
      vc: 'ff',
      count: 0, // 视频超时重连计数
      maxCount: 5, // 最大重连数
      lagStatus: 0, // 视频卡顿统计
      reconnectTimer: null,
      isNetworkOffline: false
    }
  },
  mounted() {
    let eleResize = new ElementResize(".playerDom");
    eleResize.listen((evt) => {
      if (this.jessibuca) {
        this.jessibuca.resize();
      }
    })
    window.addEventListener('online', this.onNetworkOnline);
    window.addEventListener('offline', this.onNetworkOffline);
    // 定时重连 - 每隔2.5分钟主动刷新一次流
    this.preventiveReconnectTimer = setInterval(() => {
      console.log('执行预防性流刷新');
      if (this.url && navigator.onLine && this.jessibuca) {
        this.$emit('onRefreshStream');
      }
    }, 150000); // 2.5分钟 
    
    this.networkCheckInterval = setInterval(() => {
      const wasOffline = this.isNetworkOffline;
      this.isNetworkOffline = !navigator.onLine;
      
      // 从离线变为在线时触发重连
      if (wasOffline && !this.isNetworkOffline) {
        console.log('网络状态检测：网络已恢复');
        this.onNetworkOnline();
      }
    }, 2000);   
  },
  unmounted() {
    this.cleanup();
    window.removeEventListener('online', this.onNetworkOnline);
    window.removeEventListener('offline', this.onNetworkOffline);
  },
  destroyed() {
    window.removeEventListener('online', this.onNetworkOnline);
    window.removeEventListener('offline', this.onNetworkOffline);
    this.cleanup();
    if (this.networkCheckInterval) {
      clearInterval(this.networkCheckInterval);
    }   
    if (this.preventiveReconnectTimer) {
      clearInterval(this.preventiveReconnectTimer);
    }    
  },
  methods: {
    cleanup() {
      if (this.jessibuca) {
        this.jessibuca.destroy();
        this.jessibuca = null;
      }
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }
    //   this.networkCheckInterval = setInterval(() => {
    //   const wasOffline = this.isNetworkOffline;
    //   this.isNetworkOffline = !navigator.onLine;
      
    //   // 从离线变为在线时触发重连
    //   if (wasOffline && !this.isNetworkOffline) {
    //     console.log('网络状态检测：网络已恢复');
    //     this.onNetworkOnline();
    //   }
    // }, 2000);

    },

    handleReconnect() {
      // 添加重试计数
      if (!this.retryCount) this.retryCount = 0;
      this.retryCount++;    
      
      // 计算退避时间 (1秒、2秒、4秒...)
      const delay = Math.min(Math.pow(2, this.retryCount - 1) * 1000, 10000);
      
      if (this.isNetworkOffline) {
        console.log('网络断开状态，等待网络恢复...');
        return;
      }

      // 增加重连次数判断
      if (this.count >= this.maxCount) {
        console.log('达到最大重连次数，停止重连');
        this.count = 0;
        return;
      }
      
      this.count++;
      console.log(`第 ${this.count} 次重试，延迟 ${delay}ms`);

      this.cleanup();
      
      // 使用计算出的延迟时间延迟触发重连事件
      setTimeout(() => {
        // 播放错误时触发重连
        this.$emit('onReconnect');
      }, delay);
      
      // 超过5次重试后重置退避计数，避免延迟过长
      if (this.retryCount >= 5) {
        this.retryCount = 0;
      }
    },
    create(options) {
      const self = this;
      options = options || {};
      this.jessibuca = new window.Jessibuca(
        Object.assign(
          {
            container: this.$refs.container,
            videoBuffer: 0.2,
            loadingText: "加载中",
            isResize: false,
            debug: true,
            forceNoOffscreen: true,
            isNotMute: false,
            isFlv: true,
            decoder: '/static/jessibuca/decoder.js'
          },
          options
        )
      )

      this.jessibuca.on("load", function () {
        console.log("当前播放URL:", self.url); 
        self.count = 0; // 重置重连计数
        self.lagStatus = 0;
        self.jessibuca.play(self.url);
      })

      this.jessibuca.on("performance", (performance) => {
        if (performance === 0 && self.lagStatus >= self.maxCount) {
          self.cleanup();
          self.lagStatus = 0;
          self.$nextTick(() => {
            self.create();
          })
          return;
        }
        if (performance === 0) {
          self.lagStatus++;
        }
      })

      this.jessibuca.on("delayTimeout", function () {
        // 如果URL存在，直接重新使用该URL播放
        if (self.url) {
          self.cleanup();
          self.$nextTick(() => {
            self.create();
          });
        } else {
          // URL不存在，请求新的URL
          self.handleReconnect();
        }
      })

      this.jessibuca.on("error", function (error) {
        console.log('播放错误类型:', error);
        if (error === 'fetchError') {
          console.log('可能是授权问题，直接请求新地址');
          self.cleanup();
          self.$emit('onRefreshStream'); // 请求全新流地址而不是重连
          self.errorCount = 0;
          return;
        }

        // 记录错误次数，避免无限重试
        if (!self.errorCount) self.errorCount = 0;
        self.errorCount++;
        if (self.errorCount > 3) {
          console.log('多次错误，请求全新流地址');
          self.cleanup();
          self.$emit('onReconnect'); 
          self.errorCount = 0;
          return;
        }
        switch (error) {
          case 'playError':
          case 'fetchError':
          case 'websocketError':
            console.log(`发生${error}错误，准备重连...`);
            // 如果URL存在，直接重试当前URL
            if (self.url) {
              self.cleanup();
              self.$nextTick(() => {
                self.create();
              });
            } else {
              // URL不存在，请求新的URL
             self.$emit('onReconnect');
            }
            break;
          default:
            self.cleanup();
            break;
        }
      })
    },

  onNetworkOnline() {
    console.log('网络已恢复');
    this.isNetworkOffline = false;
    
    // 清理旧的播放器实例
    this.cleanup();
    setTimeout(() => {
      console.log('发送重连事件');
      this.$emit('onReconnect');
    }, 1000);
  },
  onNetworkOffline() {
    console.log('网络已断开');
    this.isNetworkOffline = true;
    this.cleanup();
  },    
  },


watch: {
  url: {
    handler(newUrl, oldUrl) {
      console.log('URL已更新:', newUrl);
      console.log('旧URL:', oldUrl);     
      this.cleanup();
      if (newUrl) {
        this.count = 0; // 重置重连计数
        // 确保新旧URL不同时才重新创建
        if (newUrl !== oldUrl) {
          console.log('使用新URL创建播放器');
          this.$nextTick(() => {
            this.create();
          });
        }
      }
    },
    immediate: true
  }
}
}
</script>

<style lang="scss" scoped>
.player-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgb(0, 0, 0);
  
  .container {
    position: absolute;
    width: 100%;
    height: 100%;
    
    canvas {
      width: 100%;
      height: 100%;
    }
  }
}
</style>