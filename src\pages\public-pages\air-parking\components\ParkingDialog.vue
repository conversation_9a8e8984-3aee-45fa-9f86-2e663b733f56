<template>
  <el-dialog :close-on-click-modal="false" custom-class="custom-dialog" :show-close="false"
    :visible.sync="dialogVisible" width="600px">
    <header class="fixed-code__title" slot="title" style="font-size: 18px; font-weight: bold;text-align:center">
      {{ formData.id ? '编辑车场' : '新增车场' }}
      <i class="el-icon-close dialog-header-iconfont" @click="handleClose"></i>
    </header>
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="small">
      <el-form-item label="车场编号" prop="lotNo">
        <div class="flexs">
          <el-input v-model="form.lotNo" style="width: 300px; margin-right: 20px;" placeholder="请输入车场编号" />
          <el-button type="primary" @click="handleSettlementChange(form.lotNo)">查询</el-button>
        </div>
      </el-form-item>
      <el-form-item label="车场名称" prop="lotName">
        {{form.lotName}}
      </el-form-item>
      <el-form-item label="省市" prop="region">
        <el-cascader
          v-model="form.region"
          :options="regionOptions"
          :props="{ value: 'value', label : 'label', children: 'children' }"
          placeholder="请选择省市"
          style="width: 100%;"
          @change="handleRegionChange"
        />
      </el-form-item>
      <el-form-item label="详细地址" prop="address">
        <el-input v-model="form.address" placeholder="请输入详细地址" />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="form.phone" placeholder="请输入手机号" maxlength="11" />
      </el-form-item>
      <el-form-item label="枢纽名称" prop="airportId">
        <el-select v-model="form.airportId" placeholder="请选择关联枢纽" style="width: 100%;">
          <el-option 
            v-for="airport in airportList" 
            :key="airport.id" 
            :label="airport.name" 
            :value="airport.id" 
          />
        </el-select>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  fetchParkSettlement,
  dropdownAirport,
  dropdownAreaTree
} from "@/api/airParking";
export default {
  name: 'ParkingDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        airportId: '',
        name: '',
        naprovinceNameme: '',
        provinceCode: '',
        address: '',
        phone: '',
        region: [],
      },
      
      requestUrl: this.$PUBLIC_URL.AIRPORT_API, // 请求接口
      rules: {
        airportId: [{ required: true, message: '请选择关联枢纽', trigger: 'change' }],
        name: [{ required: true, message: '请输入车场名称', trigger: 'blur' }],
        region: [{ required: true, message: '请选择省市区', trigger: 'change' }],
        address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
        lotNo: [{ required: true, message: '请输入车场编号', trigger: 'blur' }],
        lotName: [{ required: true, message: '请输入车场名称', trigger: 'blur' }],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ]
      },
      airportList: [],
      hubList: [],
      regionOptions: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        let formData = this.formData;
        console.log(formData,'formData')
        formData.region = [formData.provinceCode,formData.cityCode]
        this.form = { ...formData }
        console.log(this.form,'this.form')
        this.loadAirportList()
        this.loadAreaTreeList()
        this.loadHubList()
      }
    }
  },
  methods: {
    handleClose() {
      // this.$refs.form.resetFields()
      this.dialogVisible = false
    },
    async handleSubmit() {
      try {
        await this.$refs.form.validate()
        console.log("提交表单:", this.form);
        this.$emit('submit', this.form)
        this.handleClose()
      } catch (error) {
        // 表单验证失败
      }
    },
    getLabelsByValue(value) {
      console.log("value:", value);
      if (!value) return [];
      const labels = [];
      let currentOptions = this.regionOptions;
      value.forEach((val, index) => {
        const option = currentOptions.find(item => item.value === val);
        if (option) {
          labels.push(option.label);
          // 如果不是最后一级，继续查找下一级的选项
          if (index < value.length - 1 && option.children) {
            currentOptions = option.children;
          }
        }
      });
      
      return labels;
    },
    handleRegionChange(value) {
      // 省市区变化处理
      console.log('选择的省市区:', value)
      let lableValue = this.getLabelsByValue(value);
      this.$set(this.form, 'provinceName', lableValue[0]);
      this.$set(this.form, 'cityName', lableValue[1]);
      this.$set(this.form, 'provinceCode', value[0]);
      this.$set(this.form, 'cityCode', value[1]);
    },

    async handleSettlementChange(value) {
      try {
        const response = await fetchParkSettlement(this.requestUrl, value);
    
        // 防止 response 或 response.data 不存在导致解构失败
        if (!response || !response.data) {
          throw new Error('响应数据为空');
        }
    
        const { code, data } = response.data;
        if (code == 200) {
          this.$set(this.form, 'lotName', data);
        } else {
          this.$set(this.form, 'lotName', '');
        }
      } catch (error) {
        console.error("fetchParkSettlement 请求失败:", error); // 记录错误日志
        this.$message.error("加载数据失败，请稍后重试");
      }
    },

    // 加载省市列表
    async loadAreaTreeList() {
      try {
        // 这里应该调用实际的API
        const response = await dropdownAreaTree(this.requestUrl);

        if (!response || !response.data) {
          throw new Error('响应数据为空');
        }
        const { code, data } = response.data;
        console.log(code, data,'获取省市列表成功');
        if (code == 200) {
          // 筛选只保留省市两级，移除区县级别
          this.regionOptions = this.filterProvinceCity(data);
        } else {
          this.regionOptions = [];
        }
      } catch (error) {
        console.error('dropdownAreaTree 加载省市列表失败:', error)
      }
    },

    // 筛选省市数据，只保留两级
    filterProvinceCity(data) {
      if (!Array.isArray(data)) return [];

      return data.map(province => {
        const filteredProvince = {
          ...province
        };

        // 如果省份有子级（市），则保留市级，但完全移除市下面的children字段
        if (province.children && Array.isArray(province.children)) {
          filteredProvince.children = province.children.map(city => {
            // 创建新的市对象，不包含children字段
            const { children, ...cityWithoutChildren } = city;
            return cityWithoutChildren;
          });
        } else {
          // 如果省份没有子级，也移除children字段
          delete filteredProvince.children;
        }
        return filteredProvince;
      });
    },
    // 加载机场列表
    async loadAirportList() {
      try {
        // 这里应该调用实际的API
        const response = await dropdownAirport(this.requestUrl);
    
        // 防止 response 或 response.data 不存在导致解构失败
        if (!response || !response.data) {
          throw new Error('响应数据为空');
        }
    
        const { code, data } = response.data;
        console.log(code, data);
        if (code == 200) {
          this.airportList = data;
        } else {
          this.airportList = [];
        }
      } catch (error) {
        console.error('dropdownAirport 加载机场列表失败:', error)
      }
    },
    async loadHubList() {
      // 加载枢纽列表
      try {
        // 这里应该调用实际的API
        this.hubList = [
          { id: 1, name: '北京交通枢纽' },
          { id: 2, name: '上海交通枢纽' },
          { id: 3, name: '广州交通枢纽' },
          { id: 4, name: '深圳交通枢纽' }
        ]
      } catch (error) {
        console.error('加载枢纽列表失败:', error)
      }
    }
  }
}
</script>

<style scoped>
/* 继承机场弹窗的样式 */
.flexs{
  display: flex;
}
</style>