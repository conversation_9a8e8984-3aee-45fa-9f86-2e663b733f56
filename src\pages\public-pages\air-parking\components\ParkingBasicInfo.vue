<template>
  <div class="parking-basic-info">
    <el-form :model="formData" :rules="rules" ref="basicForm" label-width="140px" size="small">
      <!-- 联系信息区域 -->
      <div class="form-section">
        <h4 class="section-title">
          <i class="el-icon-phone"></i>
          联系信息
        </h4>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="formData.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="会议地点" prop="meetingLocation">
              <el-input v-model="formData.meetingLocation" placeholder="请输入会议地点" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="停车位置" prop="positionType">
              <el-select v-model="formData.positionType" placeholder="请选择停车位置" style="width: 100%">
                <el-option
                  v-for="city in positionList"
                  :key="city.value"
                  :label="city.label"
                  :value="city.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保存位置" prop="savePosition">
              <el-input v-model="formData.savePosition" placeholder="请输入保存位置" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 服务信息区域 -->
      <div class="form-section">
        <h4 class="section-title">
          <i class="el-icon-service"></i>
          服务信息
        </h4>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="车场类型" prop="vehicleType">
              <el-select v-model="formData.vehicleType" placeholder="请选择车场类型" style="width: 100%">
                <el-option label="室内停车场" value="indoor" />
                <el-option label="室外停车场" value="outdoor" />
                <el-option label="地下停车场" value="underground" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="接驳距离" prop="shuttleDistance">
              <el-input v-model="formData.shuttleDistance" placeholder="如：5.6" />
              <span class="unit-text">KM</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="接驳时长" prop="shuttleDuration">
              <el-input v-model="formData.shuttleDuration" placeholder="如：24小时" />
              <span class="unit-text">分钟</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="接驳地点" prop="shuttleLocation">
              <el-input v-model="formData.shuttleLocation" placeholder="请输入接驳地点" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="接驳时间" prop="shuttleTime">
              <el-input v-model="formData.shuttleTime" placeholder="请输入接驳时间" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 服务说明区域 -->
      <div class="form-section">
        <h4 class="section-title">
          <i class="el-icon-document"></i>
          服务说明
        </h4>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="载客说明" prop="passengerInfo">
              <el-input
                type="textarea"
                v-model="formData.passengerInfo"
                placeholder="请输入载客说明，如：最多可载客25人，载重5人"
                :rows="3"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="温馨提示" prop="warmTips">
              <el-input
                type="textarea"
                v-model="formData.warmTips"
                placeholder="请输入温馨提示内容"
                :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <!-- 收费屏据照片上传 -->
      <el-form-item label="收费屏据照片">
        <div class="upload-section">
          <el-upload
            class="fee-screen-upload"
            :action="uploadAction"
            :before-upload="beforeUpload"
            :on-success="onFeeScreenSuccess"
            :on-remove="onFeeScreenRemove"
            :file-list="feeScreenFileList"
            list-type="picture-card"
            :limit="1"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
          <div class="upload-tip">只能上传1张收费屏据照片，支持jpg/png格式，大小不超过2MB</div>
        </div>
      </el-form-item>
      
      <!-- 停车场照片上传 -->
      <el-form-item label="停车场照片">
        <div class="upload-section">
          <el-upload
            class="parking-photos-upload"
            :action="uploadAction"
            :before-upload="beforeUpload"
            :on-success="onParkingPhotoSuccess"
            :on-remove="onParkingPhotoRemove"
            :file-list="parkingPhotosFileList"
            list-type="picture-card"
            :limit="9"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
          <div class="upload-tip">最多上传9张停车场照片，支持jpg/png格式，大小不超过2MB</div>
        </div>
      </el-form-item>
    </el-form>
    
    <div class="form-actions">
      <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
    </div>
    
    <!-- 地图选择弹窗 -->
    <el-dialog title="选择位置" :visible.sync="mapDialogVisible" width="800px">
      <div class="map-container">
        <div id="mapContainer" style="height: 400px;"></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="mapDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmLocation">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  dropdownAirport
} from "@/api/airParking";

export default {
  name: 'ParkingBasicInfo',
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    airportList: {
      type: Array,
      default: () => []
    },
    regionOptions: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      saving: false,
      mapDialogVisible: false,
      selectedLocation: null,
      formData: {
        lotName: '',
        airportId: '',
        province: '',
        city: '',
        district: '',
        address: '',
        longitude: '',
        latitude: '',
        phone: '',
        servicePhone: '',
        parkingSpaces: '',
        feeStandard: '',
        feeScreenPhoto: '',
        parkingPhotos: []
      },
      rules: {
        lotName: [
          { required: true, message: '请输入车场名称', trigger: 'blur' }
        ],
        airportId: [
          { required: true, message: '请选择配对机场', trigger: 'change' }
        ],
        region: [
          { required: true, message: '请选择省份', trigger: 'change' }
        ],
        city: [
          { required: true, message: '请选择城市', trigger: 'change' }
        ],
        positionType: [
          { required: true, message: '请选择停车位置', trigger: 'change' }
        ],
        district: [
          { required: true, message: '请选择区县', trigger: 'change' }
        ],
        address: [
          { required: true, message: '请输入详细地址', trigger: 'blur' }
        ],
        longitude: [
          { required: true, message: '请输入经度', trigger: 'blur' },
          { pattern: /^-?((1[0-7]\d)|(\d{1,2}))(\.\d+)?$/, message: '请输入有效的经度值', trigger: 'blur' }
        ],
        latitude: [
          { required: true, message: '请输入纬度', trigger: 'blur' },
          { pattern: /^-?([1-8]?\d(\.\d+)?|90(\.0+)?)$/, message: '请输入有效的纬度值', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入联系手机', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' }
        ],
        servicePhone: [
          { required: true, message: '请输入客服电话', trigger: 'blur' }
        ],
        parkingSpaces: [
          { required: true, message: '请输入停车位数', trigger: 'blur' }
        ],
        feeStandard: [
          { required: true, message: '请输入停车收费标准', trigger: 'blur' }
        ]
      },
      positionList: [
        {value: 1, label: '室外'},
        {value: 0, label: '室内'}
      ],
      requestUrl: this.$PUBLIC_URL.AIRPORT_API, // 请求接口
      cityList: [],
      districtList: [],
      feeScreenFileList: [],
      parkingPhotosFileList: [],
      uploadAction: '#' // 实际项目中应该是真实的上传接口
    }
  },
  computed: {
    cityOptions() {
      const cityMap = {
        guangdong: [
          { label: '深圳市', value: 'shenzhen' },
          { label: '广州市', value: 'guangzhou' },
          { label: '东莞市', value: 'dongguan' },
          { label: '佛山市', value: 'foshan' }
        ],
        beijing: [
          { label: '北京市', value: 'beijing' }
        ],
        shanghai: [
          { label: '上海市', value: 'shanghai' }
        ],
        zhejiang: [
          { label: '杭州市', value: 'hangzhou' },
          { label: '宁波市', value: 'ningbo' },
          { label: '温州市', value: 'wenzhou' }
        ]
      }
      return cityMap[this.formData.province] || []
    },
    districtOptions() {
      const districtMap = {
        shenzhen: [
          { label: '宝安区', value: 'baoan' },
          { label: '南山区', value: 'nanshan' },
          { label: '福田区', value: 'futian' },
          { label: '罗湖区', value: 'luohu' }
        ],
        guangzhou: [
          { label: '天河区', value: 'tianhe' },
          { label: '越秀区', value: 'yuexiu' },
          { label: '海珠区', value: 'haizhu' }
        ],
        beijing: [
          { label: '朝阳区', value: 'chaoyang' },
          { label: '海淀区', value: 'haidian' },
          { label: '西城区', value: 'xicheng' }
        ],
        shanghai: [
          { label: '浦东新区', value: 'pudong' },
          { label: '黄浦区', value: 'huangpu' },
          { label: '徐汇区', value: 'xuhui' }
        ],
        hangzhou: [
          { label: '西湖区', value: 'xihu' },
          { label: '拱墅区', value: 'gongshu' },
          { label: '江干区', value: 'jianggan' }
        ]
      }
      return districtMap[this.formData.city] || []
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal) {
          let formData = this.formData;
          formData.region = [newVal.provinceCode,newVal.cityCode]
          formData.positionType = 1
          this.formData = { ...formData, ...newVal }
          this.initFileList()
          // this.loadAirportList()
        }
      },
      immediate: true,
      deep: true
    },
    'formData.province'() {
      this.cityList = this.cityOptions
      this.formData.city = ''
      this.formData.district = ''
    },
    'formData.city'() {
      this.districtList = this.districtOptions
      this.formData.district = ''
    }
  },
  mounted() {
    this.initFileList()
  },
  methods: {
    // 加载机场列表
    async loadAirportList() {
      try {
        // 这里应该调用实际的API
        const response = await dropdownAirport(this.requestUrl);
    
        // 防止 response 或 response.data 不存在导致解构失败
        if (!response || !response.data) {
          throw new Error('响应数据为空');
        }
    
        const { code, data } = response.data;
        if (code == 200) {
          this.airportList = data;
        } else {
          this.airportList = [];
        }
      } catch (error) {
        console.error('dropdownAirport 加载机场列表失败:', error)
      }
    },

    getLabelsByValue(value) {
      if (!value) return [];
      const labels = [];
      let currentOptions = this.regionOptions;
      value.forEach((val, index) => {
        const option = currentOptions.find(item => item.code === val);
        if (option) {
          labels.push(option.name);
          // 如果不是最后一级，继续查找下一级的选项
          if (index < value.length - 1 && option.children) {
            currentOptions = option.children;
          }
        }
      });
      
      return labels;
    },

    handleRegionChange(value) {
      // 省市区变化处理
      console.log('选择的省市区:', value)
      let lableValue = this.getLabelsByValue(value);
      this.$set(this.formData, 'provinceName', lableValue[0]);
      this.$set(this.formData, 'cityName', lableValue[1]);
      this.$set(this.formData, 'provinceCode', value[0]);
      this.$set(this.formData, 'cityCode', value[1]);
    },
    
    initFileList() {
      // 初始化收费屏据照片
      if (this.formData.feeScreenPhoto) {
        this.feeScreenFileList = [{
          name: 'fee-screen.jpg',
          url: this.formData.feeScreenPhoto
        }]
      }
      
      // 初始化停车场照片
      if (this.formData.parkingPhotos && this.formData.parkingPhotos.length > 0) {
        this.parkingPhotosFileList = this.formData.parkingPhotos.map((url, index) => ({
          name: `parking-photo-${index + 1}.jpg`,
          url: url
        }))
      }
    },
    
    onProvinceChange() {
      this.cityList = this.cityOptions
      this.formData.city = ''
      this.formData.district = ''
    },
    
    getCurrentLocation() {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            this.formData.longitude = position.coords.longitude.toFixed(6)
            this.formData.latitude = position.coords.latitude.toFixed(6)
            this.$message.success('位置获取成功')
          },
          (error) => {
            this.$message.error('位置获取失败，请手动输入或使用地图选择')
          }
        )
      } else {
        this.$message.error('浏览器不支持地理位置获取')
      }
    },
    
    openMapSelector() {
      this.mapDialogVisible = true
      this.$nextTick(() => {
        this.initMap()
      })
    },
    
    initMap() {
      // 这里可以集成百度地图、高德地图等
      // 示例代码，实际需要根据使用的地图API进行调整
      this.$message.info('地图功能需要集成具体的地图API')
    },
    
    confirmLocation() {
      if (this.selectedLocation) {
        this.formData.longitude = this.selectedLocation.lng.toFixed(6)
        this.formData.latitude = this.selectedLocation.lat.toFixed(6)
        this.mapDialogVisible = false
        this.$message.success('位置选择成功')
      } else {
        this.$message.warning('请先在地图上选择位置')
      }
    },
    
    beforeUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return true
    },
    
    onFeeScreenSuccess(response, file) {
      this.formData.feeScreenPhoto = response.url || URL.createObjectURL(file.raw)
      this.$message.success('收费屏据照片上传成功')
    },
    
    onFeeScreenRemove() {
      this.formData.feeScreenPhoto = ''
    },
    
    onParkingPhotoSuccess(response, file) {
      const url = response.url || URL.createObjectURL(file.raw)
      this.formData.parkingPhotos.push(url)
      this.$message.success('停车场照片上传成功')
    },
    
    onParkingPhotoRemove(file) {
      const index = this.parkingPhotosFileList.findIndex(item => item.uid === file.uid)
      if (index > -1) {
        this.formData.parkingPhotos.splice(index, 1)
      }
    },
    
    handleSave() {
      this.$refs.basicForm.validate((valid) => {
        if (valid) {
          this.saving = true
          // 模拟保存过程
          setTimeout(() => {
            this.saving = false
            this.$emit('save', this.formData)
          }, 400)
        } else {
          this.$message.error('请完善必填信息')
        }
      })
    },
  }
}
</script>

<style lang="scss" scoped>
// 使用内联样式，避免SCSS变量冲突

.parking-basic-info {
  // 表单区域分组
  .form-section {
    margin-bottom: 32px;
    padding: 24px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #f0f2f5;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 20px 0;
      padding-bottom: 12px;
      border-bottom: 2px solid #f0f2f5;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      i {
        margin-right: 8px;
        color: #409EFF;
        font-size: 18px;
      }
    }
  }

  // 表单整体样式
  ::v-deep .el-form {
    .el-form-item {
      margin-bottom: 20px;

      .el-form-item__label {
        font-weight: 500;
        color: #303133;
        line-height: 1.6;
        padding-bottom: 4px;
        text-align: right;
        padding-right: 16px;
        // 确保标签对齐
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 40px;
      }

      .el-form-item__content {
        position: relative;
        display: flex;
        align-items: center;
        min-height: 40px;

        .unit-text {
          margin-left: 8px;
          color: #909399;
          font-size: 14px;
          font-weight: 500;
          padding: 4px 8px;
          background: #f5f7fa;
          border-radius: 4px;
          white-space: nowrap;
        }
      }

      // 输入框样式
      .el-input {
        .el-input__inner {
          border-radius: 6px;
          border: 1px solid #e4e7ed;
          transition: all 0.3s ease;
          font-size: 14px;
          height: 40px;
          line-height: 40px;

          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }

          &:hover {
            border-color: #66b1ff;
          }
        }

        .el-input__suffix {
          .el-button {
            height: 32px;
            padding: 0 12px;
            font-size: 12px;
            border-radius: 4px;
            margin-right: 8px;
          }
        }
      }

      // 文本域样式
      .el-textarea {
        .el-textarea__inner {
          border-radius: 6px;
          border: 1px solid #e4e7ed;
          transition: all 0.3s ease;
          font-size: 14px;
          line-height: 1.6;
          resize: vertical;

          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }

          &:hover {
            border-color: #66b1ff;
          }
        }
      }

      // 选择器样式
      .el-select,
      .el-cascader {
        width: 100%;

        .el-input__inner {
          height: 40px;
          line-height: 40px;
        }
      }
    }
  }

  // 上传区域样式
  .upload-section {
    margin-top: 16px;

    .upload-tip {
      margin-top: 12px;
      padding: 12px 16px;
      background: linear-gradient(90deg, #f0f9ff 0%, #e0f2fe 100%);
      border-left: 4px solid $primary-color;
      border-radius: 6px;
      color: $text-secondary;
      font-size: 13px;
      line-height: 1.6;

      &::before {
        content: '💡';
        margin-right: 8px;
      }
    }
  }

  // 收费屏据和停车场照片上传
  .fee-screen-upload,
  .parking-photos-upload {
    ::v-deep .el-upload {
      border: 2px dashed #d9d9d9;
      border-radius: $border-radius;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: $transition;
      background: #fafbfc;

      &:hover {
        border-color: $primary-color;
        background: rgba(64, 158, 255, 0.05);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
      }

      .el-icon-plus {
        font-size: 28px;
        color: #c0c4cc;
        transition: $transition;
      }

      &:hover .el-icon-plus {
        color: $primary-color;
        transform: scale(1.1);
      }
    }

    ::v-deep .el-upload-list {
      .el-upload-list__item {
        transition: $transition;
        border-radius: $border-radius;
        border: 1px solid #f0f2f5;
        margin: 8px;

        &:hover {
          transform: scale(1.02);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .el-upload-list__item-actions {
          background: rgba(0, 0, 0, 0.7);
          border-radius: 6px;

          .el-upload-list__item-preview,
          .el-upload-list__item-delete {
            color: white;
            font-size: 16px;

            &:hover {
              color: $primary-color;
            }
          }
        }
      }
    }
  }

  // 表单操作区域
  .form-actions {
    text-align: center;
    margin-top: 40px;
    padding: 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: $border-radius;
    border: 1px solid #f0f2f5;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .el-button {
      margin: 0 12px;
      padding: 12px 32px;
      border-radius: 6px;
      font-weight: 500;
      font-size: 14px;
      transition: $transition;

      &.el-button--primary {
        background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
        border: none;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
        }

        &:active {
          transform: translateY(0);
        }

        &.is-loading {
          transform: none;
        }
      }

      &:not(.el-button--primary) {
        border: 1px solid #e4e7ed;
        color: $text-regular;

        &:hover {
          border-color: $primary-color;
          color: $primary-color;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  // 地图容器
  .map-container {
    width: 100%;
    height: 400px;
    border: 1px solid #dcdfe6;
    border-radius: $border-radius;
    overflow: hidden;
    position: relative;
    background: #f5f7fa;

    &::after {
      content: '地图加载中...';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: $text-secondary;
      font-size: 16px;
    }
  }

  // 地图弹窗样式
  ::v-deep .el-dialog {
    border-radius: $border-radius;

    .el-dialog__header {
      background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
      border-bottom: 1px solid #f0f2f5;
      padding: 20px 24px;

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: $text-primary;
      }
    }

    .el-dialog__body {
      padding: 24px;
    }

    .el-dialog__footer {
      padding: 16px 24px;
      background: #f8f9fa;
      border-top: 1px solid #f0f2f5;

      .el-button {
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: 500;

        &.el-button--primary {
          background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
          border: none;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    ::v-deep .el-form {
      .el-row {
        .el-col {
          margin-bottom: 16px;
        }
      }
    }

    .form-actions {
      padding: 20px 16px;

      .el-button {
        margin: 8px 6px;
        padding: 10px 20px;
        font-size: 13px;
      }
    }

    .upload-section {
      .upload-tip {
        font-size: 12px;
        padding: 10px 12px;
      }
    }
  }

  // 特殊输入框样式
  .el-input-group {
    .el-input-group__append {
      background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
      border-color: #e4e7ed;
      color: $text-regular;
      font-weight: 500;

      .el-button {
        background: transparent;
        border: none;
        color: $primary-color;
        font-weight: 500;

        &:hover {
          background: rgba(64, 158, 255, 0.1);
        }
      }
    }
  }

  // 级联选择器特殊样式
  ::v-deep .el-cascader {
    .el-cascader__label {
      color: $text-regular;
    }
  }
}

// 全局覆盖样式
::v-deep .el-form-item__error {
  color: $danger-color;
  font-size: 12px;
  line-height: 1.4;
  padding-top: 4px;
}

::v-deep .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: $border-radius;
}
</style>