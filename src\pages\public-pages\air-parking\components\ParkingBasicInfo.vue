<template>
  <div class="parking-basic-info">
    <el-form :model="formData" :rules="rules" ref="basicForm" label-width="140px" size="small">
      <!-- 联系信息区域 -->
      <div class="form-section">
        <h4 class="section-title">
          <i class="el-icon-phone"></i>
          联系信息
        </h4>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="formData.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车场名称" prop="lotName">
              <el-input v-model="formData.lotName" placeholder="请输入车场名称" />
            </el-form-item>
          </el-col>
          
          
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="省市区域" prop="region">
              <el-cascader
                v-model="formData.region"
                :options="regionOptions"
                :props="{ value: 'value', label: 'label', children: 'children' }"
                placeholder="请选择省市区域"
                style="width: 100%;"
                @change="handleRegionChange"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="详细地址" prop="address">
              <el-input v-model="formData.address" placeholder="请输入详细地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="枢纽名称" prop="airportId">
              <el-select
                v-model="formData.airportId"
                placeholder="请选择枢纽名称"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="airport in airportList"
                  :key="airport.id"
                  :label="airport.name"
                  :value="airport.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 服务信息区域 -->
      <div class="form-section">
        <h4 class="section-title">
          <i class="el-icon-service"></i>
          服务信息
        </h4>
        <el-row :gutter="24">
          <el-col :span="12">
          <el-form-item label="经度" prop="longitude">
            <el-input v-model="formData.longitude" placeholder="请输入经度" step="0.000001">
              <template slot="append">
                <el-button @click="getCurrentLocation" size="mini">获取当前位置</el-button>
              </template>
            </el-input>
          </el-form-item>
          </el-col>
          <el-col :span="12">
          <el-form-item label="纬度" prop="latitude">
            <el-input v-model="formData.latitude" placeholder="请输入纬度"  step="0.000001">
              <template slot="append">
                <el-button @click="openMapSelector" size="mini">地图选择</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="停车位置" prop="positionType">
              <el-select v-model="formData.positionType" placeholder="请选择停车位置" style="width: 100%">
                <el-option
                  v-for="city in positionList"
                  :key="city.value"
                  :label="city.label"
                  :value="city.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="停车收费标准" prop="feeStandard">
              <el-input v-model="formData.feeStandard" placeholder="请输入收费标准" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="车场评分" prop="rating">
              <el-input v-model="formData.rating" placeholder="请输入车场评分" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车场评价" prop="comments">
              <el-input v-model="formData.comments" placeholder="请输入车场评价" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="车场标签" prop="tags">
              <div class="tags-container">
                <el-tag
                  v-for="(tag, index) in (formData.tags || [])"
                  :key="index"
                  closable
                  @close="removeTag(index)"
                  class="tag-item"
                  size="small"
                >
                  {{ tag }}
                </el-tag>
                <el-input
                  v-if="inputVisible"
                  ref="saveTagInput"
                  v-model="inputValue"
                  size="small"
                  class="input-new-tag"
                  @keyup.enter.native="handleInputConfirm"
                  @blur="handleInputConfirm"
                  placeholder="输入标签名称"
                />
                <el-button
                  v-else
                  class="button-new-tag"
                  size="small"
                  type="text"
                  @click="showInput"
                >
                  <i class="el-icon-plus"></i> 添加标签
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 收费屏据照片上传 -->
      <el-form-item label="收费牌照片">
        <div class="upload-section">
          <el-upload
            class="fee-screen-upload"
            :action="uploadAction"
            :before-upload="beforeUpload"
            :on-success="onFeeScreenSuccess"
            :on-remove="onFeeScreenRemove"
            :on-exceed="onFeeScreenExceed"
            :file-list="feeScreenFileList"
            list-type="picture-card"
            :limit="3"
            :multiple="true"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
          <div class="upload-tip">支持多选上传，最多3张收费屏据照片，支持jpg/png格式，大小不超过2MB</div>
        </div>
      </el-form-item>
      
      <!-- 停车场照片上传 -->
      <el-form-item label="停车场照片">
        <div class="upload-section">
          <el-upload
            class="parking-photos-upload"
            :action="uploadAction"
            :before-upload="beforeUpload"
            :on-success="onParkingPhotoSuccess"
            :on-remove="onParkingPhotoRemove"
            :on-exceed="onParkingPhotoExceed"
            :file-list="parkingPhotosFileList"
            list-type="picture-card"
            :limit="9"
            :multiple="true"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
          <div class="upload-tip">支持多选上传，最多9张停车场照片，支持jpg/png格式，大小不超过2MB</div>
        </div>
      </el-form-item>
    </el-form>
    
    <div class="form-actions">
      <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
    </div>
    
    <!-- 地图选择弹窗 -->
    <el-dialog title="选择位置" :visible.sync="mapDialogVisible" width="800px">
      <div class="map-container">
        <div id="mapContainer" style="height: 400px;"></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="mapDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmLocation">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ParkingBasicInfo',
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    airportList: {
      type: Array,
      default: () => []
    },
    regionOptions: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      saving: false,
      mapDialogVisible: false,
      selectedLocation: null,
      inputVisible: false,
      inputValue: '',
      formData: {
        lotName: '',
        airportId: '',
        district: '',
        address: '',
        longitude: '',
        latitude: '',
        phone: '',
        servicePhone: '',
        parkingSpaces: '',
        feeStandard: '',
        feeScreenPhoto: '',
        parkingPhotos: [],
        tags: []
      },
      rules: {
        lotName: [
          { required: true, message: '请输入车场名称', trigger: 'blur' }
        ],
        airportId: [
          { required: true, message: '请选择枢纽名称', trigger: 'change' }
        ],
        region: [
          { required: true, message: '请选择省份', trigger: 'change' }
        ],

        positionType: [
          { required: true, message: '请选择停车位置', trigger: 'change' }
        ],
        district: [
          { required: true, message: '请选择区县', trigger: 'change' }
        ],
        address: [
          { required: true, message: '请输入详细地址', trigger: 'blur' }
        ],
        longitude: [
          { required: true, message: '请输入经度', trigger: 'blur' },
          { pattern: /^-?((1[0-7]\d)|(\d{1,2}))(\.\d+)?$/, message: '请输入有效的经度值', trigger: 'blur' }
        ],
        latitude: [
          { required: true, message: '请输入纬度', trigger: 'blur' },
          { pattern: /^-?([1-8]?\d(\.\d+)?|90(\.0+)?)$/, message: '请输入有效的纬度值', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入联系手机', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' }
        ],
        servicePhone: [
          { required: true, message: '请输入客服电话', trigger: 'blur' }
        ],
        comments:[
          { required: true, message: '请输入车场评价', trigger: 'blur' }
        ],
        rating:[
          { required: true, message: '请输入车场评分', trigger: 'blur' }
        ],
        parkingSpaces: [
          { required: true, message: '请输入停车位数', trigger: 'blur' }
        ],
        tags: [
          { required: true, message: '请输入标签', trigger: 'blur' }
        ],
        feeStandard: [
          { required: true, message: '请输入停车收费标准', trigger: 'blur' }
        ]
      },
      positionList: [
        {value: 1, label: '室外'},
        {value: 0, label: '室内'}
      ],
      requestUrl: this.$PUBLIC_URL.AIRPORT_API, // 请求接口
      cityList: [],
      districtList: [],
      feeScreenFileList: [],
      parkingPhotosFileList: [],
      uploadAction: '#' // 实际项目中应该是真实的上传接口
    }
  },
  computed: {

  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal) {
          console.log(newVal,'  newVal');
          let formData = this.formData;
          formData.region = [newVal.provinceCode,newVal.cityCode]
          formData.positionType = 1

          // 处理标签字段：如果是字符串则转换为数组
          if (newVal.tags) {
            if (typeof newVal.tags === 'string') {
              newVal.tags = this.parseTagsFromString(newVal.tags);
            }
          }

          this.formData = { ...formData, ...newVal }
          this.initFileList()
          // this.loadAirportList()
        }
      },
      immediate: true,
      deep: true
    },
  },
  mounted() {
    this.initFileList()
  },
  methods: {
    // 标签相关方法
    removeTag(index) {
      if (this.formData.tags && Array.isArray(this.formData.tags) && index >= 0 && index < this.formData.tags.length) {
        this.formData.tags.splice(index, 1);
      }
    },

    showInput() {
      this.inputVisible = true;
      this.$nextTick(() => {
        if (this.$refs.saveTagInput && this.$refs.saveTagInput.$refs && this.$refs.saveTagInput.$refs.input) {
          this.$refs.saveTagInput.$refs.input.focus();
        }
      });
    },

    handleInputConfirm() {
      const inputValue = this.inputValue;
      if (inputValue && inputValue.trim()) {
        // 确保 tags 数组存在
        if (!this.formData.tags) {
          this.formData.tags = [];
        }
        // 检查是否已存在该标签
        if (!this.formData.tags.includes(inputValue.trim())) {
          this.formData.tags.push(inputValue.trim());
        }
      }
      this.inputVisible = false;
      this.inputValue = '';
    },

    // 处理从服务器返回的逗号分隔字符串转换为数组
    parseTagsFromString(tagsString) {
      if (!tagsString || typeof tagsString !== 'string') {
        return [];
      }
      return tagsString.split(',').map(tag => tag.trim()).filter(tag => tag);
    },

    // 将标签数组转换为逗号分隔的字符串用于提交
    formatTagsToString() {
      if (!this.formData.tags || !Array.isArray(this.formData.tags)) {
        return '';
      }
      return this.formData.tags.join(',');
    },

    // 准备提交数据的通用方法
    prepareSubmitData() {
      const submitData = { ...this.formData };

      // 将标签数组转换为逗号分隔的字符串
      if (submitData.tags && Array.isArray(submitData.tags)) {
        submitData.tags = submitData.tags.join(',');
      }

      // 可以在这里添加其他数据转换逻辑
      console.log('提交数据格式转换:', {
        原始标签: this.formData.tags,
        转换后标签: submitData.tags
      });

      return submitData;
    },

    getLabelsByValue(value) {
      console.log("value:", value);
      if (!value) return [];
      const labels = [];
      let currentOptions = this.regionOptions;
      value.forEach((val, index) => {
        const option = currentOptions.find(item => item.value === val);
        if (option) {
          labels.push(option.label);
          // 如果不是最后一级，继续查找下一级的选项
          if (index < value.length - 1 && option.children) {
            currentOptions = option.children;
          }
        }
      });
      
      return labels;
    },

    handleRegionChange(value) {
      // 省市区变化处理
      console.log('选择的省市区:', value)

      // 安全检查 value 参数
      if (!value || !Array.isArray(value)) {
        console.warn('handleRegionChange: value is not a valid array', value);
        return;
      }

      let lableValue = this.getLabelsByValue(value);

      // 安全访问数组元素，确保不会出现 undefined 错误
      this.$set(this.formData, 'provinceName', lableValue && lableValue[0] ? lableValue[0] : '');
      this.$set(this.formData, 'cityName', lableValue && lableValue[1] ? lableValue[1] : '');
      this.$set(this.formData, 'provinceCode', value[0] || '');
      this.$set(this.formData, 'cityCode', value[1] || '');
    },
    
    initFileList() {
      // 初始化收费屏据照片
      if (this.formData.feeScreenPhoto) {
        this.feeScreenFileList = [{
          name: 'fee-screen.jpg',
          url: this.formData.feeScreenPhoto
        }]
      }
      
      // 初始化停车场照片
      if (this.formData.parkingPhotos && this.formData.parkingPhotos.length > 0) {
        this.parkingPhotosFileList = this.formData.parkingPhotos.map((url, index) => ({
          name: `parking-photo-${index + 1}.jpg`,
          url: url
        }))
      }
    },
    
    getCurrentLocation() {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            this.formData.longitude = position.coords.longitude.toFixed(6)
            this.formData.latitude = position.coords.latitude.toFixed(6)
            this.$message.success('位置获取成功')
          },
          (error) => {
            this.$message.error('位置获取失败，请手动输入或使用地图选择')
          }
        )
      } else {
        this.$message.error('浏览器不支持地理位置获取')
      }
    },
    
    openMapSelector() {
      this.mapDialogVisible = true
      this.$nextTick(() => {
        this.initMap()
      })
    },
    
    initMap() {
      // 这里可以集成百度地图、高德地图等
      // 示例代码，实际需要根据使用的地图API进行调整
      this.$message.info('地图功能需要集成具体的地图API')
    },
    
    confirmLocation() {
      if (this.selectedLocation) {
        this.formData.longitude = this.selectedLocation.lng.toFixed(6)
        this.formData.latitude = this.selectedLocation.lat.toFixed(6)
        this.mapDialogVisible = false
        this.$message.success('位置选择成功')
      } else {
        this.$message.warning('请先在地图上选择位置')
      }
    },
    
    beforeUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return true
    },
    
    onFeeScreenSuccess(response, file) {
      this.formData.feeScreenPhoto = response.url || URL.createObjectURL(file.raw)
      this.$message.success('收费屏据照片上传成功')
    },
    
    onFeeScreenRemove() {
      this.formData.feeScreenPhoto = ''
    },

    // 收费屏据照片超出限制处理
    onFeeScreenExceed(files, fileList) {
      this.$message.warning(`最多只能上传3张收费屏据照片，当前已选择${fileList.length}张，本次选择了${files.length}张`)
    },

    onParkingPhotoSuccess(response, file) {
      const url = response.url || URL.createObjectURL(file.raw)
      this.formData.parkingPhotos.push(url)
      this.$message.success('停车场照片上传成功')
    },

    onParkingPhotoRemove(file) {
      const index = this.parkingPhotosFileList.findIndex(item => item.uid === file.uid)
      if (index > -1) {
        this.formData.parkingPhotos.splice(index, 1)
      }
    },

    // 停车场照片超出限制处理
    onParkingPhotoExceed(files, fileList) {
      this.$message.warning(`最多只能上传9张停车场照片，当前已选择${fileList.length}张，本次选择了${files.length}张`)
    },
    
    handleSave() {
      this.$refs.basicForm.validate((valid) => {
        if (valid) {
          this.saving = true

          // 准备提交的数据，进行格式转换
          const submitData = this.prepareSubmitData();

          // 模拟保存过程
          setTimeout(() => {
            this.saving = false
            this.$emit('save', submitData)
          }, 400)
        } else {
          this.$message.error('请完善必填信息')
        }
      })
    },
  }
}
</script>

<style lang="scss" scoped>
// 使用内联样式，避免SCSS变量冲突

.parking-basic-info {
  // 表单区域分组
  .form-section {
    margin-bottom: 32px;
    padding: 24px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #f0f2f5;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 20px 0;
      padding-bottom: 12px;
      border-bottom: 2px solid #f0f2f5;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      i {
        margin-right: 8px;
        color: #409EFF;
        font-size: 18px;
      }
    }
  }

  // 表单整体样式
  ::v-deep .el-form {
    .el-form-item {
      margin-bottom: 20px;

      .el-form-item__label {
        font-weight: 500;
        color: #303133;
        line-height: 1.6;
        padding-bottom: 4px;
        text-align: right;
        padding-right: 16px;
        // 确保标签对齐
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 40px;
      }

      .el-form-item__content {
        position: relative;
        display: flex;
        align-items: center;
        min-height: 40px;

        .unit-text {
          margin-left: 8px;
          color: #909399;
          font-size: 14px;
          font-weight: 500;
          padding: 4px 8px;
          background: #f5f7fa;
          border-radius: 4px;
          white-space: nowrap;
        }
      }

      // 输入框样式
      .el-input {
        .el-input__inner {
          border-radius: 6px;
          border: 1px solid #e4e7ed;
          transition: all 0.3s ease;
          font-size: 14px;
          height: 40px;
          line-height: 40px;

          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }

          &:hover {
            border-color: #66b1ff;
          }
        }

        .el-input__suffix {
          .el-button {
            height: 32px;
            padding: 0 12px;
            font-size: 12px;
            border-radius: 4px;
            margin-right: 8px;
          }
        }
      }

      // 文本域样式
      .el-textarea {
        .el-textarea__inner {
          border-radius: 6px;
          border: 1px solid #e4e7ed;
          transition: all 0.3s ease;
          font-size: 14px;
          line-height: 1.6;
          resize: vertical;

          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }

          &:hover {
            border-color: #66b1ff;
          }
        }
      }

      // 选择器样式
      .el-select,
      .el-cascader {
        width: 100%;

        .el-input__inner {
          height: 40px;
          line-height: 40px;
        }
      }
    }
  }

  // 上传区域样式
  .upload-section {
    margin-top: 16px;

    .upload-tip {
      margin-top: 12px;
      padding: 12px 16px;
      background: linear-gradient(90deg, #f0f9ff 0%, #e0f2fe 100%);
      border-left: 4px solid #409EFF;
      border-radius: 6px;
      color: #909399;
      font-size: 13px;
      line-height: 1.6;

      &::before {
        content: '💡';
        margin-right: 8px;
      }
    }
  }

  // 收费屏据和停车场照片上传
  .fee-screen-upload,
  .parking-photos-upload {
    ::v-deep .el-upload {
      border: 2px dashed #d9d9d9;
      border-radius: 8px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
      background: #fafbfc;

      &:hover {
        border-color: #409EFF;
        background: rgba(64, 158, 255, 0.05);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
      }

      .el-icon-plus {
        font-size: 28px;
        color: #c0c4cc;
        transition: all 0.3s ease;
      }

      &:hover .el-icon-plus {
        color: #409EFF;
        transform: scale(1.1);
      }
    }

    ::v-deep .el-upload-list {
      .el-upload-list__item {
        transition: all 0.3s ease;
        border-radius: 8px;
        border: 1px solid #f0f2f5;
        margin: 8px;

        &:hover {
          transform: scale(1.02);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .el-upload-list__item-actions {
          background: rgba(0, 0, 0, 0.7);
          border-radius: 6px;

          .el-upload-list__item-preview,
          .el-upload-list__item-delete {
            color: white;
            font-size: 16px;

            &:hover {
              color: #409EFF;
            }
          }
        }
      }
    }
  }

  // 表单操作区域
  .form-actions {
    text-align: center;
    margin-top: 40px;
    padding: 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 8px;
    border: 1px solid #f0f2f5;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .el-button {
      margin: 0 12px;
      padding: 12px 32px;
      border-radius: 6px;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.3s ease;

      &.el-button--primary {
        background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
        border: none;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
        }

        &:active {
          transform: translateY(0);
        }

        &.is-loading {
          transform: none;
        }
      }

      &:not(.el-button--primary) {
        border: 1px solid #e4e7ed;
        color: #606266;

        &:hover {
          border-color: #409EFF;
          color: #409EFF;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  // 地图容器
  .map-container {
    width: 100%;
    height: 400px;
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    background: #f5f7fa;

    &::after {
      content: '地图加载中...';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #909399;
      font-size: 16px;
    }
  }

  // 地图弹窗样式
  ::v-deep .el-dialog {
    border-radius: 8px;

    .el-dialog__header {
      background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
      border-bottom: 1px solid #f0f2f5;
      padding: 20px 24px;

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }

    .el-dialog__body {
      padding: 24px;
    }

    .el-dialog__footer {
      padding: 16px 24px;
      background: #f8f9fa;
      border-top: 1px solid #f0f2f5;

      .el-button {
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: 500;

        &.el-button--primary {
          background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
          border: none;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    ::v-deep .el-form {
      .el-row {
        .el-col {
          margin-bottom: 16px;
        }
      }
    }

    .form-actions {
      padding: 20px 16px;

      .el-button {
        margin: 8px 6px;
        padding: 10px 20px;
        font-size: 13px;
      }
    }

    .upload-section {
      .upload-tip {
        font-size: 12px;
        padding: 10px 12px;
      }
    }
  }

  // 特殊输入框样式
  .el-input-group {
    .el-input-group__append {
      background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
      border-color: #e4e7ed;
      color: #606266;
      font-weight: 500;

      .el-button {
        background: transparent;
        border: none;
        color: #409EFF;
        font-weight: 500;

        &:hover {
          background: rgba(64, 158, 255, 0.1);
        }
      }
    }
  }

  // 级联选择器特殊样式
  ::v-deep .el-cascader {
    .el-cascader__label {
      color: #606266;
    }
  }
}

// 标签相关样式
.tags-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  min-height: 32px;

  .tag-item {
    margin-right: 8px;
    margin-bottom: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    height: 28px;
    line-height: 1;
    vertical-align: middle;
  }

  .input-new-tag {
    width: 120px;
    height: 28px;
    line-height: 28px;
    vertical-align: middle;

    ::v-deep .el-input__inner {
      height: 28px;
      line-height: 28px;
      text-align: center;
      padding: 0 8px;
    }
  }

  .button-new-tag {
    height: 28px;
    line-height: 26px;
    padding: 0 12px;
    border: 1px dashed #d9d9d9;
    background: #fafafa;
    color: #666;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    vertical-align: middle;

    &:hover {
      color: #409EFF;
      border-color: #409EFF;
    }
  }
}

// 标签组件样式优化
::v-deep .el-tag {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  height: 28px !important;
  line-height: 1 !important;
  vertical-align: middle !important;
  padding: 0 8px !important;
  font-size: 12px !important;

  .el-tag__close {
    margin-left: 6px !important;
    vertical-align: middle !important;
    line-height: 1 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
}

// 标签容器内的元素对齐
::v-deep .tags-container {
  .el-tag,
  .el-input,
  .el-button {
    vertical-align: middle !important;
    margin-bottom: 0 !important;
  }

  .el-input {
    .el-input__inner {
      vertical-align: middle !important;
      display: inline-flex !important;
      align-items: center !important;
    }
  }

  .el-button {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;

    i {
      margin-right: 4px;
      line-height: 1;
    }
  }
}

// 全局覆盖样式
::v-deep .el-form-item__error {
  color: #F56C6C;
  font-size: 12px;
  line-height: 1.4;
  padding-top: 4px;
}

::v-deep .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
}
</style>