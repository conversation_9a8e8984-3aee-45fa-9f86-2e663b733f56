<template>
  <div class="aligned-form-demo">
    <div class="form-container">
      <!-- 联系信息区域 -->
      <div class="form-section">
        <div class="section-header">
          <h4 class="section-title">
            <i class="el-icon-phone"></i>
            联系信息
          </h4>
        </div>
        
        <el-form 
          :model="contactForm" 
          label-width="140px" 
          size="medium"
          class="aligned-form"
        >
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="联系电话">
                <el-input v-model="contactForm.phone" placeholder="请输入联系电话" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="会议地点">
                <el-input v-model="contactForm.meetingLocation" placeholder="请输入会议地点" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="停车位置">
                <el-select v-model="contactForm.parkingPosition" placeholder="请选择停车位置" style="width: 100%">
                  <el-option label="室内停车场" value="indoor" />
                  <el-option label="室外停车场" value="outdoor" />
                  <el-option label="地下停车场" value="underground" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="保存位置">
                <el-input v-model="contactForm.savePosition" placeholder="请输入保存位置" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 服务信息区域 -->
      <div class="form-section">
        <div class="section-header">
          <h4 class="section-title">
            <i class="el-icon-service"></i>
            服务信息
          </h4>
        </div>
        
        <el-form 
          :model="serviceForm" 
          label-width="140px" 
          size="medium"
          class="aligned-form"
        >
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="车场类型">
                <el-select v-model="serviceForm.vehicleType" placeholder="请选择" style="width: 100%">
                  <el-option label="小型车场" value="small" />
                  <el-option label="中型车场" value="medium" />
                  <el-option label="大型车场" value="large" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="接驳距离">
                <div class="input-with-unit">
                  <el-input v-model="serviceForm.distance" placeholder="如：5.6" />
                  <span class="unit-label">KM</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="接驳时长">
                <div class="input-with-unit">
                  <el-input v-model="serviceForm.duration" placeholder="如：24小时" />
                  <span class="unit-label">分钟</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="接驳地点">
                <el-input v-model="serviceForm.location" placeholder="请输入接驳地点" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="接驳时间">
                <el-input v-model="serviceForm.time" placeholder="请输入接驳时间" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 服务说明区域 -->
      <div class="form-section">
        <div class="section-header">
          <h4 class="section-title">
            <i class="el-icon-document"></i>
            服务说明
          </h4>
        </div>
        
        <el-form 
          :model="descriptionForm" 
          label-width="140px" 
          size="medium"
          class="aligned-form"
        >
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="载客说明">
                <el-input 
                  type="textarea" 
                  v-model="descriptionForm.passengerInfo" 
                  placeholder="请输入载客说明，如：最多可载客25人，载重5人"
                  :rows="4"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="温馨提示">
                <el-input 
                  type="textarea" 
                  v-model="descriptionForm.tips" 
                  placeholder="请输入温馨提示内容"
                  :rows="4"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AlignedForm',
  data() {
    return {
      contactForm: {
        phone: '',
        meetingLocation: '',
        parkingPosition: '',
        savePosition: ''
      },
      serviceForm: {
        vehicleType: '',
        distance: '',
        duration: '',
        location: '',
        time: ''
      },
      descriptionForm: {
        passengerInfo: '',
        tips: ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/parking-config-theme.scss';

.aligned-form-demo {
  padding: $spacing-xxl;
  background: $bg-secondary;
  min-height: 100vh;
  
  .form-container {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .form-section {
    margin-bottom: $spacing-xxxl;
    background: $bg-primary;
    border-radius: $border-radius-large;
    box-shadow: $box-shadow-base;
    overflow: hidden;
    
    .section-header {
      padding: $spacing-xl $spacing-xxxl;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid $border-color-lighter;
      
      .section-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: $text-primary;
        @include flex-center(row);
        justify-content: flex-start;
        
        i {
          margin-right: $spacing-md;
          color: $primary-color;
          font-size: 20px;
        }
      }
    }
    
    .aligned-form {
      padding: $spacing-xxxl;
      
      ::v-deep .el-form-item {
        margin-bottom: $spacing-xl;
        
        .el-form-item__label {
          font-weight: 500;
          color: $text-primary;
          line-height: 1.6;
          text-align: right;
          padding-right: $spacing-lg;
          // 关键：确保所有标签高度一致，垂直居中对齐
          display: flex;
          align-items: center;
          justify-content: flex-end;
          height: 40px;
          // 确保标签宽度一致
          min-width: 140px;
        }
        
        .el-form-item__content {
          // 确保内容区域也垂直居中
          display: flex;
          align-items: center;
          min-height: 40px;
          
          .input-with-unit {
            display: flex;
            align-items: center;
            width: 100%;
            
            .el-input {
              flex: 1;
            }
            
            .unit-label {
              margin-left: $spacing-md;
              color: $text-secondary;
              font-size: 14px;
              font-weight: 500;
              padding: $spacing-xs $spacing-md;
              background: $bg-tertiary;
              border-radius: $border-radius-small;
              white-space: nowrap;
              min-width: 50px;
              text-align: center;
            }
          }
        }
        
        // 输入框统一样式
        .el-input,
        .el-select,
        .el-textarea {
          .el-input__inner,
          .el-textarea__inner {
            border-radius: $border-radius-base;
            border: 1px solid $border-color-base;
            transition: $transition;
            font-size: 14px;
            
            &:focus {
              border-color: $primary-color;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
            
            &:hover {
              border-color: $primary-light;
            }
          }
          
          .el-input__inner {
            height: 40px;
            line-height: 40px;
          }
          
          .el-textarea__inner {
            line-height: 1.6;
            resize: vertical;
          }
        }
        
        // 选择器下拉箭头对齐
        .el-select {
          .el-input__suffix {
            display: flex;
            align-items: center;
            height: 40px;
          }
        }
      }
    }
  }
}

// 响应式设计
@include respond-to(mobile) {
  .aligned-form-demo {
    padding: $spacing-lg;
    
    .form-section {
      .section-header {
        padding: $spacing-lg $spacing-xl;
        
        .section-title {
          font-size: 16px;
        }
      }
      
      .aligned-form {
        padding: $spacing-xl;
        
        ::v-deep .el-form-item {
          .el-form-item__label {
            text-align: left;
            padding-right: 0;
            padding-bottom: $spacing-xs;
            height: auto;
            min-width: auto;
          }
          
          .el-form-item__content {
            .input-with-unit {
              .unit-label {
                margin-left: $spacing-sm;
                padding: $spacing-xs $spacing-sm;
                font-size: 12px;
                min-width: 40px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
