<template>
  <div>
    <el-dialog custom-class="custom-dialog dialog" width="710px" :show-close="false" :visible.sync="dialogVisible" :close-on-click-modal="false">
      <header class="fixed-code__title" slot="title" style="font-size: 18px;font-weight: bold;">服务购买
        <i class="el-icon-close dialog-header-iconfont" @click="dialogVisible=false"></i>
      </header>
      <div class="content">
        <div v-for="(item, index) in channelList" :key="index" class="service-item">
          <el-button :style="{ backgroundColor: item.checked ? '#ADD8E6' : '#D3D3D3',width: '150px' }" @click="setBtn(item)">{{ item.comPassName }}</el-button>
          <span>开始时间</span>
          <el-date-picker
            v-model="item.buyTime"
            type="date"
            :clearable="false"
            value-format="timestamp"
            placeholder="选择日期"
            style="width: 150px;"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
          <span>购买</span>
          <el-input-number @change="queryPrice" v-model="item.count" :min="1" size="small" />
          <span>月</span>
          <el-checkbox v-model="item.checked" @change="queryPrice" style="margin-left: 10px;"></el-checkbox>
        </div>
      </div>
      <div class="footer-btn">
        <span>服务费合计：{{ totalCost }} 元</span>
        <el-button type="primary" @click="toPay">去付费</el-button>
      </div>
    </el-dialog>
    <canvas id="canvas" style="display:none"></canvas>
    <canvas id="img" style="display:none"></canvas>
    <el-dialog custom-class="custom-dialog confirm-dialog" width="400px" :show-close="false" :visible.sync="imgVisable" :close-on-click-modal="false">
      <header class="fixed-code__title" slot="title" style="font-size: 18px;font-weight: bold;">支付
        <i class="el-icon-close dialog-header-iconfont" @click="closeFn"></i>
      </header>
      <span style="font-weight:bold">请扫码支付{{totalCost}}元</span>
      <img :src="qrsrc" style="width: 200px;height: 200px;">
      <footer slot="footer" class="dialog-footer">
        <el-button @click="closeFn" class="dialog-footer-btn" style="background:#3C75CF;color:#fff">返回</el-button>
      </footer>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false,
      channelList: [],
      imgVisable: false,
      unitPrice: 0,
      rowData: {},
      qrsrc: '',
      comid:null,
      tradeNo:null,
      timer: null,
      totalCost:0,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      }
    }
  },
  computed: {
  },
  methods: {
    show(data) {
      this.rowData = data;
      this.comid = data.comId;
      this.totalCost = 0;
      this.getChannelList(data)
    },
    queryPrice(){
      let number = 0;
      this.tradeNo = null;
      this.totalCost = 0;
      console.log(JSON.stringify(this.channelList));
      this.channelList.forEach(item => {
          if(item.checked){
              number += item.count;
          }
      });
      const params = {
        serviceId: 13,
        comid: this.comid,
        number: number
      }
      this.$axios.post('/valueServicePrice/queryEomsAuthorizer', params).then(res => {
        const { data, code } = res.data
        if (code === 200) {
          this.totalCost = data.price;
          this.tradeNo = data.tradeNo;
        } else {
          this.$message.warning(data.message);
        }
      })
    },
    getChannelList(data) {
      const params = {
        type: 2,
        comId: data ? data.comId : sessionStorage.getItem('comid'),
        eomsId: (data && data.eomsId !== 'undefined') ? data.eomsId : '-1'
      }
      this.$axios.post('/eoms/findEomsChannelConfig', params).then(res => {
        const { data, code } = res.data
        if (code === 200 && data.rows && data.rows.length) {
          this.unitPrice = data.price || 0
          this.channelList = data.rows.map(item => {
            return {
              ...item,
              count: 1,
              checked: false,
              buyTime: item.buyTime ? item.buyTime * 1000 : new Date().getTime()
            }
          })
          this.dialogVisible = true
        } else {
          this.$message.warning('暂无可购买服务项')
        }
      })
    },
    setBtn(val) {
      val.checked = !val.checked
      this.queryPrice();
    },
    toPay() {
      if (!this.channelList.some(item => item.checked)) {
        this.$message.warning('请选择服务项')
        return
      }
      clearInterval(this.timer)
      const list = this.channelList.filter(o => o.checked)
      let channelVoList = list.map(item => {
        return {
          id: item.id,
          comPassId: item.comPassId,
          unit: item.count,
          buyTime: item.buyTime / 1000
        }
      })
      const row = this.rowData
      const params = {
        loginuin: sessionStorage.getItem('loginuin'),
        count: list.reduce((total, item) => total + item.count, 0),
        total: this.totalCost,
        eomsId: row ? row.eomsId : '-1',
        cityId: row ? row.cityId : '',
        comId: row ? row.comId : sessionStorage.getItem('comid'),
        tradeNo:this.tradeNo,
        channelVoList
      }
      this.$axios.post('/eoms/channel/tobuy', params).then(res => {
        const data = res.data
        if (data.code === 200) {
          if (data.data) {
            this.getQrcode(data.data)
            this.timer = setInterval(() => {
              this.getPayState(data.data)
            }, 2000)
            this.imgVisable = true
          } else {
            this.dialogVisible = false
            this.$message.success('购买成功')

          }
        } else {
          this.$message.error(data.message || '购买失败')
        }
      })
    },
    closeFn() {
      clearInterval(this.timer)
      this.imgVisable = false
    },
    getPayState(data) {
      const match = data.match(/[?&]trade_no=([^&]+)/);
      this.$axios.post('/message/getcodestate', this.$qs.stringify({trade_no: match[1]})).then(res => {
        const data = res.data
        if (data.state === 2) {
          // 支付中
        } else if (data.state === 1) {
          //支付成功
          clearInterval(this.timer)
          this.imgVisable = false
          this.dialogVisible = false
          this.$message.success('支付成功')
          this.$emit('refresh')
        } else {
          // 支付失败
          clearInterval(this.timer)
          this.imgVisable = false
          this.$message.error('支付失败')
        }
      })
    }, 
    getQrcode(url) {
      var canvas = document.getElementById('canvas')
      this.QRCode.toCanvas(canvas, url, { errorCorrectionLevel: 'H' }, function(error) {
        if (error) {
          // doing
        } else {
          //
        }
      })
      var context = canvas.getContext('2d')
      var imageData = context.getImageData(0, 0, canvas.width, canvas.height)
      var img = document.getElementById('img')
      img.width = canvas.width
      img.height = canvas.height
      var context2 = img.getContext('2d')
      context2.fillStyle = 'white'
      context2.fillRect(0, 0, canvas.width, canvas.height)
      context2.putImageData(imageData, 0, 0)
      context2.font = 'bold 10px 微软雅黑'
      context2.fillStyle = 'black'
      var url = img.toDataURL('image/png')
      this.qrsrc = url
    }
  }
}
</script>

<style lang="scss" scoped>
.fixed-code__title {
  text-align: center;
}
.service-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.service-item > * {
  margin: 0 5px;
}
/deep/.el-dialog.custom-dialog.confirm-dialog  {
  margin-top: 25vh !important;
  .el-dialog__body {
    text-align: center;
  }
}
/deep/.el-dialog.custom-dialog.dialog {
  height: 510px;
  .el-dialog__body {
    padding: 30px 25px;
  }
}
.content {
  height: 340px;
  overflow: auto;
}
.footer-btn {
  text-align: center;
  margin-top: 20px;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 5px;
  span {
    font-weight: bold;
    font-size: 18px;
    margin-right: 30px;
  }
}
</style>
