const md5 = require('js-md5')
const signKey = 'bolinksignkey'

/**
 * @desc 支付金额签名
 */
class Sign {
  constructor(params) {
    this.params = params || {}
  }
  getSignature() {
    const data = this.nonEmpty()
    console.log('data: ', data)
    const sortData = this.sortASCII(data)
    console.log('sortData: ', sortData)
    const formatUrl = this.formatUrlEncode(sortData,1, null, false) +`&key=${md5(signKey).toLowerCase()}`
    console.log('formatUrl: ', formatUrl)
    return md5(formatUrl).toUpperCase()
  }
  // 非空处理
  nonEmpty() {
    const metadata = this.params
    const obj = {}
    if (metadata instanceof FormData) {
      for (let [key, value] of metadata) {
        if (metadata.get(key) !== undefined && metadata.get(key) !== null && metadata.get(key) !== '') {
          obj[key] = metadata.get(key)
        }
      }
    } else {
      for (let key in metadata) {
        if (metadata.hasOwnProperty(key)) {
          if (metadata[key] !== undefined && metadata[key] !== null && metadata[key] !== '') {
            obj[key] = metadata[key]
          }
        }
      }
    }

    return obj
  }
  // 按照ascii 升序排列
  sortASCII(obj) {
    const arr = []
    Object.keys(obj).forEach(item => arr.push(item))
    const sortArr = arr.sort()
    const sortObj = {}
    for(let i in sortArr) {
      sortObj[sortArr[i]] = obj[sortArr[i]]
    }
    return sortObj
  }
  // &fu
  /**
   * @desc 格式化json 为参数字符串
   * @param param json
   * @param idx 循环几次用&拼接
   * @param key { String } 参数字符串的前缀
   * @param encode { Boolean } url编码
   * @return {string}
   */
  formatUrlEncode(param, idx, key, encode) {
    if (param === undefined || param === null) { return '' }
    let paramStr = ''
    const t = typeof (param)
    if (t === 'string' || t === 'number' || t === 'boolean') {
      const one_is = idx < 3 ? '' : '&'
      paramStr += one_is + key + '=' + ((encode === null || encode) ? encodeURIComponent(param) : param)
    } else {
      for (const i in param) {
        const k = key === null ? i : key + (param instanceof Array ? '[' + i + ']' : '.' + i)
        if (param[i] !== undefined) {
          idx++
          paramStr += this.formatUrlEncode(param[i], idx, k, encode)
        }
      }
    }
    return paramStr
  }
}
export default Sign
