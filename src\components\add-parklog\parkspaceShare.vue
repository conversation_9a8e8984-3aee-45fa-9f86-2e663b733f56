<template>
  <div class="parkShareSet-container">
    <div class="parkShareSet-item">
      <div class="title">车场共享分账</div>
      <div class="parkShareSetFrom-info">
        <el-form v-loading="loading" ref="ledgerSharedForm" label-width="100px" class="share-setting-form">
          <el-form-item prop="" label="">
            <!-- <div class="join-title">业主</div> -->
            <el-table :data="ledgertList" v-if="userIdentity == 1" border>
                <el-table-column property="id" label="分账主体" align="center">
                    <template slot-scope="scope">
                      {{scope.row.entityInfo}}
                    </template>
                </el-table-column>
                <el-table-column property="name" label="分账比例%" align="center">
                    <template slot-scope="scope">
                      <el-input-number v-model="scope.row.entityratio" :precision="2" :min="0" :max="100"></el-input-number>
                    </template>
                </el-table-column>
            </el-table>
            <!-- <div class="join-title">商户</div> -->
            <el-table :data="ledgertLists" v-if="userIdentity == 2" border>
                <el-table-column property="id" label="分账主体" align="center">
                    <template slot-scope="scope">
                      {{scope.row.entityInfo}}
                    </template>
                </el-table-column>
                <el-table-column property="name" label="分账比例%" align="center">
                    <template slot-scope="scope">
                      <el-input-number v-model="scope.row.entityratio" :precision="2" :min="0" :max="100"></el-input-number>
                    </template>
                </el-table-column>
            </el-table>
            <!-- <div class="join-title">租客</div> -->
            <el-table :data="ledgertListl" v-if="userIdentity == 3" border>
                <el-table-column property="id" label="分账主体" align="center">
                    <template slot-scope="scope">
                      {{scope.row.entityInfo}}
                    </template>
                </el-table-column>
                <el-table-column property="name" label="分账比例%" align="center">
                    <template slot-scope="scope">
                      <el-input-number v-model="scope.row.entityratio" :precision="2" :min="0" :max="100"></el-input-number>
                    </template>
                </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" @click="ledgerSharedConfirm" :loading="ledgerSharedLoading" style="width: 90px;">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="parkShareSet-item">
      <div class="title">车场共享收费</div>
      <div class="parkShareSetFrom-info">
        <el-form v-loading="loading" ref="parkFeesForm" label-width="150px" :model="parkFeesForm" :rules="parkFeesFormRules">
          <el-form-item prop="" label="收费标准">
            <div class="">
              <el-switch
                v-model="multiples"
                active-text="多选"
                inactive-text="单选"
                @change="weekChanges">
              </el-switch>
            </div>
            <div v-if="multiples">
              <el-checkbox-group v-model="weekdays" style="margin-bottom: 30px;">
                <el-checkbox-button v-for="item in weeksList" :label="item.label">{{item.name}}</el-checkbox-button>
              </el-checkbox-group>
            </div>
            <div v-else>
              <el-radio-group v-model="weekday" style="margin-bottom: 30px;" @change="shareFeeInquiry()">
                <el-radio-button v-for="item in weeksList" :label="item.label">{{item.name}}</el-radio-button>
              </el-radio-group>
            </div>
            <div class="join-title"><el-button size="small" @click="frameTimeJoin">添加</el-button></div>
            <div class="frameTime-tableinfo">
              <el-table :data="frameTimeList" style="width: 100%" border >
                <el-table-column property="frameTime" label="共享时段" align="center"  width="270">
                      <template slot-scope="scope">
                        <el-time-picker
                          is-range
                          :clearable="false"
                          v-model="scope.row.frameTime"
                          class="time-picker"
                          format="HH:mm"
                          value-format="HH:mm"
                          range-separator="至"
                          :picker-options="scope.row.pickerOptions"
                          style="width: 240px;"
                          start-placeholder="开始时间"
                          end-placeholder="结束时间"
                          placeholder="选择时间范围"
                          @change="handleTimeChange(scope.$index)">
                        </el-time-picker>
                      </template>
                  </el-table-column>
                  <el-table-column property="unitPrice" label="共享单价(元/15分钟)" align="center" width="200">
                      <template slot-scope="scope">
                          <el-input-number   style="width: 160px;"  v-model="scope.row.unitPrice" :precision="2" :min="0" :max="1000" :disabled="shareCharge == 0"></el-input-number>
                      </template>
                  </el-table-column>
                  <el-table-column property="maxFee" label="封顶(元)" align="center" width="200">
                      <template slot-scope="scope">
                          <el-input-number  style="width: 160px;" v-model="scope.row.maxFee" :precision="2" :min="0" :max="10000" :disabled="shareCharge == 0"></el-input-number>
                      </template>
                  </el-table-column>
                  <el-table-column label="操作" align="center" width="100">
                      <template slot-scope="scope">
                          <el-button type="text" v-if="scope.index !== 0" @click="subParamData(scope.$index)" plain size="small">删除</el-button>
                      </template>
                  </el-table-column>
              </el-table>
            </div>
          </el-form-item>
          <el-form-item prop="overtimeChargeFactor" label="超时收费倍数">
            <el-input-number v-model="parkFeesForm.overtimeChargeFactor" :precision="2" :min="0" :max="1000" :disabled="shareCharge == 0"></el-input-number>  倍
          </el-form-item>
          <el-form-item prop="freeMinutes" label="免费时长">
            <el-input-number v-model="parkFeesForm.freeMinutes" :min="0" :max="1000" :disabled="shareCharge == 0"></el-input-number> 分钟
          </el-form-item>
          <el-form-item prop="freeAfterParkMinutes" label="场内缴费后免费时长">
            <el-input-number v-model="parkFeesForm.freeAfterParkMinutes" :min="0" :max="1000" :disabled="shareCharge == 0"></el-input-number> 分钟
          </el-form-item>
          <!-- <el-form-item prop="max12hourLimit" label="每12小时限额">
            <el-radio-group v-model="parkFeesForm.max12hourLimit">
                <el-radio :label="1"><el-input-number v-model="parkFeesForm.max12hourLimitNum" :precision="2" :min="0" :max="1000"></el-input-number></el-radio>
                <el-radio :label="0">无上限</el-radio>
              </el-radio-group>
          </el-form-item> -->
          <el-form-item prop="shareStateSwitch" label="今日分享">
            <el-switch
                v-model="shareStateSwitch"
                active-text="开启"
                inactive-text="关闭">
              </el-switch>
          </el-form-item>
          <el-form-item prop="dailyLimit" label="每日限额">
            <el-radio-group v-model="parkFeesForm.dailyLimit" :disabled="shareCharge == 0">
                <el-radio :label="1"><el-input-number v-model="parkFeesForm.dailyLimitNum" :precision="2" :min="0" :max="1000" :disabled="shareCharge == 0"></el-input-number></el-radio>
                <el-radio :label="0">无上限</el-radio>
              </el-radio-group>
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" @click="parkFeesConfirm" :loading="parkFeesLoading" style="width: 90px;">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
  
</template>
<script>
export default {
  name: 'parkspaceShare',
  data() {
    return {
      loading: false,
      parkFeesLoading:false,
      multiples:false, //单选或者多选
      weeksList:[
        {
          label:'1',
          name:'周一'
        },{
          label:'2',
          name:'周二'
        },{
          label:'3',
          name:'周三'
        },{
          label:'4',
          name:'周四'
        },{
          label:'5',
          name:'周五'
        },{
          label:'6',
          name:'周六'
        },{
          label:'7',
          name:'周日'
        }
      ],
      shareStateSwitch:false,
      weekdays:['1'],
      weekday:1,
      parkFeesForm:{
        max12hourLimit:1, //每12小时限额
        dailyLimit:1, //每日限额
        dailyLimitNum:0,
        freeMinutes:'', //免费时长
        overtimeChargeFactor:'', //超时收费倍数
        freeAfterParkMinutes:'' //场内缴费后免费时长
      },
      frameTimeList:[
        {
          frameTime:['00:00', '23:59'],
          maxFee:'',
          unitPrice:'',
          pickerOptions:{
            selectableRange: '00:00:00 - 23:59:59'
          }
        }
      ],
      ledgerSharedLoading:false,
      rateLoading:false,
      parkFeesFormRules:{
        overtimeChargeFactor: [
          { required: true, message: '请输入超时收费倍数', trigger: 'blur' }
        ],
        freeMinutes: [
          { required: true, message: '请输入免费时长', trigger: 'blur' }
        ],
        freeAfterParkMinutes: [
          { required: true, message: '请输入场内缴费后免费时长', trigger: 'blur' }
        ],
      },
      rateFormFormRules: {
        time: [
          { required: true, message: '请输入时长', trigger: 'blur' }
        ],
        money: [
          { required: true, message: '请输入费用', trigger: 'blur' }
        ],
        reserveStartTime: [
          { required: true, message: '请选择预约起始', trigger: 'blur' }
        ],
        freeTime:  [
          { required: true, message: '请输入免费时长', trigger: 'blur' }
        ],
        reserveLimit: [
          { required: true, message: '请选择预约上限', trigger: 'blur' }
        ],
        reserveTimes: [
          { required: true, message: '请输入预约次数', trigger: 'blur' }
        ],
      },
      shareLoading: false,
      stateList: [
        {
          label: '先付费后离场',
          value: 0
        },
      ],
      timeyList: [
        {
          label: '15',
          value: 15
        },
        {
          label: '20',
          value: 20
        },
        {
          label: '30',
          value: 30
        },
      ],
      identityList: [
        {
          label: '共享车位预约车',
          value: 0
        },
      ],
      userIdentity:1,
      ledgertList:[
        {
          entityInfo:'车场',
          entityratio:'0',
          type:1
        },
        {
          entityInfo:'平台',
          entityratio:'0',
          type:1
        },
        {
          entityInfo:'业主',
          entityratio:'0',
          type:1
        }
      ],
      ledgertLists:[
        {
          entityInfo:'车场',
          entityratio:'0',
          type:2
        },
        {
          entityInfo:'平台',
          entityratio:'0',
          type:2
        },
        {
          entityInfo:'商户',
          entityratio:'0',
          type:2
        }
      ],
      ledgertListl:[
        {
          entityInfo:'车场',
          entityratio:'0',
          type:3
        },
        {
          entityInfo:'平台',
          entityratio:'0',
          type:3
        },
        {
          entityInfo:'租客',
          entityratio:'0',
          type:3
        }
      ],
      comid: '',
      unionId: '',
      shareCharge:1,
      parkId: '',
      parkingFrom:{},
      is4G: 0 //判断是否是纯云车场
    }
  },
  methods: {
    //车位共享收费查询
    shareFeeInquiry(parkingFrom) {
      if(parkingFrom){
        this.parkingFrom = parkingFrom;
      }else{
        parkingFrom = this.parkingFrom;
      }
      let para = { shareLotId: parkingFrom.id,comid: parkingFrom.comid,weekday:this.weekday}

      if(this.multiples){
        para = { shareLotId: parkingFrom.id,comid: parkingFrom.comid,weekdays:this.weekdays}
      }else{
        this.weekdays = [this.weekday.toString()]
      }

      this.$axios
        .post(`/parking/lot/share/feeConfig/query`, para)
        .then(res => {
          let data = res.data
          if (data.code === 200) {
            var dataInfo = data.data;
            console.log(dataInfo)
            let shareFeeConfInfo = dataInfo.parkingLotShareFeeConfig;
            this.shareCharge = dataInfo.shareCharge;
            if(shareFeeConfInfo){
              if(shareFeeConfInfo.dailyLimit > 0){
                shareFeeConfInfo.dailyLimitNum = shareFeeConfInfo.dailyLimit;
                shareFeeConfInfo.dailyLimit = 1;
              }

              if(shareFeeConfInfo.max12hourLimit > 0){
                shareFeeConfInfo.max12hourLimitNum = shareFeeConfInfo.max12hourLimit;
                shareFeeConfInfo.max12hourLimit = 1;
              }
              // this.weekday = shareFeeConfInfo.weekday;
              if(this.multiples){
                this.weekdays = [shareFeeConfInfo.weekday.toString()];
              }
              if(shareFeeConfInfo.shareState == 1){
                this.shareStateSwitch = true
              }else{
                this.shareStateSwitch = false
              }
              this.parkFeesForm = shareFeeConfInfo;
              let parkLotShareFeeTimeInfo = dataInfo.parkingLotShareFeeTimeSlots;
              parkLotShareFeeTimeInfo.forEach(item => {
                item.frameTime = [item.btime,item.etime]
              });
              console.log(parkLotShareFeeTimeInfo)
              if(parkLotShareFeeTimeInfo.length > 0){
                this.frameTimeList = parkLotShareFeeTimeInfo
              }else {
                this.frameTimeList = [
                  {
                    frameTime:['00:00', '23:59'],
                    maxFee:'',
                    unitPrice:'',
                    pickerOptions:{
                      selectableRange: '00:00:00 - 23:59:59'
                    }
                  }
                ]
              }
            }else{
              this.shareCharge = 1;
              this.parkFeesForm = {
                max12hourLimit:1, //每12小时限额
                dailyLimit:1, //每日限额
                dailyLimitNum:0,
                freeMinutes:'', //免费时长
                overtimeChargeFactor:'', //超时收费倍数
                freeAfterParkMinutes:'' //场内缴费后免费时长
              },
              this.shareStateSwitch = false;
              this.frameTimeList = [
                {
                  frameTime:['00:00', '23:59'],
                  maxFee:'',
                  unitPrice:'',
                  pickerOptions:{
                    selectableRange: '00:00:00 - 23:59:59'
                  }
                }
              ]
            } 
          }
        })
        .catch(err => {
          console.log(err)
          this.loading = false
        })
    },

    weekChanges(e){
      // this.weekday = 1;
      console.log(this.weekdays)
      if(this.weekdays.length > 1){
        this.weekdays = ['1']
      }
      this.shareFeeInquiry()
    },

    handleTimeChange(index) {
      const selectedTimeRange = this.frameTimeList[index].frameTime;
      // if(index == 0){
      //   if(selectedTimeRange[0] !== '00:00'){
      //     if(this.frameTimeList.length > 1){
      //       const nextTimeRange = this.frameTimeList[index + 1].frameTime;
      //       this.frameTimeList[index].frameTime = ['00:00', nextTimeRange[0]];
      //     }else {
      //       this.frameTimeList[index].frameTime = ['00:00', '23:59'];
      //     } 
      //   } 
      // }
      if (index > 0) {
        const prevTimeRange = this.frameTimeList[index - 1].frameTime;
        if (selectedTimeRange[0] !== prevTimeRange[1]) {
          // 时间不连续，给出提示或者进行其他处理
          this.$message.error('选择的时间必须与前一个时间范围连续');
          // 可以选择将当前选择的时间重置，例如：
          this.frameTimeList[index].frameTime = [prevTimeRange[1], selectedTimeRange[1]];
        }
      }
      if (index < this.frameTimeList.length - 1) {
        const nextTimeRange = this.frameTimeList[index + 1].frameTime;
        if (selectedTimeRange[1] !== nextTimeRange[0]) {
          // 时间不连续，给出提示或者进行其他处理
          this.$message.error('选择的时间必须与后一个时间范围连续');
          // 可以选择将当前选择的时间重置，例如：
          this.frameTimeList[index].frameTime = [selectedTimeRange[0], nextTimeRange[0]];
        }
      }
    },

    //时间段新增
    frameTimeJoin() {
      // 获取最后一个时间段的结束时间
      const lastEndTime = this.frameTimeList[this.frameTimeList.length - 1].frameTime[1]
      // 新时间段的开始时间必须是最后一个时间段的结束时间
      const newStartTime = lastEndTime;
      const newEndTime = '23:59';

      if (newStartTime === newEndTime) {
        this.$message.warning('已达到最大时间范围，无法再添加');
        return;
      }
      const unitPrice = this.frameTimeList[this.frameTimeList.length - 1].unitPrice;
      const maxFee = this.frameTimeList[this.frameTimeList.length - 1].maxFee;
      this.frameTimeList.push({
        frameTime: [newStartTime, newEndTime],
        unitPrice,
        maxFee,
        pickerOptions: {
          selectableRange: `${newStartTime}:00 - 23:59:59`
        }
      });
    },

    /* 删除时间段（行） */
    subParamData(index) {
      if (index > 0) {
        this.frameTimeList.splice(index, 1);
        // 更新最后一个时间段的结束时间为 23:59
        if (this.frameTimeList.length > 0) {
          let frameTimeList =  JSON.parse(JSON.stringify(this.frameTimeList)) 
          frameTimeList[frameTimeList.length - 1].frameTime[1] = '23:59';
          this.frameTimeList = frameTimeList;
          console.log(this.frameTimeList[this.frameTimeList.length - 1])
        }
      }
    },

    //车场共享分账查询
    feesSharedQuiry(parkingFrom) {
      if(parkingFrom){
        this.parkingFrom = parkingFrom;
      }else{
        parkingFrom = this.parkingFrom;
      }
      let para = {comid: parkingFrom.comid, shareLotId: parkingFrom.id}
      this.$axios
        .post(`/parking/lot/share/reserveProfit/query`, para)
        .then(res => {
          let data = res.data
          console.log(data,'-data')
          if (data.code === 200) {
            if(data.data !== null){
              let dataInfo = data.data.parkingLotShareReserveProfits;
              let userIdentity = data.data.userIdentity;
              this.userIdentity = userIdentity;
              // let childInfo = dataInfo.filter(item => item.type == userIdentity)
              if(dataInfo){
                dataInfo.forEach(item => {
                  if(item.type == 1){
                    this.ledgertList = [
                      {
                        id:item.id || '',
                        entityInfo:'车场',
                        entityratio:item.parkRate || 0,
                        type:1
                      },
                      {
                        id:item.id || '',
                        entityInfo:'平台',
                        entityratio:item.platformRate || 0,
                        type:1
                      },
                      {
                        id:item.id || '',
                        entityInfo:'业主',
                        entityratio:item.otherRate || 0,
                        type:1
                      }
                    ]
                  }

                  if(item.type == 2){
                    this.ledgertLists = [
                      {
                        id:item.id || '',
                        entityInfo:'车场',
                        entityratio:item.parkRate || 0,
                        type:2
                      },
                      {
                        id:item.id || '',
                        entityInfo:'平台',
                        entityratio:item.platformRate || 0,
                        type:2
                      },
                      {
                        id:item.id || '',
                        entityInfo:'商户',
                        entityratio:item.otherRate || 0,
                        type:2
                      }
                    ]
                  }

                  if(item.type == 3){
                    this.ledgertListl = [
                      {
                        id:item.id || '',
                        entityInfo:'车场',
                        entityratio:item.parkRate || 0,
                        type:3
                      },
                      {
                        id:item.id || '',
                        entityInfo:'平台',
                        entityratio:item.platformRate || 0,
                        type:3
                      },
                      {
                        id:item.id || '',
                        entityInfo:'租客',
                        entityratio:item.otherRate || 0,
                        type:3
                      }
                    ]
                  }
                });
              }
            }else{
              if(parkingFrom.ownerIdentity){
                this.userIdentity = parkingFrom.ownerIdentity + 1;
              }else {
                this.userIdentity = 1;
              }
              console.log(this.userIdentity)

              if(this.userIdentity == 1){
                    this.ledgertList = [
                      {
                        entityInfo:'车场',
                        entityratio:'0',
                        type:1
                      },
                      {
                        entityInfo:'平台',
                        entityratio:'0',
                        type:1
                      },
                      {
                        entityInfo:'业主',
                        entityratio:'0',
                        type:1
                      }
                    ]
                  }else if(this.userIdentity == 2){
                    this.ledgertLists = [
                      {
                        entityInfo:'车场',
                        entityratio:'0',
                        type:2
                      },
                      {
                        entityInfo:'平台',
                        entityratio:'0',
                        type:2
                      },
                      {
                        entityInfo:'商户',
                        entityratio:'0',
                        type:2
                      }
                    ]
                  }else if(this.userIdentity == 3){
                    this.ledgertListl = [
                      {
                        entityInfo:'车场',
                        entityratio:'0',
                        type:3
                      },
                      {
                        entityInfo:'平台',
                        entityratio:'0',
                        type:3
                      },
                      {
                        entityInfo:'租客',
                        entityratio:'0',
                        type:3
                      }
                    ]
                  }
            }
            
            
          }
        })
        .catch(err => {
          console.log(err)
          this.loading = false
        })
    },

    //车场共享收费配置
    parkFeesConfirm(){
      console.log(this.parkFeesForm)
      this.$refs.parkFeesForm.validate(valid => {
        if (valid) {
          this.parkFeesLoading = true;
          console.log(this.frameTimeList)
          let timeSlot = []
          let frameTimeListInfo = this.frameTimeList;
          frameTimeListInfo.forEach(item => {
            timeSlot.push({
              id:item.id,
              btime:item.frameTime[0],
              etime:item.frameTime[1],
              unitPrice:item.unitPrice,
              maxFee:item.maxFee
            })
          });
          if(this.shareStateSwitch == true){
            this.parkFeesForm.shareState = 1
          }else{
            this.parkFeesForm.shareState = 0
          }
          let para = {
            ...this.parkFeesForm,
            comid: this.parkingFrom.comid,
            shareLotId: this.parkingFrom.id,
            timeSlot,
          }
          if(this.multiples){
            para.weekdays = this.weekdays
            delete para.weekday
          }else{
            para.weekday = this.weekday
          }
          if(para.dailyLimit == 1){
            para.dailyLimit = para.dailyLimitNum
          }
          if(para.max12hourLimit == 1){
            para.max12hourLimit = para.max12hourLimitNum
          }
          this.parkFeesLoading = false;
          console.log(para)

          this.$axios
            .post('/parking/lot/share/feeConfig/set', para)
            .then(res => {
              let data = res.data
              console.log(data)
              this.rateLoading = false;
              if (data.code === 200) {
                this.$message.success(data.message);
                this.weekday = 1;
                this.weekdays = ['1'];
                this.multiples = false;
                this.shareFeeInquiry()
              } else {
                this.$message.error(data.message);
              }
            })

            .catch(err => {
              console.log(err)
            })
        }
      });
    },

    shareCancel() {
      // this.$refs['parkShareForm'].resetFields();
      this.shareSearch(this.unionId, this.parkId)
    },
    
    ledgerSharedConfirm() {
      let infoList = []
      let ledgertList = this.ledgertList;
      let ledgertLists = this.ledgertLists;
      let ledgertListl = this.ledgertListl;
      if(this.userIdentity == 1){
        let entityratioNum = Number(ledgertList[0].entityratio) + Number(ledgertList[1].entityratio) + Number(ledgertList[2].entityratio)
        if(entityratioNum == 100){
          infoList.push({
            id:ledgertList[0].id || '',
            parkRate: ledgertList[0].entityratio,
            platformRate: ledgertList[1].entityratio,
            otherRate: ledgertList[2].entityratio,
            type:1
          })
        }else{
          this.$message.error('分账比例必须等于100');
          return;
        }
      }

      if(this.userIdentity == 2){
        let entityratioNums = Number(ledgertLists[0].entityratio) + Number(ledgertLists[1].entityratio) + Number(ledgertLists[2].entityratio)
        if(entityratioNums == 100){
          infoList.push({
            id:ledgertLists[0].id || '',
            parkRate: ledgertLists[0].entityratio,
            platformRate: ledgertLists[1].entityratio,
            otherRate: ledgertLists[2].entityratio,
            type:2
          })
        }else{
          this.$message.error('分账比例必须等于100');
          return;
        }
      }

      if(this.userIdentity == 3){
        let entityratioNuml = Number(ledgertListl[0].entityratio) + Number(ledgertListl[1].entityratio) + Number(ledgertListl[2].entityratio)
        if(entityratioNuml == 100){
          infoList.push({
            id:ledgertListl[0].id || '',
            parkRate: ledgertListl[0].entityratio,
            platformRate: ledgertListl[1].entityratio,
            otherRate: ledgertListl[2].entityratio,
            type:3
          })
        }else{
          this.$message.error('分账比例必须等于100');
          return;
        }
      }
      
      this.ledgerSharedLoading = true;
      let para = {
        comid: this.parkingFrom.comid,
        shareLotId: this.parkingFrom.id,
        info:infoList
      }
      
      console.log(para)
      this.$axios
        .post('/parking/lot/share/reserveProfit/set', para)
        .then(res => {
          let data = res.data
          console.log(data)
          this.ledgerSharedLoading = false;
          if (data.code === 200) {
            this.$message.success(data.message);
            this.feesSharedQuiry()
          } else {
            this.$message.error(data.message);
          }
        })

        .catch(err => {
          console.log(err)
        })
    }
  },
  activated() {
    // this.feesSharedQuiry()
    this.shareFeeInquiry()
  },
}
</script>
<style lang="scss" scoped>
.frameTime-tableinfo{
  width: 470px;
  overflow-y: auto;
}
.parkShareSet-container{
  height: 87vh;
  overflow: auto;
  .parkShareSet-item{
    margin-bottom: 20px;
  }
  .title{
    font-size: 18px;
    color: #000;
  }
}
.parkShareSetFrom-info{
  max-width: 1000px;
  overflow: auto;
}
.share-setting-form {
  width: 600px;
}
</style>