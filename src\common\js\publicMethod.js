export const dateFormat = function(time) {
  let nTime = new Date(time * 1000);
  let month = nTime.getMonth() + 1;
  let day = nTime.getDate();
  let hour = nTime.getHours();
  let minute = nTime.getMinutes();
  let second = nTime.getSeconds();
  return nTime.getFullYear() + '-' + (month < 10 ? ('0' + month) : month) + '-' + (day < 10 ? ('0' + day) : day) + ' ' + (hour < 10 ? ('0' + hour) : hour) + ':' + (minute < 10 ? ('0' + minute) : minute) + ':' + (second < 10 ? ('0' + second) : second);
}

export function downloadPDF(url) {
    const urlSplits=url.split('/')
    const link = document.createElement('a');
    link.href = url;
    link.download = urlSplits[urlSplits.length-1];
    link.click();
    URL.revokeObjectURL(link.href); // 清理URL对象
}