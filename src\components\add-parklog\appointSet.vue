<template>
    <div class="parking-spaceNew">
      <el-drawer
            :visible="appointSetShow"
            direction="rtl"
            @close="closeDialog"
            size="680px"
            custom-class="custom-drawer">
            <div slot="title" class="header-title">批量管理已选车位</div>
            <div class="custom-drawer-info">
              <el-form ref="refillAddForm" label-width="68px">
                  <div class="tips"><svg-tips-one theme="outline" size="16"/>对当前已选的车位执行如下设置</div>
                  <el-form-item label="允许预约">
                    <el-switch
                      v-model="parkingFrom.appointState"
                      :val="parkingFrom.appointState"
                      active-color="#2878ff"
                      inactive-color="#999"
                      :active-value="1"
                      :inactive-value="0">
                    </el-switch>
                    <div class="tips"><svg-tips-one theme="outline" size="16"/>选填，是否为已选车位批量开启/关闭预约</div>
                    <div class="switch-info" v-if="parkingFrom.appointState == 1">
                      <div class="level-title">计费</div>
                      <div class="tips"><svg-tips-one theme="outline" size="16"/>仅限用户在小程序提交的预约车，将按如下计费规则计费（其他车辆不变）</div>
                      <el-form-item label="预约费">
                          <el-input type="number" :min="0"  v-model.trim="parkingFrom.appointFee"  placeholder="" :disabled="parkingFrom.occupyState > 0"></el-input>
                          <span>元</span>
                          <div class="tips"><svg-tips-one theme="outline" size="16"/>必填，按次计费，预约费指用户在一码小程序提交预约服务产生的计费（每次计费）。</div>
                      </el-form-item>
                      <!-- <el-form-item label="停车费">
                        <div class="houseInfo">
                          <span>每</span>
                          <el-input type="number" :min="0"  :max="999" v-model.trim="parkingFrom.parkingFeeUnit" placeholder="" :disabled="parkingFrom.occupyState > 0"></el-input>
                          <span>分钟</span>
                          <el-input type="number" :min="0"  :max="999" v-model.trim="parkingFrom.parkingFee" placeholder="" :disabled="parkingFrom.occupyState > 0"></el-input>
                          <span>/ 元</span>
                        </div>
                        <div class="tips"><svg-tips-one theme="outline" size="16"/>必填，预约车入场后，将按如上计费，小于或超出设置时间的，均按设置时间计费（离场时，按当前出口支付方式支付）</div>
                      </el-form-item> -->
                    </div>
                  </el-form-item>
              </el-form>
              <footer slot="footer" class="dialog-footer">
                <el-button type="primary" @click="saveAppointSet">提交</el-button>
              </footer>
            </div>
        </el-drawer>
    </div>
</template>
<script>

export default {
  props: {
    appointSetShow: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      top:'0vh',
      parkingFrom:{
        appointState: '0',
        appointFee: 0,
        parkingFeeUnit: 0,
        parkingFee: 0
      }
      
    }
  },
  mounted() {

  },
  methods: {
    closeDialog(){
      this.parkingLotList = []
      this.$emit("closeDialog",false)
    },

   
    saveAppointSet(){
      const {
        appointFee,
        appointState
      } = this.parkingFrom;
      if(Number(appointState) == 1){
        // if(appointFee == '' || appointFee == null){
        //   this.$message({
        //     message: '请填写预约费',
        //     type: 'error'
        //   });
        //   return;
        // }

        if(appointFee < 0){
          this.$message({
            message: '预约费金额大于或者等于0',
            type: 'error'
          });
          return;
        }

        // if(parkingFeeUnit == '' || parkingFeeUnit == null){
        //   this.$message({
        //     message: '请填写时间',
        //     type: 'error'
        //   });
        //   return;
        // }

        // if(parkingFeeUnit > 999 || appointFee > 999){
        //   this.$message({
        //     message: '不能大于999',
        //     type: 'error'
        //   });
        //   return;
        // }

        // if(parkingFee == '' || appointFee == null){
        //   this.$message({
        //     message: '请填写停车费',
        //     type: 'error'
        //   });
        //   return;
        // }
      }
      this.$emit("appointSetSubmit",this.parkingFrom)
    },
  },
}
</script>
<style  lang="scss" scoped>
.header-title{
  color: #333;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
/deep/.el-drawer__header{
  margin-bottom: 10px;
  padding: 20px 30px 0 30px;
}
.custom-drawer-info{
  margin: 10px 30px;
}
.level-title{
  font-weight: bold;
  margin-bottom: 10px;
}

/deep/.el-input{
  width: 210px;
}

.el-button{
  width: 120px;
}
.tips{
  color: #999;
  font-size: 14px;
  background: #fff;
  line-height: 20px;
  margin: 6px 0;
  span{
    vertical-align: middle;
    margin-right: 6px;
  }
}

/deep/.custom-dialog .el-dialog__body{
  padding: 30px 20px;
}

/deep/.custom-dialog .el-dialog__header .el-dialog__headerbtn{
  top: 20px;
}

.houseInfo{
  display: flex;
  span{
    display: inline-block;
    width: 30px;
    margin: 0 10px;
  }
  .el-input{
    width: 70px;
  }
}

.switch-info{
  .el-input{
    width: 70px;
  }
}
</style>
