<template>
  <div class="player-box">
    <div
      v-loading="isLoading"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(255, 255, 255, 0)"
      class="video-text"
    >
      <p v-show="loadingText || offline">
        {{ offline ? "网络已断开，请检查网络连接" : loadingText }}
      </p>
    </div>
    <video ref="videoPlayer" autoplay playsinline muted :src="videoSrc"></video>
  </div>
</template>

<script>
import Hls from "hls.js";

export default {
  name: "HlsVideo",
  props: {
    options: {
      type: String,
    },
  },
  data() {
    return {
      isLoading: false,
      loadingText: "",
      videoSrc: "",
      hls: null,
      retryCount: 0, // 用于跟踪重试次数
      reconnectTimer: null, // 用于定时重连
      offline: false, // 用于跟踪网络状态
      progressTimer: null, // 用于检测视频进度
      lastTime: 0, // 用于跟踪上次播放时间
      stuckCounter: 0, // 用于检测视频卡住次数
    };
  },
  watch: {
    options: {
      deep: true,
      handler(newVal, oldVal) {
        console.log("视频地址变更:", newVal, oldVal);
      },
    },
  },
  mounted() {
    // 1、初始化 HLS player
    console.log("组件挂载，初始化播放器...");
    this.destroyPlayer();
    this.cleanVideo();
    this.initPlayer();

    // 电脑网络监听
    window.addEventListener("online", () => {
      console.log("网络已恢复");
      this.offline = false;
      this.tryReconnect();
    });

    window.addEventListener("offline", () => {
      console.log("网络已断开");
      this.offline = true;
      const videoElement = this.$refs.videoPlayer;
      videoElement && videoElement.pause();
    });
  },

  beforeDestroy() {
    console.log("组件销毁，清理播放器...");

    this.offline = false;
    clearTimeout(this.reconnectTimer);
    this.reconnectTimer = null;

    clearInterval(this.progressTimer);
    this.progressTimer = null;

    clearInterval(this.seekTimer);
    this.seekTimer = null;

    this.stuckCounter = 0; // 重置卡住计数器
    this.lastTime = 0; // 重置上次播放时间

    this.destroyPlayer();
    this.cleanVideo();

    window.removeEventListener("online", () => {});
    window.removeEventListener("offline", () => {});
  },

  methods: {
    initPlayer() {
      const videoElement = this.$refs.videoPlayer;
      this.isLoading = true;

      if (Hls.isSupported() && videoElement) {
        this.hls = new Hls({
          maxLiveSyncPlaybackRate: 1.5,
          minBufferLength: 5, // 最小缓冲5秒
          maxBufferLength: 30, // 最大缓冲60->30秒，过长会导致内存占用导致性能问题
          // liveSyncDuration: 3,
          // liveMaxLatencyDuration: 10,
          // // 低延迟核心配置
          // lowLatencyMode: true,
          // maxBufferSize: 2 * 1000 * 1000, // 从10MB减少到2MB

          // maxBufferHole: 0.1, // 更快地填充缓冲区空洞
          // highBufferWatchdogPeriod: 1, // 更快监测和处理高缓冲

          // // 更激进的直播同步设置
          // liveSyncDuration: 1.5, // 与直播边缘保持1.5秒（从3秒减少）
          // liveMaxLatencyDuration: 2.5, // 最大允许2.5秒延迟（从10秒减少）
          // liveDurationInfinity: true, // 直播无限期处理

          // // 更激进的追赶机制
          // maxLiveSyncPlaybackRate: 1.5, // 更激进的追赶速度（从1.1提高）
          // liveBackBufferLength: 0, // 不保留回放缓冲，专注前向

          // // 加载策略调整
          // startFragPrefetch: true,
          // initialLiveManifestSize: 1, // 仅需要一个分片即可开始播放

          // // 错误恢复策略 - 维持稳定性
          // fragLoadingMaxRetry: 1, // 减少重试次数加速错误处理
          // manifestLoadingMaxRetry: 1,
          // levelLoadingMaxRetry: 1,

          // // 核心性能优化
          // enableWorker: true,
          // debug: false,

          // // 关键细节调整
          // backBufferLength: 0, // 不保留回放缓冲区
          // appendErrorMaxRetry: 2, // 减少附加错误重试
          // nudgeMaxRetry: 2, // 减少卡顿恢复重试
          // abrEwmaDefaultEstimate: 1000000, // 默认1Mbps带宽估计
          // testBandwidth: false, // 禁用带宽测试，避免初始延迟
        });

        this.hls.on(Hls.Events.MEDIA_ATTACHED, () => {
          const sourceUrl = this.getStreamUrl() + `?t=${Date.now()}`; // 添加时间戳避免缓存
          this.hls.loadSource(sourceUrl);
        });

        this.hls.on(Hls.Events.MANIFEST_LOADED, () => {
          console.log("HLS manifest loaded successfully",this.hls.audioTracks.length);
          if (this.hls.audioTracks.length > 1) {
            this.isLoading = true;
          }
          videoElement.addEventListener("canplay", this.onCanPlay, {
            once: true,
          });
        });

        this.hls.on(Hls.Events.ERROR, (event, data) => {
          if (data.fatal) {
            // 相机端断网断电都在这里处理
            this.handleFatalError(data);
          } else {
            this.handleOtherError(data);
          }
        });

        videoElement.onplay = () => {
          console.log("视频开始播放，自动调整到当前直播位置...");
          
          this.isLoading = false;
          this.loadingText = "";
          videoElement.currentTime = this.hls.liveSyncPosition;
        };

        this.hls.attachMedia(videoElement);

      } else {
        this.loadingText = "浏览器不支持HLS，请使用其他浏览器或播放器。";
        this.isLoading = false;
      }
    },

    onCanPlay() {
      const videoElement = this.$refs.videoPlayer;
      if (!videoElement) return;

      videoElement
        .play()
        .then(() => {
          this.isLoading = false;
          this.loadingText = "";
          console.log("Video playing successfully");
        })
        .catch((err) => {
          this.handleVideoError({ type: "PlayError", details: err });
        });
    },

    // 检测视频是否卡住
    checkVideoProgress() {
      this.progressTimer = setInterval(() => {
        console.log("检查视频进度...");
        const videoElement = this.$refs.videoPlayer;

        if (!this.options || !this.hls || !videoElement) return;

        const now = videoElement && videoElement.currentTime;

        if (now === this.lastTime) {
          this.stuckCounter++;
          console.log(`视频卡住检测: ${this.stuckCounter} 次`);
          if (this.stuckCounter >= 3) {
            this.isLoading = true;
            this.loadingText = "视频播放卡住，正在尝试重新加载...";
          }
        } else {
          this.isLoading = false;
          this.loadingText = "";
          this.stuckCounter = 0;
          clearInterval(this.progressTimer);
          this.progressTimer = null;
        }
        this.lastTime = now;
      }, 1000);
    },

    handleVideoError(error) {
      console.error("视频播放错误:", error);
      this.loadingText = "视频加载失败，请稍后再试。";
      this.isLoading = false;
      this.tryReconnect();
    },

    handleFatalError(data) {
      console.error("致命错误:", data);

      if (data.details === "manifestIncompatibleCodecsError") {
        this.loadingText = "不兼容的编解码器，请检查流设置。";
      } else if (data.response && data.response.code === 404) {
        this.loadingText = "视频流未找到，请检查流地址。";
      } else {
        this.loadingText = "发生致命错误，正在尝试重新加载...";
      }

      switch (data.type) {
        case Hls.ErrorTypes.NETWORK_ERROR:
          console.log("网络错误，尝试重新加载...");
          // 断网断电 连续重试20次后还是不行 重新推流
          this.loadingText = "网络错误，尝试重新加载...";
          break;
        case Hls.ErrorTypes.MEDIA_ERROR:
          console.log("媒体错误，尝试重新加载...");
          break;
        case Hls.ErrorTypes.OTHER_ERROR:
          console.log("其他错误，尝试重新加载...");
          // 相机断网提示
          break;
        default:
          console.log("无法处理的错误:", data);
          this.loadingText = "无法加载视频，请稍后再试。";
      }

      this.handleNetworkError();
    },

    handleOtherError(data) {
      console.warn("非致命错误:", data);
      if (
        data.type === Hls.ErrorTypes.NETWORK_ERROR &&
        data.details === "levelLoadTimeOut"
      ) {
        console.log("Level loading timeout:", data);
        this.loadingText = "网络加载超时，正在尝试重新连接...";
        this.tryReconnect();
        return;
      }

      this.tryReconnect();
    },

    handleNetworkError() {
      const maxRetries = 5; // 最大重试次数
      if (this.retryCount < maxRetries) {
        this.tryReconnect(); // 尝试重新连接
      } else {
        console.warn("重试次数已达上限，重新推拉流...");
        this.retryCount = 0; // 重置重试计数
        this.$emit("onRefreshStream"); // 触发重新推拉流事件
      }
    },

    // 尝试重连
    tryReconnect() {
      clearTimeout(this.reconnectTimer);
      this.isLoading = true;
      this.reconnectTimer = setTimeout(() => {
      this.loadingText = "网络加载中...";
        this.retryCount++;
        console.log(`重试第 ${this.retryCount} 次...`);
        this.destroyPlayer();
        this.initPlayer();
      }, 2000);
    },

    reloadHlsVideo() {
      console.log("重新加载HLS视频...");
      this.isLoading = true;
      if (this.hls) {
        this.hls.stopLoad();
        setTimeout(() => {
          this.hls && this.hls.startLoad();
          this.isLoading = false;
        }, 200);
      }
    },

    getStreamUrl() {
      // 清理基础URL，移除可能存在的查询参数
      const baseUrl = this.options ? this.options.split("?")[0] : "";
      console.log("清理基础URL...", baseUrl);
      return `${baseUrl}/index.m3u8`;
    },

    // 销毁播放器
    destroyPlayer() {
      if (this.hls) {
        this.hls.destroy();
        this.hls = null;
      }
    },

    // 清理播放器状态
    cleanVideo() {
      const videoElement = this.$refs.videoPlayer;
      if (videoElement) {
        videoElement.pause();
        videoElement.removeAttribute("src");
        videoElement.load();
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.player-box {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #fff;
  width: 100%;
  height: 100%;
  background: rgb(30, 30, 30);
  position: relative;
}
.player-box video {
  width: 100%;
  height: 100%;
}
.video-text {
  position: absolute;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  color: #409eff;
}
</style>