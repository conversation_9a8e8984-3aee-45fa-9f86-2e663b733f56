<template>
  <el-dialog custom-class="custom-dialog dialog" width="930px" :show-close="false" :visible.sync="dialogVisible" :close-on-click-modal="false">
    <header class="fixed-code__title" slot="title" style="font-size: 18px;font-weight: bold;">
      {{ title }}<i class="el-icon-close dialog-header-iconfont" @click="closeHandler"></i>
    </header>
    <!-- 状态 8 9 5 1 -->
    <el-row v-if="eventForm.state == 8 || eventForm.state == 9 || eventForm.state == 5 || eventForm.state == 1">
      <el-col :span="8">车道区域：<span>{{eventForm.passType === 1 ? eventForm.outAreaName : eventForm.inAreaName }}</span></el-col>
      <el-col :span="8">车道名称：<span>{{eventForm.passType === 1? eventForm.outChannelStr : eventForm.inChannelName }}</span></el-col>
      <el-col :span="8">
        <span v-if="eventForm.state == 9 || eventForm.state == 8 ||eventForm.state == 5">
          入场时间：<span>{{ common.dateformat(eventForm.inTime) || '-'}}</span>
        </span>
        <span v-if="eventForm.state == 1">
          出场时间：<span>{{ common.dateformat(eventForm.outTime) || '-'}}</span>
        </span>
        </el-col>
    </el-row>
    <el-row>
      <el-col :span="8">事件来源：<span>{{ eventForm.passType === 1 ? '出场' : '入场' || '-' }}</span></el-col>
      <el-col :span="8">事件类型：<span>{{ eventType || '-' }}</span></el-col>
      <el-col :span="8">收费员：<span>{{ eventForm.dutyName || '系统' }}</span></el-col>
    </el-row>
      <!-- 疑似返场10  疑似逃费12 出-疑似跟车11 -->
    <template v-if="eventForm.state == 10 || eventForm.state == 12 ||  eventForm.state == 11">
      <el-row>
        <el-col :span="8">
          进场区域：<span>{{ eventForm.inAreaName || '-' }}</span>
        </el-col>
        <el-col :span="8">
          车道名称：<span>{{ eventForm.inChannelName || '-' }}</span>
        </el-col>
        <el-col :span="8">
          入场时间：<span>{{ common.dateformat(eventForm.inTime) || '-'}}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">出场区域：<span>{{ eventForm.outAreaName || '-'}}</span></el-col>
        <el-col :span="8">出场车道：<span>{{ eventForm.outChannelStr || '-' }}</span></el-col>
        <el-col :span="8">出场时间：<span>{{ common.dateformat(eventForm.outTime) || '-'}}</span></el-col>
      </el-row>
      <el-row>
        <el-col :span="8">应收金额：<span>{{ common.moneyFormat(eventForm.total) }}元</span></el-col>
        <el-col :span="8">已付金额：<span>{{ common.moneyFormat(eventForm.alreadyPay) }}元</span></el-col>
        <el-col :span="8">待缴费金额：<span>{{ common.moneyFormat(eventForm.payMoney) }}元</span></el-col>
      </el-row>
    </template>
    <el-row>
      <el-col :span="8">识别车牌：<span>{{ eventForm.carNumber || '-' }}</span></el-col>
      <el-col :span="8">车尾车牌：<span>{{ eventForm.trailCarNumber || '-'}}</span></el-col>
      <el-col :span="8">监控录像：
        <span v-if="eventForm.surveillanceVideo && showVideo" style="color:#87CEEB;cursor:pointer;text-decoration:underline;" @click="videoPlay(videoUrl)">播放视频</span>
        <span v-else>无视频</span>
      </el-col>
    </el-row>
    <template v-if="eventForm.state == 5">
      <el-row>
        <el-col :span="8">订单号(原): <span>{{ eventForm.order_id || '-'}}</span></el-col>
        <el-col :span="8">车道区域(原): <span>{{ eventForm.inAreaName || '-' }}</span></el-col>
        <el-col :span="8">车道名称(原): <span>{{ eventForm.inChannelName || '' }}</span></el-col>
      </el-row>
      <el-row>
        <el-col :span="8">入场时间(原): 
          <span>{{ common.dateformat(eventForm.inTime) || '-' }}</span>
        </el-col>
        <el-col :span="8">出场区域: 
          <el-select v-if="eventForm.confirmType == -1" size="mini" v-model="eventForm.outAreaId" @change="areaChange" style="width:60%">
            <el-option v-for="item in arealist" :key="item.value_no" :label="item.value_name" :value="item.value_no"></el-option>
          </el-select>
          <span v-else>{{ eventForm.outAreaName || '-' }}</span>
        </el-col>
        <el-col :span="8">出场车道: 
          <el-select v-if="eventForm.confirmType == -1" size="mini" v-model="eventForm.outChannelId" @change="channelChange" style="width:60%">
            <el-option v-for="item in channelList" :key="item.value_no" :label="item.value_name" :value="item.value_no"></el-option>
          </el-select>
          <span v-else>{{ eventForm.outAreaName || '-' }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">出场时间: 
          <el-date-picker
            style="width:70%"
            v-if="eventForm.confirmType == -1" 
            size="small"
            v-model="eventOutTime"
            :picker-options="pickerOptionsTime"
            type="datetime"
            value-format="timestamp"
            @change="timeChange"
            > 
          </el-date-picker>
          <span v-else>{{ common.dateformat(eventForm.outTime) || '-' }}</span>
        </el-col>
        <el-col :span="8">应收金额: 
          <span>{{ eventForm.total || '0.00' }}元</span>
        </el-col>
        <el-col :span="8">补缴金额: 
          <span>{{ eventForm.payMoney || '0.00' }}元</span>
        </el-col>
      </el-row>
    </template>
    <template v-if="eventForm.state == 1">
      <el-row>
        <el-col :span="8">订单号(原): <span>{{ eventForm.order_id || '-'}}</span></el-col>
        <el-col :span="8">入场区域: 
          <el-select v-if="eventForm.confirmType == -1" size="mini" v-model="eventForm.manualInAreaId" @change="areaChange" style="width:60%">
            <el-option v-for="item in arealist" :key="item.value_no" :label="item.value_name" :value="item.value_no"></el-option>
          </el-select>
          <span v-else>{{ eventForm.inAreaName || '-' }}</span>
        </el-col>
        <el-col :span="8">入场车道: 
          <el-select v-if="eventForm.confirmType == -1" size="mini" v-model="eventForm.manualInChannelId" @change="channelChange" style="width:60%">
            <el-option v-for="item in channelList" :key="item.value_no" :label="item.value_name" :value="item.value_no"></el-option>
          </el-select>
          <span v-else>{{ eventForm.inChannelName || '-' }}</span>
          <span v-if="eventForm.confirmType == -1" class="text-order" @click="showConnectInOrder()">关联在场订单</span>
        </el-col>
        <!-- <div v-if="eventForm.confirmType == -1" class="text-order" @click="showConnectInOrder()">关联在场订单</div> -->
      </el-row>
      <el-row>
        <el-col :span="8">入场时间: 
          <el-date-picker
            style="width:70%"
            v-if="eventForm.confirmType == -1" 
            size="small"
            v-model="eventIntime"
            :picker-options="pickerOptionsInTime"
            type="datetime"
            value-format="timestamp"
            @change="timeChange"
            > 
          </el-date-picker>
          <span v-else>{{ common.dateformat(eventForm.outTime) || '-' }}</span>
        </el-col>
        <el-col :span="8">应收金额: 
          <span>{{ eventForm.total || '0.00' }}元</span>
        </el-col>
        <el-col :span="8">补缴金额: 
          <span>{{ eventForm.payMoney || '0.00' }}元</span>
        </el-col>
      </el-row>
    </template>
    <el-row>
      <el-col :span="8">确认类型：<span> {{ confirmList[eventForm.confirmType] || '-'}}</span></el-col>
      <el-col :span="8">事件处理人：<span> {{ eventForm.userName || userName }}</span></el-col>
    </el-row>
    <div class="picture">
      <div class="pic-item" >
        <img v-if="eventForm.passType == 0 && eventForm.inPic" :src="eventForm.inPic" alt="" class="pic-img">
        <img v-else-if="eventForm.passType == 1 && eventForm.outPic" :src="eventForm.outPic" alt="" class="pic-img">
        <img v-else :src="defaultImg" alt="">
      </div>
      <div class="pic-item" >
        <img v-if="eventForm.trailPic" :src="eventForm.trailPic" alt="" class="pic-img">
        <img v-else :src="defaultImg" alt="">
      </div>
    </div>
    <div class="footer-btn" v-show="eventForm.confirmType === -1">
      <el-button v-if="isCheckInOrCancel" type="primary" @click="dealEvent(eventForm, 1)">确认入场</el-button>
      <el-button v-if="isCheckInOrCancel" type="primary" @click="dealEvent(eventForm, 2)">取消入场</el-button>
      <el-button v-if="isCheckBack" type="primary" @click="dealEvent(eventForm, 3)">确认返场</el-button>
      <el-button v-if="isConfirmPayOrClose" type="primary" @click="confirmPay()">确认补缴</el-button>
      <el-button v-if="isConfirmPayOrClose" type="primary" @click="dealEvent(eventForm, 4)">关闭事件</el-button>
    </div>
    <mp4-player-vue :path="mp4Player" :visibleOpen="videoVisible" @closeVideoDialog="closeVideoDialog"></mp4-player-vue>
    <connectDialog ref="dialog" @handleConfirm="handleConfirm"></connectDialog>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import common from '@/common/js/common'
import Mp4PlayerVue from '@/pages/park/abnormal-order/Mp4Player.vue'
import connectDialog from './dialog.vue'

export default {
  name: 'AbnormalForm',
  props: {
    eventForm: {
      type: Object,
      default: () => {}
    },
    type: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    Mp4PlayerVue, connectDialog
  },
  data() {
    let that = this
    return {
      common,
      rowData: {},
      dialogVisible: false,
      videoVisible: false,
      showVideo: false,
      videoUrl: '',
      userName: sessionStorage.getItem('nickname'),
      businessDomain: '',
      title: '',
      eventType: '',
      confirmList: {
        '-1': '未处理',
        0: '取消入场',
        1: '确认入场',
        2: '确认追缴',
        3: '确认返场'
      },
      defaultImg: require('@/pages/fourg/park/4g-sentry-box/images/image-icon.png'),
      mp4Player: null,
      arealist: [],
      channelList: [],
      pickerOptionsTime: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      pickerOptionsInTime: {
        disabledDate(time) {
          if (that.eventForm.outTime) {
            return time.getTime() > that.eventForm.outTime * 1000
          }
        }
      },
      hasInitialized: false,
      eventOutTime: null,
      eventIntime: null,
      connectData: {}
    }
  },
  computed: {
    // 1 重复出场 2 人工车道放行 3 模糊匹配 4 未知 5 重复入场 6 无订单放行 7 高峰放行 8 疑似掉头 9 疑似跟车 10 疑似返场 11 疑似跟车 12 疑似逃费 
    isCheckInOrCancel() {
      return this.eventForm.state === 8 || this.eventForm.state === 9
    },
    isCheckBack() {
      return this.eventForm.state === 10
    },
    isConfirmPayOrClose() {
      return [1, 5, 10, 11, 12].includes(this.eventForm.state)
    },
    ...mapGetters(['abnormalList'])
  },
  watch: {
    eventForm: {
      handler(val) {
        if (!this.hasInitialized && val.surveillanceVideo) {
          this.getVideoUrl(val)
        }
        if (!this.hasInitialized && (val.state === 5 || val.state === 1)) {
          this.eventOutTime = this.converTime(this.eventForm.outTime)
          this.eventIntime = this.converTime(this.eventForm.inTime)
          this.getAreaList()
          this.getChannelList()
          this.queryOrderPrice()
        }
        this.hasInitialized = true
      },
      deep: true
    }
  },
  methods: {
    // 显示弹窗
    show(data) {
      console.log(data, 'data')
      const {businessDomain,parkName,parkId,passType,eventType}=data||{}
      this.rowData = data
      this.businessDomain = businessDomain
      const pass = passType === 1 ? '出场' : '入场'
      this.eventType = this.formatType(eventType)
      this.title = `【${parkName}${parkId}】${pass}事件-${this.eventType}`
      this.$nextTick(() => {
        this.dialogVisible = true
      })
      this.eventOutTime = null
      this.eventIntime = null
    },
    closeHandler(){
      this.dialogVisible = false
      this.hasInitialized = false
    },
    // 事件完成后的处理操作 1. 关闭弹窗 2. 删除列表中的数据
    afterComplete() {
      this.dialogVisible = false
      this.hasInitialized = false
      let list = this.abnormalList
      list = list.filter(item => item.exceptionTbId !== this.eventForm.id)
      this.$store.commit('watch/SET_ABNORMAL_LIST', list)
      this.$emit('update')
    },
    converTime(time) {
      if (!time) return null
      return time.toString().length === 10 ? time * 1000 : time;
    },
    // 获取视频地址
    getVideoUrl(form) {
      const { id, create_time } = form
      const params = {
        id: id,
        createTime: create_time,
        userId: sessionStorage.getItem('userid'),
        userName: sessionStorage.getItem('nickname')
      }
      this.$axios.post(`${this.businessDomain}/zld/exceptionThrough/getVideoUrl`, params).then(res => {
        const data = res.data
        if (data.code === 200 && data.data) {
          this.showVideo = true
          this.videoUrl = data.data
        }
      })
    },
    // 关闭视频弹窗
    closeVideoDialog() {
      this.videoVisible = false
    },
    // 格式化事件类型
    formatType(type) {
      return this.type[type]
    },
    // 播放视频
    videoPlay(url) {
      this.mp4Player = url
      this.videoVisible = true
    },
    // 处理事件
    dealEvent(form, type) {
      // 1 确认入场 2 取消入场 3 确认返场 4 关闭事件
      let api
      switch (type) {
        case 1:
          api = '/exceptionThrough/inPark'
          break
        case 2:
          api = '/exceptionThrough/cancelInPark'
          break
        case 3:
          api = '/exceptionThrough/returnPark'
          break
        case 4:
          api = '/exceptionThrough/cancel'
          break
      }
      const params = {
        id: form.id,
        createTime: form.create_time,
        userId: sessionStorage.getItem('userid'),
        userName: sessionStorage.getItem('nickname')
      }
      this.$axios.post(`${this.businessDomain}/zld${api}`, params).then(res => {
        const data = res.data
        if (data.code === 200) {
          this.$message.success(data.message || '操作成功')
          this.afterComplete()
        } else {
          this.$message.error(data.message || '操作失败')
        }
      })
    },
    concatParams(params) {
       if (this.eventForm.state === 1) {
        const data = this.connectData
        if (data.orderId) {
          params = {
            ...params,
            selectedOrderId: data.orderId,
            selectedCarNumber: data.carNumber,
            selectedInTime: data.inTime,
            manualInAreaId: this.eventForm.manualInAreaId,
            manualInChannelId: this.eventForm.manualInChannelId,
            manualInTime: this.eventIntime / 1000
          }
        } else {
          params = {
            ...params,
            manualInAreaId: this.eventForm.manualInAreaId,
            manualInChannelId: this.eventForm.manualInChannelId,
            manualInTime: this.eventIntime / 1000
          }
        }
      }
      return params
    },
    // 确认补缴
    confirmPay() {
      const { id, create_time, outTime, outAreaId, outChannelId, payMoney }  = this.eventForm;
      let params = {
        id: id,
        createTime: create_time,
        outTime: outTime,
        outAreaId: outAreaId,
        outChannelId: outChannelId,
        payMoney: payMoney,
        userName: sessionStorage.getItem('nickname')
      }
      params = this.concatParams(params)
      this.$axios.post(`${this.businessDomain}/zld/exceptionThrough/confirmRecovery`, params).then(res => {
        const data = res.data
        if (data.code === 200) {
          this.$message.success(data.message || '操作成功')
          this.afterComplete()
        } else {
          this.$message.error(data.message || '操作失败')
        }
      })
    },
    // 获取出场区域
    getAreaList() {
      this.$axios.get(`/getdata/getComArea?comid=${this.rowData.comId}`).then(res => {
        const data = res.data
        if (data && data.length) {
          this.arealist = data.map(item => {
            return {
              value_no: Number(item.value_no),
              value_name: item.value_name
            }
          })
        }
      })
    },
    // 获取出场车道
    getChannelList(areaid = null) {
      let passType
      let { state } = this.eventForm
      if (state === 1) {
        passType = 0
      } else if (state === 5) {
        passType = 1
      }
      this.$axios.get(`/getdata/getParkChannels?comid=${this.rowData.comId}&passType=${passType}&areaId=${areaid}`).then(res => {
        const data = res.data
        if (data && data.length) {
          this.channelList = data
        } else {
          this.channelList = []
          this.eventForm.outChannelId = ''
        }
      })
    },
    // 查询订单价格
    queryOrderPrice() {
      const { id, create_time, outTime, outAreaId, outChannelId } = this.eventForm
      let params = {
        id: id,
        createTime: create_time,
        outTime: outTime,
        outAreaId: outAreaId,
        outChannelId: outChannelId
      }
      params = this.concatParams(params)
      const api = `${this.businessDomain}/zld/exceptionThrough/queryPrice`
      this.$axios.post(api, params).then(res => {
        const data = res.data
        if (data.code === 200 && data.data) {
          const { price, total, alreadyPay, deduction } = data.data
          this.eventForm['payMoney'] = price
          this.eventForm['total'] = total
          this.eventForm['alreadyPay'] = alreadyPay
          this.eventForm['deduction'] = deduction
        }
      })
    },
    // 出场区域改变
    areaChange(val) {
      this.getChannelList(val)
      setTimeout(() => {
        this.queryOrderPrice()
      }, 100)
    },
    // 出场车道改变
    channelChange() {
      this.queryOrderPrice()
    },
    // 时间改变
    timeChange(val) {
      // this.eventForm.outTime = val / 1000
      this.queryOrderPrice()
    },
    showConnectInOrder() {
      this.$refs.dialog.show(this.eventForm, this.rowData)
    },
    handleConfirm(data) {
      this.connectData = data
      this.eventForm['order_id'] = data.orderId
      this.eventForm['manualInAreaId'] = data.inAreaId
      this.eventForm['manualInChannelId'] = data.inChannelId ? Number(data.inChannelId) : ''
      this.eventIntime = this.converTime(data.inTime)
      this.queryOrderPrice()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/utils.scss';
/deep/.el-dialog.custom-dialog.dialog {
  .fixed-code__title {
    text-align: center;
  }
  .el-dialog__body {
    background: #10223f;
    padding: vw(20);
    .el-row {
      margin: 0 0 vw(20) vw(20);
      .el-select.el-select--small {
        width: vw(150);
        margin-top: vw(-6);
      }
      .el-input__inner {
        color: #ccc;
      }
      .el-date-editor.el-input.el-input--small.el-input--prefix.el-input--suffix.el-date-editor--datetime {
        width: vw(190);
        margin-top: vw(-6);
      }
    }
    .el-col.el-col-8 {
      color: #ccc;
    }
    span {
      font-weight: bold;
    }
  }
  .picture {
    display: flex;
    justify-content: space-between;
    height: vw(250);
    margin-bottom: vw(20);
    .pic-item {
      margin: vw(10);
      display: flex;
      justify-content: center;
      align-items: center;
      width: 50%;
      background: #e1e1e1;
      .pic-img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .footer-btn {
    display: flex;
    justify-content: flex-end;
    & > button {
      margin-left: vw(20);
    }
  }
  .text-order {
    text-decoration: underline;
    color: #87CEEB;
    cursor: pointer;
    font-size: vw(12);
  }
}
</style>
