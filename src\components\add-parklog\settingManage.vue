<template>
  <div class="parking-spaceNew">
    <el-drawer
          :visible="parkingShow"
          direction="rtl"
          @close="closeDialog"
          size="720px"
          custom-class="custom-drawer">
          <div slot="title" class="parkInfoTab">
            <el-tabs v-model="activeName">
              <el-tab-pane label="车位信息" name="parkInfo">
                <div class="custom-drawer-info">
                  <el-form ref="refillAddForm" label-width="180px" :rules="parkingFormRules" :model="parkingForm">
                    <div class="header"> <div class="level-title">车位信息</div></div>
                      <el-form-item label="电站名称:">
                        <span>{{ parkingForm.powerStationName || '--'  }}</span>
                      </el-form-item>
                      <el-form-item label="车场名称:">
                          <span>{{parkingForm.comName || '--' }}</span>
                      </el-form-item>
                      <el-form-item label="车场ID:">
                          <span>{{parkingForm.comid || '--' }}</span>
                      </el-form-item>
                      <el-form-item label="车位区域:">
                          <span>{{ parkingForm.comAreaInfoName || '--'  }}</span>
                      </el-form-item>
                       <el-form-item label="车位类型:">
                          <span>{{ parkingForm.parkingLotType || '--' }}</span>
                      </el-form-item>
                      <el-form-item label="车位号:">
                          <span>{{ parkingForm.parkingLotNumber || '--'  }}</span>
                      </el-form-item>
                      <el-form-item label="车位ID:">
                          <span>{{parkingForm.id || '--' }}</span>
                      </el-form-item>
                      <el-form-item label="应用模式:">
                        <el-select v-model="parkingForm.applyMode" placeholder="请选择" >
                            <el-option
                              v-for="item in parkAppModeList"
                              :key="item.value_no"
                              :label="item.value_name"
                              :value="item.value_no">
                            </el-option>
                          </el-select>
                      </el-form-item>
                     <el-form-item label="车位备注:">
                          <el-input type="textarea" style="width:300px;" v-model.trim="parkingForm.remark" placeholder="选填，最大30字符" maxlength="30"></el-input>
                      </el-form-item>
                  </el-form>
                  <footer slot="footer" class="dialog-footer">  
                      <el-button type="primary" @click="saveParklot">提交</el-button>
                  </footer>
                </div>
              </el-tab-pane>
              <el-tab-pane label="车位准入" name="parkAccess">
                <!-- <placeholder-infor ref="parkAccessInfo" :bolinkId="parkingForm.id" :comName="parkingForm.comName" :empowerType="parkingForm.empowerType" :updateType="2"></placeholder-infor> -->
                <allowIn-info  ref="parkAccessInfo" v-bind="$attrs" v-on="$listeners" :bolinkId="parkingForm.id" :comName="parkingForm.comName" :updateType="2" :setType="1"></allowIn-info>
              </el-tab-pane>
              <el-tab-pane label="车位预约" name="reservatInfor">
                <!-- <reservat-infor ref="reservatInfo" :bolinkId="parkingForm.id" :comName="parkingForm.comName" :empowerType="parkingForm.empowerType"></reservat-infor> -->
                <preorder-info ref="reservatInfo" v-bind="$attrs" v-on="$listeners" :bolinkId="parkingForm.id" :comName="parkingForm.comName"></preorder-info>
              </el-tab-pane>
              <el-tab-pane label="车位占位" name="placeholderInfor">
                <placeholder-infor ref="seatInfo" v-bind="$attrs" v-on="$listeners"  :bolinkId="parkingForm.id" :comName="parkingForm.comName" :updateType="1"></placeholder-infor>
              </el-tab-pane>
              <el-tab-pane label="车位码" name="parkSpaceCodeInfor" v-if="parkingForm.qrCode">
                <!-- <placeholder-infor ref="seatInfo" v-bind="$attrs" v-on="$listeners"  :bolinkId="parkingForm.id" :comName="parkingForm.comName" :empowerType="parkingForm.empowerType" :updateType="1"></placeholder-infor> -->
                <div class="codeHeader">
                  <div class="title">车位码</div>
                </div>
                <el-form>
                  <el-form-item label-width="200px" label="车场ID:">
                    <span>{{parkingForm.comid || '--' }}</span>
                  </el-form-item>
                  <el-form-item label-width="200px" label="车位ID:">
                    <span>{{parkingForm.id || '--' }}</span>
                  </el-form-item>
                  <el-form-item label-width="200px" label="二维码内容:">
                    <span>{{parkingForm.qrCode || '--' }}</span>
                  </el-form-item>
                  <el-form-item label-width="200px" label="车位二维码" >
                    <QRCode :width="100" :height="100" :content="parkingForm.qrCode" :load="false"></QRCode>
                  </el-form-item>
                </el-form>
                <div class="downCodeBtn">
                  <el-button type="primary" class="custom-btns-style" @click="drawQrcode(parkingForm.qrCode)">下载</el-button>
                </div>
              </el-tab-pane>
              <el-tab-pane label="抬杆记录" name="liftingRecord">
                <div class="codeHeader">
                  <div class="title">抬杆记录</div>
                </div>         

                  <el-form
                    class="shop-custom-form-search"
                    style="display:flex;align-items:center;"
                  >
                    <el-form-item label="时间" style="width:62%">
                      <el-date-picker style="width: 354px" class="shop-custom-datepicker" v-model="searchFormData.currentData"
                      type="datetimerange" :editable="false" :clearable="false" align="right" unlink-panels range-separator="至"
                      :picker-options="chargeTimePickerOptions" value-format="timestamp" :default-time="['00:00:00', '23:59:59']" @change="changeCurrentDate">
                    </el-date-picker>
                    </el-form-item>
                  <div style="margin:0 0 15px 50px">
                    <el-button size="small" @click="searchFn">查询</el-button>
                  </div>
                </el-form>
              <div class="table-wrapper-style">
                    <el-table :data="tableData" border style="width: 100%" height="650">
                        <el-table-column  prop="createTime" label="时间" align="center">
                          <template slot-scope="scope">
                            {{ scope.row.createTime ? formatDate(scope.row.createTime) : '--' }}
                          </template>
                        </el-table-column>
                        <el-table-column label="记录"  align="center">
                          <template slot-scope="scope">
                              {{scope.row.state == 1 ? '开闸' : '关闸'}}
                          </template>
                        </el-table-column>
                    </el-table>
                    <div style="margin:20px 0;text-align:right">
                      <el-pagination  @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[20, 40, 80]"
                            :page-size="pageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            background
                            :total="total">
                      </el-pagination>
                    </div>          
                 
                </div>
            
              </el-tab-pane>
              <el-tab-pane label="识别记录" name="identificationRecord">
                <div class="codeHeader">
                  <div class="title">识别记录</div>
                </div> 
                <div>    

                  <el-form
                    class="shop-custom-form-search"
                    style="display:flex;align-items:center;"
                  >
                    <el-form-item label="时间" style="width:62%">
                      <el-date-picker style="width: 354px" class="shop-custom-datepicker" v-model="recordSearchFormData.currentData"
                      type="datetimerange" :editable="false" :clearable="false" align="right" unlink-panels range-separator="至"
                      :picker-options="recordChargeTimePickerOptions" value-format="timestamp" :default-time="['00:00:00', '23:59:59']" @change="recordChangeCurrentDate">
                    </el-date-picker>
                    </el-form-item>
                  <div style="margin:0 0 15px 50px">
                    <el-button size="small" @click="recodeSearchFn">查询</el-button>
                  </div>
                </el-form>
                <div class="table-wrapper-style">
                    <el-table :data="recordTableData" border style="width: 100%" height="650">
                        <el-table-column  prop="createTime" label="时间" width="200" align="center">
                          <template slot-scope="scope">
                            {{ scope.row.createTime ? formatDate(scope.row.createTime) : '--' }}
                          </template>
                        </el-table-column>
                        <el-table-column prop="carNumber" label="车牌"  align="center">
                        </el-table-column>
                        <el-table-column prop="carType" label="车牌类型"  align="center">
                        </el-table-column>
                        <el-table-column prop="imgState" label="车位状态"  align="center">
                        </el-table-column>
                        <el-table-column  label="详情"  align="center">
                          <template slot-scope="scope">
                              <el-button @click="handleClick(scope.row)" type="text" size="small" v-if="scope.row.path">图片</el-button>
                              <el-button  type="text" size="small" v-else style="color:#7d8089">图片</el-button>                            
                          </template>
                        </el-table-column>
                    </el-table>
                    <div style="margin:20px 0;text-align:right">
                      <el-pagination  @size-change="handleSizeChangeRecord"
                            @current-change="handleCurrentChangeRecord"
                            :current-page="currentPageRecord"
                            :page-sizes="[20, 40, 80]"
                            :page-size="pageSizeRecord"
                            layout="total, sizes, prev, pager, next, jumper"
                            background
                            :total="totalRecord">
                      </el-pagination>
                    </div>          
                 
                </div>
              </div>  
              </el-tab-pane>
            </el-tabs>
          </div>
      </el-drawer>
      <el-dialog custom-class="custom-dialog" width="550px" :show-close="false" :visible.sync="showImgDialog"
      height="600px">
        <header class="fixed-code__title" slot="title" style="font-size: 18px; font-weight: bold">
          查看图片
          <i class="el-icon-close dialog-header-iconfont" @click="showImgDialog=false"></i>
        </header>
        <div class="dialogContainer">
          <div class="currentImg">
            <img :src="currentImg" class="currentImg" />
          </div>
        </div>
     </el-dialog>
      <canvas id="canvas" style="display:none"></canvas>
      <canvas id="img" style="display:none;"></canvas>
  </div>
</template>
<script>
import placeholderInfor from '@/components/add-parklog/placeholder-infor.vue';
import preorderInfo from '@/components/add-parklog/preorderInfo.vue';
import allowInInfo from '@/components/add-parklog/allowInInfo.vue'
import QRCode from '@/components/CustomQRCode/index.vue'
import TabPane from '@/components/table/TabPane';
import common from '@/common/js/common';
import { chargeTimePickerOptions ,	PickerReset } from '@/common/js/checkMethod';
export default {
name:'settingManage',
components: {
  placeholderInfor,
  preorderInfo,
  allowInInfo,
  QRCode,
  TabPane
},
props: {
  parkType:  {
    type: String,
    default: '0'
  },
  parkingForm:  {
    type: Object,
    default: {}
  },
  parkingShow: {
    type: Boolean,
    default: false
  },
  
  regionList: {
    type: Array,
    default: [] //区域列表
  },
  getParkingList: {
    type: Array,
    default: [] //类型列表
  },
  setActiveName:{
    type:String,
    default: 'parkInfo'
  },
},
data() {
  return {
    chargeTimePickerOptions: chargeTimePickerOptions(),
    recordChargeTimePickerOptions:chargeTimePickerOptions(),
    currentImg:'',
    showImgDialog:false,
    parkAppModeList:[
      {
        value_name: '默认',
        value_no: '0'
      },
      {
        value_name: '相机地锁',
        value_no: '1'
      }
    ],
    qrsrc:'',
    activeName: 'parkInfo',
    top:'0vh',
    parkingFormRules: {
        total: [
            {required: true, message: '应收金额不能为空', trigger: 'blur'}
        ],
        add_money: [
            {required: true, message: '充值金额不能为空', trigger: 'blur'},

        ],
    },
    recordTableData:[],
    tableData: [],
    pageSize:20, //同步条数
    currentPage:1, //同步页码
    total:20,  //同步总数 
    pageSizeRecord:20, //识别记录 同步条数
    currentPageRecord:1, //识别记录 同步页码
    totalRecord:20,  //识别记录 同步总数 
    searchFormData:{},
    recordSearchFormData:{},

  }
},
mounted() {
  
},
watch:{
  setActiveName(nVal, oVal){
    if(nVal){
      this.activeName = JSON.parse(JSON.stringify(nVal));
    }
  }
},
methods: {
    handleClick(row){
      this.currentImg=row.path
      this.showImgDialog=true

    },
    /* 修改时间 */
    changeCurrentDate(val) {
      if (val != null && val != '') {
          this.searchFormData.date = val[0] + encodeURI(encodeURI('至')) + val[1];
        } else {
          this.searchFormData.date = '';
        }
        this.chargeTimePickerOptions.minDate = undefined;
        this.chargeTimePickerOptions.maxDate = undefined;
    },
    recordChangeCurrentDate(val) {
      if (val != null && val != '') {
          this.recordSearchFormData.date = val[0] + encodeURI(encodeURI('至')) + val[1];
        } else {
          this.recordSearchFormData.date = '';
        }
        this.recordChargeTimePickerOptions.minDate = undefined;
        this.recordChargeTimePickerOptions.maxDate = undefined;
    },
    searchFn(){
      let searchFormData=JSON.parse(JSON.stringify(this.searchFormData)) 
      let timer=searchFormData.currentData  
      if (!timer||timer.length!=2) {
          let currentTime = common.currentDateArray(1)
          timer = [common.timestampFormat(currentTime[0]), common.timestampFormat(currentTime[1])]
          this.searchFormData.currentData = timer
        }
        const day = (timer[1] - timer[0]) / (24 * 3600000);
        if (day > 90) {
          return this.$message.error("最多支持查询90天数据");
        }
        searchFormData.stime = new Date(timer[0]).getTime() / 1000
        searchFormData.etime = new Date(timer[1]).getTime()/ 1000
        this.searchFormData=searchFormData
        this.getLiftRodRecord(this.currentPage, this.pageSize)    
    },
    // 时间戳处理
    formatDate(timestamp) {
        const time = common.dateformat(timestamp);
        return time;
    },
    /* 识别记录 */
    recodeSearchFn(){
      let recordSearchFormData=JSON.parse(JSON.stringify(this.recordSearchFormData)) 
      let timer=recordSearchFormData.currentData  
      if (!timer||timer.length!=2) {
          let currentTime = common.currentDateArray(1)
          timer = [common.timestampFormat(currentTime[0]), common.timestampFormat(currentTime[1])]
          this.recordSearchFormData.currentData = timer
        }
        const day = (timer[1] - timer[0]) / (24 * 3600000);
        if (day > 90) {
          return this.$message.error("最多支持查询90天数据");
        }
        recordSearchFormData.stime = new Date(timer[0]).getTime() / 1000
        recordSearchFormData.etime = new Date(timer[1]).getTime()/ 1000
        this.recordSearchFormData=recordSearchFormData
        this.getCarInOutImg(this.currentPage, this.pageSize)    
    },
    handleSizeChange(pageSize) {
			this.pageSize = pageSize
			this.getLiftRodRecord(this.currentPage, pageSize)
		},
    handleCurrentChange(currentPage) {
			this.getLiftRodRecord(currentPage, this.pageSize)
			this.currentPage = currentPage
		},
    handleSizeChangeRecord(pageSize) {
			this.pageSizeRecord = pageSize
			this.getCarInOutImg(this.currentPageRecord, pageSize)
		},
    handleCurrentChangeRecord(currentPage) {
			this.getCarInOutImg(currentPage, this.pageSizeRecord)
			this.currentPageRecord = currentPage
		},
    /* 查询抬杆记录 */
		getLiftRodRecord(currentPage, pageSize) {
			let data = { page: 1, rp: 20,...this.searchFormData }
			if (!currentPage) {
				data = { page: 1, rp: 20 }
			} else {
				data.page = currentPage
				data.rp = pageSize
			}
      delete data.currentData
      delete data.date
      this.$axios.post('/chargePileLocking/queryLiftRodRecord', data).then(res => {
        let {code,data,message} = res.data
        if (code == 200) {
          this.total = data.total         
          data.voList&&data.voList.forEach((item)=>{            
            item.liftTimeStr=item.liftTime?common.dateformat(item.liftTime):'-- --'
            item.dropTimeStr=item.dropTime?common.dateformat(item.dropTime):'-- --'
          })
          this.tableData=data.voList
          
        } else {
          this.tableData=[]
        }        

    }).catch(err => {
        console.log(err)
        
    });		
		},
    /* 查询识别记录 */
		getCarInOutImg(currentPage, pageSize) {
			let data = { page: 1, rp: 20,...this.recordSearchFormData }
			if (!currentPage) {
				data = { page: 1, rp: 20 }
			} else {
				data.page = currentPage
				data.rp = pageSize
			}
      delete data.currentData
      delete data.date
      this.$axios.post('/carInOutImg/query', data).then(res => {
        let {code,data,message} = res.data
        if (code == 200) {
          this.totalRecord = data.total      
          data.voList&&data.voList.forEach((item)=>{            
            item.createTimeStr=item.createTime?common.dateformat(item.createTime):'-- --'
            item.carType=item.carType==1?'蓝牌':item.carType==2?'绿牌':item.carType==3?'无牌':''
            item.imgState=(item.imgState==1||item.imgState==2)?'入位':item.imgState==4?'离位':''
          })
          this.recordTableData=data.voList
          
        } else {
          this.recordTableData=[]
        }        

    }).catch(err => {
        console.log(err)
        
    });		
		},
  // regionFormat(id){
  //   this.regionList.forEach(item => {
  //     if(item.value_no == id) {
  //       return item.value_name;
  //     }
  //   })
  //   return '--'
  // },
  drawQrcode(code){
    if(!code) return
    var canvas = document.getElementById('canvas')
    this.QRCode.toCanvas(canvas, code, {
        errorCorrectionLevel: 'H',
        width:1000,height:1000
    }, function (error) {
        if (error) {
          this.loadingQrcode = false
        } else {}
    })
    var context = canvas.getContext('2d');
    console.log(canvas)
    var imageData = context.getImageData(0, 0, canvas.width, canvas.height);
    var img = document.getElementById("img");
    img.width = canvas.width
    img.height = canvas.height
    var context2 = img.getContext('2d');
    context2.fillStyle = "white";
    context2.fillRect(0, 0, canvas.width, canvas.height);
    context2.putImageData(imageData, 0, 50);
    context2.font = "bold 50px 微软雅黑"
    context2.fillStyle = "black"
    context2.textAlign = 'center'
    context2.fillText(`车位ID:${this.parkingForm.id}`,500,100)
    this.proQrcode = img.toDataURL("image/png");
    this.downloadproQrcode()
  },
  downloadproQrcode(){
    let a = document.createElement("a"); // 生成一个a元素
    let event = new MouseEvent("click"); // 创建一个单击事件
    a.download = '车位二维码'; // 设置图片名称
    a.href = this.proQrcode; // 将生成的URL设置为a.href属性
    a.dispatchEvent(event); // 触发a的单击事件
  },

  closeDialog(){
    this.activeName = 'parkInfo';
    this.$refs.seatInfo.clearData();
    this.$refs.parkAccessInfo.clearData();
    this.$refs.reservatInfo.clearData();
    this.$emit("closeDialog",false)
  },

  // 数据编辑处理
  processEcho(){
    let currentTime = common.currentDateArray(1);   
    setTimeout(() => {
      const comAreaInfoId = this.parkingForm.comAreaInfoId;
      const applyMode = this.parkingForm.applyMode;
      this.$set(this.parkingForm,'comAreaInfoId',comAreaInfoId.toString());
      this.$set(this.parkingForm,'applyMode',applyMode.toString());
      // 以下获取下各个子组件的数据
      this.$refs.reservatInfo.getReservationInfor(this.parkingForm); //车位预约
      this.$refs.seatInfo.getPlaceholderInfor(this.parkingForm); // 车位占位
      this.$refs.parkAccessInfo.getAllowInInfo(this.parkingForm); // 车位准入
      this.searchFormData={
        currentData: [common.timestampFormat(currentTime[0]), common.timestampFormat(currentTime[1])],
        parkId:this.parkingForm.id,
        stime:common.timestampFormat(currentTime[0])/1000,
        etime:common.timestampFormat(currentTime[1])/1000,
      }
      this.recordSearchFormData={
        currentData: [common.timestampFormat(currentTime[0]), common.timestampFormat(currentTime[1])],
        parkId:this.parkingForm.id,
        stime:common.timestampFormat(currentTime[0])/1000,
        etime:common.timestampFormat(currentTime[1])/1000,
      } 
      this.getLiftRodRecord(1,20)
      this.getCarInOutImg(this.currentPage, this.pageSize)    //识别记录
      this.getLiftRodRecord(this.currentPage, this.pageSize)  //抬杠记录
    }, 100); 

  },
  saveParklot(){
    var parkingFormInfo = JSON.parse(JSON.stringify(this.parkingForm));
    const {
      parkingLotNumber,
      comAreaInfoId,
      appointState
    } = parkingFormInfo;
    const regionList = this.regionList; //区域列表
    if(Number(appointState) == 1){}

    regionList.forEach(item=>{
      if(item.value_no == comAreaInfoId){
        parkingFormInfo.comAreaInfoName = item.value_name;
      }
    })
    this.$emit("addParkingForm", parkingFormInfo)

  },
},
}
</script>
<style  lang="scss" scoped>
.header-title{
color: #333;
font-size: 18px;
font-weight: bold;
display: flex;
align-items: center;
margin-bottom: 10px;
}

.codeHeader {
      display: inline-flex;
      position: relative;
      height: 30px;
      margin-bottom: 30px;
      align-items: center;
  
      .title {
        margin-left: 8px;
        font-size: 16px;
        width: 300px;
  
        .setting {}
      }
  
      .title::before {
        position: absolute;
        top: 5px;
        left: 0;
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-image: linear-gradient(-170deg, #6dbfff 0%, #1355d9 100%);
      }
  }

.parkInfoTab{

}
/deep/.el-drawer__close-btn{
position: absolute;
right: 20px;
top: 30px;
}

.belong-input{
display: flex;
/deep/.el-input{
  width: 130px;
  margin-right: 10px;
}
.beName-info{
  width: 180px;
}
}

/deep/.el-drawer__header{
margin-bottom: 10px;
padding: 20px 30px 0 30px;
}
.custom-drawer-info{
height: 86vh;
overflow: auto;
}

/* /deep/.el-select .el-input{
width: 300px !important;
} */

.downCodeBtn{
  text-align: center;
}

.header{
      display: inline-flex;
      position: relative;
      height: 30px;
      margin-bottom: 30px;
      align-items: center;
      color:#000;
.level-title {
       margin-left: 8px;
       font-size: 16px;
       width: 300px;
       font-weight: bold;
       .setting {}
     }
 
     .level-title::before {
       position: absolute;
       top: 5px;
       left: 0;
       content: "";
       display: inline-block;
       width: 3px;
       height: 20px;
       background-image: linear-gradient(-170deg, #6dbfff 0%, #1355d9 100%);
     }
}
.tips{
color: #999;
font-size: 14px;
background: #fff;
line-height: 20px;
margin: 6px 0;
span{
  vertical-align: middle;
  margin-right: 6px;
}
}

.el-button{
width: 120px;
}
/deep/.el-tabs__nav-wrap::after{
display: none;
}

.header-tips{
color: #B8741A;
margin-left: 20px;
margin: 0 10px;
}

/* /deep/.el-input{
width: 210px;
} */
 /deep/ .custom-drawer-info .el-select .el-input{
width: 300px !important;
}
 ::v-deep .custom-drawer-info .el-input__inner{
width: 300px;
} 

.houseInfo{
display: flex;
span{
  display: inline-block;
  width: 30px;
  margin: 0 10px;
}
.el-input{
  width: 70px;
}
}

.switch-info{
.el-input{
  width: 70px;
}
}

.dialogContainer {
    width: 450px;
    display: flex;
    justify-content: center;
  }
  .currentImg {
    width: 328px;
  }

</style>
