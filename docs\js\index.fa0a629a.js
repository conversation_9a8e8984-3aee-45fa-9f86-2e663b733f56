!function(t){function n(e){if(d[e])return d[e].exports;var r=d[e]={i:e,l:!1,exports:{}};return t[e].call(r.exports,r,r.exports,n),r.l=!0,r.exports}var d={};n.m=t,n.c=d,n.i=function(t){return t},n.d=function(t,d,e){n.o(t,d)||Object.defineProperty(t,d,{configurable:!1,enumerable:!0,get:e})},n.n=function(t){var d=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(d,"a",d),d},n.o=function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},n.p="",n(n.s=24)}([function(t,n){function d(t,n){var d=t[1]||"",r=t[3];if(!r)return d;if(n&&"function"==typeof btoa){var i=e(r);return[d].concat(r.sources.map(function(t){return"/*# sourceURL="+r.sourceRoot+t+" */"})).concat([i]).join("\n")}return[d].join("\n")}function e(t){return"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(t))))+" */"}t.exports=function(t){var n=[];return n.toString=function(){return this.map(function(n){var e=d(n,t);return n[2]?"@media "+n[2]+"{"+e+"}":e}).join("")},n.i=function(t,d){"string"==typeof t&&(t=[[null,t,""]]);for(var e={},r=0;r<this.length;r++){var i=this[r][0];"number"==typeof i&&(e[i]=!0)}for(r=0;r<t.length;r++){var a=t[r];"number"==typeof a[0]&&e[a[0]]||(d&&!a[2]?a[2]=d:d&&(a[2]="("+a[2]+") and ("+d+")"),n.push(a))}},n}},function(t,n){t.exports=function(t,n,d,e){var r,i=t=t||{},a=typeof t.default;"object"!==a&&"function"!==a||(r=t,i=t.default);var o="function"==typeof i?i.options:i;if(n&&(o.render=n.render,o.staticRenderFns=n.staticRenderFns),d&&(o._scopeId=d),e){var c=Object.create(o.computed||null);Object.keys(e).forEach(function(t){var n=e[t];c[t]=function(){return n}}),o.computed=c}return{esModule:r,exports:i,options:o}}},function(t,n,d){function e(t){for(var n=0;n<t.length;n++){var d=t[n],e=h[d.id];if(e){e.refs++;for(var r=0;r<e.parts.length;r++)e.parts[r](d.parts[r]);for(;r<d.parts.length;r++)e.parts.push(i(d.parts[r]));e.parts.length>d.parts.length&&(e.parts.length=d.parts.length)}else{for(var a=[],r=0;r<d.parts.length;r++)a.push(i(d.parts[r]));h[d.id]={id:d.id,refs:1,parts:a}}}}function r(){var t=document.createElement("style");return t.type="text/css",l.appendChild(t),t}function i(t){var n,d,e=document.querySelector('style[data-vue-ssr-id~="'+t.id+'"]');if(e){if(f)return b;e.parentNode.removeChild(e)}if(g){var i=p++;e=u||(u=r()),n=a.bind(null,e,i,!1),d=a.bind(null,e,i,!0)}else e=r(),n=o.bind(null,e),d=function(){e.parentNode.removeChild(e)};return n(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;n(t=e)}else d()}}function a(t,n,d,e){var r=d?"":e.css;if(t.styleSheet)t.styleSheet.cssText=m(n,r);else{var i=document.createTextNode(r),a=t.childNodes;a[n]&&t.removeChild(a[n]),a.length?t.insertBefore(i,a[n]):t.appendChild(i)}}function o(t,n){var d=n.css,e=n.media,r=n.sourceMap;if(e&&t.setAttribute("media",e),r&&(d+="\n/*# sourceURL="+r.sources[0]+" */",d+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(r))))+" */"),t.styleSheet)t.styleSheet.cssText=d;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(d))}}var c="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!c)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var s=d(71),h={},l=c&&(document.head||document.getElementsByTagName("head")[0]),u=null,p=0,f=!1,b=function(){},g="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());t.exports=function(t,n,d){f=d;var r=s(t,n);return e(r),function(n){for(var d=[],i=0;i<r.length;i++){var a=r[i],o=h[a.id];o.refs--,d.push(o)}n?(r=s(t,n),e(r)):r=[];for(var i=0;i<d.length;i++){var o=d[i];if(0===o.refs){for(var c=0;c<o.parts.length;c++)o.parts[c]();delete h[o.id]}}}};var m=function(){var t=[];return function(n,d){return t[n]=d,t.filter(Boolean).join("\n")}}()},function(t,n){var d;d=function(){return this}();try{d=d||Function("return this")()||(0,eval)("this")}catch(t){"object"==typeof window&&(d=window)}t.exports=d},function(t,n,d){(function(t){var d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){"object"==("undefined"==typeof window?"undefined":d(window))&&window||"object"==("undefined"==typeof self?"undefined":d(self))&&self;t(n)}(function(t){function n(t){return t.replace(/[&<>]/gm,function(t){return C[t]})}function d(t){return t.nodeName.toLowerCase()}function e(t,n){var d=t&&t.exec(n);return d&&0===d.index}function r(t){return N.test(t)}function i(t){var n,d,e,i,a=t.className+" ";if(a+=t.parentNode?t.parentNode.className:"",d=L.exec(a))return _(d[1])?d[1]:"no-highlight";for(a=a.split(/\s+/),n=0,e=a.length;e>n;n++)if(i=a[n],r(i)||_(i))return i}function a(t,n){var d,e={};for(d in t)e[d]=t[d];if(n)for(d in n)e[d]=n[d];return e}function o(t){var n=[];return function t(e,r){for(var i=e.firstChild;i;i=i.nextSibling)3===i.nodeType?r+=i.nodeValue.length:1===i.nodeType&&(n.push({event:"start",offset:r,node:i}),r=t(i,r),d(i).match(/br|hr|img|input/)||n.push({event:"stop",offset:r,node:i}));return r}(t,0),n}function c(t,e,r){function i(){return t.length&&e.length?t[0].offset!==e[0].offset?t[0].offset<e[0].offset?t:e:"start"===e[0].event?t:e:t.length?t:e}function a(t){function e(t){return" "+t.nodeName+'="'+n(t.value)+'"'}h+="<"+d(t)+w.map.call(t.attributes,e).join("")+">"}function o(t){h+="</"+d(t)+">"}function c(t){("start"===t.event?a:o)(t.node)}for(var s=0,h="",l=[];t.length||e.length;){var u=i();if(h+=n(r.substring(s,u[0].offset)),s=u[0].offset,u===t){l.reverse().forEach(o);do{c(u.splice(0,1)[0]),u=i()}while(u===t&&u.length&&u[0].offset===s);l.reverse().forEach(a)}else"start"===u[0].event?l.push(u[0].node):l.pop(),c(u.splice(0,1)[0])}return h+n(r.substr(s))}function s(t){function n(t){return t&&t.source||t}function d(d,e){return new RegExp(n(d),"m"+(t.cI?"i":"")+(e?"g":""))}function e(r,i){if(!r.compiled){if(r.compiled=!0,r.k=r.k||r.bK,r.k){var o={},c=function(n,d){t.cI&&(d=d.toLowerCase()),d.split(" ").forEach(function(t){var d=t.split("|");o[d[0]]=[n,d[1]?Number(d[1]):1]})};"string"==typeof r.k?c("keyword",r.k):x(r.k).forEach(function(t){c(t,r.k[t])}),r.k=o}r.lR=d(r.l||/\w+/,!0),i&&(r.bK&&(r.b="\\b("+r.bK.split(" ").join("|")+")\\b"),r.b||(r.b=/\B|\b/),r.bR=d(r.b),r.e||r.eW||(r.e=/\B|\b/),r.e&&(r.eR=d(r.e)),r.tE=n(r.e)||"",r.eW&&i.tE&&(r.tE+=(r.e?"|":"")+i.tE)),r.i&&(r.iR=d(r.i)),null==r.r&&(r.r=1),r.c||(r.c=[]);var s=[];r.c.forEach(function(t){t.v?t.v.forEach(function(n){s.push(a(t,n))}):s.push("self"===t?r:t)}),r.c=s,r.c.forEach(function(t){e(t,r)}),r.starts&&e(r.starts,i);var h=r.c.map(function(t){return t.bK?"\\.?("+t.b+")\\.?":t.b}).concat([r.tE,r.i]).map(n).filter(Boolean);r.t=h.length?d(h.join("|"),!0):{exec:function(){return null}}}}e(t)}function h(t,d,r,i){function a(t,n){var d,r;for(d=0,r=n.c.length;r>d;d++)if(e(n.c[d].bR,t))return n.c[d]}function o(t,n){if(e(t.eR,n)){for(;t.endsParent&&t.parent;)t=t.parent;return t}return t.eW?o(t.parent,n):void 0}function c(t,n){return!r&&e(n.iR,t)}function u(t,n){var d=y.cI?n[0].toLowerCase():n[0];return t.k.hasOwnProperty(d)&&t.k[d]}function p(t,n,d,e){var r=e?"":T.classPrefix,i='<span class="'+r,a=d?"":O;return(i+=t+'">')+n+a}function f(){var t,d,e,r;if(!x.k)return n(L);for(r="",d=0,x.lR.lastIndex=0,e=x.lR.exec(L);e;)r+=n(L.substring(d,e.index)),t=u(x,e),t?(U+=t[1],r+=p(t[0],n(e[0]))):r+=n(e[0]),d=x.lR.lastIndex,e=x.lR.exec(L);return r+n(L.substr(d))}function b(){var t="string"==typeof x.sL;if(t&&!k[x.sL])return n(L);var d=t?h(x.sL,L,!0,A[x.sL]):l(L,x.sL.length?x.sL:void 0);return x.r>0&&(U+=d.r),t&&(A[x.sL]=d.top),p(d.language,d.value,!1,!0)}function g(){N+=null!=x.sL?b():f(),L=""}function m(t){N+=t.cN?p(t.cN,"",!0):"",x=Object.create(t,{parent:{value:x}})}function v(t,n){if(L+=t,null==n)return g(),0;var d=a(n,x);if(d)return d.skip?L+=n:(d.eB&&(L+=n),g(),d.rB||d.eB||(L=n)),m(d,n),d.rB?0:n.length;var e=o(x,n);if(e){var r=x;r.skip?L+=n:(r.rE||r.eE||(L+=n),g(),r.eE&&(L=n));do{x.cN&&(N+=O),x.skip||(U+=x.r),x=x.parent}while(x!==e.parent);return e.starts&&m(e.starts,""),r.rE?0:n.length}if(c(n,x))throw new Error('Illegal lexeme "'+n+'" for mode "'+(x.cN||"<unnamed>")+'"');return L+=n,n.length||1}var y=_(t);if(!y)throw new Error('Unknown language: "'+t+'"');s(y);var w,x=i||y,A={},N="";for(w=x;w!==y;w=w.parent)w.cN&&(N=p(w.cN,"",!0)+N);var L="",U=0;try{for(var C,R,j=0;x.t.lastIndex=j,C=x.t.exec(d);)R=v(d.substring(j,C.index),C[0]),j=C.index+R;for(v(d.substr(j)),w=x;w.parent;w=w.parent)w.cN&&(N+=O);return{r:U,value:N,language:t,top:x}}catch(t){if(t.message&&-1!==t.message.indexOf("Illegal"))return{r:0,value:n(d)};throw t}}function l(t,d){d=d||T.languages||x(k);var e={r:0,value:n(t)},r=e;return d.filter(_).forEach(function(n){var d=h(n,t,!1);d.language=n,d.r>r.r&&(r=d),d.r>e.r&&(r=e,e=d)}),r.language&&(e.second_best=r),e}function u(t){return T.tabReplace||T.useBR?t.replace(U,function(t,n){return T.useBR&&"\n"===t?"<br>":T.tabReplace?n.replace(/\t/g,T.tabReplace):void 0}):t}function p(t,n,d){var e=n?A[n]:d,r=[t.trim()];return t.match(/\bhljs\b/)||r.push("hljs"),-1===t.indexOf(e)&&r.push(e),r.join(" ").trim()}function f(t){var n,d,e,a,s,f=i(t);r(f)||(T.useBR?(n=document.createElementNS("http://www.w3.org/1999/xhtml","div"),n.innerHTML=t.innerHTML.replace(/\n/g,"").replace(/<br[ \/]*>/g,"\n")):n=t,s=n.textContent,e=f?h(f,s,!0):l(s),d=o(n),d.length&&(a=document.createElementNS("http://www.w3.org/1999/xhtml","div"),a.innerHTML=e.value,e.value=c(d,o(a),s)),e.value=u(e.value),t.innerHTML=e.value,t.className=p(t.className,f,e.language),t.result={language:e.language,re:e.r},e.second_best&&(t.second_best={language:e.second_best.language,re:e.second_best.r}))}function b(t){T=a(T,t)}function g(){if(!g.called){g.called=!0;var t=document.querySelectorAll("pre code");w.forEach.call(t,f)}}function m(){addEventListener("DOMContentLoaded",g,!1),addEventListener("load",g,!1)}function v(n,d){var e=k[n]=d(t);e.aliases&&e.aliases.forEach(function(t){A[t]=n})}function y(){return x(k)}function _(t){return t=(t||"").toLowerCase(),k[t]||k[A[t]]}var w=[],x=Object.keys,k={},A={},N=/^(no-?highlight|plain|text)$/i,L=/\blang(?:uage)?-([\w-]+)\b/i,U=/((^(<[^>]+>|\t|)+|(?:\n)))/gm,O="</span>",T={classPrefix:"hljs-",tabReplace:null,useBR:!1,languages:void 0},C={"&":"&amp;","<":"&lt;",">":"&gt;"};return t.highlight=h,t.highlightAuto=l,t.fixMarkup=u,t.highlightBlock=f,t.configure=b,t.initHighlighting=g,t.initHighlightingOnLoad=m,t.registerLanguage=v,t.listLanguages=y,t.getLanguage=_,t.inherit=a,t.IR="[a-zA-Z]\\w*",t.UIR="[a-zA-Z_]\\w*",t.NR="\\b\\d+(\\.\\d+)?",t.CNR="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",t.BNR="\\b(0b[01]+)",t.RSR="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",t.BE={b:"\\\\[\\s\\S]",r:0},t.ASM={cN:"string",b:"'",e:"'",i:"\\n",c:[t.BE]},t.QSM={cN:"string",b:'"',e:'"',i:"\\n",c:[t.BE]},t.PWM={b:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|like)\b/},t.C=function(n,d,e){var r=t.inherit({cN:"comment",b:n,e:d,c:[]},e||{});return r.c.push(t.PWM),r.c.push({cN:"doctag",b:"(?:TODO|FIXME|NOTE|BUG|XXX):",r:0}),r},t.CLCM=t.C("//","$"),t.CBCM=t.C("/\\*","\\*/"),t.HCM=t.C("#","$"),t.NM={cN:"number",b:t.NR,r:0},t.CNM={cN:"number",b:t.CNR,r:0},t.BNM={cN:"number",b:t.BNR,r:0},t.CSSNM={cN:"number",b:t.NR+"(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",r:0},t.RM={cN:"regexp",b:/\//,e:/\/[gimuy]*/,i:/\n/,c:[t.BE,{b:/\[/,e:/\]/,r:0,c:[t.BE]}]},t.TM={cN:"title",b:t.IR,r:0},t.UTM={cN:"title",b:t.UIR,r:0},t.METHOD_GUARD={b:"\\.\\s*"+t.UIR,r:0},t}),t.registerLanguage("css",function(t){var n={b:/[A-Z\_\.\-]+\s*:/,rB:!0,e:";",eW:!0,c:[{cN:"attribute",b:/\S/,e:":",eE:!0,starts:{eW:!0,eE:!0,c:[{b:/[\w-]+\(/,rB:!0,c:[{cN:"built_in",b:/[\w-]+/},{b:/\(/,e:/\)/,c:[t.ASM,t.QSM]}]},t.CSSNM,t.QSM,t.ASM,t.CBCM,{cN:"number",b:"#[0-9A-Fa-f]+"},{cN:"meta",b:"!important"}]}}]};return{cI:!0,i:/[=\/|'\$]/,c:[t.CBCM,{cN:"selector-id",b:/#[A-Za-z0-9_-]+/},{cN:"selector-class",b:/\.[A-Za-z0-9_-]+/},{cN:"selector-attr",b:/\[/,e:/\]/,i:"$"},{cN:"selector-pseudo",b:/:(:)?[a-zA-Z0-9\_\-\+\(\)"'.]+/},{b:"@(font-face|page)",l:"[a-z-]+",k:"font-face page"},{b:"@",e:"[{;]",i:/:/,c:[{cN:"keyword",b:/\w+/},{b:/\s/,eW:!0,eE:!0,r:0,c:[t.ASM,t.QSM,t.CSSNM]}]},{cN:"selector-tag",b:"[a-zA-Z-][a-zA-Z0-9_-]*",r:0},{b:"{",e:"}",i:/\S/,c:[t.CBCM,n]}]}}),t.registerLanguage("xml",function(t){var n={eW:!0,i:/</,r:0,c:[{cN:"attr",b:"[A-Za-z0-9\\._:-]+",r:0},{b:/=\s*/,r:0,c:[{cN:"string",endsParent:!0,v:[{b:/"/,e:/"/},{b:/'/,e:/'/},{b:/[^\s"'=<>`]+/}]}]}]};return{aliases:["html","xhtml","rss","atom","xjb","xsd","xsl","plist"],cI:!0,c:[{cN:"meta",b:"<!DOCTYPE",e:">",r:10,c:[{b:"\\[",e:"\\]"}]},t.C("\x3c!--","--\x3e",{r:10}),{b:"<\\!\\[CDATA\\[",e:"\\]\\]>",r:10},{b:/<\?(php)?/,e:/\?>/,sL:"php",c:[{b:"/\\*",e:"\\*/",skip:!0}]},{cN:"tag",b:"<style(?=\\s|>|$)",e:">",k:{name:"style"},c:[n],starts:{e:"</style>",rE:!0,sL:["css","xml"]}},{cN:"tag",b:"<script(?=\\s|>|$)",e:">",k:{name:"script"},c:[n],starts:{e:"<\/script>",rE:!0,sL:["actionscript","javascript","handlebars","xml"]}},{cN:"meta",v:[{b:/<\?xml/,e:/\?>/,r:10},{b:/<\?\w+/,e:/\?>/}]},{cN:"tag",b:"</?",e:"/?>",c:[{cN:"name",b:/[^\/><\s]+/,r:0},n]}]}}),t.registerLanguage("json",function(t){var n={literal:"true false null"},d=[t.QSM,t.CNM],e={e:",",eW:!0,eE:!0,c:d,k:n},r={b:"{",e:"}",c:[{cN:"attr",b:/"/,e:/"/,c:[t.BE],i:"\\n"},t.inherit(e,{b:/:/})],i:"\\S"},i={b:"\\[",e:"\\]",c:[t.inherit(e)],i:"\\S"};return d.splice(d.length,0,r,i),{c:d,k:n,i:"\\S"}}),t.registerLanguage("http",function(t){var n="HTTP/[0-9\\.]+";return{aliases:["https"],i:"\\S",c:[{b:"^"+n,e:"$",c:[{cN:"number",b:"\\b\\d{3}\\b"}]},{b:"^[A-Z]+ (.*?) "+n+"$",rB:!0,e:"$",c:[{cN:"string",b:" ",e:" ",eB:!0,eE:!0},{b:n},{cN:"keyword",b:"[A-Z]+"}]},{cN:"attribute",b:"^\\w",e:": ",eE:!0,i:"\\n|\\s|=",starts:{e:"$",r:0}},{b:"\\n\\n",starts:{sL:[],eW:!0}}]}}),t.registerLanguage("javascript",function(t){var n="[A-Za-z$_][0-9A-Za-z$_]*",d={keyword:"in of if for while finally var new function do return void else break catch instanceof with throw case default try this switch continue typeof delete let yield const export super debugger as async await static import from as",literal:"true false null undefined NaN Infinity",built_in:"eval isFinite isNaN parseFloat parseInt decodeURI decodeURIComponent encodeURI encodeURIComponent escape unescape Object Function Boolean Error EvalError InternalError RangeError ReferenceError StopIteration SyntaxError TypeError URIError Number Math Date String RegExp Array Float32Array Float64Array Int16Array Int32Array Int8Array Uint16Array Uint32Array Uint8Array Uint8ClampedArray ArrayBuffer DataView JSON Intl arguments require module console window document Symbol Set Map WeakSet WeakMap Proxy Reflect Promise"},e={cN:"number",v:[{b:"\\b(0[bB][01]+)"},{b:"\\b(0[oO][0-7]+)"},{b:t.CNR}],r:0},r={cN:"subst",b:"\\$\\{",e:"\\}",k:d,c:[]},i={cN:"string",b:"`",e:"`",c:[t.BE,r]};r.c=[t.ASM,t.QSM,i,e,t.RM];var a=r.c.concat([t.CBCM,t.CLCM]);return{aliases:["js","jsx"],k:d,c:[{cN:"meta",r:10,b:/^\s*['"]use (strict|asm)['"]/},{cN:"meta",b:/^#!/,e:/$/},t.ASM,t.QSM,i,t.CLCM,t.CBCM,e,{b:/[{,]\s*/,r:0,c:[{b:n+"\\s*:",rB:!0,r:0,c:[{cN:"attr",b:n,r:0}]}]},{b:"("+t.RSR+"|\\b(case|return|throw)\\b)\\s*",k:"return throw case",c:[t.CLCM,t.CBCM,t.RM,{cN:"function",b:"(\\(.*?\\)|"+n+")\\s*=>",rB:!0,e:"\\s*=>",c:[{cN:"params",v:[{b:n},{b:/\(\s*\)/},{b:/\(/,e:/\)/,eB:!0,eE:!0,k:d,c:a}]}]},{b:/</,e:/(\/\w+|\w+\/)>/,sL:"xml",c:[{b:/<\w+\s*\/>/,skip:!0},{b:/<\w+/,e:/(\/\w+|\w+\/)>/,skip:!0,c:[{b:/<\w+\s*\/>/,skip:!0},"self"]}]}],r:0},{cN:"function",bK:"function",e:/\{/,eE:!0,c:[t.inherit(t.TM,{b:n}),{cN:"params",b:/\(/,e:/\)/,eB:!0,eE:!0,c:a}],i:/\[|%/},{b:/\$[(.]/},t.METHOD_GUARD,{cN:"class",bK:"class",e:/[{;=]/,eE:!0,i:/[:"\[\]]/,c:[{bK:"extends"},t.UTM]},{bK:"constructor",e:/\{/,eE:!0}],i:/#(?!!)/}})}).call(n,d(4))},function(t,n){function d(){throw new Error("setTimeout has not been defined")}function e(){throw new Error("clearTimeout has not been defined")}function r(t){if(h===setTimeout)return setTimeout(t,0);if((h===d||!h)&&setTimeout)return h=setTimeout,setTimeout(t,0);try{return h(t,0)}catch(n){try{return h.call(null,t,0)}catch(n){return h.call(this,t,0)}}}function i(t){if(l===clearTimeout)return clearTimeout(t);if((l===e||!l)&&clearTimeout)return l=clearTimeout,clearTimeout(t);try{return l(t)}catch(n){try{return l.call(null,t)}catch(n){return l.call(this,t)}}}function a(){b&&p&&(b=!1,p.length?f=p.concat(f):g=-1,f.length&&o())}function o(){if(!b){var t=r(a);b=!0;for(var n=f.length;n;){for(p=f,f=[];++g<n;)p&&p[g].run();g=-1,n=f.length}p=null,b=!1,i(t)}}function c(t,n){this.fun=t,this.array=n}function s(){}var h,l,u=t.exports={};!function(){try{h="function"==typeof setTimeout?setTimeout:d}catch(t){h=d}try{l="function"==typeof clearTimeout?clearTimeout:e}catch(t){l=e}}();var p,f=[],b=!1,g=-1;u.nextTick=function(t){var n=new Array(arguments.length-1);if(arguments.length>1)for(var d=1;d<arguments.length;d++)n[d-1]=arguments[d];f.push(new c(t,n)),1!==f.length||b||r(o)},c.prototype.run=function(){this.fun.apply(null,this.array)},u.title="browser",u.browser=!0,u.env={},u.argv=[],u.version="",u.versions={},u.on=s,u.addListener=s,u.once=s,u.off=s,u.removeListener=s,u.removeAllListeners=s,u.emit=s,u.prependListener=s,u.prependOnceListener=s,u.listeners=function(t){return[]},u.binding=function(t){throw new Error("process.binding is not supported")},u.cwd=function(){return"/"},u.chdir=function(t){throw new Error("process.chdir is not supported")},u.umask=function(){return 0}},function(t,n,d){function e(t,n){for(var d=0;d<t.length;d++){var e=t[d],r=f[e.id];if(r){r.refs++;for(var i=0;i<r.parts.length;i++)r.parts[i](e.parts[i]);for(;i<e.parts.length;i++)r.parts.push(h(e.parts[i],n))}else{for(var a=[],i=0;i<e.parts.length;i++)a.push(h(e.parts[i],n));f[e.id]={id:e.id,refs:1,parts:a}}}}function r(t,n){for(var d=[],e={},r=0;r<t.length;r++){var i=t[r],a=n.base?i[0]+n.base:i[0],o=i[1],c=i[2],s=i[3],h={css:o,media:c,sourceMap:s};e[a]?e[a].parts.push(h):d.push(e[a]={id:a,parts:[h]})}return d}function i(t,n){var d=g(t.insertInto);if(!d)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var e=y[y.length-1];if("top"===t.insertAt)e?e.nextSibling?d.insertBefore(n,e.nextSibling):d.appendChild(n):d.insertBefore(n,d.firstChild),y.push(n);else if("bottom"===t.insertAt)d.appendChild(n);else{if("object"!=typeof t.insertAt||!t.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");var r=g(t.insertInto+" "+t.insertAt.before);d.insertBefore(n,r)}}function a(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t);var n=y.indexOf(t);n>=0&&y.splice(n,1)}function o(t){var n=document.createElement("style");return t.attrs.type="text/css",s(n,t.attrs),i(t,n),n}function c(t){var n=document.createElement("link");return t.attrs.type="text/css",t.attrs.rel="stylesheet",s(n,t.attrs),i(t,n),n}function s(t,n){Object.keys(n).forEach(function(d){t.setAttribute(d,n[d])})}function h(t,n){var d,e,r,i;if(n.transform&&t.css){if(!(i=n.transform(t.css)))return function(){};t.css=i}if(n.singleton){var s=v++;d=m||(m=o(n)),e=l.bind(null,d,s,!1),r=l.bind(null,d,s,!0)}else t.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(d=c(n),e=p.bind(null,d,n),r=function(){a(d),d.href&&URL.revokeObjectURL(d.href)}):(d=o(n),e=u.bind(null,d),r=function(){a(d)});return e(t),function(n){if(n){if(n.css===t.css&&n.media===t.media&&n.sourceMap===t.sourceMap)return;e(t=n)}else r()}}function l(t,n,d,e){var r=d?"":e.css;if(t.styleSheet)t.styleSheet.cssText=w(n,r);else{var i=document.createTextNode(r),a=t.childNodes;a[n]&&t.removeChild(a[n]),a.length?t.insertBefore(i,a[n]):t.appendChild(i)}}function u(t,n){var d=n.css,e=n.media;if(e&&t.setAttribute("media",e),t.styleSheet)t.styleSheet.cssText=d;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(d))}}function p(t,n,d){var e=d.css,r=d.sourceMap,i=void 0===n.convertToAbsoluteUrls&&r;(n.convertToAbsoluteUrls||i)&&(e=_(e)),r&&(e+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(r))))+" */");var a=new Blob([e],{type:"text/css"}),o=t.href;t.href=URL.createObjectURL(a),o&&URL.revokeObjectURL(o)}var f={},b=function(t){var n;return function(){return void 0===n&&(n=t.apply(this,arguments)),n}}(function(){return window&&document&&document.all&&!window.atob}),g=function(t){var n={};return function(d){if(void 0===n[d]){var e=t.call(this,d);if(e instanceof window.HTMLIFrameElement)try{e=e.contentDocument.head}catch(t){e=null}n[d]=e}return n[d]}}(function(t){return document.querySelector(t)}),m=null,v=0,y=[],_=d(43);t.exports=function(t,n){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");n=n||{},n.attrs="object"==typeof n.attrs?n.attrs:{},n.singleton||"boolean"==typeof n.singleton||(n.singleton=b()),n.insertInto||(n.insertInto="head"),n.insertAt||(n.insertAt="bottom");var d=r(t,n);return e(d,n),function(t){for(var i=[],a=0;a<d.length;a++){var o=d[a],c=f[o.id];c.refs--,i.push(c)}if(t){e(r(t,n),n)}for(var a=0;a<i.length;a++){var c=i[a];if(0===c.refs){for(var s=0;s<c.parts.length;s++)c.parts[s]();delete f[c.id]}}}};var w=function(){var t=[];return function(n,d){return t[n]=d,t.filter(Boolean).join("\n")}}()},function(t,n,d){d(66);var e=d(1)(d(20),d(59),null,null);t.exports=e.exports},function(t,n,d){"use strict";var e=d(56),r=d.n(e),i=d(54),a=d.n(i),o=d(55),c=d.n(o);n.a={routes:[{path:"/",name:"Index",component:r.a},{path:"/basics",name:"Basics",component:a.a,children:[{path:"quickstart",name:"QuickStart",component:c.a}]},{path:"/desc/:article",name:"desc",component:a.a},{path:"/quick/:article",name:"quick",component:a.a}]}},function(t,n,d){"use strict";t.exports=d(35).polyfill()},function(t,n){!function(n){"use strict";function d(t,n,d,e){var i=n&&n.prototype instanceof r?n:r,a=Object.create(i.prototype),o=new p(e||[]);return a._invoke=s(t,d,o),a}function e(t,n,d){try{return{type:"normal",arg:t.call(n,d)}}catch(t){return{type:"throw",arg:t}}}function r(){}function i(){}function a(){}function o(t){["next","throw","return"].forEach(function(n){t[n]=function(t){return this._invoke(n,t)}})}function c(t){function n(d,r,i,a){var o=e(t[d],t,r);if("throw"!==o.type){var c=o.arg,s=c.value;return s&&"object"==typeof s&&v.call(s,"__await")?Promise.resolve(s.__await).then(function(t){n("next",t,i,a)},function(t){n("throw",t,i,a)}):Promise.resolve(s).then(function(t){c.value=t,i(c)},a)}a(o.arg)}function d(t,d){function e(){return new Promise(function(e,r){n(t,d,e,r)})}return r=r?r.then(e,e):e()}var r;this._invoke=d}function s(t,n,d){var r=N;return function(i,a){if(r===U)throw new Error("Generator is already running");if(r===O){if("throw"===i)throw a;return b()}for(d.method=i,d.arg=a;;){var o=d.delegate;if(o){var c=h(o,d);if(c){if(c===T)continue;return c}}if("next"===d.method)d.sent=d._sent=d.arg;else if("throw"===d.method){if(r===N)throw r=O,d.arg;d.dispatchException(d.arg)}else"return"===d.method&&d.abrupt("return",d.arg);r=U;var s=e(t,n,d);if("normal"===s.type){if(r=d.done?O:L,s.arg===T)continue;return{value:s.arg,done:d.done}}"throw"===s.type&&(r=O,d.method="throw",d.arg=s.arg)}}}function h(t,n){var d=t.iterator[n.method];if(d===g){if(n.delegate=null,"throw"===n.method){if(t.iterator.return&&(n.method="return",n.arg=g,h(t,n),"throw"===n.method))return T;n.method="throw",n.arg=new TypeError("The iterator does not provide a 'throw' method")}return T}var r=e(d,t.iterator,n.arg);if("throw"===r.type)return n.method="throw",n.arg=r.arg,n.delegate=null,T;var i=r.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=g),n.delegate=null,T):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,T)}function l(t){var n={tryLoc:t[0]};1 in t&&(n.catchLoc=t[1]),2 in t&&(n.finallyLoc=t[2],n.afterLoc=t[3]),this.tryEntries.push(n)}function u(t){var n=t.completion||{};n.type="normal",delete n.arg,t.completion=n}function p(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(l,this),this.reset(!0)}function f(t){if(t){var n=t[_];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var d=-1,e=function n(){for(;++d<t.length;)if(v.call(t,d))return n.value=t[d],n.done=!1,n;return n.value=g,n.done=!0,n};return e.next=e}}return{next:b}}function b(){return{value:g,done:!0}}var g,m=Object.prototype,v=m.hasOwnProperty,y="function"==typeof Symbol?Symbol:{},_=y.iterator||"@@iterator",w=y.asyncIterator||"@@asyncIterator",x=y.toStringTag||"@@toStringTag",k="object"==typeof t,A=n.regeneratorRuntime;if(A)return void(k&&(t.exports=A));A=n.regeneratorRuntime=k?t.exports:{},A.wrap=d;var N="suspendedStart",L="suspendedYield",U="executing",O="completed",T={},C={};C[_]=function(){return this};var R=Object.getPrototypeOf,j=R&&R(R(f([])));j&&j!==m&&v.call(j,_)&&(C=j);var F=a.prototype=r.prototype=Object.create(C);i.prototype=F.constructor=a,a.constructor=i,a[x]=i.displayName="GeneratorFunction",A.isGeneratorFunction=function(t){var n="function"==typeof t&&t.constructor;return!!n&&(n===i||"GeneratorFunction"===(n.displayName||n.name))},A.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,a):(t.__proto__=a,x in t||(t[x]="GeneratorFunction")),t.prototype=Object.create(F),t},A.awrap=function(t){return{__await:t}},o(c.prototype),c.prototype[w]=function(){return this},A.AsyncIterator=c,A.async=function(t,n,e,r){var i=new c(d(t,n,e,r));return A.isGeneratorFunction(n)?i:i.next().then(function(t){return t.done?t.value:i.next()})},o(F),F[x]="Generator",F[_]=function(){return this},F.toString=function(){return"[object Generator]"},A.keys=function(t){var n=[];for(var d in t)n.push(d);return n.reverse(),function d(){for(;n.length;){var e=n.pop();if(e in t)return d.value=e,d.done=!1,d}return d.done=!0,d}},A.values=f,p.prototype={constructor:p,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=g,this.done=!1,this.delegate=null,this.method="next",this.arg=g,this.tryEntries.forEach(u),!t)for(var n in this)"t"===n.charAt(0)&&v.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=g)},stop:function(){this.done=!0;var t=this.tryEntries[0],n=t.completion;if("throw"===n.type)throw n.arg;return this.rval},dispatchException:function(t){function n(n,e){return i.type="throw",i.arg=t,d.next=n,e&&(d.method="next",d.arg=g),!!e}if(this.done)throw t;for(var d=this,e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e],i=r.completion;if("root"===r.tryLoc)return n("end");if(r.tryLoc<=this.prev){var a=v.call(r,"catchLoc"),o=v.call(r,"finallyLoc");if(a&&o){if(this.prev<r.catchLoc)return n(r.catchLoc,!0);if(this.prev<r.finallyLoc)return n(r.finallyLoc)}else if(a){if(this.prev<r.catchLoc)return n(r.catchLoc,!0)}else{if(!o)throw new Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return n(r.finallyLoc)}}}},abrupt:function(t,n){for(var d=this.tryEntries.length-1;d>=0;--d){var e=this.tryEntries[d];if(e.tryLoc<=this.prev&&v.call(e,"finallyLoc")&&this.prev<e.finallyLoc){var r=e;break}}r&&("break"===t||"continue"===t)&&r.tryLoc<=n&&n<=r.finallyLoc&&(r=null);var i=r?r.completion:{};return i.type=t,i.arg=n,r?(this.method="next",this.next=r.finallyLoc,T):this.complete(i)},complete:function(t,n){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&n&&(this.next=n),T},finish:function(t){for(var n=this.tryEntries.length-1;n>=0;--n){var d=this.tryEntries[n];if(d.finallyLoc===t)return this.complete(d.completion,d.afterLoc),u(d),T}},catch:function(t){for(var n=this.tryEntries.length-1;n>=0;--n){var d=this.tryEntries[n];if(d.tryLoc===t){var e=d.completion;if("throw"===e.type){var r=e.arg;u(d)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,d){return this.delegate={iterator:f(t),resultName:n,nextLoc:d},"next"===this.method&&(this.arg=g),T}}}(function(){return this}()||Function("return this")())},function(t,n,d){var e=d(26);"string"==typeof e&&(e=[[t.i,e,""]]);var r={hmr:!0};r.transform=void 0;d(6)(e,r);e.locals&&(t.exports=e.locals)},function(t,n,d){var e=d(27);"string"==typeof e&&(e=[[t.i,e,""]]);var r={hmr:!0};r.transform=void 0;d(6)(e,r);e.locals&&(t.exports=e.locals)},function(t,n,d){d(64);var e=d(1)(d(21),d(57),null,null);t.exports=e.exports},function(t,n,d){"use strict";function e(t,n){if(!t)throw new Error("[vue-router] "+n)}function r(t,n){t||"undefined"!=typeof console&&console.warn("[vue-router] "+n)}function i(t){return Object.prototype.toString.call(t).indexOf("Error")>-1}function a(t,n){switch(typeof n){case"undefined":return;case"object":return n;case"function":return n(t);case"boolean":return n?t.params:void 0;default:r(!1,'props in "'+t.path+'" is a '+typeof n+", expecting an object, function or boolean.")}}function o(t,n){for(var d in n)t[d]=n[d];return t}function c(t,n,d){void 0===n&&(n={});var e,i=d||s;try{e=i(t||"")}catch(t){r(!1,t.message),e={}}for(var a in n)e[a]=n[a];return e}function s(t){var n={};return(t=t.trim().replace(/^(\?|#|&)/,""))?(t.split("&").forEach(function(t){var d=t.replace(/\+/g," ").split("="),e=Bt(d.shift()),r=d.length>0?Bt(d.join("=")):null;void 0===n[e]?n[e]=r:Array.isArray(n[e])?n[e].push(r):n[e]=[n[e],r]}),n):n}function h(t){var n=t?Object.keys(t).map(function(n){var d=t[n];if(void 0===d)return"";if(null===d)return Kt(n);if(Array.isArray(d)){var e=[];return d.forEach(function(t){void 0!==t&&(null===t?e.push(Kt(n)):e.push(Kt(n)+"="+Kt(t)))}),e.join("&")}return Kt(n)+"="+Kt(d)}).filter(function(t){return t.length>0}).join("&"):null;return n?"?"+n:""}function l(t,n,d,e){var r=e&&e.options.stringifyQuery,i=n.query||{};try{i=u(i)}catch(t){}var a={name:n.name||t&&t.name,meta:t&&t.meta||{},path:n.path||"/",hash:n.hash||"",query:i,params:n.params||{},fullPath:f(n,r),matched:t?p(t):[]};return d&&(a.redirectedFrom=f(d,r)),Object.freeze(a)}function u(t){if(Array.isArray(t))return t.map(u);if(t&&"object"==typeof t){var n={};for(var d in t)n[d]=u(t[d]);return n}return t}function p(t){for(var n=[];t;)n.unshift(t),t=t.parent;return n}function f(t,n){var d=t.path,e=t.query;void 0===e&&(e={});var r=t.hash;void 0===r&&(r="");var i=n||h;return(d||"/")+i(e)+r}function b(t,n){return n===Dt?t===n:!!n&&(t.path&&n.path?t.path.replace(zt,"")===n.path.replace(zt,"")&&t.hash===n.hash&&g(t.query,n.query):!(!t.name||!n.name)&&(t.name===n.name&&t.hash===n.hash&&g(t.query,n.query)&&g(t.params,n.params)))}function g(t,n){if(void 0===t&&(t={}),void 0===n&&(n={}),!t||!n)return t===n;var d=Object.keys(t),e=Object.keys(n);return d.length===e.length&&d.every(function(d){var e=t[d],r=n[d];return"object"==typeof e&&"object"==typeof r?g(e,r):String(e)===String(r)})}function m(t,n){return 0===t.path.replace(zt,"/").indexOf(n.path.replace(zt,"/"))&&(!n.hash||t.hash===n.hash)&&v(t.query,n.query)}function v(t,n){for(var d in n)if(!(d in t))return!1;return!0}function y(t){if(!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey||t.defaultPrevented||void 0!==t.button&&0!==t.button)){if(t.currentTarget&&t.currentTarget.getAttribute){if(/\b_blank\b/i.test(t.currentTarget.getAttribute("target")))return}return t.preventDefault&&t.preventDefault(),!0}}function _(t){if(t)for(var n,d=0;d<t.length;d++){if(n=t[d],"a"===n.tag)return n;if(n.children&&(n=_(n.children)))return n}}function w(t){if(!w.installed||Et!==t){w.installed=!0,Et=t;var n=function(t){return void 0!==t},d=function(t,d){var e=t.$options._parentVnode;n(e)&&n(e=e.data)&&n(e=e.registerRouteInstance)&&e(t,d)};t.mixin({beforeCreate:function(){n(this.$options.router)?(this._routerRoot=this,this._router=this.$options.router,this._router.init(this),t.util.defineReactive(this,"_route",this._router.history.current)):this._routerRoot=this.$parent&&this.$parent._routerRoot||this,d(this,this)},destroyed:function(){d(this)}}),Object.defineProperty(t.prototype,"$router",{get:function(){return this._routerRoot._router}}),Object.defineProperty(t.prototype,"$route",{get:function(){return this._routerRoot._route}}),t.component("router-view",$t),t.component("router-link",Ht);var e=t.config.optionMergeStrategies;e.beforeRouteEnter=e.beforeRouteLeave=e.beforeRouteUpdate=e.created}}function x(t,n,d){var e=t.charAt(0);if("/"===e)return t;if("?"===e||"#"===e)return n+t;var r=n.split("/");d&&r[r.length-1]||r.pop();for(var i=t.replace(/^\//,"").split("/"),a=0;a<i.length;a++){var o=i[a];".."===o?r.pop():"."!==o&&r.push(o)}return""!==r[0]&&r.unshift(""),r.join("/")}function k(t){var n="",d="",e=t.indexOf("#");e>=0&&(n=t.slice(e),t=t.slice(0,e));var r=t.indexOf("?");return r>=0&&(d=t.slice(r+1),t=t.slice(0,r)),{path:t,query:d,hash:n}}function A(t){return t.replace(/\/\//g,"/")}function N(t,n){for(var d,e=[],r=0,i=0,a="",o=n&&n.delimiter||"/";null!=(d=tn.exec(t));){var c=d[0],s=d[1],h=d.index;if(a+=t.slice(i,h),i=h+c.length,s)a+=s[1];else{var l=t[i],u=d[2],p=d[3],f=d[4],b=d[5],g=d[6],m=d[7];a&&(e.push(a),a="");var v=null!=u&&null!=l&&l!==u,y="+"===g||"*"===g,_="?"===g||"*"===g,w=d[2]||o,x=f||b;e.push({name:p||r++,prefix:u||"",delimiter:w,optional:_,repeat:y,partial:v,asterisk:!!m,pattern:x?R(x):m?".*":"[^"+C(w)+"]+?"})}}return i<t.length&&(a+=t.substr(i)),a&&e.push(a),e}function L(t,n){return T(N(t,n))}function U(t){return encodeURI(t).replace(/[\/?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}function O(t){return encodeURI(t).replace(/[?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}function T(t){for(var n=new Array(t.length),d=0;d<t.length;d++)"object"==typeof t[d]&&(n[d]=new RegExp("^(?:"+t[d].pattern+")$"));return function(d,e){for(var r="",i=d||{},a=e||{},o=a.pretty?U:encodeURIComponent,c=0;c<t.length;c++){var s=t[c];if("string"!=typeof s){var h,l=i[s.name];if(null==l){if(s.optional){s.partial&&(r+=s.prefix);continue}throw new TypeError('Expected "'+s.name+'" to be defined')}if(Wt(l)){if(!s.repeat)throw new TypeError('Expected "'+s.name+'" to not repeat, but received `'+JSON.stringify(l)+"`");if(0===l.length){if(s.optional)continue;throw new TypeError('Expected "'+s.name+'" to not be empty')}for(var u=0;u<l.length;u++){if(h=o(l[u]),!n[c].test(h))throw new TypeError('Expected all "'+s.name+'" to match "'+s.pattern+'", but received `'+JSON.stringify(h)+"`");r+=(0===u?s.prefix:s.delimiter)+h}}else{if(h=s.asterisk?O(l):o(l),!n[c].test(h))throw new TypeError('Expected "'+s.name+'" to match "'+s.pattern+'", but received "'+h+'"');r+=s.prefix+h}}else r+=s}return r}}function C(t){return t.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function R(t){return t.replace(/([=!:$\/()])/g,"\\$1")}function j(t,n){return t.keys=n,t}function F(t){return t.sensitive?"":"i"}function S(t,n){var d=t.source.match(/\((?!\?)/g);if(d)for(var e=0;e<d.length;e++)n.push({name:e,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return j(t,n)}function E(t,n,d){for(var e=[],r=0;r<t.length;r++)e.push(M(t[r],n,d).source);return j(new RegExp("(?:"+e.join("|")+")",F(d)),n)}function $(t,n,d){return I(N(t,d),n,d)}function I(t,n,d){Wt(n)||(d=n||d,n=[]),d=d||{};for(var e=d.strict,r=!1!==d.end,i="",a=0;a<t.length;a++){var o=t[a];if("string"==typeof o)i+=C(o);else{var c=C(o.prefix),s="(?:"+o.pattern+")";n.push(o),o.repeat&&(s+="(?:"+c+s+")*"),s=o.optional?o.partial?c+"("+s+")?":"(?:"+c+"("+s+"))?":c+"("+s+")",i+=s}}var h=C(d.delimiter||"/"),l=i.slice(-h.length)===h;return e||(i=(l?i.slice(0,-h.length):i)+"(?:"+h+"(?=$))?"),i+=r?"$":e&&l?"":"(?="+h+"|$)",j(new RegExp("^"+i,F(d)),n)}function M(t,n,d){return Wt(n)||(d=n||d,n=[]),d=d||{},t instanceof RegExp?S(t,n):Wt(t)?E(t,n,d):$(t,n,d)}function P(t,n,d){try{return(nn[t]||(nn[t]=Jt.compile(t)))(n||{},{pretty:!0})}catch(t){return r(!1,"missing param for "+d+": "+t.message),""}}function K(t,n,d,e){var r=n||[],i=d||Object.create(null),a=e||Object.create(null);t.forEach(function(t){B(r,i,a,t)});for(var o=0,c=r.length;o<c;o++)"*"===r[o]&&(r.push(r.splice(o,1)[0]),c--,o--);return{pathList:r,pathMap:i,nameMap:a}}function B(t,n,d,i,a,o){var c=i.path,s=i.name;e(null!=c,'"path" is required in a route configuration.'),e("string"!=typeof i.component,'route config "component" for path: '+String(c||s)+" cannot be a string id. Use an actual component instead.");var h=i.pathToRegexpOptions||{},l=D(c,a,h.strict);"boolean"==typeof i.caseSensitive&&(h.sensitive=i.caseSensitive);var u={path:l,regex:z(l,h),components:i.components||{default:i.component},instances:{},name:s,parent:a,matchAs:o,redirect:i.redirect,beforeEnter:i.beforeEnter,meta:i.meta||{},props:null==i.props?{}:i.components?i.props:{default:i.props}};if(i.children&&(i.name&&!i.redirect&&i.children.some(function(t){return/^\/?$/.test(t.path)})&&r(!1,"Named Route '"+i.name+"' has a default child route. When navigating to this named route (:to=\"{name: '"+i.name+"'\"), the default child route will not be rendered. Remove the name from this route and use the name of the default child route for named links instead."),i.children.forEach(function(e){var r=o?A(o+"/"+e.path):void 0;B(t,n,d,e,u,r)})),void 0!==i.alias){(Array.isArray(i.alias)?i.alias:[i.alias]).forEach(function(e){var r={path:e,children:i.children};B(t,n,d,r,a,u.path||"/")})}n[u.path]||(t.push(u.path),n[u.path]=u),s&&(d[s]?o||r(!1,'Duplicate named routes definition: { name: "'+s+'", path: "'+u.path+'" }'):d[s]=u)}function z(t,n){var d=Jt(t,[],n),e=Object.create(null);return d.keys.forEach(function(n){r(!e[n.name],'Duplicate param keys in route with path: "'+t+'"'),e[n.name]=!0}),d}function D(t,n,d){return d||(t=t.replace(/\/$/,"")),"/"===t[0]?t:null==n?t:A(n.path+"/"+t)}function q(t,n,d,e){var i="string"==typeof t?{path:t}:t;if(i.name||i._normalized)return i;if(!i.path&&i.params&&n){i=Q({},i),i._normalized=!0;var a=Q(Q({},n.params),i.params);if(n.name)i.name=n.name,i.params=a;else if(n.matched.length){var o=n.matched[n.matched.length-1].path;i.path=P(o,a,"path "+n.path)}else r(!1,"relative params navigation requires a current route.");return i}var s=k(i.path||""),h=n&&n.path||"/",l=s.path?x(s.path,h,d||i.append):h,u=c(s.query,i.query,e&&e.options.parseQuery),p=i.hash||s.hash;return p&&"#"!==p.charAt(0)&&(p="#"+p),{_normalized:!0,path:l,query:u,hash:p}}function Q(t,n){for(var d in n)t[d]=n[d];return t}function H(t,n){function d(t){K(t,h,u,p)}function i(t,d,e){var i=q(t,d,!1,n),a=i.name;if(a){var o=p[a];if(r(o,"Route with name '"+a+"' does not exist"),!o)return c(null,i);var s=o.regex.keys.filter(function(t){return!t.optional}).map(function(t){return t.name});if("object"!=typeof i.params&&(i.params={}),d&&"object"==typeof d.params)for(var l in d.params)!(l in i.params)&&s.indexOf(l)>-1&&(i.params[l]=d.params[l]);if(o)return i.path=P(o.path,i.params,'named route "'+a+'"'),c(o,i,e)}else if(i.path){i.params={};for(var f=0;f<h.length;f++){var b=h[f],g=u[b];if(V(g.regex,i.path,i.params))return c(g,i,e)}}return c(null,i)}function a(t,d){var a=t.redirect,o="function"==typeof a?a(l(t,d,null,n)):a;if("string"==typeof o&&(o={path:o}),!o||"object"!=typeof o)return r(!1,"invalid redirect option: "+JSON.stringify(o)),c(null,d);var s=o,h=s.name,u=s.path,f=d.query,b=d.hash,g=d.params;if(f=s.hasOwnProperty("query")?s.query:f,b=s.hasOwnProperty("hash")?s.hash:b,g=s.hasOwnProperty("params")?s.params:g,h){return e(p[h],'redirect failed: named route "'+h+'" not found.'),i({_normalized:!0,name:h,query:f,hash:b,params:g},void 0,d)}if(u){var m=W(u,t);return i({_normalized:!0,path:P(m,g,'redirect route with path "'+m+'"'),query:f,hash:b},void 0,d)}return r(!1,"invalid redirect option: "+JSON.stringify(o)),c(null,d)}function o(t,n,d){var e=P(d,n.params,'aliased route with path "'+d+'"'),r=i({_normalized:!0,path:e});if(r){var a=r.matched,o=a[a.length-1];return n.params=r.params,c(o,n)}return c(null,n)}function c(t,d,e){return t&&t.redirect?a(t,e||d):t&&t.matchAs?o(t,d,t.matchAs):l(t,d,e,n)}var s=K(t),h=s.pathList,u=s.pathMap,p=s.nameMap;return{match:i,addRoutes:d}}function V(t,n,d){var e=n.match(t);if(!e)return!1;if(!d)return!0;for(var r=1,i=e.length;r<i;++r){var a=t.keys[r-1],o="string"==typeof e[r]?decodeURIComponent(e[r]):e[r];a&&(d[a.name]=o)}return!0}function W(t,n){return x(t,n.parent?n.parent.path:"/",!0)}function J(){window.history.replaceState({key:at()},""),window.addEventListener("popstate",function(t){G(),t.state&&t.state.key&&ot(t.state.key)})}function X(t,n,d,r){if(t.app){var i=t.options.scrollBehavior;i&&(e("function"==typeof i,"scrollBehavior must be a function"),t.app.$nextTick(function(){var t=Z(),a=i(n,d,r?t:null);a&&("function"==typeof a.then?a.then(function(n){rt(n,t)}).catch(function(t){e(!1,t.toString())}):rt(a,t))}))}}function G(){var t=at();t&&(dn[t]={x:window.pageXOffset,y:window.pageYOffset})}function Z(){var t=at();if(t)return dn[t]}function Y(t,n){var d=document.documentElement,e=d.getBoundingClientRect(),r=t.getBoundingClientRect();return{x:r.left-e.left-n.x,y:r.top-e.top-n.y}}function tt(t){return et(t.x)||et(t.y)}function nt(t){return{x:et(t.x)?t.x:window.pageXOffset,y:et(t.y)?t.y:window.pageYOffset}}function dt(t){return{x:et(t.x)?t.x:0,y:et(t.y)?t.y:0}}function et(t){return"number"==typeof t}function rt(t,n){var d="object"==typeof t;if(d&&"string"==typeof t.selector){var e=document.querySelector(t.selector);if(e){var r=t.offset&&"object"==typeof t.offset?t.offset:{};r=dt(r),n=Y(e,r)}else tt(t)&&(n=nt(t))}else d&&tt(t)&&(n=nt(t));n&&window.scrollTo(n.x,n.y)}function it(){return rn.now().toFixed(3)}function at(){return an}function ot(t){an=t}function ct(t,n){G();var d=window.history;try{n?d.replaceState({key:an},"",t):(an=it(),d.pushState({key:an},"",t))}catch(d){window.location[n?"replace":"assign"](t)}}function st(t){ct(t,!0)}function ht(t,n,d){var e=function(r){r>=t.length?d():t[r]?n(t[r],function(){e(r+1)}):e(r+1)};e(0)}function lt(t){return function(n,d,e){var a=!1,o=0,c=null;ut(t,function(t,n,d,s){if("function"==typeof t&&void 0===t.cid){a=!0,o++;var h,l=bt(function(n){ft(n)&&(n=n.default),t.resolved="function"==typeof n?n:Et.extend(n),d.components[s]=n,--o<=0&&e()}),u=bt(function(t){var n="Failed to resolve async component "+s+": "+t;r(!1,n),c||(c=i(t)?t:new Error(n),e(c))});try{h=t(l,u)}catch(t){u(t)}if(h)if("function"==typeof h.then)h.then(l,u);else{var p=h.component;p&&"function"==typeof p.then&&p.then(l,u)}}}),a||e()}}function ut(t,n){return pt(t.map(function(t){return Object.keys(t.components).map(function(d){return n(t.components[d],t.instances[d],t,d)})}))}function pt(t){return Array.prototype.concat.apply([],t)}function ft(t){return t.__esModule||on&&"Module"===t[Symbol.toStringTag]}function bt(t){var n=!1;return function(){for(var d=[],e=arguments.length;e--;)d[e]=arguments[e];if(!n)return n=!0,t.apply(this,d)}}function gt(t){if(!t)if(Vt){var n=document.querySelector("base");t=n&&n.getAttribute("href")||"/",t=t.replace(/^https?:\/\/[^\/]+/,"")}else t="/";return"/"!==t.charAt(0)&&(t="/"+t),t.replace(/\/$/,"")}function mt(t,n){var d,e=Math.max(t.length,n.length);for(d=0;d<e&&t[d]===n[d];d++);return{updated:n.slice(0,d),activated:n.slice(d),deactivated:t.slice(d)}}function vt(t,n,d,e){var r=ut(t,function(t,e,r,i){var a=yt(t,n);if(a)return Array.isArray(a)?a.map(function(t){return d(t,e,r,i)}):d(a,e,r,i)});return pt(e?r.reverse():r)}function yt(t,n){return"function"!=typeof t&&(t=Et.extend(t)),t.options[n]}function _t(t){return vt(t,"beforeRouteLeave",xt,!0)}function wt(t){return vt(t,"beforeRouteUpdate",xt)}function xt(t,n){if(n)return function(){return t.apply(n,arguments)}}function kt(t,n,d){return vt(t,"beforeRouteEnter",function(t,e,r,i){return At(t,r,i,n,d)})}function At(t,n,d,e,r){return function(i,a,o){return t(i,a,function(t){o(t),"function"==typeof t&&e.push(function(){Nt(t,n.instances,d,r)})})}}function Nt(t,n,d,e){n[d]?t(n[d]):e()&&setTimeout(function(){Nt(t,n,d,e)},16)}function Lt(t){var n=window.location.pathname;return t&&0===n.indexOf(t)&&(n=n.slice(t.length)),(n||"/")+window.location.search+window.location.hash}function Ut(t){var n=Lt(t);if(!/^\/#/.test(n))return window.location.replace(A(t+"/#"+n)),!0}function Ot(){var t=Tt();return"/"===t.charAt(0)||(jt("/"+t),!1)}function Tt(){var t=window.location.href,n=t.indexOf("#");return-1===n?"":t.slice(n+1)}function Ct(t){var n=window.location.href,d=n.indexOf("#");return(d>=0?n.slice(0,d):n)+"#"+t}function Rt(t){en?ct(Ct(t)):window.location.hash=t}function jt(t){en?st(Ct(t)):window.location.replace(Ct(t))}function Ft(t,n){return t.push(n),function(){var d=t.indexOf(n);d>-1&&t.splice(d,1)}}function St(t,n,d){var e="hash"===d?"#"+n:n;return t?A(t+"/"+e):e}var Et,$t={name:"router-view",functional:!0,props:{name:{type:String,default:"default"}},render:function(t,n){var d=n.props,e=n.children,r=n.parent,i=n.data;i.routerView=!0;for(var c=r.$createElement,s=d.name,h=r.$route,l=r._routerViewCache||(r._routerViewCache={}),u=0,p=!1;r&&r._routerRoot!==r;)r.$vnode&&r.$vnode.data.routerView&&u++,r._inactive&&(p=!0),r=r.$parent;if(i.routerViewDepth=u,p)return c(l[s],i,e);var f=h.matched[u];if(!f)return l[s]=null,c();var b=l[s]=f.components[s];i.registerRouteInstance=function(t,n){var d=f.instances[s];(n&&d!==t||!n&&d===t)&&(f.instances[s]=n)},(i.hook||(i.hook={})).prepatch=function(t,n){f.instances[s]=n.componentInstance};var g=i.props=a(h,f.props&&f.props[s]);if(g){g=i.props=o({},g);var m=i.attrs=i.attrs||{};for(var v in g)b.props&&v in b.props||(m[v]=g[v],delete g[v])}return c(b,i,e)}},It=/[!'()*]/g,Mt=function(t){return"%"+t.charCodeAt(0).toString(16)},Pt=/%2C/g,Kt=function(t){return encodeURIComponent(t).replace(It,Mt).replace(Pt,",")},Bt=decodeURIComponent,zt=/\/?$/,Dt=l(null,{path:"/"}),qt=[String,Object],Qt=[String,Array],Ht={name:"router-link",props:{to:{type:qt,required:!0},tag:{type:String,default:"a"},exact:Boolean,append:Boolean,replace:Boolean,activeClass:String,exactActiveClass:String,event:{type:Qt,default:"click"}},render:function(t){var n=this,d=this.$router,e=this.$route,r=d.resolve(this.to,e,this.append),i=r.location,a=r.route,o=r.href,c={},s=d.options.linkActiveClass,h=d.options.linkExactActiveClass,u=null==s?"router-link-active":s,p=null==h?"router-link-exact-active":h,f=null==this.activeClass?u:this.activeClass,g=null==this.exactActiveClass?p:this.exactActiveClass,v=i.path?l(null,i,null,d):a;c[g]=b(e,v),c[f]=this.exact?c[g]:m(e,v);var w=function(t){y(t)&&(n.replace?d.replace(i):d.push(i))},x={click:y};Array.isArray(this.event)?this.event.forEach(function(t){x[t]=w}):x[this.event]=w;var k={class:c};if("a"===this.tag)k.on=x,k.attrs={href:o};else{var A=_(this.$slots.default);if(A){A.isStatic=!1;var N=Et.util.extend;(A.data=N({},A.data)).on=x;(A.data.attrs=N({},A.data.attrs)).href=o}else k.on=x}return t(this.tag,k,this.$slots.default)}},Vt="undefined"!=typeof window,Wt=Array.isArray||function(t){return"[object Array]"==Object.prototype.toString.call(t)},Jt=M,Xt=N,Gt=L,Zt=T,Yt=I,tn=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");Jt.parse=Xt,Jt.compile=Gt,Jt.tokensToFunction=Zt,Jt.tokensToRegExp=Yt;var nn=Object.create(null),dn=Object.create(null),en=Vt&&function(){var t=window.navigator.userAgent;return(-1===t.indexOf("Android 2.")&&-1===t.indexOf("Android 4.0")||-1===t.indexOf("Mobile Safari")||-1!==t.indexOf("Chrome")||-1!==t.indexOf("Windows Phone"))&&(window.history&&"pushState"in window.history)}(),rn=Vt&&window.performance&&window.performance.now?window.performance:Date,an=it(),on="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag,cn=function(t,n){this.router=t,this.base=gt(n),this.current=Dt,this.pending=null,this.ready=!1,this.readyCbs=[],this.readyErrorCbs=[],this.errorCbs=[]};cn.prototype.listen=function(t){this.cb=t},cn.prototype.onReady=function(t,n){this.ready?t():(this.readyCbs.push(t),n&&this.readyErrorCbs.push(n))},cn.prototype.onError=function(t){this.errorCbs.push(t)},cn.prototype.transitionTo=function(t,n,d){var e=this,r=this.router.match(t,this.current);this.confirmTransition(r,function(){e.updateRoute(r),n&&n(r),e.ensureURL(),e.ready||(e.ready=!0,e.readyCbs.forEach(function(t){t(r)}))},function(t){d&&d(t),t&&!e.ready&&(e.ready=!0,e.readyErrorCbs.forEach(function(n){n(t)}))})},cn.prototype.confirmTransition=function(t,n,d){var e=this,a=this.current,o=function(t){i(t)&&(e.errorCbs.length?e.errorCbs.forEach(function(n){n(t)}):(r(!1,"uncaught error during route navigation:"),console.error(t))),d&&d(t)};if(b(t,a)&&t.matched.length===a.matched.length)return this.ensureURL(),o();var c=mt(this.current.matched,t.matched),s=c.updated,h=c.deactivated,l=c.activated,u=[].concat(_t(h),this.router.beforeHooks,wt(s),l.map(function(t){return t.beforeEnter}),lt(l));this.pending=t;var p=function(n,d){if(e.pending!==t)return o();try{n(t,a,function(t){!1===t||i(t)?(e.ensureURL(!0),o(t)):"string"==typeof t||"object"==typeof t&&("string"==typeof t.path||"string"==typeof t.name)?(o(),"object"==typeof t&&t.replace?e.replace(t):e.push(t)):d(t)})}catch(t){o(t)}};ht(u,p,function(){var d=[];ht(kt(l,d,function(){return e.current===t}).concat(e.router.resolveHooks),p,function(){if(e.pending!==t)return o();e.pending=null,n(t),e.router.app&&e.router.app.$nextTick(function(){d.forEach(function(t){t()})})})})},cn.prototype.updateRoute=function(t){var n=this.current;this.current=t,this.cb&&this.cb(t),this.router.afterHooks.forEach(function(d){d&&d(t,n)})};var sn=function(t){function n(n,d){var e=this;t.call(this,n,d);var r=n.options.scrollBehavior;r&&J();var i=Lt(this.base);window.addEventListener("popstate",function(t){var d=e.current,a=Lt(e.base);e.current===Dt&&a===i||e.transitionTo(a,function(t){r&&X(n,t,d,!0)})})}return t&&(n.__proto__=t),n.prototype=Object.create(t&&t.prototype),n.prototype.constructor=n,n.prototype.go=function(t){window.history.go(t)},n.prototype.push=function(t,n,d){var e=this,r=this,i=r.current;this.transitionTo(t,function(t){ct(A(e.base+t.fullPath)),X(e.router,t,i,!1),n&&n(t)},d)},n.prototype.replace=function(t,n,d){var e=this,r=this,i=r.current;this.transitionTo(t,function(t){st(A(e.base+t.fullPath)),X(e.router,t,i,!1),n&&n(t)},d)},n.prototype.ensureURL=function(t){if(Lt(this.base)!==this.current.fullPath){var n=A(this.base+this.current.fullPath);t?ct(n):st(n)}},n.prototype.getCurrentLocation=function(){return Lt(this.base)},n}(cn),hn=function(t){function n(n,d,e){t.call(this,n,d),e&&Ut(this.base)||Ot()}return t&&(n.__proto__=t),n.prototype=Object.create(t&&t.prototype),n.prototype.constructor=n,n.prototype.setupListeners=function(){var t=this,n=this.router,d=n.options.scrollBehavior,e=en&&d;e&&J(),window.addEventListener(en?"popstate":"hashchange",function(){var n=t.current;Ot()&&t.transitionTo(Tt(),function(d){e&&X(t.router,d,n,!0),en||jt(d.fullPath)})})},n.prototype.push=function(t,n,d){var e=this,r=this,i=r.current;this.transitionTo(t,function(t){Rt(t.fullPath),X(e.router,t,i,!1),n&&n(t)},d)},n.prototype.replace=function(t,n,d){var e=this,r=this,i=r.current;this.transitionTo(t,function(t){jt(t.fullPath),X(e.router,t,i,!1),n&&n(t)},d)},n.prototype.go=function(t){window.history.go(t)},n.prototype.ensureURL=function(t){var n=this.current.fullPath;Tt()!==n&&(t?Rt(n):jt(n))},n.prototype.getCurrentLocation=function(){return Tt()},n}(cn),ln=function(t){function n(n,d){t.call(this,n,d),this.stack=[],this.index=-1}return t&&(n.__proto__=t),n.prototype=Object.create(t&&t.prototype),n.prototype.constructor=n,n.prototype.push=function(t,n,d){var e=this;this.transitionTo(t,function(t){e.stack=e.stack.slice(0,e.index+1).concat(t),e.index++,n&&n(t)},d)},n.prototype.replace=function(t,n,d){var e=this;this.transitionTo(t,function(t){e.stack=e.stack.slice(0,e.index).concat(t),n&&n(t)},d)},n.prototype.go=function(t){var n=this,d=this.index+t;if(!(d<0||d>=this.stack.length)){var e=this.stack[d];this.confirmTransition(e,function(){n.index=d,n.updateRoute(e)})}},n.prototype.getCurrentLocation=function(){var t=this.stack[this.stack.length-1];return t?t.fullPath:"/"},n.prototype.ensureURL=function(){},n}(cn),un=function(t){void 0===t&&(t={}),this.app=null,this.apps=[],this.options=t,this.beforeHooks=[],this.resolveHooks=[],this.afterHooks=[],this.matcher=H(t.routes||[],this);var n=t.mode||"hash";switch(this.fallback="history"===n&&!en&&!1!==t.fallback,this.fallback&&(n="hash"),Vt||(n="abstract"),this.mode=n,n){case"history":this.history=new sn(this,t.base);break;case"hash":this.history=new hn(this,t.base,this.fallback);break;case"abstract":this.history=new ln(this,t.base);break;default:e(!1,"invalid mode: "+n)}},pn={currentRoute:{configurable:!0}};un.prototype.match=function(t,n,d){return this.matcher.match(t,n,d)},pn.currentRoute.get=function(){return this.history&&this.history.current},un.prototype.init=function(t){var n=this;if(e(w.installed,"not installed. Make sure to call `Vue.use(VueRouter)` before creating root instance."),this.apps.push(t),!this.app){this.app=t;var d=this.history;if(d instanceof sn)d.transitionTo(d.getCurrentLocation());else if(d instanceof hn){var r=function(){d.setupListeners()};d.transitionTo(d.getCurrentLocation(),r,r)}d.listen(function(t){n.apps.forEach(function(n){n._route=t})})}},un.prototype.beforeEach=function(t){return Ft(this.beforeHooks,t)},un.prototype.beforeResolve=function(t){return Ft(this.resolveHooks,t)},un.prototype.afterEach=function(t){return Ft(this.afterHooks,t)},un.prototype.onReady=function(t,n){this.history.onReady(t,n)},un.prototype.onError=function(t){this.history.onError(t)},un.prototype.push=function(t,n,d){this.history.push(t,n,d)},un.prototype.replace=function(t,n,d){this.history.replace(t,n,d)},un.prototype.go=function(t){this.history.go(t)},un.prototype.back=function(){this.go(-1)},un.prototype.forward=function(){this.go(1)},un.prototype.getMatchedComponents=function(t){var n=t?t.matched?t:this.resolve(t).route:this.currentRoute;return n?[].concat.apply([],n.matched.map(function(t){return Object.keys(t.components).map(function(n){return t.components[n]})})):[]},un.prototype.resolve=function(t,n,d){var e=q(t,n||this.history.current,d,this),r=this.match(e,n),i=r.redirectedFrom||r.fullPath;return{location:e,route:r,href:St(this.history.base,i,this.mode),normalizedTo:e,resolved:r}},un.prototype.addRoutes=function(t){this.matcher.addRoutes(t),this.history.current!==Dt&&this.history.transitionTo(this.history.getCurrentLocation())},Object.defineProperties(un.prototype,pn),un.install=w,un.version="2.8.1",Vt&&window.Vue&&window.Vue.use(un),n.a=un},function(t,n,d){"use strict";(function(t,d){function e(t){return void 0===t||null===t}function r(t){return void 0!==t&&null!==t}function i(t){return!0===t}function a(t){return!1===t}function o(t){return"string"==typeof t||"number"==typeof t||"symbol"==typeof t||"boolean"==typeof t}function c(t){return null!==t&&"object"==typeof t}function s(t){return Ne.call(t).slice(8,-1)}function h(t){return"[object Object]"===Ne.call(t)}function l(t){return"[object RegExp]"===Ne.call(t)}function u(t){var n=parseFloat(String(t));return n>=0&&Math.floor(n)===n&&isFinite(t)}function p(t){return null==t?"":"object"==typeof t?JSON.stringify(t,null,2):String(t)}function f(t){var n=parseFloat(t);return isNaN(n)?t:n}function b(t,n){for(var d=Object.create(null),e=t.split(","),r=0;r<e.length;r++)d[e[r]]=!0;return n?function(t){return d[t.toLowerCase()]}:function(t){return d[t]}}function g(t,n){if(t.length){var d=t.indexOf(n);if(d>-1)return t.splice(d,1)}}function m(t,n){return Oe.call(t,n)}function v(t){var n=Object.create(null);return function(d){return n[d]||(n[d]=t(d))}}function y(t,n){function d(d){var e=arguments.length;return e?e>1?t.apply(n,arguments):t.call(n,d):t.call(n)}return d._length=t.length,d}function _(t,n){return t.bind(n)}function w(t,n){n=n||0;for(var d=t.length-n,e=new Array(d);d--;)e[d]=t[d+n];return e}function x(t,n){for(var d in n)t[d]=n[d];return t}function k(t){for(var n={},d=0;d<t.length;d++)t[d]&&x(n,t[d]);return n}function A(t,n,d){}function N(t,n){if(t===n)return!0;var d=c(t),e=c(n);if(!d||!e)return!d&&!e&&String(t)===String(n);try{var r=Array.isArray(t),i=Array.isArray(n);if(r&&i)return t.length===n.length&&t.every(function(t,d){return N(t,n[d])});if(r||i)return!1;var a=Object.keys(t),o=Object.keys(n);return a.length===o.length&&a.every(function(d){return N(t[d],n[d])})}catch(t){return!1}}function L(t,n){for(var d=0;d<t.length;d++)if(N(t[d],n))return d;return-1}function U(t){var n=!1;return function(){n||(n=!0,t.apply(this,arguments))}}function O(t){var n=(t+"").charCodeAt(0);return 36===n||95===n}function T(t,n,d,e){Object.defineProperty(t,n,{value:d,enumerable:!!e,writable:!0,configurable:!0})}function C(t){if(!Be.test(t)){var n=t.split(".");return function(t){for(var d=0;d<n.length;d++){if(!t)return;t=t[n[d]]}return t}}}function R(t){return"function"==typeof t&&/native code/.test(t.toString())}function j(t){br.target&&gr.push(br.target),br.target=t}function F(){br.target=gr.pop()}function S(t){return new mr(void 0,void 0,void 0,String(t))}function E(t){var n=new mr(t.tag,t.data,t.children,t.text,t.elm,t.context,t.componentOptions,t.asyncFactory);return n.ns=t.ns,n.isStatic=t.isStatic,n.key=t.key,n.isComment=t.isComment,n.fnContext=t.fnContext,n.fnOptions=t.fnOptions,n.fnScopeId=t.fnScopeId,n.isCloned=!0,n}function $(t){kr=t}function I(t,n,d){t.__proto__=n}function M(t,n,d){for(var e=0,r=d.length;e<r;e++){var i=d[e];T(t,i,n[i])}}function P(t,n){if(c(t)&&!(t instanceof mr)){var d;return m(t,"__ob__")&&t.__ob__ instanceof Ar?d=t.__ob__:kr&&!er()&&(Array.isArray(t)||h(t))&&Object.isExtensible(t)&&!t._isVue&&(d=new Ar(t)),n&&d&&d.vmCount++,d}}function K(t,n,d,e,r){var i=new br,a=Object.getOwnPropertyDescriptor(t,n);if(!a||!1!==a.configurable){var o=a&&a.get;o||2!==arguments.length||(d=t[n]);var c=a&&a.set,s=!r&&P(d);Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:function(){var n=o?o.call(t):d;return br.target&&(i.depend(),s&&(s.dep.depend(),Array.isArray(n)&&D(n))),n},set:function(n){var a=o?o.call(t):d;n===a||n!==n&&a!==a||(e&&e(),c?c.call(t,n):d=n,s=!r&&P(n),i.notify())}})}}function B(t,n,d){if((e(t)||o(t))&&ar("Cannot set reactive property on undefined, null, or primitive value: "+t),Array.isArray(t)&&u(n))return t.length=Math.max(t.length,n),t.splice(n,1,d),d;if(n in t&&!(n in Object.prototype))return t[n]=d,d;var r=t.__ob__;return t._isVue||r&&r.vmCount?(ar("Avoid adding reactive properties to a Vue instance or its root $data at runtime - declare it upfront in the data option."),d):r?(K(r.value,n,d),r.dep.notify(),d):(t[n]=d,d)}function z(t,n){if((e(t)||o(t))&&ar("Cannot delete reactive property on undefined, null, or primitive value: "+t),Array.isArray(t)&&u(n))return void t.splice(n,1);var d=t.__ob__;if(t._isVue||d&&d.vmCount)return void ar("Avoid deleting properties on a Vue instance or its root $data - just set it to null.");m(t,n)&&(delete t[n],d&&d.dep.notify())}function D(t){for(var n=void 0,d=0,e=t.length;d<e;d++)n=t[d],n&&n.__ob__&&n.__ob__.dep.depend(),Array.isArray(n)&&D(n)}function q(t,n){if(!n)return t;for(var d,e,r,i=Object.keys(n),a=0;a<i.length;a++)d=i[a],e=t[d],r=n[d],m(t,d)?h(e)&&h(r)&&q(e,r):B(t,d,r);return t}function Q(t,n,d){return d?function(){var e="function"==typeof n?n.call(d,d):n,r="function"==typeof t?t.call(d,d):t;return e?q(e,r):r}:n?t?function(){return q("function"==typeof n?n.call(this,this):n,"function"==typeof t?t.call(this,this):t)}:n:t}function H(t,n){return n?t?t.concat(n):Array.isArray(n)?n:[n]:t}function V(t,n,d,e){var r=Object.create(t||null);return n?(Y(e,n,d),x(r,n)):r}function W(t){for(var n in t.components)J(n)}function J(t){/^[a-zA-Z][\w-]*$/.test(t)||ar('Invalid component name: "'+t+'". Component names can only contain alphanumeric characters and the hyphen, and must start with a letter.'),(Le(t)||Ke.isReservedTag(t))&&ar("Do not use built-in or reserved HTML elements as component id: "+t)}function X(t,n){var d=t.props;if(d){var e,r,i,a={};if(Array.isArray(d))for(e=d.length;e--;)r=d[e],"string"==typeof r?(i=Ce(r),a[i]={type:null}):ar("props must be strings when using array syntax.");else if(h(d))for(var o in d)r=d[o],i=Ce(o),a[i]=h(r)?r:{type:r};else ar('Invalid value for option "props": expected an Array or an Object, but got '+s(d)+".",n);t.props=a}}function G(t,n){var d=t.inject;if(d){var e=t.inject={};if(Array.isArray(d))for(var r=0;r<d.length;r++)e[d[r]]={from:d[r]};else if(h(d))for(var i in d){var a=d[i];e[i]=h(a)?x({from:i},a):{from:a}}else ar('Invalid value for option "inject": expected an Array or an Object, but got '+s(d)+".",n)}}function Z(t){var n=t.directives;if(n)for(var d in n){var e=n[d];"function"==typeof e&&(n[d]={bind:e,update:e})}}function Y(t,n,d){h(n)||ar('Invalid value for option "'+t+'": expected an Object, but got '+s(n)+".",d)}function tt(t,n,d){function e(e){var r=Nr[e]||Or;c[e]=r(t[e],n[e],d,e)}W(n),"function"==typeof n&&(n=n.options),X(n,d),G(n,d),Z(n);var r=n.extends;if(r&&(t=tt(t,r,d)),n.mixins)for(var i=0,a=n.mixins.length;i<a;i++)t=tt(t,n.mixins[i],d);var o,c={};for(o in t)e(o);for(o in n)m(t,o)||e(o);return c}function nt(t,n,d,e){if("string"==typeof d){var r=t[n];if(m(r,d))return r[d];var i=Ce(d);if(m(r,i))return r[i];var a=Re(i);if(m(r,a))return r[a];var o=r[d]||r[i]||r[a];return e&&!o&&ar("Failed to resolve "+n.slice(0,-1)+": "+d,t),o}}function dt(t,n,d,e){var r=n[t],i=!m(d,t),a=d[t],o=ct(Boolean,r.type);if(o>-1)if(i&&!m(r,"default"))a=!1;else if(""===a||a===Fe(t)){var c=ct(String,r.type);(c<0||o<c)&&(a=!0)}if(void 0===a){a=et(e,r,t);var s=kr;$(!0),P(a),$(s)}return rt(r,t,a,e,i),a}function et(t,n,d){if(m(n,"default")){var e=n.default;return c(e)&&ar('Invalid default value for prop "'+d+'": Props with type Object/Array must use a factory function to return the default value.',t),t&&t.$options.propsData&&void 0===t.$options.propsData[d]&&void 0!==t._props[d]?t._props[d]:"function"==typeof e&&"Function"!==at(n.type)?e.call(t):e}}function rt(t,n,d,e,r){if(t.required&&r)return void ar('Missing required prop: "'+n+'"',e);if(null!=d||t.required){var i=t.type,a=!i||!0===i,o=[];if(i){Array.isArray(i)||(i=[i]);for(var c=0;c<i.length&&!a;c++){var h=it(d,i[c]);o.push(h.expectedType||""),a=h.valid}}if(!a)return void ar('Invalid prop: type check failed for prop "'+n+'". Expected '+o.map(Re).join(", ")+", got "+s(d)+".",e);var l=t.validator;l&&(l(d)||ar('Invalid prop: custom validator check failed for prop "'+n+'".',e))}}function it(t,n){var d,e=at(n);if(Tr.test(e)){var r=typeof t;d=r===e.toLowerCase(),d||"object"!==r||(d=t instanceof n)}else d="Object"===e?h(t):"Array"===e?Array.isArray(t):t instanceof n;return{valid:d,expectedType:e}}function at(t){var n=t&&t.toString().match(/^\s*function (\w+)/);return n?n[1]:""}function ot(t,n){return at(t)===at(n)}function ct(t,n){if(!Array.isArray(n))return ot(n,t)?0:-1;for(var d=0,e=n.length;d<e;d++)if(ot(n[d],t))return d;return-1}function st(t,n,d){if(n)for(var e=n;e=e.$parent;){var r=e.$options.errorCaptured;if(r)for(var i=0;i<r.length;i++)try{var a=!1===r[i].call(e,t,n,d);if(a)return}catch(t){ht(t,e,"errorCaptured hook")}}ht(t,n,d)}function ht(t,n,d){if(Ke.errorHandler)try{return Ke.errorHandler.call(null,t,n,d)}catch(t){lt(t,null,"config.errorHandler")}lt(t,n,d)}function lt(t,n,d){if(ar("Error in "+d+': "'+t.toString()+'"',n),!De&&!qe||"undefined"==typeof console)throw t;console.error(t)}function ut(){Rr=!1;var t=Cr.slice(0);Cr.length=0;for(var n=0;n<t.length;n++)t[n]()}function pt(t){return t._withTask||(t._withTask=function(){jr=!0;var n=t.apply(null,arguments);return jr=!1,n})}function ft(t,n){var d;if(Cr.push(function(){if(t)try{t.call(n)}catch(t){st(t,n,"nextTick")}else d&&d(n)}),Rr||(Rr=!0,jr?Ur():Lr()),!t&&"undefined"!=typeof Promise)return new Promise(function(t){d=t})}function bt(t){gt(t,Qr),Qr.clear()}function gt(t,n){var d,e,r=Array.isArray(t);if(!(!r&&!c(t)||Object.isFrozen(t)||t instanceof mr)){if(t.__ob__){var i=t.__ob__.dep.id;if(n.has(i))return;n.add(i)}if(r)for(d=t.length;d--;)gt(t[d],n);else for(e=Object.keys(t),d=e.length;d--;)gt(t[e[d]],n)}}function mt(t){function n(){var t=arguments,d=n.fns;if(!Array.isArray(d))return d.apply(null,arguments);for(var e=d.slice(),r=0;r<e.length;r++)e[r].apply(null,t)}return n.fns=t,n}function vt(t,n,d,r,i){var a,o,c,s;for(a in t)o=t[a],c=n[a],s=Wr(a),e(o)?ar('Invalid handler for event "'+s.name+'": got '+String(o),i):e(c)?(e(o.fns)&&(o=t[a]=mt(o)),d(s.name,o,s.once,s.capture,s.passive,s.params)):o!==c&&(c.fns=o,t[a]=c);for(a in n)e(t[a])&&(s=Wr(a),r(s.name,n[a],s.capture))}function yt(t,n,d){function a(){d.apply(this,arguments),g(o.fns,a)}t instanceof mr&&(t=t.data.hook||(t.data.hook={}));var o,c=t[n];e(c)?o=mt([a]):r(c.fns)&&i(c.merged)?(o=c,o.fns.push(a)):o=mt([c,a]),o.merged=!0,t[n]=o}function _t(t,n,d){var i=n.options.props;if(!e(i)){var a={},o=t.attrs,c=t.props;if(r(o)||r(c))for(var s in i){var h=Fe(s),l=s.toLowerCase();s!==l&&o&&m(o,l)&&or('Prop "'+l+'" is passed to component '+sr(d||n)+', but the declared prop name is "'+s+'". Note that HTML attributes are case-insensitive and camelCased props need to use their kebab-case equivalents when using in-DOM templates. You should probably use "'+h+'" instead of "'+s+'".'),wt(a,c,s,h,!0)||wt(a,o,s,h,!1)}return a}}function wt(t,n,d,e,i){if(r(n)){if(m(n,d))return t[d]=n[d],i||delete n[d],!0;if(m(n,e))return t[d]=n[e],i||delete n[e],!0}return!1}function xt(t){for(var n=0;n<t.length;n++)if(Array.isArray(t[n]))return Array.prototype.concat.apply([],t);return t}function kt(t){return o(t)?[S(t)]:Array.isArray(t)?Nt(t):void 0}function At(t){return r(t)&&r(t.text)&&a(t.isComment)}function Nt(t,n){var d,a,c,s,h=[];for(d=0;d<t.length;d++)a=t[d],e(a)||"boolean"==typeof a||(c=h.length-1,s=h[c],Array.isArray(a)?a.length>0&&(a=Nt(a,(n||"")+"_"+d),At(a[0])&&At(s)&&(h[c]=S(s.text+a[0].text),a.shift()),h.push.apply(h,a)):o(a)?At(s)?h[c]=S(s.text+a):""!==a&&h.push(S(a)):At(a)&&At(s)?h[c]=S(s.text+a.text):(i(t._isVList)&&r(a.tag)&&e(a.key)&&r(n)&&(a.key="__vlist"+n+"_"+d+"__"),h.push(a)));return h}function Lt(t,n){return(t.__esModule||ir&&"Module"===t[Symbol.toStringTag])&&(t=t.default),c(t)?n.extend(t):t}function Ut(t,n,d,e,r){var i=yr();return i.asyncFactory=t,i.asyncMeta={data:n,context:d,children:e,tag:r},i}function Ot(t,n,d){if(i(t.error)&&r(t.errorComp))return t.errorComp;if(r(t.resolved))return t.resolved;if(i(t.loading)&&r(t.loadingComp))return t.loadingComp;if(!r(t.contexts)){var a=t.contexts=[d],o=!0,s=function(){for(var t=0,n=a.length;t<n;t++)a[t].$forceUpdate()},h=U(function(d){t.resolved=Lt(d,n),o||s()}),l=U(function(n){ar("Failed to resolve async component: "+String(t)+(n?"\nReason: "+n:"")),r(t.errorComp)&&(t.error=!0,s())}),u=t(h,l);return c(u)&&("function"==typeof u.then?e(t.resolved)&&u.then(h,l):r(u.component)&&"function"==typeof u.component.then&&(u.component.then(h,l),r(u.error)&&(t.errorComp=Lt(u.error,n)),r(u.loading)&&(t.loadingComp=Lt(u.loading,n),0===u.delay?t.loading=!0:setTimeout(function(){e(t.resolved)&&e(t.error)&&(t.loading=!0,s())},u.delay||200)),r(u.timeout)&&setTimeout(function(){e(t.resolved)&&l("timeout ("+u.timeout+"ms)")},u.timeout))),o=!1,t.loading?t.loadingComp:t.resolved}t.contexts.push(d)}function Tt(t){return t.isComment&&t.asyncFactory}function Ct(t){if(Array.isArray(t))for(var n=0;n<t.length;n++){var d=t[n];if(r(d)&&(r(d.componentOptions)||Tt(d)))return d}}function Rt(t){t._events=Object.create(null),t._hasHookEvent=!1;var n=t.$options._parentListeners;n&&St(t,n)}function jt(t,n,d){d?Vr.$once(t,n):Vr.$on(t,n)}function Ft(t,n){Vr.$off(t,n)}function St(t,n,d){Vr=t,vt(n,d||{},jt,Ft,t),Vr=void 0}function Et(t,n){var d={};if(!t)return d;for(var e=0,r=t.length;e<r;e++){var i=t[e],a=i.data;if(a&&a.attrs&&a.attrs.slot&&delete a.attrs.slot,i.context!==n&&i.fnContext!==n||!a||null==a.slot)(d.default||(d.default=[])).push(i);else{var o=a.slot,c=d[o]||(d[o]=[]);"template"===i.tag?c.push.apply(c,i.children||[]):c.push(i)}}for(var s in d)d[s].every($t)&&delete d[s];return d}function $t(t){return t.isComment&&!t.asyncFactory||" "===t.text}function It(t,n){n=n||{};for(var d=0;d<t.length;d++)Array.isArray(t[d])?It(t[d],n):n[t[d].key]=t[d].fn;return n}function Mt(t){var n=t.$options,d=n.parent;if(d&&!n.abstract){for(;d.$options.abstract&&d.$parent;)d=d.$parent;d.$children.push(t)}t.$parent=d,t.$root=d?d.$root:t,t.$children=[],t.$refs={},t._watcher=null,t._inactive=null,t._directInactive=!1,t._isMounted=!1,t._isDestroyed=!1,t._isBeingDestroyed=!1}function Pt(t,n,d){t.$el=n,t.$options.render||(t.$options.render=yr,t.$options.template&&"#"!==t.$options.template.charAt(0)||t.$options.el||n?ar("You are using the runtime-only build of Vue where the template compiler is not available. Either pre-compile the templates into render functions, or use the compiler-included build.",t):ar("Failed to mount component: template or render function not defined.",t)),qt(t,"beforeMount");var e;return e=Ke.performance&&Dr?function(){var n=t._name,e=t._uid,r="vue-perf-start:"+e,i="vue-perf-end:"+e;Dr(r);var a=t._render();Dr(i),qr("vue "+n+" render",r,i),Dr(r),t._update(a,d),Dr(i),qr("vue "+n+" patch",r,i)}:function(){t._update(t._render(),d)},new ai(t,e,A,null,!0),d=!1,null==t.$vnode&&(t._isMounted=!0,qt(t,"mounted")),t}function Kt(t,n,d,e,r){Xr=!0;var i=!!(r||t.$options._renderChildren||e.data.scopedSlots||t.$scopedSlots!==Ae);if(t.$options._parentVnode=e,t.$vnode=e,t._vnode&&(t._vnode.parent=e),t.$options._renderChildren=r,t.$attrs=e.data.attrs||Ae,t.$listeners=d||Ae,n&&t.$options.props){$(!1);for(var a=t._props,o=t.$options._propKeys||[],c=0;c<o.length;c++){var s=o[c],h=t.$options.props;a[s]=dt(s,h,n,t)}$(!0),t.$options.propsData=n}d=d||Ae;var l=t.$options._parentListeners;t.$options._parentListeners=d,St(t,d,l),i&&(t.$slots=Et(r,e.context),t.$forceUpdate()),Xr=!1}function Bt(t){for(;t&&(t=t.$parent);)if(t._inactive)return!0;return!1}function zt(t,n){if(n){if(t._directInactive=!1,Bt(t))return}else if(t._directInactive)return;if(t._inactive||null===t._inactive){t._inactive=!1;for(var d=0;d<t.$children.length;d++)zt(t.$children[d]);qt(t,"activated")}}function Dt(t,n){if(!(n&&(t._directInactive=!0,Bt(t))||t._inactive)){t._inactive=!0;for(var d=0;d<t.$children.length;d++)Dt(t.$children[d]);qt(t,"deactivated")}}function qt(t,n){j();var d=t.$options[n];if(d)for(var e=0,r=d.length;e<r;e++)try{d[e].call(t)}catch(d){st(d,t,n+" hook")}t._hasHookEvent&&t.$emit("hook:"+n),F()}function Qt(){ri=Zr.length=Yr.length=0,ti={},ni={},di=ei=!1}function Ht(){ei=!0;var t,n;for(Zr.sort(function(t,n){return t.id-n.id}),ri=0;ri<Zr.length;ri++)if(t=Zr[ri],n=t.id,ti[n]=null,t.run(),null!=ti[n]&&(ni[n]=(ni[n]||0)+1,ni[n]>Gr)){ar("You may have an infinite update loop "+(t.user?'in watcher with expression "'+t.expression+'"':"in a component render function."),t.vm);break}var d=Yr.slice(),e=Zr.slice();Qt(),Jt(d),Vt(e),rr&&Ke.devtools&&rr.emit("flush")}function Vt(t){for(var n=t.length;n--;){var d=t[n],e=d.vm;e._watcher===d&&e._isMounted&&qt(e,"updated")}}function Wt(t){t._inactive=!1,Yr.push(t)}function Jt(t){for(var n=0;n<t.length;n++)t[n]._inactive=!0,zt(t[n],!0)}function Xt(t){var n=t.id;if(null==ti[n]){if(ti[n]=!0,ei){for(var d=Zr.length-1;d>ri&&Zr[d].id>t.id;)d--;Zr.splice(d+1,0,t)}else Zr.push(t);di||(di=!0,ft(Ht))}}function Gt(t,n,d){oi.get=function(){return this[n][d]},oi.set=function(t){this[n][d]=t},Object.defineProperty(t,d,oi)}function Zt(t){t._watchers=[];var n=t.$options;n.props&&Yt(t,n.props),n.methods&&an(t,n.methods),n.data?tn(t):P(t._data={},!0),n.computed&&dn(t,n.computed),n.watch&&n.watch!==Ze&&on(t,n.watch)}function Yt(t,n){var d=t.$options.propsData||{},e=t._props={},r=t.$options._propKeys=[];!t.$parent||$(!1);for(var i in n)!function(i){r.push(i);var a=dt(i,n,d,t),o=Fe(i);(Ue(o)||Ke.isReservedAttr(o))&&ar('"'+o+'" is a reserved attribute and cannot be used as component prop.',t),K(e,i,a,function(){t.$parent&&!Xr&&ar("Avoid mutating a prop directly since the value will be overwritten whenever the parent component re-renders. Instead, use a data or computed property based on the prop's value. Prop being mutated: \""+i+'"',t)}),i in t||Gt(t,"_props",i)}(i);$(!0)}function tn(t){var n=t.$options.data;n=t._data="function"==typeof n?nn(n,t):n||{},h(n)||(n={},ar("data functions should return an object:\nhttps://vuejs.org/v2/guide/components.html#data-Must-Be-a-Function",t));for(var d=Object.keys(n),e=t.$options.props,r=t.$options.methods,i=d.length;i--;){var a=d[i];r&&m(r,a)&&ar('Method "'+a+'" has already been defined as a data property.',t),e&&m(e,a)?ar('The data property "'+a+'" is already declared as a prop. Use prop default value instead.',t):O(a)||Gt(t,"_data",a)}P(n,!0)}function nn(t,n){j();try{return t.call(n,n)}catch(t){return st(t,n,"data()"),{}}finally{F()}}function dn(t,n){var d=t._computedWatchers=Object.create(null),e=er();for(var r in n){var i=n[r],a="function"==typeof i?i:i.get;null==a&&ar('Getter is missing for computed property "'+r+'".',t),e||(d[r]=new ai(t,a||A,A,ci)),r in t?r in t.$data?ar('The computed property "'+r+'" is already defined in data.',t):t.$options.props&&r in t.$options.props&&ar('The computed property "'+r+'" is already defined as a prop.',t):en(t,r,i)}}function en(t,n,d){var e=!er();"function"==typeof d?(oi.get=e?rn(n):d,oi.set=A):(oi.get=d.get?e&&!1!==d.cache?rn(n):d.get:A,oi.set=d.set?d.set:A),oi.set===A&&(oi.set=function(){ar('Computed property "'+n+'" was assigned to but it has no setter.',this)}),Object.defineProperty(t,n,oi)}function rn(t){return function(){var n=this._computedWatchers&&this._computedWatchers[t];if(n)return n.dirty&&n.evaluate(),br.target&&n.depend(),n.value}}function an(t,n){var d=t.$options.props;for(var e in n)null==n[e]&&ar('Method "'+e+'" has an undefined value in the component definition. Did you reference the function correctly?',t),d&&m(d,e)&&ar('Method "'+e+'" has already been defined as a prop.',t),e in t&&O(e)&&ar('Method "'+e+'" conflicts with an existing Vue instance method. Avoid defining component methods that start with _ or $.'),t[e]=null==n[e]?A:Se(n[e],t)}function on(t,n){for(var d in n){var e=n[d];if(Array.isArray(e))for(var r=0;r<e.length;r++)cn(t,d,e[r]);else cn(t,d,e)}}function cn(t,n,d,e){return h(d)&&(e=d,d=d.handler),"string"==typeof d&&(d=t[d]),t.$watch(n,d,e)}function sn(t){var n=t.$options.provide;n&&(t._provided="function"==typeof n?n.call(t):n)}function hn(t){var n=ln(t.$options.inject,t);n&&($(!1),Object.keys(n).forEach(function(d){K(t,d,n[d],function(){ar('Avoid mutating an injected value directly since the changes will be overwritten whenever the provided component re-renders. injection being mutated: "'+d+'"',t)})}),$(!0))}function ln(t,n){if(t){for(var d=Object.create(null),e=ir?Reflect.ownKeys(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}):Object.keys(t),r=0;r<e.length;r++){for(var i=e[r],a=t[i].from,o=n;o;){if(o._provided&&m(o._provided,a)){d[i]=o._provided[a];break}o=o.$parent}if(!o)if("default"in t[i]){var c=t[i].default;d[i]="function"==typeof c?c.call(n):c}else ar('Injection "'+i+'" not found',n)}return d}}function un(t,n){var d,e,i,a,o;if(Array.isArray(t)||"string"==typeof t)for(d=new Array(t.length),e=0,i=t.length;e<i;e++)d[e]=n(t[e],e);else if("number"==typeof t)for(d=new Array(t),e=0;e<t;e++)d[e]=n(e+1,e);else if(c(t))for(a=Object.keys(t),d=new Array(a.length),e=0,i=a.length;e<i;e++)o=a[e],d[e]=n(t[o],o,e);return r(d)&&(d._isVList=!0),d}function pn(t,n,d,e){var r,i=this.$scopedSlots[t];if(i)d=d||{},e&&(c(e)||ar("slot v-bind without argument expects an Object",this),d=x(x({},e),d)),r=i(d)||n;else{var a=this.$slots[t];a&&(a._rendered&&ar('Duplicate presence of slot "'+t+'" found in the same render tree - this will likely cause render errors.',this),a._rendered=!0),r=a||n}var o=d&&d.slot;return o?this.$createElement("template",{slot:o},r):r}function fn(t){return nt(this.$options,"filters",t,!0)||$e}function bn(t,n){return Array.isArray(t)?-1===t.indexOf(n):t!==n}function gn(t,n,d,e,r){var i=Ke.keyCodes[n]||d;return r&&e&&!Ke.keyCodes[n]?bn(r,e):i?bn(i,t):e?Fe(e)!==n:void 0}function mn(t,n,d,e,r){if(d)if(c(d)){Array.isArray(d)&&(d=k(d));var i;for(var a in d)!function(a){if("class"===a||"style"===a||Ue(a))i=t;else{var o=t.attrs&&t.attrs.type;i=e||Ke.mustUseProp(n,o,a)?t.domProps||(t.domProps={}):t.attrs||(t.attrs={})}if(!(a in i)&&(i[a]=d[a],r)){(t.on||(t.on={}))["update:"+a]=function(t){d[a]=t}}}(a)}else ar("v-bind without argument expects an Object or Array value",this);return t}function vn(t,n){var d=this._staticTrees||(this._staticTrees=[]),e=d[t];return e&&!n?e:(e=d[t]=this.$options.staticRenderFns[t].call(this._renderProxy,null,this),_n(e,"__static__"+t,!1),e)}function yn(t,n,d){return _n(t,"__once__"+n+(d?"_"+d:""),!0),t}function _n(t,n,d){if(Array.isArray(t))for(var e=0;e<t.length;e++)t[e]&&"string"!=typeof t[e]&&wn(t[e],n+"_"+e,d);else wn(t,n,d)}function wn(t,n,d){t.isStatic=!0,t.key=n,t.isOnce=d}function xn(t,n){if(n)if(h(n)){var d=t.on=t.on?x({},t.on):{};for(var e in n){var r=d[e],i=n[e];d[e]=r?[].concat(r,i):i}}else ar("v-on without argument expects an Object value",this);return t}function kn(t){t._o=yn,t._n=f,t._s=p,t._l=un,t._t=pn,t._q=N,t._i=L,t._m=vn,t._f=fn,t._k=gn,t._b=mn,t._v=S,t._e=yr,t._u=It,t._g=xn}function An(t,n,d,e,r){var a,o=r.options;m(e,"_uid")?(a=Object.create(e),a._original=e):(a=e,e=e._original);var c=i(o._compiled),s=!c;this.data=t,this.props=n,this.children=d,this.parent=e,this.listeners=t.on||Ae,this.injections=ln(o.inject,e),this.slots=function(){return Et(d,e)},c&&(this.$options=o,this.$slots=this.slots(),this.$scopedSlots=t.scopedSlots||Ae),o._scopeId?this._c=function(t,n,d,r){var i=jn(a,t,n,d,r,s);return i&&!Array.isArray(i)&&(i.fnScopeId=o._scopeId,i.fnContext=e),i}:this._c=function(t,n,d,e){return jn(a,t,n,d,e,s)}}function Nn(t,n,d,e,i){var a=t.options,o={},c=a.props;if(r(c))for(var s in c)o[s]=dt(s,c,n||Ae);else r(d.attrs)&&Un(o,d.attrs),r(d.props)&&Un(o,d.props);var h=new An(d,o,i,e,t),l=a.render.call(null,h._c,h);if(l instanceof mr)return Ln(l,d,h.parent,a);if(Array.isArray(l)){for(var u=kt(l)||[],p=new Array(u.length),f=0;f<u.length;f++)p[f]=Ln(u[f],d,h.parent,a);return p}}function Ln(t,n,d,e){var r=E(t);return r.fnContext=d,r.fnOptions=e,n.slot&&((r.data||(r.data={})).slot=n.slot),r}function Un(t,n){for(var d in n)t[Ce(d)]=n[d]}function On(t,n,d,a,o){if(!e(t)){var s=d.$options._base;if(c(t)&&(t=s.extend(t)),"function"!=typeof t)return void ar("Invalid Component definition: "+String(t),d);var h;if(e(t.cid)&&(h=t,void 0===(t=Ot(h,s,d))))return Ut(h,n,d,a,o);n=n||{},Mn(t),r(n.model)&&Rn(t.options,n);var l=_t(n,t,o);if(i(t.options.functional))return Nn(t,l,n,d,a);var u=n.on;if(n.on=n.nativeOn,i(t.options.abstract)){var p=n.slot;n={},p&&(n.slot=p)}Cn(n);var f=t.options.name||o;return new mr("vue-component-"+t.cid+(f?"-"+f:""),n,void 0,void 0,void 0,d,{Ctor:t,propsData:l,listeners:u,tag:o,children:a},h)}}function Tn(t,n,d,e){var i={_isComponent:!0,parent:n,_parentVnode:t,_parentElm:d||null,_refElm:e||null},a=t.data.inlineTemplate;return r(a)&&(i.render=a.render,i.staticRenderFns=a.staticRenderFns),new t.componentOptions.Ctor(i)}function Cn(t){for(var n=t.hook||(t.hook={}),d=0;d<hi.length;d++){var e=hi[d];n[e]=si[e]}}function Rn(t,n){var d=t.model&&t.model.prop||"value",e=t.model&&t.model.event||"input";(n.props||(n.props={}))[d]=n.model.value;var i=n.on||(n.on={});r(i[e])?i[e]=[n.model.callback].concat(i[e]):i[e]=n.model.callback}function jn(t,n,d,e,r,a){return(Array.isArray(d)||o(d))&&(r=e,e=d,d=void 0),i(a)&&(r=ui),Fn(t,n,d,e,r)}function Fn(t,n,d,e,i){if(r(d)&&r(d.__ob__))return ar("Avoid using observed data object as vnode data: "+JSON.stringify(d)+"\nAlways create fresh vnode data objects in each render!",t),yr();if(r(d)&&r(d.is)&&(n=d.is),!n)return yr();r(d)&&r(d.key)&&!o(d.key)&&ar("Avoid using non-primitive value as key, use string/number value instead.",t),Array.isArray(e)&&"function"==typeof e[0]&&(d=d||{},d.scopedSlots={default:e[0]},e.length=0),i===ui?e=kt(e):i===li&&(e=xt(e));var a,c;if("string"==typeof n){var s;c=t.$vnode&&t.$vnode.ns||Ke.getTagNamespace(n),a=Ke.isReservedTag(n)?new mr(Ke.parsePlatformTagName(n),d,e,void 0,void 0,t):r(s=nt(t.$options,"components",n))?On(s,d,t,e,n):new mr(n,d,e,void 0,void 0,t)}else a=On(n,d,t,e);return Array.isArray(a)?a:r(a)?(r(c)&&Sn(a,c),r(d)&&En(d),a):yr()}function Sn(t,n,d){if(t.ns=n,"foreignObject"===t.tag&&(n=void 0,d=!0),r(t.children))for(var a=0,o=t.children.length;a<o;a++){var c=t.children[a];r(c.tag)&&(e(c.ns)||i(d)&&"svg"!==c.tag)&&Sn(c,n,d)}}function En(t){c(t.style)&&bt(t.style),c(t.class)&&bt(t.class)}function $n(t){t._vnode=null,t._staticTrees=null;var n=t.$options,d=t.$vnode=n._parentVnode,e=d&&d.context;t.$slots=Et(n._renderChildren,e),t.$scopedSlots=Ae,t._c=function(n,d,e,r){return jn(t,n,d,e,r,!1)},t.$createElement=function(n,d,e,r){return jn(t,n,d,e,r,!0)};var r=d&&d.data;K(t,"$attrs",r&&r.attrs||Ae,function(){!Xr&&ar("$attrs is readonly.",t)},!0),K(t,"$listeners",n._parentListeners||Ae,function(){!Xr&&ar("$listeners is readonly.",t)},!0)}function In(t,n){var d=t.$options=Object.create(t.constructor.options),e=n._parentVnode;d.parent=n.parent,d._parentVnode=e,d._parentElm=n._parentElm,d._refElm=n._refElm;var r=e.componentOptions;d.propsData=r.propsData,d._parentListeners=r.listeners,d._renderChildren=r.children,d._componentTag=r.tag,n.render&&(d.render=n.render,d.staticRenderFns=n.staticRenderFns)}function Mn(t){var n=t.options;if(t.super){var d=Mn(t.super);if(d!==t.superOptions){t.superOptions=d;var e=Pn(t);e&&x(t.extendOptions,e),n=t.options=tt(d,t.extendOptions),n.name&&(n.components[n.name]=t)}}return n}function Pn(t){var n,d=t.options,e=t.extendOptions,r=t.sealedOptions;for(var i in d)d[i]!==r[i]&&(n||(n={}),n[i]=Kn(d[i],e[i],r[i]));return n}function Kn(t,n,d){if(Array.isArray(t)){var e=[];d=Array.isArray(d)?d:[d],n=Array.isArray(n)?n:[n];for(var r=0;r<t.length;r++)(n.indexOf(t[r])>=0||d.indexOf(t[r])<0)&&e.push(t[r]);return e}return t}function Bn(t){this instanceof Bn||ar("Vue is a constructor and should be called with the `new` keyword"),this._init(t)}function zn(t){t.use=function(t){var n=this._installedPlugins||(this._installedPlugins=[]);if(n.indexOf(t)>-1)return this;var d=w(arguments,1);return d.unshift(this),"function"==typeof t.install?t.install.apply(t,d):"function"==typeof t&&t.apply(null,d),n.push(t),this}}function Dn(t){t.mixin=function(t){return this.options=tt(this.options,t),this}}function qn(t){t.cid=0;var n=1;t.extend=function(t){t=t||{};var d=this,e=d.cid,r=t._Ctor||(t._Ctor={});if(r[e])return r[e];var i=t.name||d.options.name;i&&J(i);var a=function(t){this._init(t)};return a.prototype=Object.create(d.prototype),a.prototype.constructor=a,a.cid=n++,a.options=tt(d.options,t),a.super=d,a.options.props&&Qn(a),a.options.computed&&Hn(a),a.extend=d.extend,a.mixin=d.mixin,a.use=d.use,Me.forEach(function(t){a[t]=d[t]}),i&&(a.options.components[i]=a),a.superOptions=d.options,a.extendOptions=t,a.sealedOptions=x({},a.options),r[e]=a,a}}function Qn(t){var n=t.options.props;for(var d in n)Gt(t.prototype,"_props",d)}function Hn(t){var n=t.options.computed;for(var d in n)en(t.prototype,d,n[d])}function Vn(t){Me.forEach(function(n){t[n]=function(t,d){return d?("component"===n&&J(t),"component"===n&&h(d)&&(d.name=d.name||t,d=this.options._base.extend(d)),"directive"===n&&"function"==typeof d&&(d={bind:d,update:d}),this.options[n+"s"][t]=d,d):this.options[n+"s"][t]}})}function Wn(t){return t&&(t.Ctor.options.name||t.tag)}function Jn(t,n){return Array.isArray(t)?t.indexOf(n)>-1:"string"==typeof t?t.split(",").indexOf(n)>-1:!!l(t)&&t.test(n)}function Xn(t,n){var d=t.cache,e=t.keys,r=t._vnode;for(var i in d){var a=d[i];if(a){var o=Wn(a.componentOptions);o&&!n(o)&&Gn(d,i,e,r)}}}function Gn(t,n,d,e){var r=t[n];!r||e&&r.tag===e.tag||r.componentInstance.$destroy(),t[n]=null,g(d,n)}function Zn(t){for(var n=t.data,d=t,e=t;r(e.componentInstance);)(e=e.componentInstance._vnode)&&e.data&&(n=Yn(e.data,n));for(;r(d=d.parent);)d&&d.data&&(n=Yn(n,d.data));return td(n.staticClass,n.class)}function Yn(t,n){return{staticClass:nd(t.staticClass,n.staticClass),class:r(t.class)?[t.class,n.class]:n.class}}function td(t,n){return r(t)||r(n)?nd(t,dd(n)):""}function nd(t,n){return t?n?t+" "+n:t:n||""}function dd(t){return Array.isArray(t)?ed(t):c(t)?rd(t):"string"==typeof t?t:""}function ed(t){for(var n,d="",e=0,i=t.length;e<i;e++)r(n=dd(t[e]))&&""!==n&&(d&&(d+=" "),d+=n);return d}function rd(t){var n="";for(var d in t)t[d]&&(n&&(n+=" "),n+=d);return n}function id(t){return Ci(t)?"svg":"math"===t?"math":void 0}function ad(t){if(!De)return!0;if(Ri(t))return!1;if(t=t.toLowerCase(),null!=ji[t])return ji[t];var n=document.createElement(t);return t.indexOf("-")>-1?ji[t]=n.constructor===window.HTMLUnknownElement||n.constructor===window.HTMLElement:ji[t]=/HTMLUnknownElement/.test(n.toString())}function od(t){if("string"==typeof t){var n=document.querySelector(t);return n||(ar("Cannot find element: "+t),document.createElement("div"))}return t}function cd(t,n){var d=document.createElement(t);return"select"!==t?d:(n.data&&n.data.attrs&&void 0!==n.data.attrs.multiple&&d.setAttribute("multiple","multiple"),d)}function sd(t,n){return document.createElementNS(Oi[t],n)}function hd(t){return document.createTextNode(t)}function ld(t){return document.createComment(t)}function ud(t,n,d){t.insertBefore(n,d)}function pd(t,n){t.removeChild(n)}function fd(t,n){t.appendChild(n)}function bd(t){return t.parentNode}function gd(t){return t.nextSibling}function md(t){return t.tagName}function vd(t,n){t.textContent=n}function yd(t,n){t.setAttribute(n,"")}function _d(t,n){var d=t.data.ref;if(r(d)){var e=t.context,i=t.componentInstance||t.elm,a=e.$refs;n?Array.isArray(a[d])?g(a[d],i):a[d]===i&&(a[d]=void 0):t.data.refInFor?Array.isArray(a[d])?a[d].indexOf(i)<0&&a[d].push(i):a[d]=[i]:a[d]=i}}function wd(t,n){return t.key===n.key&&(t.tag===n.tag&&t.isComment===n.isComment&&r(t.data)===r(n.data)&&xd(t,n)||i(t.isAsyncPlaceholder)&&t.asyncFactory===n.asyncFactory&&e(n.asyncFactory.error))}function xd(t,n){if("input"!==t.tag)return!0;var d,e=r(d=t.data)&&r(d=d.attrs)&&d.type,i=r(d=n.data)&&r(d=d.attrs)&&d.type;return e===i||Fi(e)&&Fi(i)}function kd(t,n,d){var e,i,a={};for(e=n;e<=d;++e)i=t[e].key,r(i)&&(a[i]=e);return a}function Ad(t,n){(t.data.directives||n.data.directives)&&Nd(t,n)}function Nd(t,n){var d,e,r,i=t===$i,a=n===$i,o=Ld(t.data.directives,t.context),c=Ld(n.data.directives,n.context),s=[],h=[];for(d in c)e=o[d],r=c[d],e?(r.oldValue=e.value,Od(r,"update",n,t),r.def&&r.def.componentUpdated&&h.push(r)):(Od(r,"bind",n,t),r.def&&r.def.inserted&&s.push(r));if(s.length){var l=function(){for(var d=0;d<s.length;d++)Od(s[d],"inserted",n,t)};i?yt(n,"insert",l):l()}if(h.length&&yt(n,"postpatch",function(){for(var d=0;d<h.length;d++)Od(h[d],"componentUpdated",n,t)}),!i)for(d in o)c[d]||Od(o[d],"unbind",t,t,a)}function Ld(t,n){var d=Object.create(null);if(!t)return d;var e,r;for(e=0;e<t.length;e++)r=t[e],r.modifiers||(r.modifiers=Pi),d[Ud(r)]=r,r.def=nt(n.$options,"directives",r.name,!0);return d}function Ud(t){return t.rawName||t.name+"."+Object.keys(t.modifiers||{}).join(".")}function Od(t,n,d,e,r){var i=t.def&&t.def[n];if(i)try{i(d.elm,t,d,e,r)}catch(e){st(e,d.context,"directive "+t.name+" "+n+" hook")}}function Td(t,n){var d=n.componentOptions;if(!(r(d)&&!1===d.Ctor.options.inheritAttrs||e(t.data.attrs)&&e(n.data.attrs))){var i,a,o=n.elm,c=t.data.attrs||{},s=n.data.attrs||{};r(s.__ob__)&&(s=n.data.attrs=x({},s));for(i in s)a=s[i],c[i]!==a&&Cd(o,i,a);(Ve||Je)&&s.value!==c.value&&Cd(o,"value",s.value);for(i in c)e(s[i])&&(Ni(i)?o.removeAttributeNS(Ai,Li(i)):xi(i)||o.removeAttribute(i))}}function Cd(t,n,d){t.tagName.indexOf("-")>-1?Rd(t,n,d):ki(n)?Ui(d)?t.removeAttribute(n):(d="allowfullscreen"===n&&"EMBED"===t.tagName?"true":n,t.setAttribute(n,d)):xi(n)?t.setAttribute(n,Ui(d)||"false"===d?"false":"true"):Ni(n)?Ui(d)?t.removeAttributeNS(Ai,Li(n)):t.setAttributeNS(Ai,n,d):Rd(t,n,d)}function Rd(t,n,d){if(Ui(d))t.removeAttribute(n);else{if(Ve&&!We&&"TEXTAREA"===t.tagName&&"placeholder"===n&&!t.__ieph){var e=function(n){n.stopImmediatePropagation(),t.removeEventListener("input",e)};t.addEventListener("input",e),t.__ieph=!0}t.setAttribute(n,d)}}function jd(t,n){var d=n.elm,i=n.data,a=t.data;if(!(e(i.staticClass)&&e(i.class)&&(e(a)||e(a.staticClass)&&e(a.class)))){var o=Zn(n),c=d._transitionClasses;r(c)&&(o=nd(o,dd(c))),o!==d._prevClass&&(d.setAttribute("class",o),d._prevClass=o)}}function Fd(t){if(r(t[Di])){var n=Ve?"change":"input";t[n]=[].concat(t[Di],t[n]||[]),delete t[Di]}r(t[qi])&&(t.change=[].concat(t[qi],t.change||[]),delete t[qi])}function Sd(t,n,d){var e=mi;return function r(){null!==t.apply(null,arguments)&&$d(n,r,d,e)}}function Ed(t,n,d,e,r){n=pt(n),d&&(n=Sd(n,t,e)),mi.addEventListener(t,n,Ye?{capture:e,passive:r}:e)}function $d(t,n,d,e){(e||mi).removeEventListener(t,n._withTask||n,d)}function Id(t,n){if(!e(t.data.on)||!e(n.data.on)){var d=n.data.on||{},r=t.data.on||{};mi=n.elm,Fd(d),vt(d,r,Ed,$d,n.context),mi=void 0}}function Md(t,n){if(!e(t.data.domProps)||!e(n.data.domProps)){var d,i,a=n.elm,o=t.data.domProps||{},c=n.data.domProps||{};r(c.__ob__)&&(c=n.data.domProps=x({},c));for(d in o)e(c[d])&&(a[d]="");for(d in c){if(i=c[d],"textContent"===d||"innerHTML"===d){if(n.children&&(n.children.length=0),i===o[d])continue;1===a.childNodes.length&&a.removeChild(a.childNodes[0])}if("value"===d){a._value=i;var s=e(i)?"":String(i);Pd(a,s)&&(a.value=s)}else a[d]=i}}}function Pd(t,n){return!t.composing&&("OPTION"===t.tagName||Kd(t,n)||Bd(t,n))}function Kd(t,n){var d=!0;try{d=document.activeElement!==t}catch(t){}return d&&t.value!==n}function Bd(t,n){var d=t.value,e=t._vModifiers;if(r(e)){if(e.lazy)return!1;if(e.number)return f(d)!==f(n);if(e.trim)return d.trim()!==n.trim()}return d!==n}function zd(t){var n=Dd(t.style);return t.staticStyle?x(t.staticStyle,n):n}function Dd(t){return Array.isArray(t)?k(t):"string"==typeof t?Vi(t):t}function qd(t,n){var d,e={};if(n)for(var r=t;r.componentInstance;)(r=r.componentInstance._vnode)&&r.data&&(d=zd(r.data))&&x(e,d);(d=zd(t.data))&&x(e,d);for(var i=t;i=i.parent;)i.data&&(d=zd(i.data))&&x(e,d);return e}function Qd(t,n){var d=n.data,i=t.data;if(!(e(d.staticStyle)&&e(d.style)&&e(i.staticStyle)&&e(i.style))){var a,o,c=n.elm,s=i.staticStyle,h=i.normalizedStyle||i.style||{},l=s||h,u=Dd(n.data.style)||{};n.data.normalizedStyle=r(u.__ob__)?x({},u):u;var p=qd(n,!0);for(o in l)e(p[o])&&Xi(c,o,"");for(o in p)(a=p[o])!==l[o]&&Xi(c,o,null==a?"":a)}}function Hd(t,n){if(n&&(n=n.trim()))if(t.classList)n.indexOf(" ")>-1?n.split(/\s+/).forEach(function(n){return t.classList.add(n)}):t.classList.add(n);else{var d=" "+(t.getAttribute("class")||"")+" ";d.indexOf(" "+n+" ")<0&&t.setAttribute("class",(d+n).trim())}}function Vd(t,n){if(n&&(n=n.trim()))if(t.classList)n.indexOf(" ")>-1?n.split(/\s+/).forEach(function(n){return t.classList.remove(n)}):t.classList.remove(n),t.classList.length||t.removeAttribute("class");else{for(var d=" "+(t.getAttribute("class")||"")+" ",e=" "+n+" ";d.indexOf(e)>=0;)d=d.replace(e," ");d=d.trim(),d?t.setAttribute("class",d):t.removeAttribute("class")}}function Wd(t){if(t){if("object"==typeof t){var n={};return!1!==t.css&&x(n,ta(t.name||"v")),x(n,t),n}return"string"==typeof t?ta(t):void 0}}function Jd(t){ca(function(){ca(t)})}function Xd(t,n){var d=t._transitionClasses||(t._transitionClasses=[]);d.indexOf(n)<0&&(d.push(n),Hd(t,n))}function Gd(t,n){t._transitionClasses&&g(t._transitionClasses,n),Vd(t,n)}function Zd(t,n,d){var e=Yd(t,n),r=e.type,i=e.timeout,a=e.propCount;if(!r)return d();var o=r===da?ia:oa,c=0,s=function(){t.removeEventListener(o,h),d()},h=function(n){n.target===t&&++c>=a&&s()};setTimeout(function(){c<a&&s()},i+1),t.addEventListener(o,h)}function Yd(t,n){var d,e=window.getComputedStyle(t),r=e[ra+"Delay"].split(", "),i=e[ra+"Duration"].split(", "),a=te(r,i),o=e[aa+"Delay"].split(", "),c=e[aa+"Duration"].split(", "),s=te(o,c),h=0,l=0;return n===da?a>0&&(d=da,h=a,l=i.length):n===ea?s>0&&(d=ea,h=s,l=c.length):(h=Math.max(a,s),d=h>0?a>s?da:ea:null,l=d?d===da?i.length:c.length:0),{type:d,timeout:h,propCount:l,hasTransform:d===da&&sa.test(e[ra+"Property"])}}function te(t,n){for(;t.length<n.length;)t=t.concat(t);return Math.max.apply(null,n.map(function(n,d){return ne(n)+ne(t[d])}))}function ne(t){return 1e3*Number(t.slice(0,-1))}function de(t,n){var d=t.elm;r(d._leaveCb)&&(d._leaveCb.cancelled=!0,d._leaveCb());var i=Wd(t.data.transition);if(!e(i)&&!r(d._enterCb)&&1===d.nodeType){for(var a=i.css,o=i.type,s=i.enterClass,h=i.enterToClass,l=i.enterActiveClass,u=i.appearClass,p=i.appearToClass,b=i.appearActiveClass,g=i.beforeEnter,m=i.enter,v=i.afterEnter,y=i.enterCancelled,_=i.beforeAppear,w=i.appear,x=i.afterAppear,k=i.appearCancelled,A=i.duration,N=Jr,L=Jr.$vnode;L&&L.parent;)L=L.parent,N=L.context;var O=!N._isMounted||!t.isRootInsert;if(!O||w||""===w){var T=O&&u?u:s,C=O&&b?b:l,R=O&&p?p:h,j=O?_||g:g,F=O&&"function"==typeof w?w:m,S=O?x||v:v,E=O?k||y:y,$=f(c(A)?A.enter:A);null!=$&&re($,"enter",t);var I=!1!==a&&!We,M=ae(F),P=d._enterCb=U(function(){I&&(Gd(d,R),Gd(d,C)),P.cancelled?(I&&Gd(d,T),E&&E(d)):S&&S(d),d._enterCb=null});t.data.show||yt(t,"insert",function(){var n=d.parentNode,e=n&&n._pending&&n._pending[t.key];e&&e.tag===t.tag&&e.elm._leaveCb&&e.elm._leaveCb(),F&&F(d,P)}),j&&j(d),I&&(Xd(d,T),Xd(d,C),Jd(function(){Gd(d,T),P.cancelled||(Xd(d,R),M||(ie($)?setTimeout(P,$):Zd(d,o,P)))})),t.data.show&&(n&&n(),F&&F(d,P)),I||M||P()}}}function ee(t,n){function d(){k.cancelled||(t.data.show||((i.parentNode._pending||(i.parentNode._pending={}))[t.key]=t),p&&p(i),_&&(Xd(i,h),Xd(i,u),Jd(function(){Gd(i,h),k.cancelled||(Xd(i,l),w||(ie(x)?setTimeout(k,x):Zd(i,s,k)))})),b&&b(i,k),_||w||k())}var i=t.elm;r(i._enterCb)&&(i._enterCb.cancelled=!0,i._enterCb());var a=Wd(t.data.transition);if(e(a)||1!==i.nodeType)return n();if(!r(i._leaveCb)){var o=a.css,s=a.type,h=a.leaveClass,l=a.leaveToClass,u=a.leaveActiveClass,p=a.beforeLeave,b=a.leave,g=a.afterLeave,m=a.leaveCancelled,v=a.delayLeave,y=a.duration,_=!1!==o&&!We,w=ae(b),x=f(c(y)?y.leave:y);r(x)&&re(x,"leave",t);var k=i._leaveCb=U(function(){i.parentNode&&i.parentNode._pending&&(i.parentNode._pending[t.key]=null),_&&(Gd(i,l),Gd(i,u)),k.cancelled?(_&&Gd(i,h),m&&m(i)):(n(),g&&g(i)),i._leaveCb=null});v?v(d):d()}}function re(t,n,d){"number"!=typeof t?ar("<transition> explicit "+n+" duration is not a valid number - got "+JSON.stringify(t)+".",d.context):isNaN(t)&&ar("<transition> explicit "+n+" duration is NaN - the duration expression might be incorrect.",d.context)}function ie(t){return"number"==typeof t&&!isNaN(t)}function ae(t){if(e(t))return!1;var n=t.fns;return r(n)?ae(Array.isArray(n)?n[0]:n):(t._length||t.length)>1}function oe(t,n){!0!==n.data.show&&de(n)}function ce(t,n,d){se(t,n,d),(Ve||Je)&&setTimeout(function(){se(t,n,d)},0)}function se(t,n,d){var e=n.value,r=t.multiple;if(r&&!Array.isArray(e))return void ar('<select multiple v-model="'+n.expression+'"> expects an Array value for its binding, but got '+Object.prototype.toString.call(e).slice(8,-1),d);for(var i,a,o=0,c=t.options.length;o<c;o++)if(a=t.options[o],r)i=L(e,le(a))>-1,a.selected!==i&&(a.selected=i);else if(N(le(a),e))return void(t.selectedIndex!==o&&(t.selectedIndex=o));r||(t.selectedIndex=-1)}function he(t,n){return n.every(function(n){return!N(n,t)})}function le(t){return"_value"in t?t._value:t.value}function ue(t){t.target.composing=!0}function pe(t){t.target.composing&&(t.target.composing=!1,fe(t.target,"input"))}function fe(t,n){var d=document.createEvent("HTMLEvents");d.initEvent(n,!0,!0),t.dispatchEvent(d)}function be(t){return!t.componentInstance||t.data&&t.data.transition?t:be(t.componentInstance._vnode)}function ge(t){var n=t&&t.componentOptions;return n&&n.Ctor.options.abstract?ge(Ct(n.children)):t}function me(t){var n={},d=t.$options;for(var e in d.propsData)n[e]=t[e];var r=d._parentListeners;for(var i in r)n[Ce(i)]=r[i];return n}function ve(t,n){if(/\d-keep-alive$/.test(n.tag))return t("keep-alive",{props:n.componentOptions.propsData})}function ye(t){for(;t=t.parent;)if(t.data.transition)return!0}function _e(t,n){return n.key===t.key&&n.tag===t.tag}function we(t){t.elm._moveCb&&t.elm._moveCb(),t.elm._enterCb&&t.elm._enterCb()}function xe(t){t.data.newPos=t.elm.getBoundingClientRect()}function ke(t){var n=t.data.pos,d=t.data.newPos,e=n.left-d.left,r=n.top-d.top;if(e||r){t.data.moved=!0;var i=t.elm.style;i.transform=i.WebkitTransform="translate("+e+"px,"+r+"px)",i.transitionDuration="0s"}}var Ae=Object.freeze({}),Ne=Object.prototype.toString,Le=b("slot,component",!0),Ue=b("key,ref,slot,slot-scope,is"),Oe=Object.prototype.hasOwnProperty,Te=/-(\w)/g,Ce=v(function(t){return t.replace(Te,function(t,n){return n?n.toUpperCase():""})}),Re=v(function(t){return t.charAt(0).toUpperCase()+t.slice(1)}),je=/\B([A-Z])/g,Fe=v(function(t){return t.replace(je,"-$1").toLowerCase()}),Se=Function.prototype.bind?_:y,Ee=function(t,n,d){return!1},$e=function(t){return t},Ie="data-server-rendered",Me=["component","directive","filter"],Pe=["beforeCreate","created","beforeMount","mounted","beforeUpdate","updated","beforeDestroy","destroyed","activated","deactivated","errorCaptured"],Ke={optionMergeStrategies:Object.create(null),silent:!1,productionTip:!0,devtools:!0,performance:!1,errorHandler:null,warnHandler:null,ignoredElements:[],keyCodes:Object.create(null),isReservedTag:Ee,isReservedAttr:Ee,isUnknownElement:Ee,getTagNamespace:A,parsePlatformTagName:$e,mustUseProp:Ee,_lifecycleHooks:Pe},Be=/[^\w.$]/,ze="__proto__"in{},De="undefined"!=typeof window,qe="undefined"!=typeof WXEnvironment&&!!WXEnvironment.platform,Qe=qe&&WXEnvironment.platform.toLowerCase(),He=De&&window.navigator.userAgent.toLowerCase(),Ve=He&&/msie|trident/.test(He),We=He&&He.indexOf("msie 9.0")>0,Je=He&&He.indexOf("edge/")>0,Xe=(He&&He.indexOf("android"),He&&/iphone|ipad|ipod|ios/.test(He)||"ios"===Qe),Ge=He&&/chrome\/\d+/.test(He)&&!Je,Ze={}.watch,Ye=!1;if(De)try{var tr={};Object.defineProperty(tr,"passive",{get:function(){Ye=!0}}),window.addEventListener("test-passive",null,tr)}catch(t){}var nr,dr,er=function(){return void 0===nr&&(nr=!De&&!qe&&void 0!==t&&"server"===t.process.env.VUE_ENV),nr},rr=De&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__,ir="undefined"!=typeof Symbol&&R(Symbol)&&"undefined"!=typeof Reflect&&R(Reflect.ownKeys);dr="undefined"!=typeof Set&&R(Set)?Set:function(){function t(){this.set=Object.create(null)}return t.prototype.has=function(t){return!0===this.set[t]},t.prototype.add=function(t){this.set[t]=!0},t.prototype.clear=function(){this.set=Object.create(null)},t}();var ar=A,or=A,cr=A,sr=A,hr="undefined"!=typeof console,lr=/(?:^|[-_])(\w)/g,ur=function(t){return t.replace(lr,function(t){return t.toUpperCase()}).replace(/[-_]/g,"")};ar=function(t,n){var d=n?cr(n):"";Ke.warnHandler?Ke.warnHandler.call(null,t,n,d):hr&&!Ke.silent&&console.error("[Vue warn]: "+t+d)},or=function(t,n){hr&&!Ke.silent&&console.warn("[Vue tip]: "+t+(n?cr(n):""))},sr=function(t,n){if(t.$root===t)return"<Root>";var d="function"==typeof t&&null!=t.cid?t.options:t._isVue?t.$options||t.constructor.options:t||{},e=d.name||d._componentTag,r=d.__file;if(!e&&r){var i=r.match(/([^\/\\]+)\.vue$/);e=i&&i[1]}return(e?"<"+ur(e)+">":"<Anonymous>")+(r&&!1!==n?" at "+r:"")};var pr=function(t,n){for(var d="";n;)n%2==1&&(d+=t),n>1&&(t+=t),n>>=1;return d};cr=function(t){if(t._isVue&&t.$parent){for(var n=[],d=0;t;){if(n.length>0){var e=n[n.length-1];if(e.constructor===t.constructor){d++,t=t.$parent;continue}d>0&&(n[n.length-1]=[e,d],d=0)}n.push(t),t=t.$parent}return"\n\nfound in\n\n"+n.map(function(t,n){return""+(0===n?"---\x3e ":pr(" ",5+2*n))+(Array.isArray(t)?sr(t[0])+"... ("+t[1]+" recursive calls)":sr(t))}).join("\n")}return"\n\n(found in "+sr(t)+")"};var fr=0,br=function(){this.id=fr++,this.subs=[]};br.prototype.addSub=function(t){this.subs.push(t)},br.prototype.removeSub=function(t){g(this.subs,t)},br.prototype.depend=function(){br.target&&br.target.addDep(this)},br.prototype.notify=function(){for(var t=this.subs.slice(),n=0,d=t.length;n<d;n++)t[n].update()},br.target=null;var gr=[],mr=function(t,n,d,e,r,i,a,o){this.tag=t,this.data=n,this.children=d,this.text=e,this.elm=r,this.ns=void 0,this.context=i,this.fnContext=void 0,this.fnOptions=void 0,this.fnScopeId=void 0,this.key=n&&n.key,this.componentOptions=a,this.componentInstance=void 0,this.parent=void 0,this.raw=!1,this.isStatic=!1,this.isRootInsert=!0,this.isComment=!1,this.isCloned=!1,this.isOnce=!1,this.asyncFactory=o,this.asyncMeta=void 0,this.isAsyncPlaceholder=!1},vr={child:{configurable:!0}};vr.child.get=function(){return this.componentInstance},Object.defineProperties(mr.prototype,vr);var yr=function(t){void 0===t&&(t="");var n=new mr;return n.text=t,n.isComment=!0,n},_r=Array.prototype,wr=Object.create(_r);["push","pop","shift","unshift","splice","sort","reverse"].forEach(function(t){var n=_r[t];T(wr,t,function(){for(var d=[],e=arguments.length;e--;)d[e]=arguments[e];var r,i=n.apply(this,d),a=this.__ob__;switch(t){case"push":case"unshift":r=d;break;case"splice":r=d.slice(2)}return r&&a.observeArray(r),a.dep.notify(),i})});var xr=Object.getOwnPropertyNames(wr),kr=!0,Ar=function(t){if(this.value=t,this.dep=new br,this.vmCount=0,T(t,"__ob__",this),Array.isArray(t)){(ze?I:M)(t,wr,xr),this.observeArray(t)}else this.walk(t)};Ar.prototype.walk=function(t){for(var n=Object.keys(t),d=0;d<n.length;d++)K(t,n[d])},Ar.prototype.observeArray=function(t){for(var n=0,d=t.length;n<d;n++)P(t[n])};var Nr=Ke.optionMergeStrategies;Nr.el=Nr.propsData=function(t,n,d,e){return d||ar('option "'+e+'" can only be used during instance creation with the `new` keyword.'),Or(t,n)},Nr.data=function(t,n,d){return d?Q(t,n,d):n&&"function"!=typeof n?(ar('The "data" option should be a function that returns a per-instance value in component definitions.',d),t):Q(t,n)},Pe.forEach(function(t){Nr[t]=H}),Me.forEach(function(t){Nr[t+"s"]=V}),Nr.watch=function(t,n,d,e){if(t===Ze&&(t=void 0),n===Ze&&(n=void 0),!n)return Object.create(t||null);if(Y(e,n,d),!t)return n;var r={};x(r,t);for(var i in n){var a=r[i],o=n[i];a&&!Array.isArray(a)&&(a=[a]),r[i]=a?a.concat(o):Array.isArray(o)?o:[o]}return r},Nr.props=Nr.methods=Nr.inject=Nr.computed=function(t,n,d,e){if(n&&Y(e,n,d),!t)return n;var r=Object.create(null);return x(r,t),n&&x(r,n),r},Nr.provide=Q;var Lr,Ur,Or=function(t,n){return void 0===n?t:n},Tr=/^(String|Number|Boolean|Function|Symbol)$/,Cr=[],Rr=!1,jr=!1;if(void 0!==d&&R(d))Ur=function(){d(ut)};else if("undefined"==typeof MessageChannel||!R(MessageChannel)&&"[object MessageChannelConstructor]"!==MessageChannel.toString())Ur=function(){setTimeout(ut,0)};else{var Fr=new MessageChannel,Sr=Fr.port2;Fr.port1.onmessage=ut,Ur=function(){Sr.postMessage(1)}}if("undefined"!=typeof Promise&&R(Promise)){var Er=Promise.resolve();Lr=function(){Er.then(ut),Xe&&setTimeout(A)}}else Lr=Ur;var $r,Ir=b("Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,require"),Mr=function(t,n){ar('Property or method "'+n+'" is not defined on the instance but referenced during render. Make sure that this property is reactive, either in the data option, or for class-based components, by initializing the property. See: https://vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.',t)},Pr="undefined"!=typeof Proxy&&R(Proxy);if(Pr){var Kr=b("stop,prevent,self,ctrl,shift,alt,meta,exact");Ke.keyCodes=new Proxy(Ke.keyCodes,{set:function(t,n,d){return Kr(n)?(ar("Avoid overwriting built-in modifier in config.keyCodes: ."+n),!1):(t[n]=d,!0)}})}var Br={has:function(t,n){var d=n in t,e=Ir(n)||"_"===n.charAt(0);return d||e||Mr(t,n),d||!e}},zr={get:function(t,n){return"string"!=typeof n||n in t||Mr(t,n),t[n]}};$r=function(t){if(Pr){var n=t.$options,d=n.render&&n.render._withStripped?zr:Br;t._renderProxy=new Proxy(t,d)}else t._renderProxy=t};var Dr,qr,Qr=new dr,Hr=De&&window.performance;Hr&&Hr.mark&&Hr.measure&&Hr.clearMarks&&Hr.clearMeasures&&(Dr=function(t){return Hr.mark(t)},qr=function(t,n,d){Hr.measure(t,n,d),Hr.clearMarks(n),Hr.clearMarks(d),Hr.clearMeasures(t)});var Vr,Wr=v(function(t){var n="&"===t.charAt(0);t=n?t.slice(1):t;var d="~"===t.charAt(0);t=d?t.slice(1):t;var e="!"===t.charAt(0);return t=e?t.slice(1):t,{name:t,once:d,capture:e,passive:n}}),Jr=null,Xr=!1,Gr=100,Zr=[],Yr=[],ti={},ni={},di=!1,ei=!1,ri=0,ii=0,ai=function(t,n,d,e,r){this.vm=t,r&&(t._watcher=this),t._watchers.push(this),e?(this.deep=!!e.deep,this.user=!!e.user,this.lazy=!!e.lazy,this.sync=!!e.sync):this.deep=this.user=this.lazy=this.sync=!1,this.cb=d,this.id=++ii,this.active=!0,this.dirty=this.lazy,this.deps=[],this.newDeps=[],this.depIds=new dr,this.newDepIds=new dr,this.expression=n.toString(),"function"==typeof n?this.getter=n:(this.getter=C(n),this.getter||(this.getter=function(){},ar('Failed watching path: "'+n+'" Watcher only accepts simple dot-delimited paths. For full control, use a function instead.',t))),this.value=this.lazy?void 0:this.get()};ai.prototype.get=function(){j(this);var t,n=this.vm;try{t=this.getter.call(n,n)}catch(t){if(!this.user)throw t;st(t,n,'getter for watcher "'+this.expression+'"')}finally{this.deep&&bt(t),F(),this.cleanupDeps()}return t},ai.prototype.addDep=function(t){var n=t.id;this.newDepIds.has(n)||(this.newDepIds.add(n),this.newDeps.push(t),this.depIds.has(n)||t.addSub(this))},ai.prototype.cleanupDeps=function(){for(var t=this,n=this.deps.length;n--;){var d=t.deps[n];t.newDepIds.has(d.id)||d.removeSub(t)}var e=this.depIds;this.depIds=this.newDepIds,this.newDepIds=e,this.newDepIds.clear(),e=this.deps,this.deps=this.newDeps,this.newDeps=e,this.newDeps.length=0},ai.prototype.update=function(){this.lazy?this.dirty=!0:this.sync?this.run():Xt(this)},ai.prototype.run=function(){if(this.active){var t=this.get();if(t!==this.value||c(t)||this.deep){var n=this.value;if(this.value=t,this.user)try{this.cb.call(this.vm,t,n)}catch(t){st(t,this.vm,'callback for watcher "'+this.expression+'"')}else this.cb.call(this.vm,t,n)}}},ai.prototype.evaluate=function(){this.value=this.get(),this.dirty=!1},ai.prototype.depend=function(){for(var t=this,n=this.deps.length;n--;)t.deps[n].depend()},ai.prototype.teardown=function(){var t=this;if(this.active){this.vm._isBeingDestroyed||g(this.vm._watchers,this);for(var n=this.deps.length;n--;)t.deps[n].removeSub(t);this.active=!1}};var oi={enumerable:!0,configurable:!0,get:A,set:A},ci={lazy:!0};kn(An.prototype);var si={init:function(t,n,d,e){if(t.componentInstance&&!t.componentInstance._isDestroyed&&t.data.keepAlive){var r=t;si.prepatch(r,r)}else{(t.componentInstance=Tn(t,Jr,d,e)).$mount(n?t.elm:void 0,n)}},prepatch:function(t,n){var d=n.componentOptions;Kt(n.componentInstance=t.componentInstance,d.propsData,d.listeners,n,d.children)},insert:function(t){var n=t.context,d=t.componentInstance;d._isMounted||(d._isMounted=!0,qt(d,"mounted")),t.data.keepAlive&&(n._isMounted?Wt(d):zt(d,!0))},destroy:function(t){var n=t.componentInstance;n._isDestroyed||(t.data.keepAlive?Dt(n,!0):n.$destroy())}},hi=Object.keys(si),li=1,ui=2,pi=0;!function(t){t.prototype._init=function(t){var n=this;n._uid=pi++;var d,e;Ke.performance&&Dr&&(d="vue-perf-start:"+n._uid,e="vue-perf-end:"+n._uid,Dr(d)),n._isVue=!0,t&&t._isComponent?In(n,t):n.$options=tt(Mn(n.constructor),t||{},n),$r(n),n._self=n,Mt(n),Rt(n),$n(n),qt(n,"beforeCreate"),hn(n),Zt(n),sn(n),qt(n,"created"),Ke.performance&&Dr&&(n._name=sr(n,!1),Dr(e),qr("vue "+n._name+" init",d,e)),n.$options.el&&n.$mount(n.$options.el)}}(Bn),function(t){var n={};n.get=function(){return this._data};var d={};d.get=function(){return this._props},n.set=function(t){ar("Avoid replacing instance root $data. Use nested data properties instead.",this)},d.set=function(){ar("$props is readonly.",this)},Object.defineProperty(t.prototype,"$data",n),Object.defineProperty(t.prototype,"$props",d),t.prototype.$set=B,t.prototype.$delete=z,t.prototype.$watch=function(t,n,d){var e=this;if(h(n))return cn(e,t,n,d);d=d||{},d.user=!0;var r=new ai(e,t,n,d);return d.immediate&&n.call(e,r.value),function(){r.teardown()}}}(Bn),function(t){var n=/^hook:/;t.prototype.$on=function(t,d){var e=this,r=this;if(Array.isArray(t))for(var i=0,a=t.length;i<a;i++)e.$on(t[i],d);else(r._events[t]||(r._events[t]=[])).push(d),n.test(t)&&(r._hasHookEvent=!0);return r},t.prototype.$once=function(t,n){function d(){e.$off(t,d),n.apply(e,arguments)}var e=this;return d.fn=n,e.$on(t,d),e},t.prototype.$off=function(t,n){var d=this,e=this;if(!arguments.length)return e._events=Object.create(null),e;if(Array.isArray(t)){for(var r=0,i=t.length;r<i;r++)d.$off(t[r],n);return e}var a=e._events[t];if(!a)return e;if(!n)return e._events[t]=null,e;if(n)for(var o,c=a.length;c--;)if((o=a[c])===n||o.fn===n){a.splice(c,1);break}return e},t.prototype.$emit=function(t){var n=this,d=t.toLowerCase();d!==t&&n._events[d]&&or('Event "'+d+'" is emitted in component '+sr(n)+' but the handler is registered for "'+t+'". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use "'+Fe(t)+'" instead of "'+t+'".');var e=n._events[t];if(e){e=e.length>1?w(e):e;for(var r=w(arguments,1),i=0,a=e.length;i<a;i++)try{e[i].apply(n,r)}catch(d){st(d,n,'event handler for "'+t+'"')}}return n}}(Bn),function(t){t.prototype._update=function(t,n){var d=this;d._isMounted&&qt(d,"beforeUpdate");var e=d.$el,r=d._vnode,i=Jr;Jr=d,d._vnode=t,r?d.$el=d.__patch__(r,t):(d.$el=d.__patch__(d.$el,t,n,!1,d.$options._parentElm,d.$options._refElm),d.$options._parentElm=d.$options._refElm=null),Jr=i,e&&(e.__vue__=null),d.$el&&(d.$el.__vue__=d),d.$vnode&&d.$parent&&d.$vnode===d.$parent._vnode&&(d.$parent.$el=d.$el)},t.prototype.$forceUpdate=function(){var t=this;t._watcher&&t._watcher.update()},t.prototype.$destroy=function(){var t=this;if(!t._isBeingDestroyed){qt(t,"beforeDestroy"),t._isBeingDestroyed=!0;var n=t.$parent;!n||n._isBeingDestroyed||t.$options.abstract||g(n.$children,t),t._watcher&&t._watcher.teardown();for(var d=t._watchers.length;d--;)t._watchers[d].teardown();t._data.__ob__&&t._data.__ob__.vmCount--,t._isDestroyed=!0,t.__patch__(t._vnode,null),qt(t,"destroyed"),t.$off(),t.$el&&(t.$el.__vue__=null),t.$vnode&&(t.$vnode.parent=null)}}}(Bn),function(t){kn(t.prototype),t.prototype.$nextTick=function(t){return ft(t,this)},t.prototype._render=function(){var t=this,n=t.$options,d=n.render,e=n._parentVnode;for(var r in t.$slots)t.$slots[r]._rendered=!1;e&&(t.$scopedSlots=e.data.scopedSlots||Ae),t.$vnode=e;var i;try{i=d.call(t._renderProxy,t.$createElement)}catch(n){if(st(n,t,"render"),t.$options.renderError)try{i=t.$options.renderError.call(t._renderProxy,t.$createElement,n)}catch(n){st(n,t,"renderError"),i=t._vnode}else i=t._vnode}return i instanceof mr||(Array.isArray(i)&&ar("Multiple root nodes returned from render function. Render function should return a single root node.",t),i=yr()),i.parent=e,i}}(Bn);var fi=[String,RegExp,Array],bi={name:"keep-alive",abstract:!0,props:{include:fi,exclude:fi,max:[String,Number]},created:function(){this.cache=Object.create(null),this.keys=[]},destroyed:function(){var t=this;for(var n in t.cache)Gn(t.cache,n,t.keys)},mounted:function(){var t=this;this.$watch("include",function(n){Xn(t,function(t){return Jn(n,t)})}),this.$watch("exclude",function(n){Xn(t,function(t){return!Jn(n,t)})})},render:function(){var t=this.$slots.default,n=Ct(t),d=n&&n.componentOptions;if(d){var e=Wn(d),r=this,i=r.include,a=r.exclude;if(i&&(!e||!Jn(i,e))||a&&e&&Jn(a,e))return n;var o=this,c=o.cache,s=o.keys,h=null==n.key?d.Ctor.cid+(d.tag?"::"+d.tag:""):n.key;c[h]?(n.componentInstance=c[h].componentInstance,g(s,h),s.push(h)):(c[h]=n,s.push(h),this.max&&s.length>parseInt(this.max)&&Gn(c,s[0],s,this._vnode)),n.data.keepAlive=!0}return n||t&&t[0]}},gi={KeepAlive:bi};!function(t){var n={};n.get=function(){return Ke},n.set=function(){ar("Do not replace the Vue.config object, set individual fields instead.")},Object.defineProperty(t,"config",n),t.util={warn:ar,extend:x,mergeOptions:tt,defineReactive:K},t.set=B,t.delete=z,t.nextTick=ft,t.options=Object.create(null),Me.forEach(function(n){t.options[n+"s"]=Object.create(null)}),t.options._base=t,x(t.options.components,gi),zn(t),Dn(t),qn(t),Vn(t)}(Bn),Object.defineProperty(Bn.prototype,"$isServer",{get:er}),Object.defineProperty(Bn.prototype,"$ssrContext",{get:function(){return this.$vnode&&this.$vnode.ssrContext}}),Object.defineProperty(Bn,"FunctionalRenderContext",{value:An}),Bn.version="2.5.16";var mi,vi,yi=b("style,class"),_i=b("input,textarea,option,select,progress"),wi=function(t,n,d){return"value"===d&&_i(t)&&"button"!==n||"selected"===d&&"option"===t||"checked"===d&&"input"===t||"muted"===d&&"video"===t},xi=b("contenteditable,draggable,spellcheck"),ki=b("allowfullscreen,async,autofocus,autoplay,checked,compact,controls,declare,default,defaultchecked,defaultmuted,defaultselected,defer,disabled,enabled,formnovalidate,hidden,indeterminate,inert,ismap,itemscope,loop,multiple,muted,nohref,noresize,noshade,novalidate,nowrap,open,pauseonexit,readonly,required,reversed,scoped,seamless,selected,sortable,translate,truespeed,typemustmatch,visible"),Ai="http://www.w3.org/1999/xlink",Ni=function(t){return":"===t.charAt(5)&&"xlink"===t.slice(0,5)},Li=function(t){return Ni(t)?t.slice(6,t.length):""},Ui=function(t){return null==t||!1===t},Oi={svg:"http://www.w3.org/2000/svg",math:"http://www.w3.org/1998/Math/MathML"},Ti=b("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,hgroup,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,rtc,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,menuitem,summary,content,element,shadow,template,blockquote,iframe,tfoot"),Ci=b("svg,animate,circle,clippath,cursor,defs,desc,ellipse,filter,font-face,foreignObject,g,glyph,image,line,marker,mask,missing-glyph,path,pattern,polygon,polyline,rect,switch,symbol,text,textpath,tspan,use,view",!0),Ri=function(t){return Ti(t)||Ci(t)},ji=Object.create(null),Fi=b("text,number,password,search,email,tel,url"),Si=Object.freeze({createElement:cd,createElementNS:sd,createTextNode:hd,createComment:ld,insertBefore:ud,removeChild:pd,appendChild:fd,parentNode:bd,nextSibling:gd,tagName:md,setTextContent:vd,setStyleScope:yd}),Ei={create:function(t,n){_d(n)},update:function(t,n){t.data.ref!==n.data.ref&&(_d(t,!0),_d(n))},destroy:function(t){_d(t,!0)}},$i=new mr("",{},[]),Ii=["create","activate","update","remove","destroy"],Mi={create:Ad,update:Ad,destroy:function(t){Ad(t,$i)}},Pi=Object.create(null),Ki=[Ei,Mi],Bi={create:Td,update:Td},zi={create:jd,update:jd},Di="__r",qi="__c",Qi={create:Id,update:Id},Hi={create:Md,update:Md},Vi=v(function(t){var n={},d=/;(?![^(]*\))/g,e=/:(.+)/;return t.split(d).forEach(function(t){if(t){var d=t.split(e);d.length>1&&(n[d[0].trim()]=d[1].trim())}}),n}),Wi=/^--/,Ji=/\s*!important$/,Xi=function(t,n,d){if(Wi.test(n))t.style.setProperty(n,d);else if(Ji.test(d))t.style.setProperty(n,d.replace(Ji,""),"important");else{var e=Zi(n);if(Array.isArray(d))for(var r=0,i=d.length;r<i;r++)t.style[e]=d[r];else t.style[e]=d}},Gi=["Webkit","Moz","ms"],Zi=v(function(t){if(vi=vi||document.createElement("div").style,"filter"!==(t=Ce(t))&&t in vi)return t;for(var n=t.charAt(0).toUpperCase()+t.slice(1),d=0;d<Gi.length;d++){var e=Gi[d]+n;if(e in vi)return e}}),Yi={create:Qd,update:Qd},ta=v(function(t){return{enterClass:t+"-enter",enterToClass:t+"-enter-to",enterActiveClass:t+"-enter-active",leaveClass:t+"-leave",leaveToClass:t+"-leave-to",leaveActiveClass:t+"-leave-active"}}),na=De&&!We,da="transition",ea="animation",ra="transition",ia="transitionend",aa="animation",oa="animationend";na&&(void 0===window.ontransitionend&&void 0!==window.onwebkittransitionend&&(ra="WebkitTransition",ia="webkitTransitionEnd"),void 0===window.onanimationend&&void 0!==window.onwebkitanimationend&&(aa="WebkitAnimation",oa="webkitAnimationEnd"));var ca=De?window.requestAnimationFrame?window.requestAnimationFrame.bind(window):setTimeout:function(t){return t()},sa=/\b(transform|all)(,|$)/,ha=De?{create:oe,activate:oe,remove:function(t,n){!0!==t.data.show?ee(t,n):n()}}:{},la=[Bi,zi,Qi,Hi,Yi,ha],ua=la.concat(Ki),pa=function(t){function n(t){return new mr($.tagName(t).toLowerCase(),{},[],void 0,t)}function d(t,n){function d(){0==--d.listeners&&a(t)}return d.listeners=n,d}function a(t){var n=$.parentNode(t);r(n)&&$.removeChild(n,t)}function c(t,n){return!n&&!t.ns&&!(Ke.ignoredElements.length&&Ke.ignoredElements.some(function(n){return l(n)?n.test(t.tag):n===t.tag}))&&Ke.isUnknownElement(t.tag)}function s(t,n,d,e,a,o,s){if(r(t.elm)&&r(o)&&(t=o[s]=E(t)),t.isRootInsert=!a,!h(t,n,d,e)){var l=t.data,u=t.children,p=t.tag;r(p)?(l&&l.pre&&I++,c(t,I)&&ar("Unknown custom element: <"+p+'> - did you register the component correctly? For recursive components, make sure to provide the "name" option.',t.context),t.elm=t.ns?$.createElementNS(t.ns,p):$.createElement(p,t),y(t),g(t,u,n),r(l)&&v(t,n),f(d,t.elm,e),l&&l.pre&&I--):i(t.isComment)?(t.elm=$.createComment(t.text),f(d,t.elm,e)):(t.elm=$.createTextNode(t.text),f(d,t.elm,e))}}function h(t,n,d,e){var a=t.data;if(r(a)){var o=r(t.componentInstance)&&a.keepAlive;if(r(a=a.hook)&&r(a=a.init)&&a(t,!1,d,e),r(t.componentInstance))return u(t,n),i(o)&&p(t,n,d,e),!0}}function u(t,n){r(t.data.pendingInsert)&&(n.push.apply(n,t.data.pendingInsert),t.data.pendingInsert=null),t.elm=t.componentInstance.$el,m(t)?(v(t,n),y(t)):(_d(t),n.push(t))}function p(t,n,d,e){for(var i,a=t;a.componentInstance;)if(a=a.componentInstance._vnode,r(i=a.data)&&r(i=i.transition)){for(i=0;i<F.activate.length;++i)F.activate[i]($i,a);n.push(a);break}f(d,t.elm,e)}function f(t,n,d){r(t)&&(r(d)?d.parentNode===t&&$.insertBefore(t,n,d):$.appendChild(t,n))}function g(t,n,d){if(Array.isArray(n)){N(n);for(var e=0;e<n.length;++e)s(n[e],d,t.elm,null,!0,n,e)}else o(t.text)&&$.appendChild(t.elm,$.createTextNode(String(t.text)))}function m(t){for(;t.componentInstance;)t=t.componentInstance._vnode;return r(t.tag)}function v(t,n){for(var d=0;d<F.create.length;++d)F.create[d]($i,t);R=t.data.hook,r(R)&&(r(R.create)&&R.create($i,t),r(R.insert)&&n.push(t))}function y(t){var n;if(r(n=t.fnScopeId))$.setStyleScope(t.elm,n);else for(var d=t;d;)r(n=d.context)&&r(n=n.$options._scopeId)&&$.setStyleScope(t.elm,n),d=d.parent;r(n=Jr)&&n!==t.context&&n!==t.fnContext&&r(n=n.$options._scopeId)&&$.setStyleScope(t.elm,n)}function _(t,n,d,e,r,i){for(;e<=r;++e)s(d[e],i,t,n,!1,d,e)}function w(t){var n,d,e=t.data;if(r(e))for(r(n=e.hook)&&r(n=n.destroy)&&n(t),n=0;n<F.destroy.length;++n)F.destroy[n](t);if(r(n=t.children))for(d=0;d<t.children.length;++d)w(t.children[d])}function x(t,n,d,e){for(;d<=e;++d){var i=n[d];r(i)&&(r(i.tag)?(k(i),w(i)):a(i.elm))}}function k(t,n){if(r(n)||r(t.data)){var e,i=F.remove.length+1;for(r(n)?n.listeners+=i:n=d(t.elm,i),r(e=t.componentInstance)&&r(e=e._vnode)&&r(e.data)&&k(e,n),e=0;e<F.remove.length;++e)F.remove[e](t,n);r(e=t.data.hook)&&r(e=e.remove)?e(t,n):n()}else a(t.elm)}function A(t,n,d,i,a){var o,c,h,l,u=0,p=0,f=n.length-1,b=n[0],g=n[f],m=d.length-1,v=d[0],y=d[m],w=!a;for(N(d);u<=f&&p<=m;)e(b)?b=n[++u]:e(g)?g=n[--f]:wd(b,v)?(U(b,v,i),b=n[++u],v=d[++p]):wd(g,y)?(U(g,y,i),g=n[--f],y=d[--m]):wd(b,y)?(U(b,y,i),w&&$.insertBefore(t,b.elm,$.nextSibling(g.elm)),b=n[++u],y=d[--m]):wd(g,v)?(U(g,v,i),w&&$.insertBefore(t,g.elm,b.elm),g=n[--f],v=d[++p]):(e(o)&&(o=kd(n,u,f)),c=r(v.key)?o[v.key]:L(v,n,u,f),e(c)?s(v,i,t,b.elm,!1,d,p):(h=n[c],wd(h,v)?(U(h,v,i),n[c]=void 0,w&&$.insertBefore(t,h.elm,b.elm)):s(v,i,t,b.elm,!1,d,p)),v=d[++p]);u>f?(l=e(d[m+1])?null:d[m+1].elm,_(t,l,d,p,m,i)):p>m&&x(t,n,u,f)}function N(t){for(var n={},d=0;d<t.length;d++){var e=t[d],i=e.key;r(i)&&(n[i]?ar("Duplicate keys detected: '"+i+"'. This may cause an update error.",e.context):n[i]=!0)}}function L(t,n,d,e){for(var i=d;i<e;i++){var a=n[i];if(r(a)&&wd(t,a))return i}}function U(t,n,d,a){if(t!==n){var o=n.elm=t.elm;if(i(t.isAsyncPlaceholder))return void(r(n.asyncFactory.resolved)?T(t.elm,n,d):n.isAsyncPlaceholder=!0);if(i(n.isStatic)&&i(t.isStatic)&&n.key===t.key&&(i(n.isCloned)||i(n.isOnce)))return void(n.componentInstance=t.componentInstance);var c,s=n.data;r(s)&&r(c=s.hook)&&r(c=c.prepatch)&&c(t,n);var h=t.children,l=n.children;if(r(s)&&m(n)){for(c=0;c<F.update.length;++c)F.update[c](t,n);r(c=s.hook)&&r(c=c.update)&&c(t,n)}e(n.text)?r(h)&&r(l)?h!==l&&A(o,h,l,d,a):r(l)?(r(t.text)&&$.setTextContent(o,""),_(o,null,l,0,l.length-1,d)):r(h)?x(o,h,0,h.length-1):r(t.text)&&$.setTextContent(o,""):t.text!==n.text&&$.setTextContent(o,n.text),r(s)&&r(c=s.hook)&&r(c=c.postpatch)&&c(t,n)}}function O(t,n,d){if(i(d)&&r(t.parent))t.parent.data.pendingInsert=n;else for(var e=0;e<n.length;++e)n[e].data.hook.insert(n[e])}function T(t,n,d,e){var a,o=n.tag,c=n.data,s=n.children;if(e=e||c&&c.pre,n.elm=t,i(n.isComment)&&r(n.asyncFactory))return n.isAsyncPlaceholder=!0,!0;if(!C(t,n,e))return!1;if(r(c)&&(r(a=c.hook)&&r(a=a.init)&&a(n,!0),r(a=n.componentInstance)))return u(n,d),!0;if(r(o)){if(r(s))if(t.hasChildNodes())if(r(a=c)&&r(a=a.domProps)&&r(a=a.innerHTML)){if(a!==t.innerHTML)return"undefined"==typeof console||M||(M=!0,console.warn("Parent: ",t),console.warn("server innerHTML: ",a),console.warn("client innerHTML: ",t.innerHTML)),!1}else{for(var h=!0,l=t.firstChild,p=0;p<s.length;p++){if(!l||!T(l,s[p],d,e)){h=!1;break}l=l.nextSibling}if(!h||l)return"undefined"==typeof console||M||(M=!0,console.warn("Parent: ",t),console.warn("Mismatching childNodes vs. VNodes: ",t.childNodes,s)),!1}else g(n,s,d);if(r(c)){var f=!1;for(var b in c)if(!P(b)){f=!0,v(n,d);break}!f&&c.class&&bt(c.class)}}else t.data!==n.text&&(t.data=n.text);return!0}function C(t,n,d){return r(n.tag)?0===n.tag.indexOf("vue-component")||!c(n,d)&&n.tag.toLowerCase()===(t.tagName&&t.tagName.toLowerCase()):t.nodeType===(n.isComment?8:3)}var R,j,F={},S=t.modules,$=t.nodeOps;for(R=0;R<Ii.length;++R)for(F[Ii[R]]=[],j=0;j<S.length;++j)r(S[j][Ii[R]])&&F[Ii[R]].push(S[j][Ii[R]]);var I=0,M=!1,P=b("attrs,class,staticClass,staticStyle,key");return function(t,d,a,o,c,h){if(e(d))return void(r(t)&&w(t));var l=!1,u=[];if(e(t))l=!0,s(d,u,c,h);else{var p=r(t.nodeType);if(!p&&wd(t,d))U(t,d,u,o);else{if(p){if(1===t.nodeType&&t.hasAttribute(Ie)&&(t.removeAttribute(Ie),a=!0),i(a)){if(T(t,d,u))return O(d,u,!0),t;ar("The client-side rendered virtual DOM tree is not matching server-rendered content. This is likely caused by incorrect HTML markup, for example nesting block-level elements inside <p>, or missing <tbody>. Bailing hydration and performing full client-side render.")}t=n(t)}var f=t.elm,b=$.parentNode(f);if(s(d,u,f._leaveCb?null:b,$.nextSibling(f)),r(d.parent))for(var g=d.parent,v=m(d);g;){for(var y=0;y<F.destroy.length;++y)F.destroy[y](g);if(g.elm=d.elm,v){for(var _=0;_<F.create.length;++_)F.create[_]($i,g);var k=g.data.hook.insert;if(k.merged)for(var A=1;A<k.fns.length;A++)k.fns[A]()}else _d(g);g=g.parent}r(b)?x(b,[t],0,0):r(t.tag)&&w(t)}}return O(d,u,l),d.elm}}({nodeOps:Si,modules:ua});We&&document.addEventListener("selectionchange",function(){var t=document.activeElement;t&&t.vmodel&&fe(t,"input")});var fa={inserted:function(t,n,d,e){"select"===d.tag?(e.elm&&!e.elm._vOptions?yt(d,"postpatch",function(){fa.componentUpdated(t,n,d)}):ce(t,n,d.context),t._vOptions=[].map.call(t.options,le)):("textarea"===d.tag||Fi(t.type))&&(t._vModifiers=n.modifiers,n.modifiers.lazy||(t.addEventListener("compositionstart",ue),t.addEventListener("compositionend",pe),t.addEventListener("change",pe),We&&(t.vmodel=!0)))},componentUpdated:function(t,n,d){if("select"===d.tag){ce(t,n,d.context);var e=t._vOptions,r=t._vOptions=[].map.call(t.options,le);if(r.some(function(t,n){return!N(t,e[n])})){(t.multiple?n.value.some(function(t){return he(t,r)}):n.value!==n.oldValue&&he(n.value,r))&&fe(t,"change")}}}},ba={bind:function(t,n,d){var e=n.value;d=be(d);var r=d.data&&d.data.transition,i=t.__vOriginalDisplay="none"===t.style.display?"":t.style.display;e&&r?(d.data.show=!0,de(d,function(){t.style.display=i})):t.style.display=e?i:"none"},update:function(t,n,d){var e=n.value;!e!=!n.oldValue&&(d=be(d),d.data&&d.data.transition?(d.data.show=!0,e?de(d,function(){t.style.display=t.__vOriginalDisplay}):ee(d,function(){t.style.display="none"})):t.style.display=e?t.__vOriginalDisplay:"none")},unbind:function(t,n,d,e,r){r||(t.style.display=t.__vOriginalDisplay)}},ga={model:fa,show:ba},ma={name:String,appear:Boolean,css:Boolean,mode:String,type:String,enterClass:String,leaveClass:String,enterToClass:String,leaveToClass:String,enterActiveClass:String,leaveActiveClass:String,appearClass:String,appearActiveClass:String,appearToClass:String,duration:[Number,String,Object]},va={name:"transition",props:ma,abstract:!0,render:function(t){var n=this,d=this.$slots.default;if(d&&(d=d.filter(function(t){return t.tag||Tt(t)}),d.length)){d.length>1&&ar("<transition> can only be used on a single element. Use <transition-group> for lists.",this.$parent);var e=this.mode;e&&"in-out"!==e&&"out-in"!==e&&ar("invalid <transition> mode: "+e,this.$parent);var r=d[0];if(ye(this.$vnode))return r;var i=ge(r);if(!i)return r;if(this._leaving)return ve(t,r);var a="__transition-"+this._uid+"-";i.key=null==i.key?i.isComment?a+"comment":a+i.tag:o(i.key)?0===String(i.key).indexOf(a)?i.key:a+i.key:i.key;var c=(i.data||(i.data={})).transition=me(this),s=this._vnode,h=ge(s);if(i.data.directives&&i.data.directives.some(function(t){return"show"===t.name})&&(i.data.show=!0),h&&h.data&&!_e(i,h)&&!Tt(h)&&(!h.componentInstance||!h.componentInstance._vnode.isComment)){var l=h.data.transition=x({},c);if("out-in"===e)return this._leaving=!0,yt(l,"afterLeave",function(){n._leaving=!1,n.$forceUpdate()}),ve(t,r);if("in-out"===e){if(Tt(i))return s;var u,p=function(){u()};yt(c,"afterEnter",p),yt(c,"enterCancelled",p),yt(l,"delayLeave",function(t){u=t})}}return r}}},ya=x({tag:String,moveClass:String},ma);delete ya.mode;var _a={props:ya,render:function(t){for(var n=this.tag||this.$vnode.data.tag||"span",d=Object.create(null),e=this.prevChildren=this.children,r=this.$slots.default||[],i=this.children=[],a=me(this),o=0;o<r.length;o++){var c=r[o];if(c.tag)if(null!=c.key&&0!==String(c.key).indexOf("__vlist"))i.push(c),d[c.key]=c,(c.data||(c.data={})).transition=a;else{var s=c.componentOptions,h=s?s.Ctor.options.name||s.tag||"":c.tag;ar("<transition-group> children must be keyed: <"+h+">")}}if(e){for(var l=[],u=[],p=0;p<e.length;p++){var f=e[p];f.data.transition=a,f.data.pos=f.elm.getBoundingClientRect(),d[f.key]?l.push(f):u.push(f)}this.kept=t(n,null,l),this.removed=u}return t(n,null,i)},beforeUpdate:function(){this.__patch__(this._vnode,this.kept,!1,!0),this._vnode=this.kept},updated:function(){var t=this.prevChildren,n=this.moveClass||(this.name||"v")+"-move";t.length&&this.hasMove(t[0].elm,n)&&(t.forEach(we),t.forEach(xe),t.forEach(ke),this._reflow=document.body.offsetHeight,t.forEach(function(t){if(t.data.moved){var d=t.elm,e=d.style;Xd(d,n),e.transform=e.WebkitTransform=e.transitionDuration="",d.addEventListener(ia,d._moveCb=function t(e){e&&!/transform$/.test(e.propertyName)||(d.removeEventListener(ia,t),d._moveCb=null,Gd(d,n))})}}))},methods:{hasMove:function(t,n){if(!na)return!1;if(this._hasMove)return this._hasMove;var d=t.cloneNode();t._transitionClasses&&t._transitionClasses.forEach(function(t){Vd(d,t)}),Hd(d,n),d.style.display="none",this.$el.appendChild(d);var e=Yd(d);return this.$el.removeChild(d),this._hasMove=e.hasTransform}}},wa={Transition:va,TransitionGroup:_a};Bn.config.mustUseProp=wi,Bn.config.isReservedTag=Ri,Bn.config.isReservedAttr=yi,Bn.config.getTagNamespace=id,Bn.config.isUnknownElement=ad,x(Bn.options.directives,ga),x(Bn.options.components,wa),Bn.prototype.__patch__=De?pa:A,Bn.prototype.$mount=function(t,n){return t=t&&De?od(t):void 0,Pt(this,t,n)},De&&setTimeout(function(){Ke.devtools&&(rr?rr.emit("init",Bn):Ge&&console[console.info?"info":"log"]("Download the Vue Devtools extension for a better development experience:\nhttps://github.com/vuejs/vue-devtools")),!1!==Ke.productionTip&&"undefined"!=typeof console&&console[console.info?"info":"log"]("You are running Vue in development mode.\nMake sure to turn on production mode when deploying for production.\nSee more tips at https://vuejs.org/guide/deployment.html")},0),n.a=Bn}).call(n,d(3),d(44).setImmediate)},function(t,n,d){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default={name:"AppContent"}},function(t,n,d){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default={name:"AppHeader",data:function(){return{active:""}},watch:{$route:function(){this.highlight()}},created:function(){this.highlight()},methods:{highlight:function(){this.active=0!==this.$route.matched.length?this.$route.matched[0].path:"",console.log(this.active)}}}},function(t,n,d){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var e=d(7),r=d.n(e);n.default={name:"UsageSidebar",components:{markdown:r.a},computed:{},data:function(){return{active:"",basics:[{en:"backend",zh:"后端项目构建"},{en:"frontend",zh:"前端项目构建"}],android:[{en:"be",zh:"后端项目结构"},{en:"fe",zh:"前端项目结构"},{en:"db",zh:"数据库表结构"},{en:"dev",zh:"二次开发"}]}},watch:{$route:function(){this.highlight()}},created:function(){this.highlight()},methods:{highlight:function(){console.log(this.$route.name),"QuickStart"!==this.$route.name?this.active=this.$route.params.article:this.active=this.$route.name},isNative:function(t){return~["QuickStart","Index"].indexOf(t)}}}},function(t,n,d){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var e=d(7),r=d.n(e);n.default={name:"GetStarted",data:function(){return{currDevOS:"macos",currTargetOs:"web",devOSList:[{id:"macos",name:"macOS"},{id:"windows",name:"Windows"},{id:"linux",name:"Linux"}],targetOSList:[{id:"web",name:"Web"},{id:"ios",name:"iOS"},{id:"android",name:"Android"}]}},computed:{article:function(){return"basic-quick-"+this.currDevOS+"-"+this.currTargetOs}},components:{markdown:r.a},methods:{changeDevOS:function(t){this.currDevOS=t},changeTargetOS:function(t){this.currTargetOs=t}}}},function(t,n,d){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(t){var e=d(40),r=d.n(e),i=d(41),a=d.n(i),o=d(36),c=d.n(o),s=d(39),h=d.n(s),l=d(37),u=d.n(l),p=d(38),f=d.n(p),b=d(25),g={backend:r.a,frontend:a.a,be:c.a,fe:h.a,db:u.a,dev:f.a};n.default={name:"Markdown",props:{article:{type:String}},data:function(){return{content:""}},mounted:function(){this.render()},watch:{article:function(){this.render()}},methods:{render:function(){var n=g[this.article];null==n?console.error("Error rendering "+this.article):(this.content=n,this.$nextTick(function(){d.i(b.a)(t)}))}}}}.call(n,d(4))},function(t,n,d){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var e=d(53),r=d.n(e),i=d(52),a=d.n(i);n.default={components:{AppHeader:r.a,AppContent:a.a}}},function(t,n,d){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var e=d(23),r=d.n(e),i=d(72),a=d.n(i);n.default={name:"Index",mounted:function(){r()("particles-js",a.a)}}},function(t,n){function d(t){var n=/^#?([a-f\d])([a-f\d])([a-f\d])$/i;t=t.replace(n,function(t,n,d,e){return n+n+d+d+e+e});var d=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t);return d?{r:parseInt(d[1],16),g:parseInt(d[2],16),b:parseInt(d[3],16)}:null}function e(t,n,d){return Math.min(Math.max(t,n),d)}function r(t,n){return n.indexOf(t)>-1}var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a=function(t,n){var a=document.querySelector("#"+t+" > .particles-js-canvas-el");this.pJS={canvas:{el:a,w:a.offsetWidth,h:a.offsetHeight},particles:{number:{value:400,density:{enable:!0,value_area:800}},color:{value:"#fff"},shape:{type:"circle",stroke:{width:0,color:"#ff0000"},polygon:{nb_sides:5},image:{src:"",width:100,height:100}},opacity:{value:1,random:!1,anim:{enable:!1,speed:2,opacity_min:0,sync:!1}},size:{value:20,random:!1,anim:{enable:!1,speed:20,size_min:0,sync:!1}},line_linked:{enable:!0,distance:100,color:"#fff",opacity:1,width:1},move:{enable:!0,speed:2,direction:"none",random:!1,straight:!1,out_mode:"out",bounce:!1,attract:{enable:!1,rotateX:3e3,rotateY:3e3}},array:[]},interactivity:{detect_on:"canvas",events:{onhover:{enable:!0,mode:"grab"},onclick:{enable:!0,mode:"push"},resize:!0},modes:{grab:{distance:100,line_linked:{opacity:1}},bubble:{distance:200,size:80,duration:.4},repulse:{distance:200,duration:.4},push:{particles_nb:4},remove:{particles_nb:2}},mouse:{}},retina_detect:!1,fn:{interact:{},modes:{},vendors:{}},tmp:{}};var o=this.pJS;n&&Object.deepExtend(o,n),o.tmp.obj={size_value:o.particles.size.value,size_anim_speed:o.particles.size.anim.speed,move_speed:o.particles.move.speed,line_linked_distance:o.particles.line_linked.distance,line_linked_width:o.particles.line_linked.width,mode_grab_distance:o.interactivity.modes.grab.distance,mode_bubble_distance:o.interactivity.modes.bubble.distance,mode_bubble_size:o.interactivity.modes.bubble.size,mode_repulse_distance:o.interactivity.modes.repulse.distance},o.fn.retinaInit=function(){o.retina_detect&&window.devicePixelRatio>1?(o.canvas.pxratio=window.devicePixelRatio,o.tmp.retina=!0):(o.canvas.pxratio=1,o.tmp.retina=!1),o.canvas.w=o.canvas.el.offsetWidth*o.canvas.pxratio,o.canvas.h=o.canvas.el.offsetHeight*o.canvas.pxratio,o.particles.size.value=o.tmp.obj.size_value*o.canvas.pxratio,o.particles.size.anim.speed=o.tmp.obj.size_anim_speed*o.canvas.pxratio,o.particles.move.speed=o.tmp.obj.move_speed*o.canvas.pxratio,o.particles.line_linked.distance=o.tmp.obj.line_linked_distance*o.canvas.pxratio,o.interactivity.modes.grab.distance=o.tmp.obj.mode_grab_distance*o.canvas.pxratio,o.interactivity.modes.bubble.distance=o.tmp.obj.mode_bubble_distance*o.canvas.pxratio,o.particles.line_linked.width=o.tmp.obj.line_linked_width*o.canvas.pxratio,o.interactivity.modes.bubble.size=o.tmp.obj.mode_bubble_size*o.canvas.pxratio,o.interactivity.modes.repulse.distance=o.tmp.obj.mode_repulse_distance*o.canvas.pxratio},o.fn.canvasInit=function(){o.canvas.ctx=o.canvas.el.getContext("2d")},o.fn.canvasSize=function(){o.canvas.el.width=o.canvas.w,o.canvas.el.height=o.canvas.h,o&&o.interactivity.events.resize&&window.addEventListener("resize",function(){o.canvas.w=o.canvas.el.offsetWidth,o.canvas.h=o.canvas.el.offsetHeight,o.tmp.retina&&(o.canvas.w*=o.canvas.pxratio,o.canvas.h*=o.canvas.pxratio),o.canvas.el.width=o.canvas.w,o.canvas.el.height=o.canvas.h,o.particles.move.enable||(o.fn.particlesEmpty(),o.fn.particlesCreate(),o.fn.particlesDraw(),o.fn.vendors.densityAutoParticles()),o.fn.vendors.densityAutoParticles()})},o.fn.canvasPaint=function(){o.canvas.ctx.fillRect(0,0,o.canvas.w,o.canvas.h)},o.fn.canvasClear=function(){o.canvas.ctx.clearRect(0,0,o.canvas.w,o.canvas.h)},o.fn.particle=function(t,n,e){if(this.radius=(o.particles.size.random?Math.random():1)*o.particles.size.value,o.particles.size.anim.enable&&(this.size_status=!1,this.vs=o.particles.size.anim.speed/100,o.particles.size.anim.sync||(this.vs=this.vs*Math.random())),this.x=e?e.x:Math.random()*o.canvas.w,this.y=e?e.y:Math.random()*o.canvas.h,this.x>o.canvas.w-2*this.radius?this.x=this.x-this.radius:this.x<2*this.radius&&(this.x=this.x+this.radius),this.y>o.canvas.h-2*this.radius?this.y=this.y-this.radius:this.y<2*this.radius&&(this.y=this.y+this.radius),o.particles.move.bounce&&o.fn.vendors.checkOverlap(this,e),this.color={},"object"==i(t.value))if(t.value instanceof Array){var r=t.value[Math.floor(Math.random()*o.particles.color.value.length)];this.color.rgb=d(r)}else void 0!=t.value.r&&void 0!=t.value.g&&void 0!=t.value.b&&(this.color.rgb={r:t.value.r,g:t.value.g,b:t.value.b}),void 0!=t.value.h&&void 0!=t.value.s&&void 0!=t.value.l&&(this.color.hsl={h:t.value.h,s:t.value.s,l:t.value.l});else"random"==t.value?this.color.rgb={r:Math.floor(256*Math.random())+0,g:Math.floor(256*Math.random())+0,b:Math.floor(256*Math.random())+0}:"string"==typeof t.value&&(this.color=t,this.color.rgb=d(this.color.value));this.opacity=(o.particles.opacity.random?Math.random():1)*o.particles.opacity.value,o.particles.opacity.anim.enable&&(this.opacity_status=!1,this.vo=o.particles.opacity.anim.speed/100,o.particles.opacity.anim.sync||(this.vo=this.vo*Math.random()));var a={};switch(o.particles.move.direction){case"top":a={x:0,y:-1};break;case"top-right":a={x:.5,y:-.5};break;case"right":a={x:1,y:-0};break;case"bottom-right":a={x:.5,y:.5};break;case"bottom":a={x:0,y:1};break;case"bottom-left":a={x:-.5,y:1};break;case"left":a={x:-1,y:0};break;case"top-left":a={x:-.5,y:-.5};break;default:a={x:0,y:0}}o.particles.move.straight?(this.vx=a.x,this.vy=a.y,o.particles.move.random&&(this.vx=this.vx*Math.random(),this.vy=this.vy*Math.random())):(this.vx=a.x+Math.random()-.5,this.vy=a.y+Math.random()-.5),this.vx_i=this.vx,this.vy_i=this.vy;var c=o.particles.shape.type;if("object"==(void 0===c?"undefined":i(c))){if(c instanceof Array){var s=c[Math.floor(Math.random()*c.length)];this.shape=s}}else this.shape=c;if("image"==this.shape){var h=o.particles.shape;this.img={src:h.image.src,ratio:h.image.width/h.image.height},this.img.ratio||(this.img.ratio=1),"svg"==o.tmp.img_type&&void 0!=o.tmp.source_svg&&(o.fn.vendors.createSvgImg(this),o.tmp.pushing&&(this.img.loaded=!1))}},o.fn.particle.prototype.draw=function(){var t=this;if(void 0!=t.radius_bubble)var n=t.radius_bubble;else var n=t.radius;if(void 0!=t.opacity_bubble)var d=t.opacity_bubble;else var d=t.opacity;if(t.color.rgb)var e="rgba("+t.color.rgb.r+","+t.color.rgb.g+","+t.color.rgb.b+","+d+")";else var e="hsla("+t.color.hsl.h+","+t.color.hsl.s+"%,"+t.color.hsl.l+"%,"+d+")";switch(o.canvas.ctx.fillStyle=e,o.canvas.ctx.beginPath(),t.shape){case"circle":o.canvas.ctx.arc(t.x,t.y,n,0,2*Math.PI,!1);break;case"edge":o.canvas.ctx.rect(t.x-n,t.y-n,2*n,2*n);break;case"triangle":o.fn.vendors.drawShape(o.canvas.ctx,t.x-n,t.y+n/1.66,2*n,3,2);break;case"polygon":o.fn.vendors.drawShape(o.canvas.ctx,t.x-n/(o.particles.shape.polygon.nb_sides/3.5),t.y-n/.76,2.66*n/(o.particles.shape.polygon.nb_sides/3),o.particles.shape.polygon.nb_sides,1);break;case"star":o.fn.vendors.drawShape(o.canvas.ctx,t.x-2*n/(o.particles.shape.polygon.nb_sides/4),t.y-n/1.52,2*n*2.66/(o.particles.shape.polygon.nb_sides/3),o.particles.shape.polygon.nb_sides,2);break;case"image":;if("svg"==o.tmp.img_type)var r=t.img.obj;else var r=o.tmp.img_obj;r&&function(){o.canvas.ctx.drawImage(r,t.x-n,t.y-n,2*n,2*n/t.img.ratio)}()}o.canvas.ctx.closePath(),o.particles.shape.stroke.width>0&&(o.canvas.ctx.strokeStyle=o.particles.shape.stroke.color,o.canvas.ctx.lineWidth=o.particles.shape.stroke.width,o.canvas.ctx.stroke()),o.canvas.ctx.fill()},o.fn.particlesCreate=function(){for(var t=0;t<o.particles.number.value;t++)o.particles.array.push(new o.fn.particle(o.particles.color,o.particles.opacity.value))},o.fn.particlesUpdate=function(){for(var t=0;t<o.particles.array.length;t++){var n=o.particles.array[t];if(o.particles.move.enable){var d=o.particles.move.speed/2;n.x+=n.vx*d,n.y+=n.vy*d}if(o.particles.opacity.anim.enable&&(1==n.opacity_status?(n.opacity>=o.particles.opacity.value&&(n.opacity_status=!1),n.opacity+=n.vo):(n.opacity<=o.particles.opacity.anim.opacity_min&&(n.opacity_status=!0),n.opacity-=n.vo),n.opacity<0&&(n.opacity=0)),o.particles.size.anim.enable&&(1==n.size_status?(n.radius>=o.particles.size.value&&(n.size_status=!1),n.radius+=n.vs):(n.radius<=o.particles.size.anim.size_min&&(n.size_status=!0),n.radius-=n.vs),n.radius<0&&(n.radius=0)),"bounce"==o.particles.move.out_mode)var e={x_left:n.radius,x_right:o.canvas.w,y_top:n.radius,y_bottom:o.canvas.h};else var e={x_left:-n.radius,x_right:o.canvas.w+n.radius,y_top:-n.radius,y_bottom:o.canvas.h+n.radius};switch(n.x-n.radius>o.canvas.w?(n.x=e.x_left,n.y=Math.random()*o.canvas.h):n.x+n.radius<0&&(n.x=e.x_right,n.y=Math.random()*o.canvas.h),n.y-n.radius>o.canvas.h?(n.y=e.y_top,n.x=Math.random()*o.canvas.w):n.y+n.radius<0&&(n.y=e.y_bottom,n.x=Math.random()*o.canvas.w),o.particles.move.out_mode){case"bounce":n.x+n.radius>o.canvas.w?n.vx=-n.vx:n.x-n.radius<0&&(n.vx=-n.vx),n.y+n.radius>o.canvas.h?n.vy=-n.vy:n.y-n.radius<0&&(n.vy=-n.vy)}if(r("grab",o.interactivity.events.onhover.mode)&&o.fn.modes.grabParticle(n),(r("bubble",o.interactivity.events.onhover.mode)||r("bubble",o.interactivity.events.onclick.mode))&&o.fn.modes.bubbleParticle(n),(r("repulse",o.interactivity.events.onhover.mode)||r("repulse",o.interactivity.events.onclick.mode))&&o.fn.modes.repulseParticle(n),o.particles.line_linked.enable||o.particles.move.attract.enable)for(var i=t+1;i<o.particles.array.length;i++){var a=o.particles.array[i];o.particles.line_linked.enable&&o.fn.interact.linkParticles(n,a),o.particles.move.attract.enable&&o.fn.interact.attractParticles(n,a),o.particles.move.bounce&&o.fn.interact.bounceParticles(n,a)}}},o.fn.particlesDraw=function(){o.canvas.ctx.clearRect(0,0,o.canvas.w,o.canvas.h),o.fn.particlesUpdate();for(var t=0;t<o.particles.array.length;t++){o.particles.array[t].draw()}},o.fn.particlesEmpty=function(){o.particles.array=[]},o.fn.particlesRefresh=function(){cancelRequestAnimFrame(o.fn.checkAnimFrame),cancelRequestAnimFrame(o.fn.drawAnimFrame),o.tmp.source_svg=void 0,o.tmp.img_obj=void 0,o.tmp.count_svg=0,o.fn.particlesEmpty(),o.fn.canvasClear(),o.fn.vendors.start()},o.fn.interact.linkParticles=function(t,n){var d=t.x-n.x,e=t.y-n.y,r=Math.sqrt(d*d+e*e);if(r<=o.particles.line_linked.distance){var i=o.particles.line_linked.opacity-r/(1/o.particles.line_linked.opacity)/o.particles.line_linked.distance;if(i>0){var a=o.particles.line_linked.color_rgb_line;o.canvas.ctx.strokeStyle="rgba("+a.r+","+a.g+","+a.b+","+i+")",o.canvas.ctx.lineWidth=o.particles.line_linked.width,o.canvas.ctx.beginPath(),o.canvas.ctx.moveTo(t.x,t.y),o.canvas.ctx.lineTo(n.x,n.y),o.canvas.ctx.stroke(),o.canvas.ctx.closePath()}}},o.fn.interact.attractParticles=function(t,n){var d=t.x-n.x,e=t.y-n.y;if(Math.sqrt(d*d+e*e)<=o.particles.line_linked.distance){var r=d/(1e3*o.particles.move.attract.rotateX),i=e/(1e3*o.particles.move.attract.rotateY);t.vx-=r,t.vy-=i,n.vx+=r,n.vy+=i}},o.fn.interact.bounceParticles=function(t,n){var d=t.x-n.x,e=t.y-n.y;Math.sqrt(d*d+e*e)<=t.radius+n.radius&&(t.vx=-t.vx,t.vy=-t.vy,n.vx=-n.vx,n.vy=-n.vy)},o.fn.modes.pushParticles=function(t,n){o.tmp.pushing=!0;for(var d=0;d<t;d++)o.particles.array.push(new o.fn.particle(o.particles.color,o.particles.opacity.value,{x:n?n.pos_x:Math.random()*o.canvas.w,y:n?n.pos_y:Math.random()*o.canvas.h})),d==t-1&&(o.particles.move.enable||o.fn.particlesDraw(),o.tmp.pushing=!1)},o.fn.modes.removeParticles=function(t){o.particles.array.splice(0,t),o.particles.move.enable||o.fn.particlesDraw()},o.fn.modes.bubbleParticle=function(t){if(o.interactivity.events.onhover.enable&&r("bubble",o.interactivity.events.onhover.mode)){var n=function(){t.opacity_bubble=t.opacity,t.radius_bubble=t.radius},d=t.x-o.interactivity.mouse.pos_x,e=t.y-o.interactivity.mouse.pos_y,i=Math.sqrt(d*d+e*e),a=1-i/o.interactivity.modes.bubble.distance;if(i<=o.interactivity.modes.bubble.distance){if(a>=0&&"mousemove"==o.interactivity.status){if(o.interactivity.modes.bubble.size!=o.particles.size.value)if(o.interactivity.modes.bubble.size>o.particles.size.value){var c=t.radius+o.interactivity.modes.bubble.size*a;c>=0&&(t.radius_bubble=c)}else{var s=t.radius-o.interactivity.modes.bubble.size,c=t.radius-s*a;t.radius_bubble=c>0?c:0}if(o.interactivity.modes.bubble.opacity!=o.particles.opacity.value)if(o.interactivity.modes.bubble.opacity>o.particles.opacity.value){var h=o.interactivity.modes.bubble.opacity*a;h>t.opacity&&h<=o.interactivity.modes.bubble.opacity&&(t.opacity_bubble=h)}else{var h=t.opacity-(o.particles.opacity.value-o.interactivity.modes.bubble.opacity)*a;h<t.opacity&&h>=o.interactivity.modes.bubble.opacity&&(t.opacity_bubble=h)}}}else n();"mouseleave"==o.interactivity.status&&n()}else if(o.interactivity.events.onclick.enable&&r("bubble",o.interactivity.events.onclick.mode)){var l=function(n,d,e,r,a){if(n!=d)if(o.tmp.bubble_duration_end){if(void 0!=e){var c=r-u*(r-n)/o.interactivity.modes.bubble.duration,s=n-c;l=n+s,"size"==a&&(t.radius_bubble=l),"opacity"==a&&(t.opacity_bubble=l)}}else if(i<=o.interactivity.modes.bubble.distance){if(void 0!=e)var h=e;else var h=r;if(h!=n){var l=r-u*(r-n)/o.interactivity.modes.bubble.duration;"size"==a&&(t.radius_bubble=l),"opacity"==a&&(t.opacity_bubble=l)}}else"size"==a&&(t.radius_bubble=void 0),"opacity"==a&&(t.opacity_bubble=void 0)};if(o.tmp.bubble_clicking){var d=t.x-o.interactivity.mouse.click_pos_x,e=t.y-o.interactivity.mouse.click_pos_y,i=Math.sqrt(d*d+e*e),u=((new Date).getTime()-o.interactivity.mouse.click_time)/1e3;u>o.interactivity.modes.bubble.duration&&(o.tmp.bubble_duration_end=!0),u>2*o.interactivity.modes.bubble.duration&&(o.tmp.bubble_clicking=!1,o.tmp.bubble_duration_end=!1)}o.tmp.bubble_clicking&&(l(o.interactivity.modes.bubble.size,o.particles.size.value,t.radius_bubble,t.radius,"size"),l(o.interactivity.modes.bubble.opacity,o.particles.opacity.value,t.opacity_bubble,t.opacity,"opacity"))}},o.fn.modes.repulseParticle=function(t){if(o.interactivity.events.onhover.enable&&r("repulse",o.interactivity.events.onhover.mode)&&"mousemove"==o.interactivity.status){var n=t.x-o.interactivity.mouse.pos_x,d=t.y-o.interactivity.mouse.pos_y,i=Math.sqrt(n*n+d*d),a={x:n/i,y:d/i},c=o.interactivity.modes.repulse.distance,s=e(1/c*(-1*Math.pow(i/c,2)+1)*c*100,0,50),h={x:t.x+a.x*s,y:t.y+a.y*s};"bounce"==o.particles.move.out_mode?(h.x-t.radius>0&&h.x+t.radius<o.canvas.w&&(t.x=h.x),h.y-t.radius>0&&h.y+t.radius<o.canvas.h&&(t.y=h.y)):(t.x=h.x,t.y=h.y)}else if(o.interactivity.events.onclick.enable&&r("repulse",o.interactivity.events.onclick.mode))if(o.tmp.repulse_finish||++o.tmp.repulse_count==o.particles.array.length&&(o.tmp.repulse_finish=!0),o.tmp.repulse_clicking){var c=Math.pow(o.interactivity.modes.repulse.distance/6,3),l=o.interactivity.mouse.click_pos_x-t.x,u=o.interactivity.mouse.click_pos_y-t.y,p=l*l+u*u,f=-c/p*1;p<=c&&function(){var n=Math.atan2(u,l);if(t.vx=f*Math.cos(n),t.vy=f*Math.sin(n),"bounce"==o.particles.move.out_mode){var d={x:t.x+t.vx,y:t.y+t.vy};d.x+t.radius>o.canvas.w?t.vx=-t.vx:d.x-t.radius<0&&(t.vx=-t.vx),d.y+t.radius>o.canvas.h?t.vy=-t.vy:d.y-t.radius<0&&(t.vy=-t.vy)}}()}else 0==o.tmp.repulse_clicking&&(t.vx=t.vx_i,t.vy=t.vy_i)},o.fn.modes.grabParticle=function(t){if(o.interactivity.events.onhover.enable&&"mousemove"==o.interactivity.status){var n=t.x-o.interactivity.mouse.pos_x,d=t.y-o.interactivity.mouse.pos_y,e=Math.sqrt(n*n+d*d);if(e<=o.interactivity.modes.grab.distance){var r=o.interactivity.modes.grab.line_linked.opacity-e/(1/o.interactivity.modes.grab.line_linked.opacity)/o.interactivity.modes.grab.distance;if(r>0){var i=o.particles.line_linked.color_rgb_line;o.canvas.ctx.strokeStyle="rgba("+i.r+","+i.g+","+i.b+","+r+")",o.canvas.ctx.lineWidth=o.particles.line_linked.width,o.canvas.ctx.beginPath(),o.canvas.ctx.moveTo(t.x,t.y),o.canvas.ctx.lineTo(o.interactivity.mouse.pos_x,o.interactivity.mouse.pos_y),o.canvas.ctx.stroke(),o.canvas.ctx.closePath()}}}},o.fn.vendors.eventsListeners=function(){"window"==o.interactivity.detect_on?o.interactivity.el=window:o.interactivity.el=o.canvas.el,(o.interactivity.events.onhover.enable||o.interactivity.events.onclick.enable)&&(o.interactivity.el.addEventListener("mousemove",function(t){if(o.interactivity.el==window)var n=t.clientX,d=t.clientY;else var n=t.offsetX||t.clientX,d=t.offsetY||t.clientY;o.interactivity.mouse.pos_x=n,o.interactivity.mouse.pos_y=d,o.tmp.retina&&(o.interactivity.mouse.pos_x*=o.canvas.pxratio,o.interactivity.mouse.pos_y*=o.canvas.pxratio),o.interactivity.status="mousemove"}),o.interactivity.el.addEventListener("mouseleave",function(t){o.interactivity.mouse.pos_x=null,o.interactivity.mouse.pos_y=null,o.interactivity.status="mouseleave"})),o.interactivity.events.onclick.enable&&o.interactivity.el.addEventListener("click",function(){if(o.interactivity.mouse.click_pos_x=o.interactivity.mouse.pos_x,o.interactivity.mouse.click_pos_y=o.interactivity.mouse.pos_y,o.interactivity.mouse.click_time=(new Date).getTime(),o.interactivity.events.onclick.enable)switch(o.interactivity.events.onclick.mode){case"push":o.particles.move.enable?o.fn.modes.pushParticles(o.interactivity.modes.push.particles_nb,o.interactivity.mouse):1==o.interactivity.modes.push.particles_nb?o.fn.modes.pushParticles(o.interactivity.modes.push.particles_nb,o.interactivity.mouse):o.interactivity.modes.push.particles_nb>1&&o.fn.modes.pushParticles(o.interactivity.modes.push.particles_nb);break;case"remove":o.fn.modes.removeParticles(o.interactivity.modes.remove.particles_nb);break;case"bubble":o.tmp.bubble_clicking=!0;break;case"repulse":o.tmp.repulse_clicking=!0,o.tmp.repulse_count=0,o.tmp.repulse_finish=!1,setTimeout(function(){o.tmp.repulse_clicking=!1},1e3*o.interactivity.modes.repulse.duration)}})},o.fn.vendors.densityAutoParticles=function(){if(o.particles.number.density.enable){var t=o.canvas.el.width*o.canvas.el.height/1e3;o.tmp.retina&&(t/=2*o.canvas.pxratio);var n=t*o.particles.number.value/o.particles.number.density.value_area,d=o.particles.array.length-n;d<0?o.fn.modes.pushParticles(Math.abs(d)):o.fn.modes.removeParticles(d)}},o.fn.vendors.checkOverlap=function(t,n){for(var d=0;d<o.particles.array.length;d++){var e=o.particles.array[d],r=t.x-e.x,i=t.y-e.y;Math.sqrt(r*r+i*i)<=t.radius+e.radius&&(t.x=n?n.x:Math.random()*o.canvas.w,t.y=n?n.y:Math.random()*o.canvas.h,o.fn.vendors.checkOverlap(t))}},o.fn.vendors.createSvgImg=function(t){var n=o.tmp.source_svg,d=/#([0-9A-F]{3,6})/gi,e=n.replace(d,function(n,d,e,r){if(t.color.rgb)var i="rgba("+t.color.rgb.r+","+t.color.rgb.g+","+t.color.rgb.b+","+t.opacity+")";else var i="hsla("+t.color.hsl.h+","+t.color.hsl.s+"%,"+t.color.hsl.l+"%,"+t.opacity+")";return i}),r=new Blob([e],{type:"image/svg+xml;charset=utf-8"}),i=window.URL||window.webkitURL||window,a=i.createObjectURL(r),c=new Image;c.addEventListener("load",function(){t.img.obj=c,t.img.loaded=!0,i.revokeObjectURL(a),o.tmp.count_svg++}),c.src=a},o.fn.vendors.destroypJS=function(){cancelAnimationFrame(o.fn.drawAnimFrame),a.remove(),pJSDom=null},o.fn.vendors.drawShape=function(t,n,d,e,r,i){var a=r*i,o=r/i,c=180*(o-2)/o,s=Math.PI-Math.PI*c/180;t.save(),t.beginPath(),t.translate(n,d),t.moveTo(0,0);for(var h=0;h<a;h++)t.lineTo(e,0),t.translate(e,0),t.rotate(s);t.fill(),t.restore()},o.fn.vendors.exportImg=function(){window.open(o.canvas.el.toDataURL("image/png"),"_blank")},o.fn.vendors.loadImg=function(t){if(o.tmp.img_error=void 0,""!=o.particles.shape.image.src)if("svg"==t){var n=new XMLHttpRequest;n.open("GET",o.particles.shape.image.src),n.onreadystatechange=function(t){4==n.readyState&&(200==n.status?(o.tmp.source_svg=t.currentTarget.response,o.fn.vendors.checkBeforeDraw()):(console.log("Error pJS - Image not found"),o.tmp.img_error=!0))},n.send()}else{var d=new Image;d.addEventListener("load",function(){o.tmp.img_obj=d,o.fn.vendors.checkBeforeDraw()}),d.src=o.particles.shape.image.src}else console.log("Error pJS - No image.src"),o.tmp.img_error=!0},o.fn.vendors.draw=function(){"image"==o.particles.shape.type?"svg"==o.tmp.img_type?o.tmp.count_svg>=o.particles.number.value?(o.fn.particlesDraw(),o.particles.move.enable?o.fn.drawAnimFrame=requestAnimFrame(o.fn.vendors.draw):cancelRequestAnimFrame(o.fn.drawAnimFrame)):o.tmp.img_error||(o.fn.drawAnimFrame=requestAnimFrame(o.fn.vendors.draw)):void 0!=o.tmp.img_obj?(o.fn.particlesDraw(),o.particles.move.enable?o.fn.drawAnimFrame=requestAnimFrame(o.fn.vendors.draw):cancelRequestAnimFrame(o.fn.drawAnimFrame)):o.tmp.img_error||(o.fn.drawAnimFrame=requestAnimFrame(o.fn.vendors.draw)):(o.fn.particlesDraw(),o.particles.move.enable?o.fn.drawAnimFrame=requestAnimFrame(o.fn.vendors.draw):cancelRequestAnimFrame(o.fn.drawAnimFrame))},o.fn.vendors.checkBeforeDraw=function(){"image"==o.particles.shape.type?"svg"==o.tmp.img_type&&void 0==o.tmp.source_svg?o.tmp.checkAnimFrame=requestAnimFrame(check):(cancelRequestAnimFrame(o.tmp.checkAnimFrame),o.tmp.img_error||(o.fn.vendors.init(),o.fn.vendors.draw())):(o.fn.vendors.init(),o.fn.vendors.draw())},o.fn.vendors.init=function(){o.fn.retinaInit(),o.fn.canvasInit(),o.fn.canvasSize(),o.fn.canvasPaint(),o.fn.particlesCreate(),o.fn.vendors.densityAutoParticles(),o.particles.line_linked.color_rgb_line=d(o.particles.line_linked.color)},o.fn.vendors.start=function(){r("image",o.particles.shape.type)?(o.tmp.img_type=o.particles.shape.image.src.substr(o.particles.shape.image.src.length-3),o.fn.vendors.loadImg(o.tmp.img_type)):o.fn.vendors.checkBeforeDraw()},o.fn.vendors.eventsListeners(),o.fn.vendors.start()};Object.deepExtend=function(t,n){for(var d in n)n[d]&&n[d].constructor&&n[d].constructor===Object?(t[d]=t[d]||{},arguments.callee(t[d],n[d])):t[d]=n[d];return t},window.requestAnimFrame=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}(),window.cancelRequestAnimFrame=function(){return window.cancelAnimationFrame||window.webkitCancelRequestAnimationFrame||window.mozCancelRequestAnimationFrame||window.oCancelRequestAnimationFrame||window.msCancelRequestAnimationFrame||clearTimeout}(),window.pJSDom=[],window.particlesJS=function(t,n){"string"!=typeof t&&(n=t,t="particles-js"),t||(t="particles-js");var d=document.getElementById(t),e=d.getElementsByClassName("particles-js-canvas-el");if(e.length)for(;e.length>0;)d.removeChild(e[0]);var r=document.createElement("canvas");r.className="particles-js-canvas-el",r.style.width="100%",r.style.height="100%",null!=document.getElementById(t).appendChild(r)&&pJSDom.push(new a(t,n))},window.particlesJS.load=function(t,n,d){var e=new XMLHttpRequest;e.open("GET",n),e.onreadystatechange=function(n){if(4==e.readyState)if(200==e.status){var r=JSON.parse(n.currentTarget.response);window.particlesJS(t,r),d&&d()}else console.log("Error pJS - XMLHttpRequest status: "+e.status),console.log("Error pJS - File config not found")},e.send()},t.exports=window.particlesJS},function(t,n,d){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var e=d(9),r=(d.n(e),d(10)),i=(d.n(r),d(15)),a=d(14),o=d(8),c=d(13),s=d.n(c),h=d(12),l=(d.n(h),d(11));d.n(l);i.a.use(a.a);var u=new a.a(o.a);new i.a({el:"#app",router:u,render:function(t){return t(s.a)}})},function(t,n,d){"use strict";function e(t){document.querySelectorAll("pre code").forEach(function(n){t.highlightBlock(n)})}n.a=e},function(t,n,d){n=t.exports=d(0)(!1),n.push([t.i,"/*\n\nOriginal highlight.js style (c) Ivan Sagalaev <<EMAIL>>\n\n*/\n.hljs {\n  display: block;\n  overflow-x: auto;\n  padding: 0.5em;\n  background: #F0F0F0; }\n\n/* Base color: saturation 0; */\n.hljs,\n.hljs-subst {\n  color: #444; }\n\n.hljs-comment {\n  color: #888888; }\n\n.hljs-keyword,\n.hljs-attribute,\n.hljs-selector-tag,\n.hljs-meta-keyword,\n.hljs-doctag,\n.hljs-name {\n  font-weight: bold; }\n\n/* User color: hue: 0 */\n.hljs-type,\n.hljs-string,\n.hljs-number,\n.hljs-selector-id,\n.hljs-selector-class,\n.hljs-quote,\n.hljs-template-tag,\n.hljs-deletion {\n  color: #880000; }\n\n.hljs-title,\n.hljs-section {\n  color: #880000;\n  font-weight: bold; }\n\n.hljs-regexp,\n.hljs-symbol,\n.hljs-variable,\n.hljs-template-variable,\n.hljs-link,\n.hljs-selector-attr,\n.hljs-selector-pseudo {\n  color: #BC6060; }\n\n/* Language color: hue: 90; */\n.hljs-literal {\n  color: #78A960; }\n\n.hljs-built_in,\n.hljs-bullet,\n.hljs-code,\n.hljs-addition {\n  color: #397300; }\n\n/* Meta color: hue: 200 */\n.hljs-meta {\n  color: #1f7199; }\n\n.hljs-meta-string {\n  color: #4d99bf; }\n\n/* Misc effects */\n.hljs-emphasis {\n  font-style: italic; }\n\n.hljs-strong {\n  font-weight: bold; }\n",""])},function(t,n,d){n=t.exports=d(0)(!1),n.push([t.i,"html {\n  font-size: 62.5%;\n  height: 100%; }\n\nbody {\n  margin: 0;\n  padding: 0;\n  color: #222222;\n  margin: 0 auto;\n  background: #F7F7F7;\n  height: 100%;\n  position: relative; }\n\nbody, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, code, form, fieldset, legend, input, textarea, p, blockquote, th, td {\n  margin: 0;\n  padding: 0; }\n\ntable {\n  border-collapse: collapse;\n  border-spacing: 0; }\n\nfieldset, img {\n  border: 0; }\n\naddress, caption, cite, code, dfn, em, th, var, i {\n  font-style: normal;\n  font-weight: normal; }\n\nol, ul {\n  list-style: none; }\n\ncaption, th {\n  text-align: left; }\n\nh1, h2, h3, h4, h5, h6 {\n  font-size: 100%;\n  font-weight: normal; }\n\nq:before, q:after {\n  content: ''; }\n\na:focus {\n  outline-style: none; }\n\nabbr, acronym {\n  border: 0;\n  font-variant: normal; }\n\nsup {\n  vertical-align: text-top; }\n\nsub {\n  vertical-align: text-bottom; }\n\ninput, select {\n  font-family: inherit;\n  font-size: inherit;\n  font-weight: inherit;\n  vertical-align: middle; }\n\ntextarea {\n  resize: none;\n  font-family: inherit;\n  font-size: inherit;\n  font-weight: inherit;\n  outline-style: none; }\n\nhtml {\n  font-size: 100%;\n  -webkit-tap-highlight-color: transparent; }\n\nbody {\n  overflow-x: hidden;\n  color: #404040; }\n\na {\n  text-decoration: none;\n  color: #000; }\n\n.clearfix:after {\n  content: \".\";\n  display: block;\n  height: 0;\n  clear: both;\n  visibility: hidden; }\n",""])},function(t,n,d){n=t.exports=d(0)(!1),n.push([t.i,"body{color:#4c4c4c;font-family:Helvetica,sans-serif}body footer{margin:30px auto;text-align:center;font-size:12px;color:#404040}body pre{margin-top:10px}body code{display:inline-block;margin:1px 0;max-width:100%;line-height:24px;background-color:#f5f5f5;padding:0 6px;color:#54b1fb}body .app-content-container>div:not(.index-container){display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;margin:40px auto 0;width:82.5%;min-width:1200px;min-height:1255px;max-width:1600px;overflow:hidden}body .app-content-container>div:not(.index-container).resources-container{min-height:0}",""])},function(t,n,d){n=t.exports=d(0)(!1),n.push([t.i,".sidebar{-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;padding-top:23px;margin-right:20px;font-size:14px;background-color:#fff;width:180px}.sidebar .sidebar-item-group{line-height:36px;color:#4c4c4c}.sidebar .sidebar-item-group .sidebar-item-group-title{padding-left:30px}.sidebar .sidebar-item-group .sidebar-item-group-title.bold{font-weight:700}.sidebar .sidebar-item-group .sidebar-item{padding-left:40px}.sidebar .sidebar-item-group .active{background:#39f}.sidebar .sidebar-item-group .active a{color:#fff}.sidebar .sidebar-item-group .router-link-active{color:#57b2f8}.sidebar .sidebar-item-group a{color:#737373;display:block;width:100%;height:100%}aside+content{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;width:0;background-color:#fff}aside+content table{margin-top:10px;margin-bottom:20px;width:100%;font-size:12px;line-height:16px;border-collapse:collapse;border-spacing:0;color:#656b78}aside+content table td,aside+content table th{border:1px solid #e8e8e8;padding:10px 12px}aside+content .hljs,aside+content table th{background-color:#f5f5f5}aside+content .hljs-deletion,aside+content .hljs-number,aside+content .hljs-quote,aside+content .hljs-selector-class,aside+content .hljs-selector-id,aside+content .hljs-string,aside+content .hljs-template-tag,aside+content .hljs-type{color:#ef6925}aside+content .hljs-attribute,aside+content .hljs-doctag,aside+content .hljs-keyword,aside+content .hljs-meta-keyword,aside+content .hljs-name,aside+content .hljs-selector-tag{color:#aa2e75}aside+content>div{padding:40px;font-size:14px;line-height:22px;color:#404040}aside+content>div.resources-wrap{padding-bottom:100px}aside+content>div .title{font-weight:700;color:#222}aside+content>div>.title{font-size:20px;line-height:30px}aside+content>div section{margin-top:50px}aside+content>div section>.title{font-size:16px;line-height:24px;color:#222}aside+content>div section .desc{margin-top:10px;color:#737373}aside+content>div section .desc+.desc{margin-top:0}",""])},function(t,n,d){n=t.exports=d(0)(!1),n.push([t.i,".markdown a{color:#e81000;font-weight:600;margin:0 2px}.markdown h1{font-size:20px;line-height:30px}.markdown h1,.markdown h2{font-weight:700;color:#222}.markdown h2{margin-top:50px;font-size:16px;line-height:24px}.markdown h3{margin-top:20px;font-size:14px;line-height:14px;color:#222;font-weight:700}.markdown p{margin-top:10px;color:#737373}.markdown p+p{margin-top:5px}.markdown ol,.markdown ul{margin-top:10px}.markdown li{list-style-type:circle}.markdown li a{color:#737373}.markdown img{max-width:100%}.markdown blockquote{border-left:5px solid #80b0f8;padding-left:5px;background-color:#f5f5f5}.markdown pre{margin-top:10px;border-left:5px solid #eadbd9;background-color:#f5f5f5;overflow-x:auto}",""])},function(t,n,d){n=t.exports=d(0)(!1),n.push([t.i,".title{font-weight:700}.os-list{display:inline-block}.os-list .active{background-color:#3898fc;color:#fff}.os-list li{display:inline-block}.os-list li button{margin:3px;padding:6px;background-color:#fff;border:1px solid #3898fc;-webkit-border-radius:5px;border-radius:5px;cursor:pointer;color:#3898fc;font-size:18px;font-family:Helvetica Neue,Helvetica,Arial,sans-serif}.os-list li button:focus{outline:0}.intro-container section ol{margin:10px 20px;list-style-type:decimal}.intro-container section ol a{color:#175199;text-decoration:underline}",""])},function(t,n,d){n=t.exports=d(0)(!1),n.push([t.i,".usagesidebar-container .phone{-webkit-box-flex:0;-webkit-flex:none;-ms-flex:none;flex:none;-webkit-align-self:stretch;-ms-flex-item-align:stretch;-ms-grid-row-align:stretch;align-self:stretch;position:relative;padding-top:40px;padding-right:40px;background-color:#fff;width:370px;-webkit-background-size:370px 370px;background-size:370px;background-position:0 40px;background-repeat:no-repeat}.usagesidebar-container .phone iframe{position:absolute;border:none;top:132px;left:24px;width:322px;height:571px}",""])},function(t,n,d){n=t.exports=d(0)(!1),n.push([t.i,'.appheader-container{height:80px;background-color:#222}.appheader-container .wrapper{margin:0 auto;width:82.5%;max-width:1600px;height:100%}.appheader-container .wrapper,.appheader-container .wrapper .logo-wrap{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.appheader-container .wrapper .logo-wrap .logo{height:18px}.appheader-container .wrapper .logo-wrap span{margin-left:10px;font-size:12px;color:#737373}.appheader-container .wrapper .nav{margin-left:60px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;height:80px;font-size:14px;color:#fff}.appheader-container .wrapper .nav li{width:72px;height:80px;line-height:80px}.appheader-container .wrapper .nav li a{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;color:#fff}.appheader-container .wrapper .nav li .star-badge{position:relative;left:20px}.appheader-container .wrapper .nav li.active{position:relative}.appheader-container .wrapper .nav li.active:after{content:"";position:absolute;left:0;bottom:0;width:100%;height:6px;background-color:#39f}.appheader-container .wrapper .nav li.active a{color:#fff}@media (max-width:600px){.appheader-container .wrapper{min-width:0;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.appheader-container .wrapper ul.nav{margin-left:25%}.appheader-container .wrapper ul.nav li:first-child,.appheader-container .wrapper ul.nav li:nth-child(2),.appheader-container .wrapper ul.nav li:nth-child(3),.appheader-container .wrapper ul.nav li:nth-child(4){display:none}.appheader-container .wrapper ul.nav li .star-badge{left:0}}',""])},function(t,n,d){n=t.exports=d(0)(!1),n.push([t.i,".index-container{margin:0!important;width:100%!important}.index-container section{width:100%}.index-container section content{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;margin:0 auto;width:100%}.index-container section content .img-div{margin-left:20px;padding:10px;-webkit-border-radius:5px;border-radius:5px}.index-container section content .img-div img{width:274px;margin:auto;display:block}.index-container section content.particles{position:relative;height:100%}.index-container section content.particles .mask{position:absolute;width:100%;height:100%;top:0;left:0;background-color:#008f4c}.index-container section.banner{height:460px;background-color:#008f4c}.index-container section.banner content{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column}.index-container section.banner content .title{margin-top:132px;font-size:48px;color:#fff;letter-spacing:0;line-height:67px;z-index:1;text-align:center}.index-container section.banner content .subtitle{font-size:20px;color:#fff;letter-spacing:0;line-height:28px;z-index:1;text-align:center}.index-container section.banner content .button-group{margin-top:66px;z-index:1}.index-container section.banner content .button-group a{display:inline-block;margin-right:10px;width:180px;height:60px;text-align:center;line-height:60px;color:#fff;border:1px solid #39f;-webkit-border-radius:4px;border-radius:4px;font-size:20px}.index-container section.banner content .button-group a.button-start{background-color:#39f}.index-container section.banner content .button-group .button-demo{display:none}.index-container section.feature{height:413px;background-color:#fff}.index-container section.feature content{height:100%;margin:0 100px;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.index-container section.feature content .feature-article{width:530px}.index-container section.feature content .feature-article .title{font-size:20px;font-weight:bolder;line-height:24px;color:#008f4c}.index-container section.feature content .feature-article .subtitle{margin-top:10px;font-size:16px;color:#737373;line-height:24px}@media (max-width:1400px){.index-container section content{min-width:0;width:auto}}@media (max-width:600px){.index-container section content{min-width:0}.index-container section.banner .particles{width:100%;margin:0}.index-container section.banner .particles .title{margin:80px 20px 0;font-size:40px;line-height:50px;text-align:center}.index-container section.banner .particles .subtitle{margin:30px 20px 0;text-align:center;font-size:18px;line-height:24px}.index-container section.banner .particles .button-group{margin-top:55px}.index-container section.banner .particles .button-group .button-doc,.index-container section.banner .particles .button-group .button-start{display:none}.index-container section.banner .particles .button-group .button-demo{display:inline-block}.index-container section.feature{height:auto}.index-container section.feature:last-child content{border-bottom:none}.index-container section.feature content{margin:0 8%;width:auto;height:auto;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;border-bottom:1px solid #dcdcdc}.index-container section.feature content .feature-article{width:100%}.index-container section.feature content .feature-article .title{text-align:center;font-size:24px}.index-container section.feature content .feature-article .subtitle{margin-top:3vw;margin-bottom:10.6vw;font-size:18px;line-height:32px;color:#737373}.index-container section.feature content img{margin-top:12vw;margin-bottom:10vw;height:30vw}.index-container section.feature:nth-child(2n) content img{margin-left:0;-webkit-box-ordinal-group:0;-webkit-order:-1;-ms-flex-order:-1;order:-1}.index-container section.feature:nth-child(odd) content img{margin-right:0}}",""])},function(t,n,d){(function(n,d){!function(n,d){t.exports=d()}(0,function(){"use strict";function t(t){var n=typeof t;return null!==t&&("object"===n||"function"===n)}function e(t){return"function"==typeof t}function r(t){B=t}function i(t){z=t}function a(){return void 0!==K?function(){K(c)}:o()}function o(){var t=setTimeout;return function(){return t(c,1)}}function c(){for(var t=0;t<P;t+=2){(0,W[t])(W[t+1]),W[t]=void 0,W[t+1]=void 0}P=0}function s(t,n){var d=this,e=new this.constructor(l);void 0===e[X]&&T(e);var r=d._state;if(r){var i=arguments[r-1];z(function(){return L(r,e,i,d._result)})}else k(d,e,t,n);return e}function h(t){var n=this;if(t&&"object"==typeof t&&t.constructor===n)return t;var d=new n(l);return y(d,t),d}function l(){}function u(){return new TypeError("You cannot resolve a promise with itself")}function p(){return new TypeError("A promises callback cannot return that same promise.")}function f(t){try{return t.then}catch(t){return tt.error=t,tt}}function b(t,n,d,e){try{t.call(n,d,e)}catch(t){return t}}function g(t,n,d){z(function(t){var e=!1,r=b(d,n,function(d){e||(e=!0,n!==d?y(t,d):w(t,d))},function(n){e||(e=!0,x(t,n))},"Settle: "+(t._label||" unknown promise"));!e&&r&&(e=!0,x(t,r))},t)}function m(t,n){n._state===Z?w(t,n._result):n._state===Y?x(t,n._result):k(n,void 0,function(n){return y(t,n)},function(n){return x(t,n)})}function v(t,n,d){n.constructor===t.constructor&&d===s&&n.constructor.resolve===h?m(t,n):d===tt?(x(t,tt.error),tt.error=null):void 0===d?w(t,n):e(d)?g(t,n,d):w(t,n)}function y(n,d){n===d?x(n,u()):t(d)?v(n,d,f(d)):w(n,d)}function _(t){t._onerror&&t._onerror(t._result),A(t)}function w(t,n){t._state===G&&(t._result=n,t._state=Z,0!==t._subscribers.length&&z(A,t))}function x(t,n){t._state===G&&(t._state=Y,t._result=n,z(_,t))}function k(t,n,d,e){var r=t._subscribers,i=r.length;t._onerror=null,r[i]=n,r[i+Z]=d,r[i+Y]=e,0===i&&t._state&&z(A,t)}function A(t){var n=t._subscribers,d=t._state;if(0!==n.length){for(var e=void 0,r=void 0,i=t._result,a=0;a<n.length;a+=3)e=n[a],r=n[a+d],e?L(d,e,r,i):r(i);t._subscribers.length=0}}function N(t,n){try{return t(n)}catch(t){return tt.error=t,tt}}function L(t,n,d,r){var i=e(d),a=void 0,o=void 0,c=void 0,s=void 0;if(i){if(a=N(d,r),a===tt?(s=!0,o=a.error,a.error=null):c=!0,n===a)return void x(n,p())}else a=r,c=!0;n._state!==G||(i&&c?y(n,a):s?x(n,o):t===Z?w(n,a):t===Y&&x(n,a))}function U(t,n){try{n(function(n){y(t,n)},function(n){x(t,n)})}catch(n){x(t,n)}}function O(){return nt++}function T(t){t[X]=nt++,t._state=void 0,t._result=void 0,t._subscribers=[]}function C(){return new Error("Array Methods must be provided an Array")}function R(t){return new dt(this,t).promise}function j(t){var n=this;return new n(M(t)?function(d,e){for(var r=t.length,i=0;i<r;i++)n.resolve(t[i]).then(d,e)}:function(t,n){return n(new TypeError("You must pass an array to race."))})}function F(t){var n=this,d=new n(l);return x(d,t),d}function S(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}function E(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}function $(){var t=void 0;if(void 0!==d)t=d;else if("undefined"!=typeof self)t=self;else try{t=Function("return this")()}catch(t){throw new Error("polyfill failed because global object is unavailable in this environment")}var n=t.Promise;if(n){var e=null;try{e=Object.prototype.toString.call(n.resolve())}catch(t){}if("[object Promise]"===e&&!n.cast)return}t.Promise=et}var I=void 0;I=Array.isArray?Array.isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t)};var M=I,P=0,K=void 0,B=void 0,z=function(t,n){W[P]=t,W[P+1]=n,2===(P+=2)&&(B?B(c):J())},D="undefined"!=typeof window?window:void 0,q=D||{},Q=q.MutationObserver||q.WebKitMutationObserver,H="undefined"==typeof self&&void 0!==n&&"[object process]"==={}.toString.call(n),V="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel,W=new Array(1e3),J=void 0;J=H?function(){return function(){return n.nextTick(c)}}():Q?function(){var t=0,n=new Q(c),d=document.createTextNode("");return n.observe(d,{characterData:!0}),function(){d.data=t=++t%2}}():V?function(){var t=new MessageChannel;return t.port1.onmessage=c,function(){return t.port2.postMessage(0)}}():void 0===D?function(){try{var t=Function("return this")().require("vertx");return K=t.runOnLoop||t.runOnContext,a()}catch(t){return o()}}():o();var X=Math.random().toString(36).substring(2),G=void 0,Z=1,Y=2,tt={error:null},nt=0,dt=function(){function t(t,n){this._instanceConstructor=t,this.promise=new t(l),this.promise[X]||T(this.promise),M(n)?(this.length=n.length,this._remaining=n.length,this._result=new Array(this.length),0===this.length?w(this.promise,this._result):(this.length=this.length||0,this._enumerate(n),0===this._remaining&&w(this.promise,this._result))):x(this.promise,C())}return t.prototype._enumerate=function(t){for(var n=0;this._state===G&&n<t.length;n++)this._eachEntry(t[n],n)},t.prototype._eachEntry=function(t,n){var d=this._instanceConstructor,e=d.resolve;if(e===h){var r=f(t);if(r===s&&t._state!==G)this._settledAt(t._state,n,t._result);else if("function"!=typeof r)this._remaining--,this._result[n]=t;else if(d===et){var i=new d(l);v(i,t,r),this._willSettleAt(i,n)}else this._willSettleAt(new d(function(n){return n(t)}),n)}else this._willSettleAt(e(t),n)},t.prototype._settledAt=function(t,n,d){var e=this.promise;e._state===G&&(this._remaining--,t===Y?x(e,d):this._result[n]=d),0===this._remaining&&w(e,this._result)},t.prototype._willSettleAt=function(t,n){var d=this;k(t,void 0,function(t){return d._settledAt(Z,n,t)},function(t){return d._settledAt(Y,n,t)})},t}(),et=function(){function t(n){this[X]=O(),this._result=this._state=void 0,this._subscribers=[],l!==n&&("function"!=typeof n&&S(),this instanceof t?U(this,n):E())}return t.prototype.catch=function(t){return this.then(null,t)},t.prototype.finally=function(t){var n=this,d=n.constructor;return n.then(function(n){return d.resolve(t()).then(function(){return n})},function(n){return d.resolve(t()).then(function(){throw n})})},t}();return et.prototype.then=s,et.all=R,et.race=j,et.resolve=h,et.reject=F,et._setScheduler=r,et._setAsap=i,et._asap=z,et.polyfill=$,et.Promise=et,et})}).call(n,d(5),d(3))},function(t,n){t.exports='<h1 id="-">后端项目结构</h1>\n<p>parkingos_cloud_vue_server 项目\n新的parkingos后端使用成熟的springmvc+myibatis来实现\n项目的源码结构和说明大概如下，并不是特别复杂</p>\n<pre><code>parkingos.com.bolink  \n│\n└───actions(spring controller类，请求的入口)\n│   \n│   \n└───dao(dao层，写后台服务的都懂)\n│   \n│   \n└───enums（枚举类）\n│   \n│   \n└───filter（过滤器，跨域头添加在这里实现）\n│   \n│   \n└───models(模型包，数据类)\n│   \n│   \n└───qo(分页，高级查询等数据库查询辅助类)\n│   \n│   \n└───service(service层，写后台服务的都懂)\n│   \n│   \n└───utils(工具类)\n    │   \n    │   \n    └───payutils（支付相关工具类）\n\n</code></pre><p>基本上熟悉spring+myibatis的技术人员都可以看懂。</p>\n<p>另外泊链在myibatis基础上封装链一个CommonDao来减少\n一些xml配置的编写，二次开发的同学也可以继续使用xml写sql的方式来\n实现对数据库的查询。</p>\n'},function(t,n){t.exports='<h1 id="-">表结构说明</h1>\n<h2 id="advert_tb">advert_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>aname</td>\n<td>名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>aurl</td>\n<td>链接地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td>添加时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>open</td>\n<td>打中次数</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>hits</td>\n<td>打开链接次数</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>appurl</td>\n<td>应用下载地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>tname</td>\n<td>礼券名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="alipay_log">alipay_log</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>notify_no</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>money</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>wxp_orderid</td>\n<td>微信公众号订单号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orderid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="area_ibeacon_tb">area_ibeacon_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>ibcid</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>reg_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pass</td>\n<td>通道号</td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>major</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>minor</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>lng</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>lat</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="auth_role_tb">auth_role_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>auth_id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>role_id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>sub_auth</td>\n<td>子权限</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>creator_id</td>\n<td>创建者编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="auth_tb">auth_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>nname</td>\n<td>节点名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isparent</td>\n<td>是否是父节点</td>\n<td>boolean</td>\n<td>false</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>nid</td>\n<td>子节点ID</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pid</td>\n<td>父节点ID</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>sub_auth</td>\n<td>子权限</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>url</td>\n<td>请求链接</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>oid</td>\n<td>所属组织类型</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>sort</td>\n<td>排序</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>actions</td>\n<td>子权限的 action</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>创建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>delete_time</td>\n<td>删除时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td>最后一次更新时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>creator_id</td>\n<td>创建者编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>deletor_id</td>\n<td>删除者编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>show</td>\n<td>是否在菜单列表显示 0：显示 1：不显示</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="berth_order_tb">berth_order_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>berth_id</td>\n<td>地磁名称（dici_tb的did）</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>in_time</td>\n<td>来车时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>out_time</td>\n<td>走车时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>total</td>\n<td>金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0:未出场 1：已出场</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orderid</td>\n<td>订单表order_tb主键</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>in_uid</td>\n<td>进场收费员</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>out_uid</td>\n<td>出场收费员</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_total</td>\n<td>实际订单收款金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>indicate</td>\n<td>地磁进场标识，进场是时生成一下00-FF的标志，出场时传相同的标示</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>dici_id</td>\n<td>车位编号（com_park_tb编号id）</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bind_flag</td>\n<td>是否可以用来绑定POS机订单 0：不可以 1：可以</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthseg_id</td>\n<td>泊位段编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>运营集团账号(索引)</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>状态 0：正常 1：删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="berth_order_tb_error">berth_order_tb_error</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>berth_id</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>in_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>out_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>total</td>\n<td></td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orderid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>in_uid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>out_uid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_total</td>\n<td></td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>indicate</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>dici_id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bind_flag</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthseg_id</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="bizcircle_tb">bizcircle_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>resume</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="bolink_ccount_tb">bolink_ccount_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>集团编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>money</td>\n<td>金额</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>giveto</td>\n<td>停车费记录到的账户 0停车场 1收费员 2集团 3泊链平台</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td>记录时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>车主编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>类型 0其它平台车主在本平台停车，车场账户收入停车费，泊链平台及本台的停车费， 1车主在其它平台支付停车费，泊链平台已扣本平台的余额，本平台扣除用户的余额</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orderid</td>\n<td>订单编号，type=0时是本平台的订单编号，为1时，第三方的订单编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="bolink_order_tb">bolink_order_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>union_id</td>\n<td>车主停车的厂商编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>money</td>\n<td>金额</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>start_time</td>\n<td>进场时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>end_time</td>\n<td>出场时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 0未结算 1已结算</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>in_time</td>\n<td>进场记录时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>out_time</td>\n<td>出场记录时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td>订单金额修改时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>union_name</td>\n<td>厂商平台名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>park_name</td>\n<td>停车场名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_id</td>\n<td>车主停车所在厂商平台的订单编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>plate_number</td>\n<td>车牌号码</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>车主编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pay_type</td>\n<td>0现金 1电子支付</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pay_union_id</td>\n<td>电子支付的厂商平台，现金支付时为-1</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>prepay</td>\n<td>预付金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>park_id</td>\n<td>第三方车场编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>duration</td>\n<td>停车时长</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>prepay_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>trade_no</td>\n<td>交易流水号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="bolink_pay_tb">bolink_pay_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>ctimedate</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>trade_no</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0新建 1已完成 2未通知 3已补发</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>callback</td>\n<td>回调数据</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>request_data</td>\n<td>请求数据</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>issend</td>\n<td>0 未发送 1已发送</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>money</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>send_time</td>\n<td>发送时间，重发时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="bonus_record_tb">bonus_record_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>bid</td>\n<td>红包编号 1:今日头条(北京)，2:传单红包，3:节日红包，4:今日头条（外地） 998:直付红包，999:收费员推荐车主，1000:为订单红包</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td>领取时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>mobile</td>\n<td>领取人手机</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0：未写入车主账户，1：已写入用户账户</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td>金额</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>类型：0普通券，1打折券</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="bonus_type_tb">bonus_type_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 0不可用，1可用</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="card_account_tb">card_account_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>card_id</td>\n<td>卡片编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>（卡片的生命周期）0：充值 1：消费 2：开卡（卡片初始化，此时的卡片还不能使用） 3：激活卡片（此时卡片方可使用） 4：绑定用户 5：注销卡片</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>charge_type</td>\n<td>充值方式：0：现金充值 1：微信公众号充值 2：微信客户端充值 3：支付宝充值 4：预支付退款 5：订单退款</td>\n<td>integer</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>consume_type</td>\n<td>消费方式 0：支付停车费（非预付） 1：预付停车费 2：补缴停车费  3：追缴停车费</td>\n<td>integer</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td>金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orderid</td>\n<td>订单编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>记录时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td>说明</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>车主编号（有可能是非卡片持有者）</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td>收费员（或者操作人）账号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>产生这笔流水所在的车场编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthseg_id</td>\n<td>产生这笔流水所在的泊位段编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berth_id</td>\n<td>产生这笔流水所在的泊位编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>运营集团账号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>账目流水状态 0：正常 1：删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="card_anlysis_tb">card_anlysis_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>统计时间</td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>all_count</td>\n<td>截止当前时间发卡总数</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>all_balance</td>\n<td>截止当前时间总余额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>slot_charge</td>\n<td>一个统计时间段内的充值金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>slot_consume</td>\n<td>一个统计时间段内消费总额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>slot_refund_count</td>\n<td>一个统计时间段内退卡总数</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>slot_refund_balance</td>\n<td>一个统计时间段内办理退卡退还的余额总数</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>slot_act_count</td>\n<td>一个统计时间段内发卡数量（激活卡片）</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>slot_act_balance</td>\n<td>一个统计时间段内发行卡片（激活卡片）的初始化余额总数</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>运营集团编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>slot_bind_count</td>\n<td>一个统计时间段内绑定的用户数量</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="card_carnumber_tb">card_carnumber_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>card_id</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>0：正常 1：删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="card_renew_tb">card_renew_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td>主键id</td>\n<td>integer</td>\n<td>nextval(&#39;seq_card_renew_tb&#39;::regclass)</td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>trade_no</td>\n<td>购买月卡流水号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>card_id</td>\n<td>月卡编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pay_time</td>\n<td>月卡续费时间</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount_receivable</td>\n<td>应收金额</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount_pay</td>\n<td>实收金额</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>collector</td>\n<td>收费人</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pay_type</td>\n<td>缴费类型</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td>车牌号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>user_id</td>\n<td>用户编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>resume</td>\n<td>备注信息</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>buy_month</td>\n<td>购买月数</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>月卡缴费记录入库时间</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td>月卡续费修改时间</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>limit_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>start_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="car_info_tb">car_info_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0:不可用，1：可用</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>新建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_auth</td>\n<td>是否已认证 -1认证失败 0未认证，1已认证 2认证中</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_comuse</td>\n<td>是否是常用车牌，0不是，1是</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pic_url1</td>\n<td>图片地地址1</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pic_url2</td>\n<td>图片地地址2</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td>说明</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="car_number_type_tb">car_number_type_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td>车牌号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>typeid</td>\n<td>车型管理</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="carower_product">carower_product</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>pid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>b_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>e_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>total</td>\n<td>金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td>备注</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td>会员名字</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>address</td>\n<td>会员地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>p_lot</td>\n<td>车位编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>act_total</td>\n<td>实际收取的金额（有优惠）</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>etc_id</td>\n<td>电子标签</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td>记录数据修改时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>card_id</td>\n<td>月卡编号（收费系统唯一编号）</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>member_id</td>\n<td>车主在收费系统编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td>车牌，多个车牌，用”,”英文分隔</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>com_id</td>\n<td>车场编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>mobile</td>\n<td>手机号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>记录云端月卡会员是否被删除</td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_type_id</td>\n<td>车辆类型，关联car_type_tb 主键</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>limit_day_type</td>\n<td>单双日限行 0 不限制，1限制</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>price_info</td>\n<td>临时月卡价格信息</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="carpic_tb">carpic_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td>主键id</td>\n<td>integer</td>\n<td>nextval(&#39;seq_carpic_tb&#39;::regclass)</td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>order_id</td>\n<td>订单编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>图片上传时间</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td>图片对应的车牌号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pic_type</td>\n<td>图片上传类型</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>resume</td>\n<td>车辆图片上传备注</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td>车辆图片修改时间</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>content</td>\n<td>存储图片base64编码</td>\n<td>text</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>picture_source</td>\n<td>车场上传图片来源</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>liftrod_id</td>\n<td>车场收费系统抬杆记录编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>park_order_type</td>\n<td>车场订单图片对应的类型</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>carpic_table_name</td>\n<td>记录车辆出入场图片在云端存储的表名</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>liftpic_table_name</td>\n<td>记录云端存储的抬杆图片表名位置</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>event_id</td>\n<td>手动匹配订单事件编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>confirmpic_table_name</td>\n<td>记录云端存储的人工确认订单车牌图片表名位置</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="car_picturs_tb">car_picturs_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>orderid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>lefttop</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>rightbottom</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pictype</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>width</td>\n<td>图片宽</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>height</td>\n<td>图片高</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="carstop_order_tb">carstop_order_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>cid</td>\n<td>泊车点编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>车主</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td>车牌</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>buid</td>\n<td>接车人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>euid</td>\n<td>还车人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>start_time</td>\n<td>车主下单时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>btime</td>\n<td>接车时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>end_time</td>\n<td>请求还车时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>etime</td>\n<td>还车时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td>金额</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态:0车主泊车请求 1泊车员已响应泊车 2正在泊车  3泊车完成 4车主取车请求 5 泊车员已响应取车 6泊车员正在取车 7等待支付 8支付成功 9订单取消</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pic</td>\n<td>泊车照片</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>lng</td>\n<td>车主、车辆经度</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>lat</td>\n<td>车主/车辆纬度</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>keyno</td>\n<td>车钥匙编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_local</td>\n<td>泊车位置</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pay_type</td>\n<td>支付方式   0现金，1余额, 2微信，3余额+微信</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>oilpic</td>\n<td>油表照片</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="carstops_price_tb">carstops_price_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>cid</td>\n<td>泊车点编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>utime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>first_price</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>first_unit</td>\n<td>首价格时长（分钟）</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>next_price</td>\n<td>下一计价价格</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>next_unit</td>\n<td>下一计价单位分钟</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>top_price</td>\n<td>封顶价格</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>fav_price</td>\n<td>优惠价</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>fav_unit</td>\n<td>优惠价格单位（分钟）</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>类型  0临停  1常停</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>creator</td>\n<td>创建人</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>resume</td>\n<td>说明</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="car_stops_tb">car_stops_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td>名称</td>\n<td>character</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>longitude</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>latitude</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>start_price</td>\n<td>起步价</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>next_price</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>max_price</td>\n<td>最高价</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态，0正常，1不可用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td>创建日期</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>utime</td>\n<td>修改日期</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cuin</td>\n<td>创建人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>resume</td>\n<td>说明</td>\n<td>character</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pic</td>\n<td>泊车点图片</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>address</td>\n<td>位置</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>creator</td>\n<td>创建人</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>city</td>\n<td>所在城市</td>\n<td>integer</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="car_type_tb">car_type_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>所属车场</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>sort</td>\n<td>排序</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cartype_id</td>\n<td>第三方车型编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>新建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td>更新时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>是否删除 0否1是</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="city_account_tb">city_account_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>cityid</td>\n<td>城市商户编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td>金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>记录时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>类型 0：充值 1：支出</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>source</td>\n<td>来源 0：停车费（非预付），1：提现，2：追缴停车费，3：预付停车费，4：预付退款（预付），5：预付补缴（预付金额不足）</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td>备注</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>withdraw_id</td>\n<td>提现记录编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orderid</td>\n<td>订单编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td>收费员账号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthseg_id</td>\n<td>产生这笔流水所在的泊位段编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berth_id</td>\n<td>产生这笔流水所在的泊位编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>运营集团账号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>账目流水状态 0：正常 1：删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="city_hotarea_tb">city_hotarea_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td>名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>adress</td>\n<td>详细地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>reason</td>\n<td>说明</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>新建日期</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_user</td>\n<td>创建人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td>修改时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_user</td>\n<td>修改人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>delete_time</td>\n<td>删除时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>delete_user</td>\n<td>删除人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 0正常 1 删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cityid</td>\n<td>城市商户号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="city_peakalert_tb">city_peakalert_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>title</td>\n<td>标题</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>content</td>\n<td>内容</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>handle_user</td>\n<td>处理人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>添加时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>handle_time</td>\n<td>处理时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0新建 1已处理</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cityid</td>\n<td>城市编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>hotarea_id</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="city_video_tb">city_video_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>video_name</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ip</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>port</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cusername</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cpassword</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>manufacture</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0：故障 1：正常 2：删除</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cityid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>latitude</td>\n<td>纬度</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>longitude</td>\n<td>经度</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>监控类型 0：路侧监控 1:封闭停车场监控</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>channelid</td>\n<td>摄像头通道</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>deviceid</td>\n<td>设备编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>address</td>\n<td>地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="collect_anlysis_tb">collect_anlysis_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cash</td>\n<td>现金</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>epay_collector</td>\n<td>收费员收取的电子金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>epay_park</td>\n<td>车场收取的电子金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>epay_group</td>\n<td>运营集团收取的电子金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>epay_city</td>\n<td>城市商户收取的电子金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="collector_account_pic_tb">collector_account_pic_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pic_name</td>\n<td>图片名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>utime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0:待审核 1:已审核，2:审核中，3:无效</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>停车场</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>auditor</td>\n<td>审核人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="collector_scroe_tb">collector_scroe_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>lala_scroe</td>\n<td></td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>nfc_score</td>\n<td></td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>praise_scroe</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pai_score</td>\n<td>照牌积分</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>online_scroe</td>\n<td>在线积分，每10分钟积0.2分</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>recom_scroe</td>\n<td>-- 推荐积分</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="collector_set_tb">collector_set_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>role_id</td>\n<td>所属角色</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>photoset</td>\n<td>拍照设置【num1,num2,num3】分别是入场可拍照片数，出场可拍照片数，未缴可拍照片数</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>change_prepay</td>\n<td>是否可更改预收金额 0不可，1可以</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>view_plot</td>\n<td>0列表，1显示泊位</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>print_sign</td>\n<td>打印小票信息【入场，出场】</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>prepayset</td>\n<td>预收设置 【num1,num2,num3...】预收金额选项</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isprepay</td>\n<td>0不可预收 1可预收</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>hidedetail</td>\n<td>1隐藏 0不隐藏首页收费汇总</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_sensortime</td>\n<td>0：取车检器时间作为录入订单时间 1：取当前时间作为录入订单时间</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>password</td>\n<td>查看汇总的权限密码</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>signout_password</td>\n<td>签退密码</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>signout_valid</td>\n<td>客户端签退是否需要密码验证 0：不需要 1：需要</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_show_card</td>\n<td>是否在收费汇总和打印小票处显示出来卡片的数据（有些运营集团没有卡片） 0：显示 1：不显示</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>print_order_place2</td>\n<td>桂林提出要在点击结算订单的时候就打印小票（已经有一个打印订单小票的地方，此处为第二个地方）0：不打印 1：打印</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_duplicate_order</td>\n<td>同一车辆在同一运营集团不同车场能否同时存在在场订单 1：是 0：否</td>\n<td>smallint</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_print_name</td>\n<td>控制打印小票要不要显示收费员名字（有的车场矫情，不让显示名字）1：打印 0：不打印</td>\n<td>smallint</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="collector_sort">collector_sort</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>park_count</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="com_account_tb">com_account_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>card_number</td>\n<td>银行卡帐号或支付宝、微信等帐号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>atype</td>\n<td>0银行卡，1支付宝，2微信</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td>绑定人姓名</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>mobile</td>\n<td>绑定人手机号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bank_name</td>\n<td>开户行</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>note</td>\n<td>备注</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0:公司，1个人 2对公</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0:可用，1禁用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>area</td>\n<td>开户地区</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bank_pint</td>\n<td>开户支行（营业网点)</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>user_id</td>\n<td>身份证号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>city</td>\n<td>收款人所在市</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pay_type</td>\n<td>结算方式</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pay_date</td>\n<td>期望日</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>use</td>\n<td>用途</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bank_no</td>\n<td>收方行号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>运营集团编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cityid</td>\n<td>城市商户编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="com_alert_tb">com_alert_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>新建日期</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>source</td>\n<td>来源</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>content</td>\n<td>内容</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>类型</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 0新建 1已审核 2已发布 3取消</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>handle_user</td>\n<td>处理人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>handle_time</td>\n<td>处理时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cityid</td>\n<td>所属城市</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="com_berthsecs_tb">com_berthsecs_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uuid</td>\n<td>唯一标识</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthsec_name</td>\n<td>泊位段名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>park_uuid</td>\n<td>所属停车场uuid</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>创建日期</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>address</td>\n<td>详细地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>longitude</td>\n<td>经度</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>latitude</td>\n<td>纬度</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_active</td>\n<td>状态 0：正常 1：禁用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="com_brake_tb">com_brake_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>brake_name</td>\n<td>道闸名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>serial</td>\n<td>串口号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ip</td>\n<td>设备地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>passid</td>\n<td>通道id</td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td></td>\n<td>integer</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>upload_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>所属车场</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="com_camera_tb">com_camera_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>camera_name</td>\n<td>摄像头名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ip</td>\n<td>摄像头IP地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>port</td>\n<td>摄像头端口</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cusername</td>\n<td>摄像头用户名</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cpassword</td>\n<td>摄像头用户密码</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>manufacturer</td>\n<td>制造厂商</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>passid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>-1和1表示正常 0表示故障</td>\n<td>integer</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>upload_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="com_comment_tb">com_comment_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comment</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="com_info_tb">com_info_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>company_name</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>phone</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>fax</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>address</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>zipcode</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>homepage</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>resume</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>longitude</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>latitude</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>parking_type</td>\n<td>-- 车位类型，0地面，1地下，2占道 3室外 4室内 5室内外</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>parking_total</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>share_number</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>auto_order</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>mobile</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>total_money</td>\n<td></td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>money</td>\n<td></td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>property</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>停车场类型，0：付费，1免费</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>stop_type</td>\n<td>0：平面排列，1：立体排列</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0:可用，1：删除,2:待审核</td>\n<td>integer</td>\n<td>2</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>biz_id</td>\n<td>商圈</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td>市场专员---引用用户表</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>city</td>\n<td>所在城市</td>\n<td>integer</td>\n<td>110000</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>recom_code</td>\n<td>推荐码：车场停车员的帐号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>nfc</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>etc</td>\n<td>0:不支持，1:Ibeacon 2:通道照牌 3:手机照牌 4:Pos机照牌</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>book</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>navi</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>monthlypay</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isnight</td>\n<td>夜晚停车，0:支持，1不支持</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isfixed</td>\n<td>0：未校验，1：已校验，2：申请校验，-1：一次未通过，-2：二次未通过，-3：三次未通过</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>iscancel</td>\n<td>是否在结算订单时去掉取消按钮,0:否，1是</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>firstprovince</td>\n<td>优先识别省份</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>mcompany</td>\n<td>经营公司</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>record_number</td>\n<td>备案号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>epay</td>\n<td>电子支付，0不支持，1支持</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_hasparker</td>\n<td>0,没有收费员在位，1有收费员在位</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isview</td>\n<td>是否在地图上显示 0不显示，1显示</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remarks</td>\n<td>备注</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>invalid_order</td>\n<td>未结算的垃圾订单数量</td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>fixed_pass_time</td>\n<td>车场校验通过的时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_type</td>\n<td>是否区分大小车，0:不区分，1：区分</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>passfree</td>\n<td>是否允许免费结算订单，0：允许，1：不允许</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>activity</td>\n<td>车场活动：0 没有活动 1申请活动 2:申请通过</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>activity_content</td>\n<td>-- 活动内容</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>upload_uin</td>\n<td>上传人</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>minprice_unit</td>\n<td>0（该收多少收多少）,0.5（收5毛 ）,1（收一块）</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isshowepay</td>\n<td>是否显示直付订单，1显示，0不显示</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ishidehdbutton</td>\n<td>-- 结算按钮（HD版）0显示  1不显示   默认0</td>\n</tr>\n<tr>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isautopay</td>\n<td>车场是否支持自动支付   0:不支持，1:支持</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>allowance</td>\n<td>车场补贴额</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_per</td>\n<td>比例</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>entry_set</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>entry_month2_set</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>chanid</td>\n<td>渠道编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>集团编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ishdmoney</td>\n<td>是否隐藏收费金额  0不隐藏，1隐藏</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pid</td>\n<td>所属车场</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>full_set</td>\n<td>车位满进场设置  0可进  1禁止</td>\n</tr>\n<tr>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>leave_set</td>\n<td>离场设置    0默认设置   1识别就抬杆</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>areaid</td>\n<td>区域编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cityid</td>\n<td>城市编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>hotarea_id</td>\n<td>热点区域编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>empty</td>\n<td>空闲车位数</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>upload_union_time</td>\n<td>上传时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>union_state</td>\n<td>0 未上传 1已上传在使用 2已上传未审核 3已上传并禁用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ukey</td>\n<td>描述车场的秘钥</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>superimposed</td>\n<td>这个车场的商户是不是支持叠加用减免券,默认不支持</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bolink_id</td>\n<td>泊链编号,先注册泊链,再注册云平台</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>beat_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="com_led_tb">com_led_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>ledip</td>\n<td>ip地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ledport</td>\n<td>端口号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>leduid</td>\n<td>素材UID</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>movemode</td>\n<td>移动方式:0：自适应，1：从右向左移动，2：从左向右移动，3：从下向上移动，4：从上向下移动，5：从右向左展开，6：从左向右展开，7：从下向上展开，8：从上向下展开，9：立即显示，10：从中间向两边展开，11：从两边向中间展开，12：从中间向上下展开，13：从上下向中间展开，14：闪烁，15：右百叶</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>movespeed</td>\n<td>移动速度</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>dwelltime</td>\n<td>停留时间：0：0s，1：1s，2：2s，3：3s，4：4s，5：5s</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ledcolor</td>\n<td>显示屏颜色：0：单基色，1：双基色</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>showcolor</td>\n<td>颜色：0：红，1：绿，2：黄</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>typeface</td>\n<td>字体：1：宋体，2：楷体，3：黑体，4：隶书，5：行书</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>typesize</td>\n<td>字号：0：12×12，1：16×16，2：24×24，3：32×32，4：48×48，5：64×64，6：80×80，7：96×96</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>matercont</td>\n<td>素材内容</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>passid</td>\n<td>通道ID</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>width</td>\n<td>led宽</td>\n<td>integer</td>\n<td>128</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>height</td>\n<td>led高</td>\n<td>integer</td>\n<td>32</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0  默认   1 余位屏</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>rsport</td>\n<td>integer DEFAULT 1, -- 转发端口  1默认: rs232-1  2:rs232-2   3:rs485</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td></td>\n<td>integer</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>upload_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="com_nfc_tb">com_nfc_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>nfc_uuid</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>(之前该字段表示NFC卡状态：0正常，1禁用） 0：激活（对应之前的正常） 1：注销（对应之前的禁用）  2：绑定用户(手机号) 3：开卡（此时的卡片还不能用，要激活后才可使用） 4：绑定车牌号（无手机号）</td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>use_times</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>-- 车主帐号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td>收费员编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td>更新时间,绑定用户时间</td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>nid</td>\n<td>扫描NFC的二维码号</td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>qrcode</td>\n<td>二维码</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>设备类型 0：NFC  1：电子标签 2：商家卡（商家自己发行的IC卡）</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>card_name</td>\n<td>卡片名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>device</td>\n<td>开卡设备</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>0：正常 1：已删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>balance</td>\n<td>卡片余额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>card_number</td>\n<td>卡面号（印在卡面上的编号）</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>tenant_id</td>\n<td>城市商户编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>group_id</td>\n<td>运营集团编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>reg_id</td>\n<td>开卡(入库)人编号</td>\n</tr>\n<tr>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cancel_id</td>\n<td>注销人（解绑）编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cancel_time</td>\n<td>注销时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>activate_id</td>\n<td>激活卡片人编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>activate_time</td>\n<td>激活时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="com_parkstatus_tb">com_parkstatus_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>total</td>\n<td>车位总数</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>empty</td>\n<td>总空闲车位数</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>sheduled</td>\n<td>总可预定车位数(如无可预定车位用0表示)</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>rscheduled</td>\n<td>空可预定车位(如无空可预定车位用0表示)</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>internal</td>\n<td>内部总车位数量(如车位不分内外车位填0)</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>rinternal</td>\n<td>内部空车位数量(如车位不分内外车位填0)</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td>时间戳</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>chanid</td>\n<td>合作方号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cityid</td>\n<td>城市商户号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="com_park_tb">com_park_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cid</td>\n<td>车位编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0空闲 1占用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>qid</td>\n<td>二维码编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_id</td>\n<td>订单编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>dici_id</td>\n<td>地磁编号</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>enter_time</td>\n<td>入场时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>end_time</td>\n<td>出场时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>address</td>\n<td>地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthsec_id</td>\n<td>泊位段编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>创建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uuid</td>\n<td>唯一编号</td>\n<td>character</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>longitude</td>\n<td>经度</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>latitude</td>\n<td>纬度</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>0正常，1已删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="com_pass_tb">com_pass_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>worksite_id</td>\n<td>工作站ID</td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>passname</td>\n<td>通道名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>passtype</td>\n<td>通道类型 0入口，1出口，2出入</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>description</td>\n<td>通道说明</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 0：正常 1：禁用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>month_set</td>\n<td>非月卡车设置   0不限制  1非月卡车禁止进入</td>\n</tr>\n<tr>\n<td></td>\n<td>integer</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>month2_set</td>\n<td>月卡第二..辆车设置   0不限制  1月卡第二辆车禁止进</td>\n<td>integer</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>channel_id</td>\n<td>第三方通道编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="com_picturs_tb">com_picturs_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>picurl</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="com_pos_tb">com_pos_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>pda</td>\n<td>设备编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>使用状态 0正常1不可用</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td>收费员</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_deleted</td>\n<td>是否删除</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>delete_user</td>\n<td>删除人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>delete_time</td>\n<td>删除时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td>修改时间</td>\n</tr>\n<tr>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_user</td>\n<td>修改人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>创建日期</td>\n</tr>\n<tr>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_user</td>\n<td>创建人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>运营集团</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="com_praise_tb">com_praise_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>praise</td>\n<td>0:贬，1：赞</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="com_temp_tb">com_temp_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>next_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="com_worksite_tb">com_worksite_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>worksite_name</td>\n<td>工作站名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>description</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>net_type</td>\n<td>工作站网络环境，0：流量，1：宽带</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>host_name</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>host_memory</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>host_internal</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>upload_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 0：正常 1：禁用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="confirm_order_tb">confirm_order_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>event_id</td>\n<td>事件编号(在收费系统事件编号唯一，不可重复)</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td>车牌号码</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>upload_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>channel_id</td>\n<td>通道编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 0-待处理 1-已处理</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="consume_anlysis_tb">consume_anlysis_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>wx_total</td>\n<td>微信交易用户新增数</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>zfb_total</td>\n<td>支付宝交易用户新增数量</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>wxp_total</td>\n<td>微信公众号交易用户新增数量</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="dataauth_tb">dataauth_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>authorizee</td>\n<td>被授权人id</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>authorizer</td>\n<td>授权人id</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>department_id</td>\n<td>部门id</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="department_tb">department_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>nid</td>\n<td>节点ID</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pid</td>\n<td>父节点</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>dname</td>\n<td>节点名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isparent</td>\n<td>是否是父节点</td>\n<td>boolean</td>\n<td>false</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="device_fault_tb">device_fault_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>sensor_id</td>\n<td>车检器编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>site_id</td>\n<td>基站编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>induce_id</td>\n<td>诱导屏编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>掉线时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>end_time</td>\n<td>恢复时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="dici_tb">dici_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>code</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0空闲 1占用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>serid</td>\n<td>地磁主机ID</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>did</td>\n<td>车检器唯一编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>operate_time</td>\n<td>操作时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>battery</td>\n<td>电池电压</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>magnetism</td>\n<td>电容电压</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>beart_time</td>\n<td>心跳时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>site_id</td>\n<td>车检器所属的基站编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>状态 0：正常 1：删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="dictionary_detail_tb">dictionary_detail_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td>主键ID</td>\n<td>bigint</td>\n<td>nextval(&#39;seq_dictionary_detail_tb&#39;::regclass)</td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>item_id</td>\n<td>车场定义字典类型ID</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>subitemdesc</td>\n<td>车场定义子项目详细说明</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>sub_item_id</td>\n<td>数据编号</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="dictionary_item_tb">dictionary_item_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td>主键id</td>\n<td>bigint</td>\n<td>nextval(&#39;seq_dictionary_item_tb&#39;::regclass)</td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>park_id</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>itemdesc</td>\n<td>车场定义的字典类别说明</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>item_name</td>\n<td>名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="directpay_anlysis_tb">directpay_anlysis_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>total</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="esc_record">esc_record</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>end_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>total</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pursue_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>act_total</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pursue_uid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>prepaid</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>overdue</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthsec_id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cid</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>nickname</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="fix_code_tb">fix_code_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>code_src</td>\n<td>二维码连接</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>code</td>\n<td>code值</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>time_limit</td>\n<td>剩余时间</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>money_limit</td>\n<td>剩余金额</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>free_limit</td>\n<td>剩余减免券张数</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态0可用 1 不可用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>shop_id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>固定码类型 1减免券 2全免券</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td>单张额度</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>固定码名称</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>end_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>validite_time</td>\n<td>有效期  小时</td>\n<td>integer</td>\n<td>24</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="flygame_pool_tb">flygame_pool_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>ptype</td>\n<td>奖券类型：0停车宝停车券 1车主停车券 2余额券 3广告券 4清空福袋 5翻倍福袋</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>money</td>\n<td>金额</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>tid</td>\n<td>车主停车券编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>aid</td>\n<td>advert_tb 广告表编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>count</td>\n<td>数量</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="flygame_score_anlysis_tb">flygame_score_anlysis_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>db_bullet_count</td>\n<td>双倍弹数量</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>db_bullet_score</td>\n<td>双倍弹积分</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>empty_bullet_count</td>\n<td>清空弹数量</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>empty_bullet_score</td>\n<td>清空弹积分</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>gift_count</td>\n<td>礼品券数量</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>gift_score</td>\n<td>礼品券积分</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>balance_count</td>\n<td>余额券数量</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>balance_score</td>\n<td>余额券积分</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ticket_count</td>\n<td>停车券数量</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ticket_score</td>\n<td>停车券积分</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>second_count</td>\n<td>第二关入场券数量</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>second_score</td>\n<td>第二关入场券积分</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>tid</td>\n<td>停车券编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cloud_count</td>\n<td>打掉云数量</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cloud_score</td>\n<td>云积分</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>crow_count</td>\n<td>打掉乌鸦数量</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>crow_score</td>\n<td>乌鸦积分</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bets_double_count</td>\n<td>子弹翻倍福袋数量</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bets_double_score</td>\n<td>子弹翻倍福袋积分</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bets_halve_count</td>\n<td>子弹减半道具数量</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bets_halve_score</td>\n<td>子弹减半道具积分</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>score_double_count</td>\n<td>积分翻倍福袋数量</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>score_double_score</td>\n<td>积分翻倍福袋积分</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>score_halve_count</td>\n<td>积分减半道具数量</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>score_halve_score</td>\n<td>积分减半道具积分</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>catapult_full_count</td>\n<td>弹弓满血福袋数量</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>catapult_full_score</td>\n<td>弹弓满血福袋积分</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>catapult_halve_count</td>\n<td>弹弓血量减半道具数量</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>catapult_halve_score</td>\n<td>弹弓血量减半道具积分</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>float_score</td>\n<td>浮动积分，记录翻倍或减半</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="flygame_score_tb">flygame_score_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>fgid</td>\n<td>flygame_pool_tb  游戏券池编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td>说明</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td>时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ptype</td>\n<td>奖券类型：0停车宝停车券 1车主停车券 2余额券 3广告券 4清空福袋 5翻倍福袋 6第二关入口 7子弹翻倍福袋 8子弹减半道具 9积分翻倍福袋 10积分减半道具 11弹弓满血福袋 12弹弓血量减半道具</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>money</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_operate</td>\n<td>是否分享或加为好机友 0否，1是</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="free_reasons_tb">free_reasons_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td>免费原因</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场id</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>sort</td>\n<td>排序</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="group_account_tb">group_account_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td>金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>记录生成时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td>收费员账号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>类型 0：充值 1：支出</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>source</td>\n<td>来源 0：停车费（非预付），1：提现，2：追缴停车费，3：预付停车费，4：预付退款（预付），5：预付补缴（预付金额不足）</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orderid</td>\n<td>订单编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>withdraw_id</td>\n<td>提现记录编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>运营集团账号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td>备注</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthseg_id</td>\n<td>产生这笔流水所在的泊位段编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berth_id</td>\n<td>产生这笔流水所在的泊位编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>账目流水状态 0：正常 1：删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="hasparker_anlysis_tb">hasparker_anlysis_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>anlysis_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>total</td>\n<td>收费员在岗并且可支付车场的数量</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="ibeacon_tb">ibeacon_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>buy_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ibcid</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td></td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="induce_ad_history_tb">induce_ad_history_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>induce_id</td>\n<td>诱导屏编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>创建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>begin_time</td>\n<td>开始时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>end_time</td>\n<td>结束时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ad</td>\n<td>广告内容</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>creator_id</td>\n<td>发布人</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="induce_ad_tb">induce_ad_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>induce_id</td>\n<td>诱导屏编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ad</td>\n<td>广告</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>begin_time</td>\n<td>生效时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>end_time</td>\n<td>结束时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isactive</td>\n<td>是否已发布 0：未发布 1：已发布</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>创建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td>更新时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>publish_time</td>\n<td>信息发布时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>updator_id</td>\n<td>操作人编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="induce_module_tb">induce_module_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>induce_id</td>\n<td>诱导屏编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>是否删除 0：正常 1：已删除</td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td>显示区域的名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>创建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>sort</td>\n<td>显示区域排序，越小的越靠前</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="induce_park_tb">induce_park_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>induce_id</td>\n<td>诱导屏编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>module_id</td>\n<td>诱导屏显示区域编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>sort</td>\n<td>排序字段</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="induce_tb">induce_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td>诱导屏名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>诱导类型 0：一级诱导 1：二级诱导 2：三级诱导</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>longitude</td>\n<td>经度</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>latitude</td>\n<td>纬度</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>address</td>\n<td>地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cityid</td>\n<td>城市商户编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 0：正常 1：故障</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>创建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td>更新时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>creator_id</td>\n<td>创建者编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>updator_id</td>\n<td>修改者编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>deletor_id</td>\n<td>删除者编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>0：正常 1：删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>did</td>\n<td>硬件唯一编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>heartbeat_time</td>\n<td>最新一次心跳时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="inspect_event_tb">inspect_event_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>end_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td></td>\n<td>integer</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthsec_id</td>\n<td>泊位段编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td>收费员</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>inspectid</td>\n<td>巡查员id</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态  0待处理  1已处理</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td>-- 备注</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>event_pic</td>\n<td>时间处理图片</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>detailtype</td>\n<td>任务详情</td>\n<td>integer</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>dici_id</td>\n<td>泊位编号（com_park_tb的id）</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="inspect_group_tb">inspect_group_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uuid</td>\n<td></td>\n<td>character</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>inspectgroup_name</td>\n<td></td>\n<td>character</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>company_id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_active</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>chanid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="liftrod_info_tb">liftrod_info_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td>主键id</td>\n<td>bigint</td>\n<td>nextval(&#39;seq_liftrod_info_tb&#39;::regclass)</td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>channel_id</td>\n<td>记录车场的通道编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>channel_name</td>\n<td>记录车场的通道名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>operate</td>\n<td>操作抬杆的类型：0抬杆1落杆2暂停</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>发送操作抬杆消息后车场执行结果返回-1 等待返回 0-失败 1-成功</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>收费系统车场编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="lift_rod_tb">lift_rod_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>收费员账户</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>img</td>\n<td>图片地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>reason</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>liftrod_id</td>\n<td>车场抬杆记录id</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td>修改时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>in_channel_id</td>\n<td>入场通道编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>out_channel_id</td>\n<td>出场通道编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td>车主姓名</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td>车牌编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_id</td>\n<td>车场订单编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>resume</td>\n<td>其他原因</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pass_id</td>\n<td>通道名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>liftpic_table_name</td>\n<td>记录图片存储mongodb的表名</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="local_info_tb">local_info_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>version</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cpu</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>memory</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>harddisk</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_update</td>\n<td>是否允许更新  0不可以   1 可以</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>limit_time</td>\n<td>本地服务器到期时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>secret</td>\n<td>本地服务器车场密钥  校验是不是来自本地服务器</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td>备注</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="logo_tb">logo_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0：渠道</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orgid</td>\n<td>组织编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>url_fir</td>\n<td>图片url</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>url_sec</td>\n<td>图片url</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="lottery_tb">lottery_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>车主账户</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orderid</td>\n<td>订单编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>创建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>lottery_result</td>\n<td>抽奖结果：0:一元余额，1：二元停车券,2:三元余额,3:未中奖</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="message_tb">message_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0:收费员，1:车主，2：停车场系统管理员，3：总管理员</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0:未读，1：已读</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>content</td>\n<td>内容</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="mobilepay_anlysis_tb">mobilepay_anlysis_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>mobilepay_count</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="mobile_tb">mobile_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>imei</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>num</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>mode</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>price</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>editor</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>distru_date</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>money_3</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_3</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>device_code</td>\n<td>设备编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>device_auth</td>\n<td>设备是否认证 0未认证 1已认证</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>auth_user</td>\n<td>认证人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>auth_time</td>\n<td>认证时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="money_record_tb">money_record_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0:充值，1：消费，2：提现</td>\n<td>integer</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td>说明</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pay_type</td>\n<td>0余额，1支付宝，2微信，3网银，4余额+支付宝,5余额+微信,6余额+网银</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="money_set_tb">money_set_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>mtype</td>\n<td>0:停车费,1:预订费,2:停车宝返现,3停车宝周奖</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>giveto</td>\n<td>0:公司账户，1：个人账户 ，2：运营集团账户</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="monitor_info_tb">monitor_info_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>net_status</td>\n<td>网络状态 1 正常 0 异常</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_show</td>\n<td>是否显示 0-隐藏 1-显示</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>show_order</td>\n<td>显示顺序（排序）</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>play_src</td>\n<td>播放地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>channel_id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 1-正常 0-删除</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="month_price_tb">month_price_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>trade_no</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>datastr</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="no_payment_tb">no_payment_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>end_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td>车牌号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 0未处理，1已处理</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>车主帐号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>逃单所在车场编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>total</td>\n<td>逃单金额（应收金额）</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td>置为逃单的收费员编号（出场收费员）</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pursue_time</td>\n<td>追缴逃单时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>act_total</td>\n<td>追缴金额（实收金额）</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pursue_uid</td>\n<td>追缴收费员</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pursue_comid</td>\n<td>追缴订单的车场</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pursue_berthseg_id</td>\n<td>追缴订单所在的泊位段编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pursue_berth_id</td>\n<td>追缴订单所在的泊位编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthseg_id</td>\n<td>逃单所在泊位段编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berth_id</td>\n<td>逃单所在泊位编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>逃单所在的运营集团</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>prepay</td>\n<td>逃单已预付的金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>状态 0： 正常 1：删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pursue_groupid</td>\n<td>追缴订单所在的运营集团编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="online_anlysis_tb">online_anlysis_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>collector_online</td>\n<td>收费员在线数</td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>inspector_online</td>\n<td>巡查员在线数</td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>统计时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>运营集团编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cityid</td>\n<td>城市商户编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="order_anlysis_tb">order_anlysis_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_count</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="order_attach_tb">order_attach_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>settle_type</td>\n<td>0:正常结算，1：异常结算</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>change_carnumber</td>\n<td>0:未更正过车牌，1：更正过车牌</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>add_carnumber</td>\n<td>0:未补录车牌，1：补录车牌</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>old_carnumber</td>\n<td>更正车牌之前的车牌</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_id</td>\n<td>订单id</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="order_log_tb">order_log_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>order_id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>log</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0:创建订单，1：结算订单,2:优惠，3:现金收费</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="order_message_tb">order_message_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td>车牌号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orderid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>already_read</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_total</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>-- 0:未结算，1：待支付，2：支付完成, -1:支付失败 -2余额不足</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_sale</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>btime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>etime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>duartion</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>message_type</td>\n<td>-- 0:订单消息，1：车位预定消息 2:充值购买产品 3直付订单消息（收费员用） 4Ibeacon解绑消息(收费员)  9Ibeacon支付消息(车主)</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="order_reserve_tb">order_reserve_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>车主编号</td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>预约开始时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>limit_time</td>\n<td>最晚到达时时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>预约状态 0:欠费 1:已补缴 2:未入场 3:已取消</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td>车牌号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>phone</td>\n<td>手机号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pay_type</td>\n<td>支付类型</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>prepaid</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>prepaid_pay_time</td>\n<td>预约支付时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>预约类型 0:车位预约 1：充电桩预约</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>arrive_time</td>\n<td>预计到达时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>plot_no</td>\n<td>车位号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="order_tb">order_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>total</td>\n<td></td>\n<td>numeric</td>\n<td>0.00</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0未支付 1已支付 2:逃单</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>end_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>auto_pay</td>\n<td>0:正常结算，1：进场异常结算的订单，2：更正过车牌的订单，3:补录来车生成的订单</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pay_type</td>\n<td>0:帐户支付,1:现金支付,2:手机支付 3:包月 4:现金预支付 5：银联卡(中央预支付，后面废弃) 6：商家卡(中央预支付，后面废弃) 8：免费放行 9：刷卡</td>\n<td>integer</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>nfc_uuid</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td>收费员帐号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td>车牌</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>imei</td>\n<td>手机串号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pid</td>\n<td>计费方式：0按时(0.5/15分钟)，1按次（12小时内10元,前1/30min，后每小时1元）</td>\n<td>integer</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pre_state</td>\n<td>0:默认状态 1：车主预支付中 2：车主预支付中并且收费员刷卡 3：预支付完成</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>类型：0普通订单，1极速通，3本地化订单 4本地服务器订单 5本地生成线上结算订单</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>need_sync</td>\n<td>预支付订单需要同步到线下  0:不需要  1:需要  2同步完成   3本地切换到线上线上生成的需要  4:线上结算的都需要同步下去</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ishd</td>\n<td>0否 1是不显示</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isclick</td>\n<td>0系统结算，1手动结算</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>prepaid</td>\n<td>预付金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>prepaid_pay_time</td>\n<td>预支付时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthnumber</td>\n<td>泊位号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthsec_id</td>\n<td>泊位段编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>所属集团编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>out_uid</td>\n<td>出场收费员</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_union_user</td>\n<td>0不是 1是</td>\n<td>integer</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_type_zh</td>\n<td>适配车场的各种车辆类型信息记录</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_id_local</td>\n<td>记录车场上传订单的编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>duration</td>\n<td>适配车场记录车辆停车时长信息</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pay_type_en</td>\n<td>适配车场车辆的缴费类型，进行记录</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>freereasons_local</td>\n<td>适配车场车辆免费停车原因</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>islocked</td>\n<td>锁车状态: 0未锁定 1已锁定 2锁定中 3锁定失败 4解锁中 5解锁失败</td>\n<td>smallint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>lock_key</td>\n<td>锁车密码</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>in_passid</td>\n<td>入场通道编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>out_passid</td>\n<td>出场通道编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount_receivable</td>\n<td>应收金额</td>\n<td>numeric</td>\n<td>0.00</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>electronic_prepay</td>\n<td></td>\n<td>numeric</td>\n<td>0.00</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>electronic_pay</td>\n<td>电子结算</td>\n<td>numeric</td>\n<td>0.00</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cash_prepay</td>\n<td>现金预付</td>\n<td>numeric</td>\n<td>0.00</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cash_pay</td>\n<td>现金结算</td>\n<td>numeric</td>\n<td>0.00</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>reduce_amount</td>\n<td>减免金额</td>\n<td>numeric</td>\n<td>0.00</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>c_type</td>\n<td>进场类型</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_type</td>\n<td>车型</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>freereasons</td>\n<td>订单上传免费原因</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td>车场出场信息备注</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>carpic_table_name</td>\n<td>记录订单图片在mongodb中存储的表名</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>work_station_uuid</td>\n<td>车场收费系统岗亭/工作站唯一标识</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="order_ticket_detail_tb">order_ticket_detail_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>otid</td>\n<td>订单红包编号，对应order_ticket_tb表ID</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>领券人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td>领取金额</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ttime</td>\n<td>领取时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0:老用户，1:新用户</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>btype</td>\n<td>类型 0普通券，1微信打折券，2:车场专用券 3:车主认证1246专享红包（顺序发放）4 充值100返大礼包 5打飞机红包</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ticketid</td>\n<td>停车券编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="order_ticket_tb">order_ticket_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>发券人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_id</td>\n<td>订单编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>money</td>\n<td>金额 为负数时，每张金额为这个负数的相反数</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bnum</td>\n<td>红包数</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td>新建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>btime</td>\n<td>开始领券时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>etime</td>\n<td>结束领券时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>exptime</td>\n<td>过期时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bwords</td>\n<td>红包祝福语</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>类型 0普通券，1微信打折券，2:车场专用券 3:车主认证1246专享红包（顺序发放）4 充值100返大礼包 5打飞机红包 6战绩分享 不限数</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="org_area_tb">org_area_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td>区域名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 0：正常 1：禁用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>所属运营集团编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>创建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="org_channel_tb">org_channel_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td>名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0:正常 1禁用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>创建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>logo_url</td>\n<td>logo地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="org_city_merchants">org_city_merchants</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td>名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0:正常 1：禁用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>balance</td>\n<td>余额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>gps</td>\n<td>位置</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>address</td>\n<td>地理位置</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_group_pursue</td>\n<td>是否能跨集团追缴订单 0：不可以 1：可以</td>\n<td>smallint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_inpark_incity</td>\n<td>同一车牌可否在城市内重复入场 0不可以 1可以</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>union_id</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ukey</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="org_group_tb">org_group_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0：正常 1：删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>chanid</td>\n<td>渠道id</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cityid</td>\n<td>城市商户编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>公司属性 0：普通运营公司 1：充电桩运营公司 2：自行车运营公司</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>balance</td>\n<td>余额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>longitude</td>\n<td>经度</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>latitude</td>\n<td>纬度</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>address</td>\n<td>地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>serverid</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>operatorid</td>\n<td>泊链运营商编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="park_account_tb">park_account_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0充值 1提现，2返现（已废弃）</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td>收费员账户</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>source</td>\n<td>来源，0：停车费（非预付），1：返现，2：泊车费，3：推荐奖，4：补交现金（已废弃），5：车场提现，6：停车宝排行榜周奖，7：追缴停车费，8：车主预付停车费，9：预付退款（预付超额），10：预付补缴（预付金额不足） 11：订单退款</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orderid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>withdraw_id</td>\n<td>提现记录编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthseg_id</td>\n<td>产生这笔流水所在的泊位段编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berth_id</td>\n<td>产生这笔流水所在的泊位编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>运营集团账号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>账目流水状态 0：正常 1：删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="park_anlysis_tb">park_anlysis_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>share_count</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>used_count</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>month_used_count</td>\n<td>月卡车占用数</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>time_used_count</td>\n<td>时租车占用数</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="park_charge_tb">park_charge_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td></td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0充值,1消费,2提现</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="park_log_tb">park_log_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td>主键id</td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>类型</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>content</td>\n<td>内容</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>operate_time</td>\n<td>操作时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>operate_user</td>\n<td>操作员</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td>备注</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>log_id</td>\n<td>车场日志编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>park_id</td>\n<td>记录对应的车场编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="park_ticket_tb">park_ticket_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>tnumber</td>\n<td>数量/每天</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>money</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>exptime</td>\n<td>有有效期(天)</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>haveget</td>\n<td>已领数量</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="park_token_tb">park_token_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>park_id</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>token</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>login_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>beat_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>server_ip</td>\n<td>所在服务器IP</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>source_ip</td>\n<td>客户端IP</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>local_id</td>\n<td>收费终端编号，支持一个车场有多个收费系统登录</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="parkuser_account_tb">parkuser_account_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>收费员登陆账号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0:充值，1提现</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>target</td>\n<td>目标(来源或去处)：0：银行卡（提现），1：支付宝（提现），2：微信（提现），3：停车宝，4：车主付停车费（非预付）或者打赏收费员，5：追缴停车费，6：车主预付停车费，7：预付退款（预付超额），8：预付补缴（预付金额不足）</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orderid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>withdraw_id</td>\n<td>提现记录编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>产生这笔流水所在的车场编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthseg_id</td>\n<td>产生这笔流水所在的泊位段编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berth_id</td>\n<td>产生这笔流水所在的泊位编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>运营集团账号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>账目流水状态 0：正常 1：删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="parkuser_cash_tb">parkuser_cash_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>收费员账号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td>现金金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orderid</td>\n<td>订单id</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0:岗亭收费 1：中央预支付收费 2：卡片操作</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctype</td>\n<td>0：收入，1：支出</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>target</td>\n<td>（该字段不适用type=1）0：停车费（非预付），1：预付停车费，2：预付退款（预付超额），3：预付补缴（预付不足），4：追缴停车费 5：卡片充值 6：卡片注销</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>card_account_id</td>\n<td>卡片充值流水编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>产生这笔流水所在的车场编号(索引)</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthseg_id</td>\n<td>产生这笔流水所在的泊位段编号(索引)</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berth_id</td>\n<td>产生这笔流水所在的泊位编号(索引)</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>运营集团账号(索引)</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>账目流水状态 0：正常 1：删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="parkuser_comment_tb">parkuser_comment_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comments</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="parkuser_income_anlysis_tb">parkuser_income_anlysis_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>主键（收费员账号、泊位编号、车场编号、运营集团编号）</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>prepay_cash</td>\n<td>现金预支付</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>add_cash</td>\n<td>现金补缴</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>refund_cash</td>\n<td>现金预支付退款</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pursue_cash</td>\n<td>现金追缴</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pfee_cash</td>\n<td>现金停车费（非预付）</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>prepay_epay</td>\n<td>电子支付预支付</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>add_epay</td>\n<td>电子支付预付补缴</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>refund_epay</td>\n<td>电子支付预付退款</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pursue_epay</td>\n<td>电子支付追缴</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pfee_epay</td>\n<td>电子停车费（非预付）</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>escape</td>\n<td>逃单未追缴的停车费</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>prepay_escape</td>\n<td>逃单未追缴的订单已预缴的金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>sensor_fee</td>\n<td>车检器停车费</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>统计时间</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>prepay_card</td>\n<td>刷卡预支付</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>add_card</td>\n<td>刷卡补缴</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>refund_card</td>\n<td>刷卡预支付退款</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pursue_card</td>\n<td>刷卡追缴</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pfee_card</td>\n<td>刷卡停车费（非预付）</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0：收费员 1：泊位 2：车场 3：运营集团</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>charge_card_cash</td>\n<td>卡片现金充值金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>return_card_count</td>\n<td>注销卡片数量</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>return_card_fee</td>\n<td>注销卡片退还的余额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>act_card_count</td>\n<td>激活卡片数量（卖卡数量）</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>act_card_fee</td>\n<td>激活卡片总面值（卖卡总面值）</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>reg_card_count</td>\n<td>开卡数量</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>reg_card_fee</td>\n<td>开卡总面值</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bind_card_count</td>\n<td>卡片绑定用户数</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="parkuser_message_tb">parkuser_message_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0推荐提醒 1 活动提醒,2周奖提醒</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>hasread</td>\n<td>是否已读：0未读，1已读</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>content</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>用户/收费员帐号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>title</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="parkuser_reward_tb">parkuser_reward_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>车主</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td>收费员</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>money</td>\n<td>金额</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ticket_id</td>\n<td>停车券编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="parkuser_work_record_tb">parkuser_work_record_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>start_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>end_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>worksite_id</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td>收费员id</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthsec_id</td>\n<td>泊位段</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>device_code</td>\n<td>设备编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>chanid</td>\n<td>合作方号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uuid</td>\n<td>唯一编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0已签到  1已签退</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>history_money</td>\n<td>上岗时，泊位段上的已预收金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>out_log</td>\n<td>签退小票内容</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>logon_state</td>\n<td>签入状态 0：正常 1：迟到</td>\n<td>smallint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>logoff_state</td>\n<td>签退状态 0：正常 1：早退</td>\n<td>smallint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>park_id</td>\n<td>记录车场编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="park_verify_tb">park_verify_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>审核人--车主</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isname</td>\n<td>0不准确，1准确</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>islocal</td>\n<td>位置是否准确，0不准确，1准确</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ispay</td>\n<td>收费是否准确 0不正确，1正确</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td>新建日期</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isresume</td>\n<td>描述是否正确 0不正确，1正确</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="peakalert_anlysis_tb">peakalert_anlysis_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>present</td>\n<td>在场车辆总数</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berths</td>\n<td>车位总数</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="phone_info_tb">phone_info_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td>名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>tele_phone</td>\n<td>分机号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>所属车场</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>park_phone</td>\n<td>车场主机号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>group_phone</td>\n<td>集团主机号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>monitor_id</td>\n<td>监控id</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_call</td>\n<td>是否有分机呼叫进来 1-是(需要切换到当前视频) 0-否/处理完毕</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>call_time</td>\n<td>分机呼叫时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>main_phone_type</td>\n<td>呼叫主机性质 1-集团 0-车场</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 1-正常 0-删除</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="price_assist_tb">price_assist_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0:每X小时X元</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>assist_unit</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>assist_price</td>\n<td></td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="price_tb">price_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>price</td>\n<td></td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0正常，1注销</td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>unit</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pay_type</td>\n<td>0:按时段，1：按次</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>b_time</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>e_time</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_sale</td>\n<td>是否打折 0否，1是</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>first_times</td>\n<td>首优惠时段，单位分钟</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>fprice</td>\n<td>首优惠价格</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>countless</td>\n<td>零头计费时长，单位分钟</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>free_time</td>\n<td>免费时长，单位:分钟</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>fpay_type</td>\n<td>超免费时长计费方式，1:免费 ，0:收费</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isnight</td>\n<td>夜晚停车，0:支持，1不支持</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isedit</td>\n<td>是否可编辑价格，目前只对日间按时价格生效,0否，1是，默认0</td>\n<td>integer</td>\n<td>0</td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>car_type</td>\n<td>0：通用，1：小车，2：大车</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_fulldaytime</td>\n<td>是否补足日间时长 0补全（默认）1不补全</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>total24</td>\n<td>24小时封顶价</td>\n<td>numeric</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>b_minute</td>\n<td>开始分钟</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>e_minute</td>\n<td>结束分钟</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td>记录车场编辑价格时的修改时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>price_id</td>\n<td>记录车场的价格编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_type_zh</td>\n<td>适配车场价格表中记录车辆类型的信息</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>describe</td>\n<td>车场价格描述信息记录</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>operate_type</td>\n<td>对数据的操作类型记录</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_sync</td>\n<td>记录数据信息是否同步</td>\n<td>integer</td>\n<td>0</td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>记录云端价格是否被删除</td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="product_package_tb">product_package_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>p_name</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>b_time</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>e_time</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remain_number</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>price</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bmin</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>emin</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>resume</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>old_price</td>\n<td>原价</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0:全天 1夜间 2日间 3月卡时间内优惠 4指定小时内免费</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>reserved</td>\n<td>是否固定车位：0不固定；1固定</td>\n</tr>\n<tr>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>limitday</td>\n<td>有效期到</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>favourable_precent</td>\n<td>优惠百分比 100免费，其它按百分比计算</td>\n<td>integer</td>\n<td>100</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>free_minutes</td>\n<td>免费时长 单位：分钟</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>out_favourable_precent</td>\n<td>超过了时间段享受0%－100%的优惠幅度</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>scope</td>\n<td>作用范围  默认0本车场有效  1父车场和旗下子车场有效</td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>card_id</td>\n<td>车场月卡编号记录</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td>车场月卡套餐修改时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>车场月卡套餐创建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>describe</td>\n<td>车场月卡信息描述记录</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>operate_type</td>\n<td>记录停车场对月卡套餐的操作类型</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_sync</td>\n<td>记录月卡套餐信息是否已经同步到车场</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>记录云端月卡套餐是否被删除</td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_type_id</td>\n<td>- car_type_tb 表ID</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>period</td>\n<td>缴费周期 月，季，半年，年</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="qr_code_tb">qr_code_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>wid</td>\n<td>worksitetb_ID</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td>收费员ID</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td>新建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0:NFC,1：收费员，2：车位，3:泊车员，4：车场 5：商户减免券 6:停车券</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0正常 1不可用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>code</td>\n<td></td>\n<td>character</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isuse</td>\n<td>是否使用 0未使用，1已使用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ticketid</td>\n<td>停车券id</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>score</td>\n<td>收费员发专用券消耗的积分</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isauto</td>\n<td>商户后台页面的二维码是不是要自动更新</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="qr_thirdpark_code">qr_thirdpark_code</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>park_id</td>\n<td>第三方车场编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>union_id</td>\n<td>第三主厂商编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>code</td>\n<td>二维码</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="recharge_tb">recharge_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>charge_type</td>\n<td>0:支付宝，1:微信支付，2:网上银行</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="recommend_tb">recommend_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>pid</td>\n<td>推荐人id</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>nid</td>\n<td>被推荐人的id</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>推荐目标：0：车主，1:车场，2：车主(虚拟账户)</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 0推荐中，1成功,3:推荐无效，用户绑定过微信公众号</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>日期</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>用户通过注册月卡会员注册车主时所在的车场id</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>money</td>\n<td>推荐奖励金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>openid</td>\n<td>微信公众号openid</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="recom_tb">recom_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>mobile</td>\n<td>用户，车场手机号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>车场收费员帐号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>类型 0用户 1车场</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 0推荐中，1成功</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>日期</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="red_packet_tb">red_packet_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>trandno</td>\n<td>订单号!!!!!</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>hbstate</td>\n<td>红包领取状态</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>random</td>\n<td>生成随机字符串</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="reg_anlysis_tb">reg_anlysis_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>bonus_num</td>\n<td>红包数</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>reg_num</td>\n<td>注册数（车牌有效）</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td>金额</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pv_number</td>\n<td>pv数</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>hit_number</td>\n<td>点击数</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>down_num</td>\n<td>下载数</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td>日期</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>atype</td>\n<td>类型：1今日头条，2传单红包，3节日红包，998直付红包，999收费员推荐，1000交易红包</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_num</td>\n<td>产生消费数</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="register_anlysis_tb">register_anlysis_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>reg_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>reg_count</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>carnumber_count</td>\n<td>有车牌的注册用户</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="remain_berth_tb">remain_berth_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthseg_id</td>\n<td>泊位段编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td>泊位剩余数量</td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td>数据更新时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0：正常 1：删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>total</td>\n<td>泊位总数</td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="reward_account_tb">reward_account_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>score</td>\n<td>积分</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0:充值积分 1：消费积分</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>target</td>\n<td>0:打赏 1：订单红包 2：专用停车券 3:停车宝充值</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>reward_id</td>\n<td>打赏记录id</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orderticket_id</td>\n<td>订单红包id</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ticket_id</td>\n<td>停车券id</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="role_auth_tb">role_auth_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>auth</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>role_id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="sensor_online_anlysis_tb">sensor_online_anlysis_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>统计时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>online</td>\n<td>在线车检器数量</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>total</td>\n<td>总车检器数量</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="session_cache">session_cache</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>sessionId</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>sessionValue</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="share_log_tb">share_log_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>s_number</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="shop_account_tb">shop_account_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>shop_id</td>\n<td>商户编号</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>shop_name</td>\n<td>商户名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ticket_limit</td>\n<td>减免劵(小时)</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ticketfree_limit</td>\n<td>全免劵(张)</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ticket_money</td>\n<td>减免金额(元)</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>operate_time</td>\n<td>充值时间/减免劵生成时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>park_id</td>\n<td>车场编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>operator</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>strid</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>add_money</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>operate_type</td>\n<td>1-续费2回收充值</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="shop_tb">shop_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td>商户名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>address</td>\n<td>地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>mobile</td>\n<td>电话</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>phone</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ticket_limit</td>\n<td>停车券额度</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>description</td>\n<td>描述</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0:正常 1：不可用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>创建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ticketfree_limit</td>\n<td>全免券的张数上限</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ticket_type</td>\n<td>减免劵类型 1-减免时长 2-减免金额</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ticket_money</td>\n<td>减免金额</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>default_limit</td>\n<td>默认显示额度</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>discount_percent</td>\n<td>商户折扣百分比</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>discount_money</td>\n<td>小时劵单价 每小时/元</td>\n<td>numeric</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>validite_time</td>\n<td>有效期/小时</td>\n<td>integer</td>\n<td>24</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ticket_unit</td>\n<td>减免劵单位 1-分钟 2-小时 3-天 4-元</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>free_money</td>\n<td>全免劵单价 每张/元</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>hand_input_enable</td>\n<td>是否可手输额度 1 支持 0不支持</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="sim_tb">sim_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>pin</td>\n<td>PIN码</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>mobile</td>\n<td>手机号码</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>money</td>\n<td>首次充值额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>入库时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>limit_time</td>\n<td>套餐过期时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>allot_time</td>\n<td>分配时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>creator_id</td>\n<td>入库人</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>绑定的设备类型 0：未绑定 1：POS机 2：基站</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>device_id</td>\n<td>设备编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>运营集团编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 0：正常 1：禁用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="sites_tb">sites_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uuid</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>voltage</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>longitude</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>latitude</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>创建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>heartbeat</td>\n<td>心跳时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>address</td>\n<td>基站地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cityid</td>\n<td>城市商户编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td>基站名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>基站状态 0:故障 1:正常 2:删除</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>0：正常 1：禁用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>运营集团编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="sites_voltage_tb">sites_voltage_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>voltage</td>\n<td>基站电压</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>录入时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>site_id</td>\n<td>基站编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="slow_alert_tb">slow_alert_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>url</td>\n<td>地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>start_time</td>\n<td>接口开始时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>end_time</td>\n<td>接口结束时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>local_host</td>\n<td>服务器地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="sub_account_tb">sub_account_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>运营集团编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td>金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>sub_orderid</td>\n<td>第三方的订单号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>记录时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>card_account_id</td>\n<td>卡片充值流水编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>车主编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>充值方式 0：微信公众号 1：微信客户端 2：支付宝客户端</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="switch_line_tb">switch_line_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>end_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="sync_info_pool_tb">sync_info_pool_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>table_name</td>\n<td>表名</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>table_id</td>\n<td>表记录中的id</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>operate</td>\n<td>0添加 1修改2删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>同步状态  0 未同步 1已同步</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="thirdpay_order_tb">thirdpay_order_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>out_trade_no</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>id</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>order_id_local</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="ticket_tb">ticket_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>limit_day</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>money</td>\n<td>减免时长</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0-未使用,1-已使用，2-回收作废</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>utime</td>\n<td>使用时间</td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>umoney</td>\n<td>作用金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0普通停车券，1专用停车券 2微信专用打折券,3减时券,4全免券,5减免金额劵</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orderid</td>\n<td>订单id</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bmoney</td>\n<td>0普通停车券，1专用停车券 2微信专用打折券,3减时券,4全免券,5减免金额劵</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>wxp_orderid</td>\n<td>微信公众号订单号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>shop_id</td>\n<td>商户id</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>resources</td>\n<td>来源 0停车宝，1购买</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_back_money</td>\n<td>车主购买的停车券，未使用过期退款，0未退款，1已退款</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pmoney</td>\n<td>购买券车主支付金额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>need_sync</td>\n<td>-1默认不需要同步   0需要同步  1同步完成</td>\n<td>integer</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ticket_id</td>\n<td>车场减免券编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>operate_user</td>\n<td>车场收费员（可以是车场收费员编号）</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state_zh</td>\n<td>减免券状态</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td>车牌编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type_zh</td>\n<td>类型</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td>备注</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="ticket_uion_tb">ticket_uion_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>req_uin</td>\n<td>求合体车主</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>res_uin</td>\n<td>响应合体车主</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>req_tid</td>\n<td>求合体车主券编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>res_tid</td>\n<td>响应合体车主券编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>req_time</td>\n<td>开始时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>res_time</td>\n<td>响应时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>win_uin</td>\n<td>获胜者，-1失败，其它是获胜车主账户</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>合体状态，0等待合体，1合体完成</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>req_money</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>res_money</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="ticket_view">ticket_view</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>limittime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="tingchebao_account_tb">tingchebao_account_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0:充值，1消费</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>utype</td>\n<td>0：停车代金券，1：支付宝服务费，2：微信服务费，3：车场返现，4：车主返现 5：泊车费 6：微信充值 7：支付宝充值 8：提现</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orderid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>withdraw_id</td>\n<td>提现记录编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>online_orderid</td>\n<td>第三方支付的订单号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>操作人账号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="tmp_p4top5_primarykey">tmp_p4top5_primarykey</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>table_type</td>\n<td>表 0：卡片表 1：车场表 2：泊位段表 3：泊位表 4：订单表 5：收费员表 6：车牌表 7：工作组表 8：工作组和泊位段对应表 9：工作组和收费员对应表  11：基站表 12：车检器表 13：用户表（车主）</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>p4_id</td>\n<td>p4表主键</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>p5_id</td>\n<td>p5表主键</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>运营集团编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="transfer_url_tb">transfer_url_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>url</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td>创建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0：正在推荐，1：推荐成功</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="union_park_tb">union_park_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>company_name</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>phone</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>address</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>resume</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>longitude</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>latitude</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>parking_type</td>\n<td>-- 车位类型，0地面，1地下，2占道 3室外 4室内 5室内外</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>parking_total</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>share_number</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>money</td>\n<td></td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>停车场类型，0：付费，1免费</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isfixed</td>\n<td>0：未校验，1：已校验，2：申请校验，-1：一次未通过，-2：二次未通过，-3：三次未通过</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>epay</td>\n<td>电子支付，0不支持，1支持</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remarks</td>\n<td>备注</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>empty</td>\n<td>空闲车位数</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>union_state</td>\n<td>0 未上传 1已上传在使用 2已上传未审核 3已上传并禁用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>monthlypay</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="user_account_tb">user_account_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>车主账户号，根据手机号查询</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0:充值，1:消费,</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td>标题</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pay_type</td>\n<td>0余额，1支付宝，2微信，3网银，4余额+支付宝,5余额+微信,6余额+网银 ,7停车券,8活动奖励,9微信公众号，10微信公众号+余额，11微信打折券,12预支付返款 13停车券退款 14：停车宝</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td>支付给收费员的账户</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>target</td>\n<td>0:普通支付，1直接支付给收费员，没有订单  2购买停车券 3停车券退款 4：账户余额退回第三方支付</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orderid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="user_friend_tb">user_friend_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>buin</td>\n<td>发起人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>euin</td>\n<td>好友</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>atype</td>\n<td>加入原因 0打灰机</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_add_hx</td>\n<td>是否加入环信好友，0否，1是</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="user_info_tb">user_info_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>nickname</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>password</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>strid</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>sex</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>email</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>phone</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>mobile</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>address</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>resume</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>reg_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>logon_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>logoff_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>online_flag</td>\n<td>21:离线，22:在线，23:在岗，(泊车员状态)24:接车，25:取车，26:小休</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>auth_flag</td>\n<td>16：巡查员 17：开卡员</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>balance</td>\n<td></td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>0:正常，1：禁用，2：待审核，3：待补充，4：待跟进，5无价值</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>recom_code</td>\n<td>推荐码，车场停车员的帐号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>md5pass</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cid</td>\n<td>用户消息帐号，用于消息推送</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>department_id</td>\n<td>部门ID</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>media</td>\n<td>媒体来源</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>isview</td>\n<td>是否可以收费，0不可以，1可以收费</td>\n<td>integer</td>\n<td>1</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>collector_pics</td>\n<td>收费员上传的图片个数</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>collector_auditor</td>\n<td>审核人员</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>imei</td>\n<td>手机串号</td>\n<td>character</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>client_type</td>\n<td>0:android,1:ios</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>version</td>\n<td>版本号</td>\n<td>character</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>wxp_openid</td>\n<td>微信公众号openid</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>wx_name</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>wx_imgurl</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>shop_id</td>\n<td>商户的id</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>credit_limit</td>\n<td>信用额度</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_auth</td>\n<td>是否认证通过，0否，1是</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>reward_score</td>\n<td>打赏积分</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>firstorderquota</td>\n<td>首单额度</td>\n<td>numeric</td>\n<td>8</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>rewardquota</td>\n<td>打赏最高限券</td>\n<td>numeric</td>\n<td>2</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>recommendquota</td>\n<td>推荐奖额度   默认5块</td>\n<td>numeric</td>\n<td>5</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ticketquota</td>\n<td>-- 默认-1  不限制</td>\n<td>numeric</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>hx_name</td>\n<td>环信账户</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>hx_pass</td>\n<td>环信密码</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>role_id</td>\n<td>角色ID</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>order_hid</td>\n<td>0否，1设置</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>chanid</td>\n<td>渠道编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>集团id</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>creator_id</td>\n<td>创建者编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cityid</td>\n<td>城市商户编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>union_state</td>\n<td>上传到泊链状态：0 未上传 1已上传并使用 2已上传未审核 3已上传并禁用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>upload_union_time</td>\n<td>上传时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>member_id</td>\n<td>停车场会员编号记录</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>operate_type</td>\n<td>记录操作类型1：添加，2：修改，3：删除</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_sync</td>\n<td>是否已同步，0：未同步，1已同步</td>\n<td>integer</td>\n<td>0</td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>user_id</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="user_liuyan_tb">user_liuyan_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>fuin</td>\n<td>发消息人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>message</td>\n<td>留言内容</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_read</td>\n<td>是否已读 0未读，1已读</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>tuin</td>\n<td>接收人</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="user_local_tb">user_local_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td>收费员账号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>distance</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_onseat</td>\n<td>是否在位，0否，1是</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>lat</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>lon</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td>h</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="user_log_tb">user_log_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>logs</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>100:创建停车场，101：修改停车场，102：删除停车场，201：修改管理员，202：创建停车员，203：修改停车员，204：禁用停车员，205：删除停车员，206：修改密码，207：添加价格，208：修改价格，209：删除价格，210：添加包月产品，211：删除包月，300：添加市场专员，301修改市场专员，302删除市场专员，400：添加商圈，401：编辑商圈，402：删除商圈</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td>登录帐号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="user_message_tb">user_message_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0 支付失败提醒1 红包提醒2 自动支付提醒3 注册提醒 4停车入场提醒5活动提醒6 推荐停车员 7收款提醒 8充值消息 9退款消息</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>hasread</td>\n<td>是否已读：0未读，1已读</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>content</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>用户/收费员帐号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>title</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="user_payaccount_tb">user_payaccount_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>车主账号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0:支付宝，1:微信</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>account</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="user_pic_tb">user_pic_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>pic_url</td>\n<td>头像地址</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 0在用 1备用</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td>上传日期</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>用户账号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>driver_year</td>\n<td>驾龄</td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>driver_pic</td>\n<td>证件照片</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>utime</td>\n<td>更新时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>resume</td>\n<td>批注</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="user_preaddfriend_tb">user_preaddfriend_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>buin</td>\n<td>自己账号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>euin</td>\n<td>车友账号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>atype</td>\n<td>好友来源 1同车场车友 2....</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>resume</td>\n<td>好友类型说明</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td>添加日期</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>utime</td>\n<td>更新日期</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="user_profile_tb">user_profile_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>low_recharge</td>\n<td>帐号余额最低冲值提醒</td>\n<td>integer</td>\n<td>30</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>voice_warn</td>\n<td>自动结算</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>auto_cash</td>\n<td>自动结算</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>enter_warn</td>\n<td>入场提醒</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>limit_money</td>\n<td>最高自动支付金额,0不自动支付,-1总是自动支付</td>\n<td>integer</td>\n<td>&#39;-100&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bolink_limit</td>\n<td>泊链限额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="user_role_tb">user_role_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>role_name</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td></td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>oid</td>\n<td>组织类型</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>resume</td>\n<td>说明</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>0 管理员 其它成员</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>adminid</td>\n<td>所属管理员账户</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_inspect</td>\n<td>是否有巡查功能  默认0无  1有</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_collector</td>\n<td>是否有收费功能 0：无 1：有</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_opencard</td>\n<td>是否有激活卡片功能 0：无 1：有</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="user_session_tb">user_session_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>token</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>version</td>\n<td>客户端版本号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>集团编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="user_worksite_tb">user_worksite_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>worksite_id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>收费员账号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="verification_code_tb">verification_code_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>verification_code</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="vip_apply_tb">vip_apply_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>mobile</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>address</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cname</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_image</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="vip_tb">vip_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bcount</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>acttotal</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>atotal</td>\n<td></td>\n<td>numeric</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>e_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="visit_info_tb">visit_info_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>contacts</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>address</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>visit_content</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="withdrawer_tb">withdrawer_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td></td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>amount</td>\n<td></td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>-- 0:等待审核，2已审核，3已到帐，4提现失败</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td>处理日期</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>acc_id</td>\n<td>帐号编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>操作人账号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>wtype</td>\n<td>1个人提现，0公司提现  2对公提现</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td>备注</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>groupid</td>\n<td>运营集团编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>cityid</td>\n<td>城市商户编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="work_berthsec_tb">work_berthsec_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>berthsec_id</td>\n<td>泊位段编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>work_group_id</td>\n<td>工作组编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>是否已签到 0：否 1: 是</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>0 正常 1 已删除</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>inspect_group_id</td>\n<td>巡查组id</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="work_detail_tb">work_detail_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>workid</td>\n<td>签到表编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uid</td>\n<td>收费员编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>orderid</td>\n<td>进场订单编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>bid</td>\n<td>泊位编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>berthsec_id</td>\n<td>泊位段编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="work_employee_tb">work_employee_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>work_group_id</td>\n<td>工作组编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>employee_id</td>\n<td>收费员编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>是否删除 0：否 1 ：是</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="work_group_tb">work_group_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uuid</td>\n<td>唯一编号</td>\n<td>character</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>workgroup_name</td>\n<td>工作组名称</td>\n<td>character</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>company_id</td>\n<td>运营公司</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_active</td>\n<td>是否可用 0 否1是</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>chanid</td>\n<td>合作方号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="work_inspector_tb">work_inspector_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>inspect_group_id</td>\n<td>巡查组编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>inspector_id</td>\n<td>巡查员编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>是否删除 0：否 1 ：是</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="work_time_tb">work_time_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>b_hour</td>\n<td>上班(小时)</td>\n<td>smallint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>b_minute</td>\n<td>上班（分钟）</td>\n<td>smallint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>e_hour</td>\n<td>下班(小时)</td>\n<td>smallint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>e_minute</td>\n<td>下班(分钟)</td>\n<td>smallint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>role_id</td>\n<td>角色编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_delete</td>\n<td>0：正常 1：删除</td>\n<td>smallint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="wxp_user_tb">wxp_user_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>openid</td>\n<td>微信公众号用户openid</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>用户ID</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>balance</td>\n<td>余额</td>\n<td>numeric</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td>车牌号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>union_state</td>\n<td>是否已同步到泊链 0未同步 1已同步</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="zld_black_tb">zld_black_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>utime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>车主账户</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 0正常，1漂白</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>remark</td>\n<td>原因</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>comid</td>\n<td>车场编号</td>\n<td>bigint</td>\n<td>&#39;-1&#39;</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>car_number</td>\n<td>车牌号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>black_uuid</td>\n<td>收费系统唯一编号</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>operator</td>\n<td>操作人</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="zld_game_tb">zld_game_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>scroe</td>\n<td></td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>iswin</td>\n<td>是否获胜 0否 1是</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>类型，0普通红包，1打折红包</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>tid</td>\n<td>券编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="zld_organize_tb">zld_organize_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td>名称</td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pid</td>\n<td>上级编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>type</td>\n<td>类型 ---zld_orgtype_tb</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="zld_orgtype_tb">zld_orgtype_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>name</td>\n<td></td>\n<td>character varying</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>pid</td>\n<td>父级组织类型编号</td>\n<td>bigint</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>state</td>\n<td>状态 0：正常 1：禁用</td>\n<td>integer</td>\n<td>0</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>create_time</td>\n<td>创建时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>delete_time</td>\n<td>删除时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>update_time</td>\n<td>最后一次更新时间</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>sort</td>\n<td>排序</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>creator_id</td>\n<td>创建者编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>deletor_id</td>\n<td>删除者编号</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n<h2 id="zld_protocol_tb">zld_protocol_tb</h2>\n<table>\n<thead>\n<tr>\n<th>column</th>\n<th>comment</th>\n<th>type</th>\n<th>default</th>\n<th>constraints</th>\n<th>values</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>id</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td>NOT NULL</td>\n<td></td>\n</tr>\n<tr>\n<td>uin</td>\n<td>车主账户</td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>ctime</td>\n<td></td>\n<td>bigint</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>is_remind</td>\n<td>是否再次提醒 0不提醒 ，1提醒</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>agree</td>\n<td>是否同意 0不同意，1同意</td>\n<td>integer</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n</tbody>\n</table>\n'},function(t,n,d){t.exports='<h1 id="-">主题定制和二次开发</h1>\n<h2 id="-">主题定制：</h2>\n<p>主题定制主要是用户根据自己的特点定制一套颜色主题。\nparkingos前端是一个以vue+ElementUI为基础开发的开源软件\n主题定制上我们只要按照element的方式进行定制就可以了</p>\n<p>主题定制是在工程parkingos_cloud_vue</p>\n<p>1.首先安装element-theme主题定制工具</p>\n<pre><code>npm i element-theme -D\n\n</code></pre><p>然后打开src/styles/element-variables.scss文件</p>\n<pre><code>/* Colors\n-------------------------- */\n$--color-white: #fff !default;\n$--color-black: #000 !default;\n\n$--color-primary: #2D3A4B !default;\n$--color-primary-light-1: mix($--color-white, $--color-primary, 10%) !default; /* 53a8ff */\n$--color-primary-light-2: mix($--color-white, $--color-primary, 20%) !default; /* 66b1ff */\n$--color-primary-light-3: mix($--color-white, $--color-primary, 30%) !default; /* 79bbff */\n$--color-primary-light-4: mix($--color-white, $--color-primary, 40%) !default; /* 8cc5ff */\n$--color-primary-light-5: mix($--color-white, $--color-primary, 50%) !default; /* a0cfff */\n$--color-primary-light-6: mix($--color-white, $--color-primary, 60%) !default; /* b3d8ff */\n$--color-primary-light-7: mix($--color-white, $--color-primary, 70%) !default; /* c6e2ff */\n$--color-primary-light-8: mix($--color-white, $--color-primary, 80%) !default; /* d9ecff */\n$--color-primary-light-9: mix($--color-white, $--color-primary, 90%) !default; /* ecf5ff */\n\n\n$--color-success: #67c23a !default;\n$--color-warning: #e6a23c !default;\n$--color-danger: #f56c6c !default;\n$--color-info: #909399 !default;\n\n$--color-success-light: mix($--color-white, $--color-success, 80%) !default;\n$--color-warning-light: mix($--color-white, $--color-warning, 80%) !default;\n$--color-danger-light: mix($--color-white, $--color-danger, 80%) !default;\n$--color-info-light: mix($--color-white, $--color-info, 80%) !default;\n\n$--color-success-lighter: mix($--color-white, $--color-success, 90%) !default;\n$--color-warning-lighter: mix($--color-white, $--color-warning, 90%) !default;\n$--color-danger-lighter: mix($--color-white, $--color-danger, 90%) !default;\n$--color-info-lighter: mix($--color-white, $--color-info, 90%) !default;\n\n$--color-text-primary: #303133 !default;\n$--color-text-regular: #606266 !default;\n$--color-text-secondary: #909399 !default;\n$--color-text-placeholder: #c0c4cc !default;\n\n/* parkingOS\n--------------------------*/\n\n$--parkingos-header-background: #2d3a4b !default;\n$--parkingos-sidemenu-background: #545c64 !default;\n$--parkingos-sidemenu-background-9: #434A50 !default; /* ecf5ff */\n$--parkingos-datacenter-background: #393a3e !default;\n...\n\n</code></pre><p>其中：</p>\n<p>$--color-primary  //是主题色</p>\n<p>$--color-text-primary //文字颜色</p>\n<p>$--parkingos-header-background  //顶部banner的颜色  </p>\n<p>$--parkingos-sidemenu-background  //左边导航底色</p>\n<p>$--parkingos-sidemenu-background-9 // 左边导航选中颜色</p>\n<p>$--parkingos-datacenter-background // 数据中心底色</p>\n<p>其他具体组件的主题可以在相应的位置修改,比如button</p>\n<pre><code>/* Button\n-------------------------- */\n$--button-font-size: 14px !default;\n$--button-font-weight: $--font-weight-primary !default;\n$--button-border-radius: $--border-radius-base !default;\n$--button-padding-vertical: 12px !default;\n$--button-padding-horizontal: 20px !default;\n\n$--button-medium-font-size: 14px !default;\n$--button-medium-border-radius: $--border-radius-base !default;\n$--button-medium-padding-vertical: 10px !default;\n$--button-medium-padding-horizontal: 20px !default;\n\n$--button-small-font-size: 12px !default;\n$--button-small-border-radius: #{$--border-radius-base - 1} !default;\n$--button-small-padding-vertical: 9px !default;\n$--button-small-padding-horizontal: 15px !default;\n\n$--button-mini-font-size: 12px !default;\n$--button-mini-border-radius: #{$--border-radius-base - 1} !default;\n$--button-mini-padding-vertical: 7px !default;\n$--button-mini-padding-horizontal: 15px !default;\n\n$--button-default-color: $--color-text-regular !default;\n$--button-default-fill: $--color-white !default;\n$--button-default-border: $--border-color-base !default;\n\n$--button-disabled-color: $--color-text-placeholder !default;\n$--button-disabled-fill: $--color-white !default;\n$--button-disabled-border: $--border-color-lighter !default;\n\n$--button-primary-border: $--color-primary !default;\n$--button-primary-color: $--color-white !default;\n$--button-primary-fill: $--color-primary !default;\n\n$--button-success-border: $--color-success !default;\n$--button-success-color: $--color-white !default;\n$--button-success-fill: $--color-success !default;\n\n$--button-warning-border: $--color-warning !default;\n$--button-warning-color: $--color-white !default;\n$--button-warning-fill: $--color-warning !default;\n\n$--button-danger-border: $--color-danger !default;\n$--button-danger-color: $--color-white !default;\n$--button-danger-fill: $--color-danger !default;\n\n$--button-info-border: $--color-info !default;\n$--button-info-color: $--color-white !default;\n$--button-info-fill: $--color-info !default;\n\n$--button-hover-tint-percent: 20% !default;\n$--button-active-shade-percent: 10% !default;\n</code></pre><p>修改完运行主题编译命令：</p>\n<p>根目录下运行</p>\n<pre><code>et -b -c src/styles/element-variables.scss\n\n</code></pre><p>然后重新运行build 部署parkingos_cloud_vue,查看结果</p>\n<p>在本地调试的时候可以在parkingos_cloud_vue在根目录下分别运行：</p>\n<pre><code>et -b -c src/styles/element-variables.scss\n\n</code></pre><pre><code>npm run dev\n\n</code></pre><p>来实时调试</p>\n<h2 id="-icon-">导航icon定制：</h2>\n<p>parkingos云平台的logo和导航图标也是可以定制的。\nlogo和图标都是用iconfont来实现的，推荐使用\n阿里的<a href="http://www.iconfont.cn/">iconfont</a>平台来进行定制.\n我们的iconfont名字命名如下图所示：\n<img src="'+d(51)+'" ></p>\n<p>在iconfont平台上新建项目，然后新建上面13个icon(命名要一样)，\n然后点击下载到本地，解压。然后将里面的：</p>\n<p>iconfont.css</p>\n<p>iconfont.eot</p>\n<p>iconfont.svg</p>\n<p>iconfont.ttf</p>\n<p>iconfont.woff</p>\n<p>这几个文件放到parkingos_cloud_vue/src/assets/下</p>\n<p>覆盖即可。</p>\n<h2 id="-">二次开发：</h2>\n<p>parkingos 后端采用标准的spring-mvc 开发\nhttp请求入口包是：</p>\n<p>parkingos.com.bolink.actions</p>\n<p>二次开发的时候到这里修改或者添加spring controller 即可。</p>\n<p>parkingos 前端采用vue+ElementUI作为框架开发。</p>\n<p>要添加和修改页面都非常简单\n比如添加页面只要修改底下几个文件夹即可</p>\n<pre><code>parkingos-cloud-vue\n│\n└───src\n    │   \n    │   \n    └───pages(新添加一个.vue文件作为页面)\n    │   \n    │   \n    └───routes.js(路由配置文件里面添加新的路由j)\n\n</code></pre><p>然后再在src/pages里的HomeCloud开头的vue文件修改添加相应的导航，\n使菜单点击到对应的vue即可</p>\n<p>vue的编写可以直接参考<a href="https://vuejs.org/">vue官网</a></p>\n'},function(t,n){t.exports='<h1 id="-">前端项目架构</h1>\n<p>parkingos_cloud_vue 项目\n前端项目使用vue+element-ui技术打造。</p>\n<h2 id="-">项目总结构：</h2>\n<pre><code>parkingos-cloud-vue\n│\n└───build(webpack 构建相关，具体可看webpack文档，完全标准化写法)\n│   \n│   \n└───config(构建环境变量配置，具体可看webpack文档，完全标准化写法)\n│   \n│   \n└───src(项目源码)\n│   \n│   \n└───theme（主题包，element主题包，可以通过修改这里的代码替换主题）\n│   \n│   \n└───index.html(主页模版)\n\n</code></pre><h2 id="-">项目源码结构：</h2>\n<pre><code>parkingos-cloud-vue\n│\n└───src\n    │\n    └───api(api地址配置及一些公用方法)\n    │   \n    │   \n    └───assets(静态资源如图片等存放目录)\n    │   \n    │   \n    └───common(公共css样式和js类)\n    │   \n    │   \n    └───components（vue公共组件，比如滚动字，公共表格，公共高级搜索等）\n    │   \n    │   \n    └───pages(具体页面，比如登陆/首页/统计等各个页面，其中LoginCloud.vue是登陆页面，登陆成果根据权限跳转其他页面)\n    │   \n    │   \n    └───main.js(整个工程的入口js)\n    │   \n    │   \n    └───App.vue(vue组建入口，main.js引入)    \n    │   \n    │   \n    └───routes.js(路由文件，vue-router的标准写法，具体可看官方文档)\n\n</code></pre>'},function(t,n){t.exports='<h1 id="-">后端模块列表</h1>\n<p>要运行云平台，需要运行以下几个模块:</p>\n<p>1.parkingos_cloud_vue_server(云平台后台模块)</p>\n<p>2.parkingos_cloud_api(sdk上报接口服务)</p>\n<p>3.parkingos_anlysis(统计分析模块)</p>\n<h2 id="-">首先安装数据库依赖</h2>\n<h3 id="1-mongodb">1.mongodb</h3>\n<p>  mongodb在parkingos中被用来存储图片（包括出入场图片）\n  mongodb在社区应用非常广泛，这里就不具体描述安装过程，推荐用户可以去<a href="https://www.mongodb.com/">mongodb官网</a>或者<a href="http://www.runoob.com/mongodb/mongodb-tutorial.html">mongodb中文文档</a>查看</p>\n<h3 id="2-postgres">2.postgres</h3>\n<p>  postgres是一个专业的关系型数据库，parkingos的数据库采用postgres来作为数据存储。postgres也是一个被广泛应用的关系型数据库。\n  社区文档非常全面。这里也不在描述安装过程。用户可以到<a href="https://www.postgresql.org">官网</a>或者<a href="http://www.postgres.cn/docs/9.6/index.html">postgres中文</a>文档网站参考</p>\n<h2 id="java-maven-">java包管理工具maven配置。</h2>\n<p>  parkingos后端统一采用java语言开发，使用maven来管理jar包依赖。maven包的安装这里就不描述了，需要的用户可以自行搜索，非常简单。\n  安装完maven后，需要增加泊链联盟的私有仓库来获取一些私有jar包。设置自己的maven根目录下的conf/settings.xml，增加一个profile和镜像:</p>\n<p>  mirror:</p>\n<pre><code>  &lt;mirror&gt;\n      &lt;id&gt;local_mirror&lt;/id&gt;\n      &lt;mirrorOf&gt;*&lt;/mirrorOf&gt;\n      &lt;name&gt;local_mirror&lt;/name&gt;\n      &lt;url&gt;http://106.75.7.55:8081/nexus/content/groups/public/&lt;/url&gt;\n  &lt;/mirror&gt;\n</code></pre><p>  profile:</p>\n<pre><code>  &lt;profile&gt;\n     &lt;id&gt;nexus&lt;/id&gt;\n     &lt;repositories&gt;\n             &lt;repository&gt;\n              &lt;id&gt;thirdparty&lt;/id&gt;\n              &lt;url&gt;http://106.75.7.55:8081/nexus/content/repositories/thirdparty/&lt;/url&gt;\n      &lt;/repository&gt;\n      &lt;repository&gt;\n              &lt;id&gt;local_nexus_releases&lt;/id&gt;\n              &lt;url&gt;http://106.75.7.55:8081/nexus/content/repositories/releases/&lt;/url&gt;\n      &lt;/repository&gt;\n      &lt;repository&gt;\n              &lt;id&gt;local_nexus_snapshots&lt;/id&gt;\n              &lt;url&gt;http://106.75.7.55:8081/nexus/content/repositories/snapshots/&lt;/url&gt;\n      &lt;/repository&gt;\n     &lt;/repositories&gt;\n&lt;/profile&gt;\n\n</code></pre><h2 id="-">云平台后台服务器模块安装</h2>\n<p>首先下载parkingos_cloud_vue_server工程(需要安装git)</p>\n<pre><code>git clone https://github.com/ParkingOS/parkingos_cloud_vue_server.git\n</code></pre><p>首先创建数据库：</p>\n<p>zldetc,然后创建用户名和密码，然后把postgres/pgv2.0.20180115.dump导入zldetc数据库。然后修改数据库配置：</p>\n<p>src\\main\\resources\\proxool.xml:</p>\n<pre><code>&lt;alias&gt;master&lt;/alias&gt;\n  &lt;driver-url&gt;**************************************************************  &lt;driver-class&gt;org.postgresql.Driver&lt;/driver-class&gt;\n  &lt;driver-properties&gt;\n      &lt;property name=&quot;user&quot; value=&quot;postgres&quot; /&gt;\n      &lt;property name=&quot;password&quot; value=&quot;1234&quot; /&gt;\n  &lt;/driver-properties&gt;\n</code></pre><p>修改基本配置文件：\nsrc\\main\\resources\\config.properties:</p>\n<pre><code>MONGOADDRESS=s.zldmongodb.com:27017   //mongodb地址\nIMAGEURL=http://**************:8080/cloud  //本服务地址,比如左边是工程部署到**************:8080\nUNIONIP=https://s.bolink.club/unionapi/ //泊链的api地址\nUNIONVALUE=yun   //填yun就可以\nUNIONADMIN=1001   //填1001就行\nSHOPOID=10      //不用修改\nTICKETURL=http://test/yun.bolink.club/zld/qr/c/   //api服务地址\nFIXCODEURL=http://**************:8080/cloud/fixcode/useticket?code=  //本服务地址,比如左边是工程部署到**************:8080\n</code></pre><p>修改完直接在根目录下执行mvn package\n然后将在target目录下生成的war包放到自己的tomcat即可。</p>\n<h2 id="sdk-api-">sdk上报api服务安装</h2>\n<p>这个项目主要是用来接收从车场sdk发送到云平台里的服务，上面的TICKETURL即指这个服务地址\napi服务地址在：\n<a href="https://github.com/ParkingOS/ParkingOS_cloud">https://github.com/ParkingOS/ParkingOS_cloud</a>\n执行</p>\n<pre><code>git clone https://github.com/ParkingOS/ParkingOS_cloud.git\n</code></pre><p>下载工程然后打开</p>\n<p>parkingos_cloud_api目录</p>\n<p>修改数据库配置成刚安装的数据库配置\nsrc\\main\\resources\\proxool.xml:</p>\n<pre><code>&lt;alias&gt;master&lt;/alias&gt;\n  &lt;driver-url&gt;**************************************************************  &lt;driver-class&gt;org.postgresql.Driver&lt;/driver-class&gt;\n  &lt;driver-properties&gt;\n      &lt;property name=&quot;user&quot; value=&quot;postgres&quot; /&gt;\n      &lt;property name=&quot;password&quot; value=&quot;1234&quot; /&gt;\n  &lt;/driver-properties&gt;\n</code></pre><p>修改配置文件\nsrc\\main\\filters\\yun.properties</p>\n<pre><code>server.domain=yun.bolink.club  //api域名地址\nserver.unionIp=https://s.bolink.club/unionapi/ //泊链api地址\ncloud.unionId=200002   //泊链联盟id\ncloud.unionKey=EA2  //泊链密钥\nwx.redirect_url=yun.bolink.club  //api域名地址\nwx.appId=xxx     //微信appid\nwx.appSecret=yyy   //微信appkey\nlog.dir=${catalina.home}/logs/upp.log\nwx.local=0\npg.port=5432  //数据库端口\n\n</code></pre><p>修改完直接在根目录下执行mvn package\n然后将在target目录下生成的war包放到自己的tomcat即可。\n这时候已经可以通过泊链的sdk把数据上传到自己的私有云,在sdk初始化的时候填写</p>\n<p>cloud_addr： api服务地址</p>\n<p>cport： api服务端口</p>\n<p>具体的可以看文档：\n<a href="https://www.kancloud.cn/bolink/sdk/465620">sdk对接文档</a></p>\n<h3 id="-">统计分析模块安装</h3>\n<p>统计分析模块是用来统计车场相关统计数据的一个模块。\n这个模块在<a href="https://github.com/ParkingOS/ParkingOS_cloud下的parkingos_anlysis目录下的一个">https://github.com/ParkingOS/ParkingOS_cloud下的parkingos_anlysis目录下的一个</a>\nanlysispark.rar包。\n解压anlysispark.rar</p>\n<p>到这里搭建云平台所需要的后端服务都已经搭建完毕</p>\n'},function(t,n){t.exports='<h1 id="-">云平台后台项目</h1>\n<p>parkingos是一个前后端分离的项目，前台是用vue+element-ui搭建的。</p>\n<p>在开始搭建之前，我们需要安装nodejs和npm</p>\n<p>Node.js 就是运行在服务端的 JavaScript。\nnpm 是javascript包管理工具。\n这里我们不展开讲，可以参考</p>\n<p><a href="http://www.runoob.com/nodejs/nodejs-tutorial.html">nodejs和npm的安装文档</a></p>\n<p>然后下载项目代码</p>\n<pre><code>git clone https://github.com/ParkingOS/parkingos_cloud_vue.git\n</code></pre><p>打开src/api/api.js</p>\n<p>修改云平台请求的服务器地址为后端项目里“云平台后台服务器模块”的服务地址，如下所示：</p>\n<pre><code>export const path = &#39;http://**************:8080/cloud&#39;;  //这里需要改成自己的私有云地址\n</code></pre><p>然后在根目录下运行(安装项目依赖的js包):</p>\n<pre><code>npm install\n</code></pre><p>然后在根目录下运行构建命令:</p>\n<pre><code>npm run build\n</code></pre><p>构建命令会生成一个user文件夹</p>\n<p>文件夹下有3个html和一个static目录</p>\n<p>其中login.html就是云平台的首页</p>\n<p>static目录里存放js/css/images 等静态资源</p>\n<p>将user的文件夹下的文件整个拷贝到tomcat的ROOT目录下\n然后访问login.html库即可</p>\n'},function(t,n,d){(function(t,n){!function(t,d){"use strict";function e(t){"function"!=typeof t&&(t=new Function(""+t));for(var n=new Array(arguments.length-1),d=0;d<n.length;d++)n[d]=arguments[d+1];var e={callback:t,args:n};return s[c]=e,o(c),c++}function r(t){delete s[t]}function i(t){var n=t.callback,e=t.args;switch(e.length){case 0:n();break;case 1:n(e[0]);break;case 2:n(e[0],e[1]);break;case 3:n(e[0],e[1],e[2]);break;default:n.apply(d,e)}}function a(t){if(h)setTimeout(a,0,t);else{var n=s[t];if(n){h=!0;try{i(n)}finally{r(t),h=!1}}}}if(!t.setImmediate){var o,c=1,s={},h=!1,l=t.document,u=Object.getPrototypeOf&&Object.getPrototypeOf(t);u=u&&u.setTimeout?u:t,"[object process]"==={}.toString.call(t.process)?function(){o=function(t){n.nextTick(function(){a(t)})}}():function(){if(t.postMessage&&!t.importScripts){var n=!0,d=t.onmessage;return t.onmessage=function(){n=!1},t.postMessage("","*"),t.onmessage=d,n}}()?function(){var n="setImmediate$"+Math.random()+"$",d=function(d){d.source===t&&"string"==typeof d.data&&0===d.data.indexOf(n)&&a(+d.data.slice(n.length))};t.addEventListener?t.addEventListener("message",d,!1):t.attachEvent("onmessage",d),o=function(d){t.postMessage(n+d,"*")}}():t.MessageChannel?function(){var t=new MessageChannel;t.port1.onmessage=function(t){a(t.data)},o=function(n){t.port2.postMessage(n)}}():l&&"onreadystatechange"in l.createElement("script")?function(){var t=l.documentElement;o=function(n){var d=l.createElement("script");d.onreadystatechange=function(){a(n),d.onreadystatechange=null,t.removeChild(d),d=null},t.appendChild(d)}}():function(){o=function(t){setTimeout(a,0,t)}}(),u.setImmediate=e,u.clearImmediate=r}}("undefined"==typeof self?void 0===t?this:t:self)}).call(n,d(3),d(5))},function(t,n){t.exports=function(t){var n="undefined"!=typeof window&&window.location;if(!n)throw new Error("fixUrls requires window.location");if(!t||"string"!=typeof t)return t;var d=n.protocol+"//"+n.host,e=d+n.pathname.replace(/\/[^\/]*$/,"/");return t.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,function(t,n){var r=n.trim().replace(/^"(.*)"$/,function(t,n){return n}).replace(/^'(.*)'$/,function(t,n){return n});if(/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/)/i.test(r))return t;var i;return i=0===r.indexOf("//")?r:0===r.indexOf("/")?d+r:e+r.replace(/^\.\//,""),"url("+JSON.stringify(i)+")"})}},function(t,n,d){(function(t){function e(t,n){this._id=t,this._clearFn=n}var r=void 0!==t&&t||"undefined"!=typeof self&&self||window,i=Function.prototype.apply;n.setTimeout=function(){return new e(i.call(setTimeout,r,arguments),clearTimeout)},n.setInterval=function(){return new e(i.call(setInterval,r,arguments),clearInterval)},n.clearTimeout=n.clearInterval=function(t){t&&t.close()},e.prototype.unref=e.prototype.ref=function(){},e.prototype.close=function(){this._clearFn.call(r,this._id)},n.enroll=function(t,n){clearTimeout(t._idleTimeoutId),t._idleTimeout=n},n.unenroll=function(t){clearTimeout(t._idleTimeoutId),t._idleTimeout=-1},n._unrefActive=n.active=function(t){clearTimeout(t._idleTimeoutId);var n=t._idleTimeout;n>=0&&(t._idleTimeoutId=setTimeout(function(){t._onTimeout&&t._onTimeout()},n))},d(42),n.setImmediate="undefined"!=typeof self&&self.setImmediate||void 0!==t&&t.setImmediate||this&&this.setImmediate,n.clearImmediate="undefined"!=typeof self&&self.clearImmediate||void 0!==t&&t.clearImmediate||this&&this.clearImmediate}).call(n,d(3))},function(t,n,d){t.exports=d.p+"assets/eu-da8078d54b4abf70799ad11a4d31071f.jpeg"},function(t,n){t.exports="data:image/png;base64,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"},function(t,n,d){t.exports=d.p+"assets/onekey-5c608ec520618beb00c312cb7a413caf.jpg"},function(t,n,d){t.exports=d.p+"assets/parkingos-8629c0547f8428da74196650a8279746.jpeg"},function(t,n,d){t.exports=d.p+"assets/spring-b6c78951c046ea0f63f2d8afd910a416.jpg"},function(t,n){t.exports="data:image/jpeg;base64,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"},function(t,n,d){t.exports=d.p+"assets/iconfont-demo-51a180261bc13bd924dcb053b67c1909.png"},function(t,n,d){d(65);var e=d(1)(d(16),d(58),null,null);t.exports=e.exports},function(t,n,d){d(69);var e=d(1)(d(17),d(62),null,null);t.exports=e.exports},function(t,n,d){d(68);var e=d(1)(d(18),d(61),null,null);t.exports=e.exports},function(t,n,d){d(67);var e=d(1)(d(19),d(60),null,null);t.exports=e.exports},function(t,n,d){d(70);var e=d(1)(d(22),d(63),null,null);t.exports=e.exports},function(t,n){t.exports={render:function(){var t=this,n=t.$createElement,d=t._self._c||n;return d("div",[d("app-header"),t._v(" "),d("article",{staticClass:"app-content-container"},[d("app-content")],1),t._v(" "),d("footer",[t._v("@ParkingOS")])],1)},staticRenderFns:[]}},function(t,n){t.exports={render:function(){var t=this,n=t.$createElement,d=t._self._c||n;return d("keep-alive",[d("router-view")],1)},staticRenderFns:[]}},function(t,n){t.exports={render:function(){var t=this,n=t.$createElement;return(t._self._c||n)("div",{class:t.article+"-container markdown",domProps:{innerHTML:t._s(t.content)}})},staticRenderFns:[]}},function(t,n,d){t.exports={render:function(){var t=this,n=t.$createElement;t._self._c;return t._m(0)},staticRenderFns:[function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",{staticClass:"intro-container specification-container"},[e("p",{staticClass:"title"}),t._v(" "),e("section",[e("p",{staticClass:"title"},[t._v("ParkingOS简介")]),t._v(" "),e("p",[e("br")]),e("p",[t._v("ParkingOS是由泊链联盟联合多家停车厂商开发并且开源的帮助停车场实现无人化的车场云平台 ")]),t._v(" "),e("br"),t._v(" "),e("p"),t._v(" "),e("p",[e("span",{staticClass:"title"},[t._v("1.系统运行环境:")]),t._v(" "),e("ol",[e("li",[t._v("windows/linux")]),t._v(" "),e("li",[e("a",{attrs:{href:"https://www.postgresql.org/"}},[t._v("postgres")])]),t._v(" "),e("li",[e("a",{attrs:{href:"https://www.mongodb.com/"}},[t._v("mongodb")])]),t._v(" "),e("li",[e("a",{attrs:{href:"http://tomcat.apache.org/"}},[t._v("tomcat")]),t._v("等javaweb容器")])]),t._v(" "),e("span",{staticClass:"title"},[t._v("2.系统构成模块/工程:")]),t._v(" "),e("ol",[e("li",[e("a",{attrs:{href:"https://github.com/ParkingOS/parkingos_cloud_vue"}},[t._v("parkingos_cloud_vue")]),t._v("(云平台前端工程)")]),t._v(" "),e("li",[e("a",{attrs:{href:"https://github.com/ParkingOS/parkingos_cloud_vue_server"}},[t._v("parkingos_cloud_vue_server")]),t._v("(云平台后端工程)")]),t._v(" "),e("li",[e("a",{attrs:{href:"https://github.com/ParkingOS/ParkingOS_cloud/tree/master/parkingos_cloud_api"}},[t._v("parkingos_cloud_api")]),t._v("(云平台sdk上报接口)")]),t._v(" "),e("li",[e("a",{attrs:{href:"https://github.com/ParkingOS/ParkingOS_cloud/tree/master/parkingos_anlysis"}},[t._v("parkingos_anlysis")]),t._v("(定时统计模块)")])]),t._v(" "),e("span",{staticClass:"title"},[t._v("3.parkingos架构图:")])]),e("div",{staticStyle:{margin:"50px 0px",border:"1px solid #000","border-radius":"2px"}},[e("img",{attrs:{src:d(48)}})]),t._v(" "),e("span",{staticClass:"title"},[t._v("4.私有云平台搭建:")]),t._v(" "),e("ol",[e("li",[t._v("点击"),e("a",{attrs:{href:"#/quick/backend"}},[t._v("后端项目搭建说明")]),t._v("查看云平台后端搭建")]),t._v(" "),e("li",[t._v("点击"),e("a",{attrs:{href:"#/quick/backend"}},[t._v("前端项目搭建说明")]),t._v("查看云平台前端搭建")])]),t._v(" "),e("span",{staticClass:"title"},[t._v("4.云平台技术架构介绍:")]),t._v(" "),e("ol",[e("li",[t._v("点击查看"),e("a",{attrs:{href:"#/desc/be"}},[t._v("后端技术架构")])]),t._v(" "),e("li",[t._v("点击查看"),e("a",{attrs:{href:"#/desc/fe"}},[t._v("前端技术架构")])]),t._v(" "),e("li",[t._v("点击查看"),e("a",{attrs:{href:"#/desc/db"}},[t._v("表结构说明")])])]),t._v(" "),e("p")])])}]}},function(t,n){t.exports={render:function(){var t=this,n=t.$createElement,d=t._self._c||n;return d("div",{staticClass:"usagesidebar-container"},[d("aside",{staticClass:"usagesidebar-sidebar sidebar"},[d("div",{staticClass:"sidebar-item-group"},[d("div",{staticClass:"sidebar-item-group-title bold"},[t._v("快速开始")]),t._v(" "),t._l(t.basics,function(n){return[d("div",{key:n.en,staticClass:"sidebar-item",class:{active:t.active===n.en}},[d("router-link",{attrs:{to:"/quick/"+(n.type&&"child"==n.type?"others/":"")+n.en.toLowerCase()}},[t._v(t._s(n.zh))])],1)]})],2),t._v(" "),d("div",{staticClass:"sidebar-item-group"},[d("div",{staticClass:"sidebar-item-group-title bold"},[t._v("项目结构说明")]),t._v(" "),t._l(t.android,function(n){return[d("div",{key:n.en,staticClass:"sidebar-item",class:{active:t.active===n.en}},[d("router-link",{attrs:{to:"/desc/"+n.en.toLowerCase()}},[t._v(t._s(n.zh))])],1)]})],2)]),t._v(" "),d("content",[t.isNative(this.$route.name)?[d("keep-alive",[d("router-view")],1)]:[d("markdown",{attrs:{article:this.$route.params.article}})]],2)])},staticRenderFns:[]}},function(t,n,d){t.exports={render:function(){var t=this,n=t.$createElement,d=t._self._c||n;return d("div",{staticClass:"appheader-container"},[d("div",{staticClass:"wrapper"},[t._m(0),t._v(" "),d("ul",{staticClass:"nav"},[d("li",{class:{active:""===t.active}},[d("router-link",{attrs:{to:"/"}},[t._v("首页")])],1),t._v(" "),d("li",{class:{active:""!==t.active}},[d("router-link",{attrs:{to:"/basics/quickstart"}},[t._v("文档")])],1),t._v(" "),t._m(1)])])])},staticRenderFns:[function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",{staticClass:"logo-wrap"},[e("img",{staticClass:"logo",attrs:{src:d(46)}}),t._v(" "),e("span",[t._v("开源停车云")])])},function(){var t=this,n=t.$createElement,d=t._self._c||n;return d("li",[d("a",{attrs:{href:"https://github.com/ParkingOS/ParkingOS_cloud"}},[d("img",{staticClass:"star-badge",attrs:{src:"https://img.shields.io/github/stars/ParkingOS/ParkingOS_cloud.svg?style=social&label=Stars"}})])])}]}},function(t,n,d){t.exports={render:function(){var t=this,n=t.$createElement,d=t._self._c||n;return d("div",{staticClass:"index-container"},[d("section",{staticClass:"banner"},[d("content",{staticClass:"particles"},[d("div",{staticClass:"mask",attrs:{id:"particles-js"}}),t._v(" "),d("p",{staticClass:"title"},[t._v("ParkingOS")]),t._v(" "),d("p",{staticClass:"subtitle"},[t._v("ParkingOS开源云平台，助力您完成停车无人化!")]),t._v(" "),d("div",{staticClass:"button-group"},[d("router-link",{staticClass:"button-doc",attrs:{to:"/basics/quickstart"}},[t._v("查看文档")]),t._v(" "),d("router-link",{staticClass:"button-demo",attrs:{to:"/basics/quickstart"}},[t._v("查看文档")])],1)])]),t._v(" "),t._m(0),t._v(" "),t._m(1),t._v(" "),t._m(2)])},staticRenderFns:[function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("section",{staticClass:"feature"},[e("content",[e("div",{staticClass:"feature-article"},[e("p",{staticClass:"title"},[t._v("全新技术实现，稳定可靠")]),t._v(" "),e("p",{staticClass:"subtitle"},[t._v("\n          前后端完全分离，架构解藕合理。前端采用vue+vue-router技术栈实现spa单页面web系统。\n          后端采用成熟的springmvc+myibatis技术框架实现，成熟稳定。\n        ")])]),t._v(" "),e("div",{staticClass:"img-div"},[e("div",[e("img",{attrs:{src:d(49)}}),t._v(" "),e("img",{attrs:{src:d(50)}})])])])])},function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("section",{staticClass:"feature"},[e("content",[e("div",{staticClass:"img-div"},[e("div",[e("img",{attrs:{src:d(47)}})])]),t._v(" "),e("div",{staticClass:"feature-article"},[e("p",{staticClass:"title"},[t._v("简单易用，一键打包")]),t._v(" "),e("p",{staticClass:"subtitle"},[t._v("前端工程构建基于最新的webpack打包工具，后端采用maven作为包管理工具进行构建，都是一键构建输出包，直接部署服务器即可")])])])])},function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("section",{staticClass:"feature"},[e("content",[e("div",{staticClass:"feature-article"},[e("p",{staticClass:"title"},[t._v("体验流畅，主题自由定制")]),t._v(" "),e("p",{staticClass:"subtitle"},[t._v("\n          基于vue+vue-router的单页面程序，完全无刷新的流畅体验。另外界面色调图标等可以自定义，为自己选择更加贴合自己的主题\n        ")])]),t._v(" "),e("div",{staticClass:"img-div"},[e("div",[e("img",{attrs:{src:d(45)}})])])])])}]}},function(t,n,d){var e=d(28);"string"==typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);d(2)("f6666c00",e,!0)},function(t,n,d){var e=d(29);"string"==typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);d(2)("cce8eb3c",e,!0)},function(t,n,d){var e=d(30);"string"==typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);d(2)("7ccaf08e",e,!0)},function(t,n,d){var e=d(31);"string"==typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);d(2)("24e664ac",e,!0)},function(t,n,d){var e=d(32);"string"==typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);d(2)("6ef01223",e,!0)},function(t,n,d){var e=d(33);"string"==typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);d(2)("2989ee00",e,!0)},function(t,n,d){var e=d(34);"string"==typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);d(2)("5f8bde41",e,!0)},function(t,n){t.exports=function(t,n){for(var d=[],e={},r=0;r<n.length;r++){var i=n[r],a=i[0],o=i[1],c=i[2],s=i[3],h={id:t+":"+r,css:o,media:c,sourceMap:s};e[a]?e[a].parts.push(h):d.push(e[a]={id:a,parts:[h]})}return d}},function(t,n){t.exports={particles:{number:{value:331,density:{enable:!0,value_area:3527.50653390415}},color:{value:"#ffffff"},shape:{type:"circle",stroke:{width:0,color:"#000000"},polygon:{nb_sides:5},image:{src:"img/github.svg",width:100,height:100}},opacity:{value:.5,random:!1,anim:{enable:!1,speed:1,opacity_min:.1,sync:!1}},size:{value:24.05118091298284,random:!0,anim:{enable:!1,speed:40,size_min:.1,sync:!1}},line_linked:{enable:!0,distance:150,color:"#ffffff",opacity:.4,width:1},move:{enable:!0,speed:6,direction:"none",random:!1,straight:!1,out_mode:"out",bounce:!1,attract:{enable:!1,rotateX:600,rotateY:1200}}},interactivity:{detect_on:"canvas",events:{onhover:{enable:!0,mode:"repulse"},onclick:{enable:!0,mode:"push"},resize:!0},modes:{grab:{distance:400,line_linked:{opacity:1}},bubble:{distance:400,size:40,duration:2,opacity:8,speed:3},repulse:{distance:200,duration:.4},push:{particles_nb:4},remove:{particles_nb:2}}},retina_detect:!0}}]);