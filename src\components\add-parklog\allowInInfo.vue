<template>
    <div class="placeholder-infor">
      <div class="header" v-if="updateType == 2">
        <div class="title">车位准入</div>
        <div class="setting">
        </div>
      </div>
      <el-form ref="setForm" label-width="220px" :model="setForm" class="charging-plie" :rules="rules">
          <div v-if="updateType == 2">
        <el-form-item label="蓝牌车">
            <el-switch
                v-model="setForm.openBlue"
                :active-value="1"
                :inactive-value="0"
                @change="openBlueChange"
                >
            </el-switch>
            <span>{{ setForm.openBlue == 1?'未预约允许进入车位':'车位相机识别未预约蓝牌车辆进入车位'}}</span>
        </el-form-item>
        <el-form-item label="入位时间" v-if="setForm.openBlue == 1">
            <div class="flex-time">
                <el-time-picker
                        is-range
                        v-model="timeAdmission.blueTime"
                        class="time-picker"
                        value-format="HH:mm:ss"
                        range-separator="至"
                        :picker-options="{
                            selectableRange: '00:00:00 - 23:59:59'
                        }"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        placeholder="选择生效时段">
                </el-time-picker>
                <svg-add-one theme="outline" size="16" @click="addblueType = 1" v-if="addblueType == 0" />
            </div>
            <div class="flex-time" v-if="addblueType == 1">
                <el-time-picker
                        is-range
                        v-model="timeAdmission.blueTimes"
                        class="time-picker"
                        value-format="HH:mm:ss"
                        range-separator="至"
                        :picker-options="{
                            selectableRange: '00:00:00 - 23:59:59'
                        }"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        placeholder="选择生效时段">
                </el-time-picker>
                <svg-reduce-one theme="outline" size="16" @click="addblueType = 0; timeAdmission.blueTimes = ''"/>
            </div>
        </el-form-item>
        <el-form-item label="绿牌车">
            <el-switch
                v-model="setForm.openGreen"
                :active-value="1"
                :inactive-value="0"
                @change="openGreenChange"
                >
            </el-switch>
            <span>{{ setForm.openGreen == 1 ?'未预约允许进入车位':'车位相机识别未预约绿牌车辆进入车位'}}</span>
        </el-form-item>
        <el-form-item label="入位时间" v-if="setForm.openGreen==1">
            <div class="flex-time">
                    <el-time-picker
                            is-range
                            v-model="timeAdmission.greenTime"
                            class="time-picker"
                            value-format="HH:mm:ss"
                            range-separator="至"
                            :picker-options="{
                                selectableRange: '00:00:00 - 23:59:59'
                            }"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            placeholder="选择生效时段">
                    </el-time-picker>
                    <svg-add-one theme="outline" size="16" @click="addgreenType = 1" v-if="addgreenType == 0" />
                </div>
                <div class="flex-time" v-if="addgreenType == 1">
                    <el-time-picker
                            is-range
                            v-model="timeAdmission.greenTimes"
                            class="time-picker"
                            value-format="HH:mm:ss"
                            range-separator="至"
                            :picker-options="{
                                selectableRange: '00:00:00 - 23:59:59'
                            }"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            placeholder="选择生效时段">
                    </el-time-picker>
                    <svg-reduce-one theme="outline" size="16" @click="addgreenType = 0; timeAdmission.greenTimes = ''"/>
                </div>
        </el-form-item>

          </div>
          
          <!-- <div class="algorConfig" v-if="updateType == 1">
            <div class="configTitle">占位识别</div>
            <div class="configSelect">
                <el-radio-group v-model="setForm.strategyType">
                    <el-radio label="1" border>相机识别算法</el-radio>
                    <el-radio label="2" border>融合算法</el-radio>
                </el-radio-group>
                <div class="configTips">相机识别算法：根据相机识别算法判断车辆进出车位</div>
                <div class="configTips">融合算法：根据道闸杆开关到位或传感器信号等与相机融合计算，判断车辆进出车位，无道闸请勿选择本算法</div>
            </div>
          </div> -->
          
      </el-form>
      <footer slot="footer" class="dialog-footer">
          <el-button type="primary" v-show="!readonly" v-if="updateType == 1" class="custom-btns-style" style="margin-left: 30px"
              @click="submit" :loading="editloading" :disabled="applyName !== null && empowerType !== 0">确 定</el-button>
              <el-button type="primary" v-show="!readonly" v-if="updateType == 2" class="custom-btns-style" style="margin-left: 30px"
              @click="accessSubmit" :loading="editloading" :disabled="applyName !== null && empowerType !== 0">确 定</el-button>
      </footer>
      <div class="header" >
        <div class="title">车牌准入白名单<span class="switchWhite">
            <el-switch v-model="setForm.vehicleAccessState" :active-value="1"
            :inactive-value="0" active-text="启用" inactive-text="禁用" @change="openWhitelistChange"></el-switch>
        </span></div>
        <div class="setting">
            <el-button icon="el-icon-plus" type="text" @click="addwWhiteVisible = true">添加</el-button>
        </div>
      </div>
      <div class="table-wrapper-style">
        <tab-pane method="raw" :queryapi="queryapi" del-method="raw" :delapi="delapi"
        :del-form="delForm" :table-items="tableitems" :nopadd="'1'"
            align-pos="right"  :searchForm="searchForm" ref="tabPane"
                    v-on:cancelDel="cancelDel"></tab-pane>
      </div>
      <el-dialog custom-class="custom-dialog" width="400px" :show-close="false" :visible.sync="addwWhiteVisible" :modal="false">
      <header class="fixed-code__title" slot="title" style="font-size: 18px;font-weight: bold;">
        添加<i class="el-icon-close dialog-header-iconfont" @click="addwWhiteVisible = false"></i>
      </header>
      <el-form ref="addForm" :model="addForm" label-width="80px" :rules="addFormRules">
		<el-form-item label="车牌" prop="licensePlate">
          <el-input v-model.trim="addForm.licensePlate" placeholder="请输入区域名称" @input="handleInput"></el-input>
        </el-form-item>
        <el-form-item label="开始时间" prop="currentData">
            <el-date-picker
                v-model="addForm.currentData"
                type="date"
                value-format="timestamp"
                placeholder="选择开始时间"
                @change="changeCurrentDate">
            </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="currentendTime">
            <el-date-picker
                v-model="addForm.currentendTime"
                type="date"
                value-format="timestamp"
                placeholder="选择结束时间"
                @change="changeEndDate">
            </el-date-picker>
        </el-form-item>
      </el-form>
      <footer slot="footer" class="dialog-footer">
        <el-button size="small" style="width: 90px;" @click="addwWhiteVisible = false">取 消</el-button>
        <el-button type="primary" @click="comitWhiteSub" size="small"
          style="width: 90px;margin-left: 60px">确 定</el-button>
      </footer>

    </el-dialog>
    </div>
  </template>
  <script>
  import TabPane from '@/components/table/TabPane';
  import common from '@/common/js/common'
  export default {
    name: 'allowInInfo',
    components: {
      TabPane
    },
    props: {
      bolinkId: {
        type: Number,
        default: 0
      },
      applyName: {
          type: String,
          default: null
      },
      empowerType: {
        type: Number,
        default: 0
      },
      updateType: {
        type: Number,
        default: 1
      },
      setType: {
        type:Number,
        default: 1, // 1-单个设置  2-批量设置
      }
    },
    data() {
      return {
          editloading: false,
          readonly: false,
          parkinglotList: [], //车位列表
          addwWhiteVisible:false,
          parkingForm:{},
          addForm:{
            licensePlate: "",//车牌号
            startTime: '',//开始时间
            endTime: '',//结束时间
            currentendTime:'',
            currentData:''
          },
          addFormRules: {
            licensePlate: [
                {required: true, message: '请输入车牌号', trigger: 'blur'}
            ],
            currentData: [
                {required: true, message: '请选择开始时间', trigger: 'blur'}
            ],
            currentendTime: [
                {required: true, message: '请选择结束时间', trigger: 'blur'}
            ],
          },
          vehicleAccessState:'1',
          queryapi:'vehicleAccessConfig/query',
          delapi: 'vehicleAccessConfig/delete',
          //删除
          delForm:{},
          tableitems: [
          {
            hasSubs: false,
            subs: [{
              label: '车牌',
              width:'110px',
              prop: 'licensePlate',
              type: 'str',
              hidden:false,
              align: 'left'
            }]
          },
          {
            hasSubs: false,
            subs: [{
              label: '开始时间',
              prop: 'startTime',
              width:'170px',
              type: 'str',
              hidden:false,
              align: 'left',
              columnType: 'render',
               render: (h, params) => {
                    return h('div', [
                        h('span', common.dateformat(params.row.startTime))
                 ]);
               }
            }]
          },
          {
            hasSubs: false,
            subs: [{
              label: '结束时间',
              prop: 'endTime',
              width:'170px',
              type: 'str',
              hidden:false,
              align: 'left',
              columnType: 'render',
               render: (h, params) => {
                    return h('div', [
                        h('span', common.dateformat(params.row.endTime))
                 ]);
               }
            }]
          },
          {
            hasSubs: false,
            subs: [{
              label: '操作',
              columnType: 'render',
              align: 'left',
              unsortable: true,
              render: (h, params) => {
                return h('div', [
                  h('ElButton', {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    style: {
                      marginRight: '5px',
                      color: 'red'
                    },
                    on: {
                      click: (e) => {
                        window.event ? window.event.cancelBubble = true : e.stopPropagation();
                        this.delForm = {
                          $index: params.index,
                          delVisible: true,
                          id: params.row.id,
                        }
                      }
                    }
                  }, '删除'),
                ]);
              }
            }]
          },
        ],
          timeAdmission:{
          },
          availableTimeSlot: {}, //收费时段
          parkingLotAccessTbs:[],
          addblueType:0,
          addgreenType:0,
          setForm: {
             openBlue:0,
             vehicleAccessState:0,
             openGreen:0,
              typePlaceholderCharge:2,
              feesStandard: {
                  noOrder: {},
                  order: {},
              }
          },
          searchFormData:{},
          searchForm:{},
          baseParams: {}, //初始化信息
          seatRuleList:[], //占位计费标准名称列表
          rules: {
              name: [{
                  required: true,
                  message: "请填写占位计费标准名称",
                  trigger: 'blur'
              }],
              typePlaceholderCharge: [{
              required: true,
              message: "请选择占位费收费类型",
              trigger: 'blur'
              }],
              prepaidPlaceholder:[
                  {
                      required: true,
                      message: "请填写预付占位押金",
                      trigger: 'blur'
                  },
                  {
                      pattern: /^[0-9]\d*\.?\d*$|^0\.\d*[1-9]\d*$/,
                      message: '金额只允许填写非负数,保留两位小数，长度限10个字符！',
                      trigger: 'blur'
                  }
                  ],
              orderFreeTime: [{
                      required: true,
                      message: "请填写充电结束后免费时长",
                      trigger: 'blur'
                  },
                  {
                      pattern: /^[0-9]+$/,
                      message: '充电结束后免费时长只允许填写正整数！',
                      trigger: 'blur'
                  }
              ],
              noOrderFreeTime: [{
                      required: true,
                      message: "请填写进入车位免费时长",
                      trigger: 'blur'
                  },
                  {
                      pattern: /^[0-9]+$/,
                      message: '进入车位免费时长只允许填写正整数！',
                      trigger: 'blur'
                  }
              ],
              time: [{
                      required: true,
                      message: "请填写时间",
                      trigger: 'blur'
                  },
                  {
                      pattern: /^[0-9]+$/,
                      message: '时间只允许填写正整数！',
                      trigger: 'blur'
                  }
              ],
              fee: [
                  {
                      required: true,
                      message: "请填写金额",
                      trigger: 'blur'
                  },
                  {
                      pattern: /^[0-9]\d*\.?\d*$|^0\.\d*[1-9]\d*$/,
                      message: '金额只允许填写非负数,保留两位小数，长度限10个字符！',
                      trigger: 'blur'
                  }
              ],
          },
      }
    },
    mounted() {
      this.initData()
    },
    methods: {
      openBlueChange(e){
        console.log('蓝牌车开关',e)
        if(!this.setForm.openBlue) { // 清空原来的选中的蓝牌车时间
            this.$set(this.timeAdmission,'blueTime', '');
        }
      },
      openGreenChange(e){
        console.log('绿牌车开关', e)
        if(!this.setForm.openGreen) {  // 清空原来选中的绿牌车时间
            this.$set(this.timeAdmission,'greenTime', '');
        }
      },
      // 输入小写转为大写
      handleInput() {
        this.addForm.licensePlate = this.addForm.licensePlate.toUpperCase();
      },
      changeCurrentDate(val){
        if(val != null && val != ''){
            this.addForm.startTime = val/1000;
        }else{
            this.addForm.startTime = '';
        }
      },
      changeEndDate(val){
        if(val != null && val != ''){
            this.addForm.endTime = val/1000;
        }else{
            this.addForm.endTime = '';
        }
      },
      
      //车牌准入开关
      openWhitelistChange(e){
        console.log(e)
        this.accessSubmit()
      },
      //关闭弹窗清除记录
      clearData(){
          this.setForm =  {
              openBlue:0,
              vehicleAccessState:0,
              openGreen:0,
              typePlaceholderCharge:2,
              feesStandard: {
                  noOrder: {},
                  order: {},
              }
          }

          this.availableTimeSlot = {}
      },
      //删除
      cancelDel(){
         this.delForm.delVisible = false;
      },
  
      /* 初始化 */
      initData() {
          let baseParams= {}
          let loginuin = sessionStorage.getItem("loginuin"),
            cityid = sessionStorage.getItem("cityid"),
            serverId = sessionStorage.getItem("serverid"),
            comid = sessionStorage.getItem("comid")
          if (comid != '0' && comid != 'undefined') {
              baseParams.comid = comid
          } else if (serverId != 'undefined'&& serverId!==null) {
              baseParams.serverId = serverId
          } else if (cityid != 'undefined') {
              baseParams.cityid = cityid
          } else {
              baseParams.backstageId = loginuin
          }
          this.baseParams = baseParams;
          this.searchFormData = {parkId: this.parkingForm.id };
          this.searchForm = JSON.parse(JSON.stringify( this.searchFormData ));
      },

      //加入准入白名单
      comitWhiteSub(){
        this.$refs.addForm.validate((valid) => {
          if (valid) {
            const selectInfo = this.parkinglotList.filter(item => item.selectType == 1)
            console.log(selectInfo)
            let api = ''
            let data;
            const {
              licensePlate,
              startTime,
              endTime
            } = this.addForm;
            if(endTime < startTime){
              this.$message.error('结束日期不能小于开始日期')
              return;
            } 
            if(selectInfo.length <= 1){
              api = '/vehicleAccessConfig/add';
              data = {
                parkId: this.parkingForm.id || selectInfo[0].id, //车位id
                licensePlate,//车牌号
                startTime,//开始时间
                endTime,//结束时间
                createBy: sessionStorage.getItem('nickname1'),
                createId: sessionStorage.getItem('loginuin'),
              }
            }else{
              api = '/vehicleAccessConfig/batchAdd';
              let dataList = [];
              selectInfo.forEach(item =>{
                dataList.push({
                  parkId: item.id, //车位id
                  licensePlate,//车牌号
                  startTime,//开始时间
                  endTime,//结束时间
                  createBy: sessionStorage.getItem('nickname1'),
                  createId: sessionStorage.getItem('loginuin'),
                })
              })
              data = dataList;
            }

            this.$axios.post(api, data).then(res => {
              let {
                code,
                message
              } = res.data
              if (code == 200) {
                this.$message.success(message)
                this.initData()
                this.addwWhiteVisible = false
              } else {
                this.$message.error(message)
              }
            }).catch(err => {
              console.log(err)
            });
          }
        })
        
      },
  
      //选择框可输入
      ruleBlurEvent(e){
          if (e.target.value) {
              // 如果是对象，要使用this.$set方法
              this.$set(this.setForm, 'name', e.target.value)
          }
      },
  
      //获取车位列表
      getParkList(parkList){
          this.parkinglotList = parkList;
      },
  
  
      //获取车位准入信息
      getAllowInInfo(parkingForm){
        let this_ = this
        console.log('调用获取车位占位信息parkingForm---->',parkingForm)
        this.parkingForm = parkingForm;
        sessionStorage.setItem('parkingFormInfo',JSON.stringify(parkingForm)|| '')
          this.$axios.get("/parkingSpaceInfo/queryBillByParkingSpaceBolinkId?bolinkId="+ parkingForm.id).then(res => {            
              let data = res.data;
              console.log('车位准入信息返回数据00', res.data)
                if (data.code === 200) {
                  let parkingLotAccessTbs = data.data.parkingLotAccessTbs;
                  if(parkingLotAccessTbs){
                    this.parkingLotAccessTbs = parkingLotAccessTbs;
                  }
                  this.setForm.chargeEmployState = data.data.chargeEmployState;
                  this.setForm.openBlue = data.data.openBlue;
                  this.setForm.vehicleAccessState = data.data.vehicleAccessState;
                  this.searchFormData ={parkId: parkingForm.id };
                  this.searchForm = JSON.parse(JSON.stringify( this.searchFormData ));
                  this.setForm.openGreen = data.data.openGreen;
                   if(parkingLotAccessTbs){
                        const blueTimeData = parkingLotAccessTbs[1];
                        const greenTimeData = parkingLotAccessTbs[2];
                        this.timeAdmission = {};
                        if(blueTimeData){
                            blueTimeData.forEach((item, index) =>{
                                if(index == 0){
                                    this.$set(this.timeAdmission, 'blueTime', [
                                        item.startTime,
                                      item.endTime
                                    ])
                                }

                                if(index == 1){
                                    this.addblueType = 1
                                    this.$set(this.timeAdmission, 'blueTimes', [
                                        item.startTime,
                                        item.endTime
                                    ])
                                }
                            })
                        }
                        if(greenTimeData){
                            greenTimeData.forEach((item, index) =>{
                                if(index == 0){
                                    this.$set(this.timeAdmission, 'greenTime', [
                                        item.startTime,
                                        item.endTime
                                    ])
                                }

                                if(index == 1){
                                    this.addgreenType = 1
                                    this.$set(this.timeAdmission, 'greenTimes', [
                                        item.startTime,
                                        item.endTime
                                    ])
                                }
                            })
                        }


                    }
                    this.setForm = {
                      id,
                      feesStandard,
                      openBlue,
                      vehicleAccessState,
                      openGreen
                    }
                    this.$forceUpdate()
              } 
            })
          .catch(err => {
              
          })
      },
  
      seatNameChange(val){
          if(!val){
              this.seatRuleList=[]
          }else{
              this.seatRuleList.forEach(item => {
                  if(item.id == val){
                      this.handleEdit(item)
                  }
              });
          }
      },

  
      // 查询搜索条件下拉
      searchCriteria(data){
        this.$axios.post('/billingSettings/queryBillList', data).then(res => {
            let data = res.data
             if (data.code === 200) {
              this.seatRuleList= data.data;
                          
             } else {
                 this.seatRuleList=[]         
             }
                      
             })
             .catch(err => {
                     this.seatRuleList=[]
             })
        },
        setTimeValue(){
            const timeData = this.timeAdmission;
            let parkingLotAccessTbs = [];
            const selectInfo = this.parkinglotList.filter(item => item.selectType == 1);
            const parkingForm = JSON.parse(sessionStorage.getItem("parkingFormInfo"));
            if(this.setType == 1) {// 单个设置
               if(timeData.blueTime){
                parkingLotAccessTbs.push({
                  comid: parkingForm.comid, //车场id
                  parkingLotId: parkingForm.id, //车位id
                  carType: 1, //车牌类型 1 蓝牌车 2 绿牌车
                  startTime: timeData.blueTime[0] || '', //准入开始时间
                  endTime: timeData.blueTime[1] || '' //准入结束时间
                })
               }
               if(timeData.blueTimes){
                parkingLotAccessTbs.push({
                  comid: parkingForm.comid, //车场id
                  parkingLotId: parkingForm.id, //车位id
                  carType: 1, //车牌类型 1 蓝牌车 2 绿牌车
                  startTime: timeData.blueTimes[0] || '', //准入开始时间
                  endTime: timeData.blueTimes[1] || '' //准入结束时间
                })
               }
               if(timeData.greenTime){
                parkingLotAccessTbs.push({
                    comid: parkingForm.comid, //车场id
                    parkingLotId: parkingForm.id, //车位id
                    carType: 2, //车牌类型 1 蓝牌车 2 绿牌车
                    startTime: timeData.greenTime[0] || '', //准入开始时间
                    endTime: timeData.greenTime[1] || '' //准入结束时间
                })
              }
              if(timeData.greenTimes){
                parkingLotAccessTbs.push({
                    comid: parkingForm.comid, //车场id
                    parkingLotId: parkingForm.id, //车位id
                    carType: 2, //车牌类型 1 蓝牌车 2 绿牌车
                    startTime: timeData.greenTimes[0] || '', //准入开始时间
                    endTime: timeData.greenTimes[1] || '' //准入结束时间
                })
              }

            } else if(this.setType == 2){ // 批量设置
                selectInfo.forEach(item => {
                    if(timeData.blueTime){
                        parkingLotAccessTbs.push({
                          comid: item.comid, //车场id
                          parkingLotId: item.id, //车位id
                          carType: 1, //车牌类型 1 蓝牌车 2 绿牌车
                          startTime: timeData.blueTime[0] || '', //准入开始时间
                          endTime: timeData.blueTime[1] || '' //准入结束时间
                        })
                    }
                    if(timeData.blueTimes){
                        parkingLotAccessTbs.push({
                          comid: item.comid, //车场id
                          parkingLotId: item.id, //车位id
                          carType: 1, //车牌类型 1 蓝牌车 2 绿牌车
                          startTime: timeData.blueTimes[0] || '', //准入开始时间
                          endTime: timeData.blueTimes[1] || '' //准入结束时间
                        })
                    }
                    if(timeData.greenTime){
                        parkingLotAccessTbs.push({
                            comid: item.comid, //车场id
                            parkingLotId: item.id, //车位id
                            carType: 2, //车牌类型 1 蓝牌车 2 绿牌车
                            startTime: timeData.greenTime[0] || '', //准入开始时间
                            endTime: timeData.greenTime[1] || '' //准入结束时间
                        })
                    }
                    if(timeData.greenTimes){
                        parkingLotAccessTbs.push({
                            comid: item.comid, //车场id
                            parkingLotId: item.id, //车位id
                            carType: 2, //车牌类型 1 蓝牌车 2 绿牌车
                            startTime: timeData.greenTimes[0] || '', //准入开始时间
                            endTime: timeData.greenTimes[1] || '' //准入结束时间
                        })
                    }
                })
               
            }
           
            return parkingLotAccessTbs;
        },

        //提交车位准入信息
        accessSubmit(){
            const timeData = this.timeAdmission;
            if(this.setForm.openBlue){
                if(!(timeData.blueTime ||  timeData.blueTimes)){
                    this.$message.warning('蓝牌车已启用，请添加蓝牌车准入时间');
                    return;
                }
            }
            if(this.setForm.openGreen){
                if(!(timeData.greenTime ||  timeData.greenTimes)){
                    this.$message.warning('绿牌车已启用，请添加绿牌车准入时间');
                    return;
                }
            }
            const _this = this
            const {
                setForm,
                bolinkId
            } = _this
            const {
                id,
            } = setForm;
            const api = '/parkingSpaceInfo/updateByBolinkId'
            const selectPull = [];
            if(this.parkinglotList){
                const selectInfo = this.parkinglotList.filter(item => item.selectType == 1)
                selectInfo.forEach(item =>{
                    selectPull.push(item.id)
                })
            }
            let parkingLotAccessTbs = [];
            parkingLotAccessTbs = this.setTimeValue();
            let data = {
                ...this.baseParams,
                parkingLotAccessTbs,
                vehicleAccessState:setForm.vehicleAccessState,
                updateType: 2,
            }
            if(bolinkId > 0){
                data.bolinkId = bolinkId; //车位id
            }
            
            if(selectPull.length > 0){
                data.bolinkIds = selectPull; //车位id数组
            }
            _this.editloading = true
            console.log(data, '车位准入数据data')
            _this.$axios.post(api, data, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(res => {
                if (res.data.code == 200) {
                    _this.$message.success(res.data.message)
                    _this.$emit('closeDialog', false)
                    _this.resetForm()
                } else if (res.data.code == 500) {
                    _this.$message.error(res.data.message)
                }
                setTimeout(() => {
                    _this.editloading = false
                }, 1000)
            }).catch(err => {
                _this.editloading = false
                setTimeout(() => {
                    _this.editloading = false
                }, 1000)
            });
        },
  resetForm(){
    this.timeAdmission = {};
  },
      /* 提交占位计费标准 */
      submit() {
          const _this = this
          this.$refs['setForm'].validate((valid) => {
              if (valid) {
                  const {
                      setForm,
                      availableTimeSlot,
                      bolinkId
                  } = _this
                  const {
                      id,
                      feesStandard,
                      name,
                      noOrderFreeTime,
                      orderFreeTime,
                      prepaidPlaceholder,
                      strategyType,
                      typePlaceholderCharge,
                      chargeEmployState
                  } = setForm;
                  let parkAvailableTime = '';
                  let chargeAvailableTime = '';
                  let availableTimeInfo = {};
                  if(availableTimeSlot){
                    console.log(availableTimeSlot)
                    parkAvailableTime = availableTimeSlot.parkAvailableTime[0] + '-' + availableTimeSlot.parkAvailableTime[1]
                    chargeAvailableTime = availableTimeSlot.chargeAvailableTime[0] + '-' + availableTimeSlot.chargeAvailableTime[1]
                    availableTimeInfo = {parkAvailableTime:[parkAvailableTime],chargeAvailableTime:[chargeAvailableTime]}
                  }
                  
                  console.log(chargeAvailableTime)
                  console.log(parkAvailableTime)
                  console.log(availableTimeInfo)
                  const api = '/parkingSpaceInfo/updateByBolinkId'
                  const selectPull = [];
                  if(this.parkinglotList){
                      const selectInfo = this.parkinglotList.filter(item => item.selectType == 1)
                      selectInfo.forEach(item =>{
                          selectPull.push(item.id)
                      })
                  }
                  let data = {
                      name,
                      noOrderFreeTime,
                      orderFreeTime,
                      prepaidPlaceholder,
                      strategyType,
                      typePlaceholderCharge,
                      ...this.baseParams,
                      deviceType: 5,
                      chargeEmployState,
                      updateType: 1,
                      occupyBasis:id || '', //占位计费模型id
                      feesStandard: JSON.stringify(feesStandard),
                      availableTimeSlots: JSON.stringify(availableTimeInfo) 
                  }
                  
                  // prepaidPlaceholder: Number(data.prepaidPlaceholder),
                  if(bolinkId > 0){
                      data.bolinkId = bolinkId; //车位id
                  }
  
                  if(selectPull.length > 0){
                      data.bolinkIds = selectPull; //车位id数组
                  }
                  _this.editloading = true
  
                  _this.$axios.post(api, data, {
                      headers: {
                          'Content-Type': 'application/json'
                      }
                  }).then(res => {
                      if (res.data.code == 200) {
                          _this.$message.success(res.data.message)

                          _this.resetForm()
                          _this.show = false
                      } else if (res.data.code == 500) {
                          _this.$message.error(res.data.message)
                          _this.show = false
                      }
                      setTimeout(() => {
                          _this.editloading = false
                      }, 1000)
                  }).catch(err => {
                      _this.editloading = false
                      setTimeout(() => {
                          _this.editloading = false
                      }, 1000)
                  });
              }
          })
      },
      /* 编辑计费标准 */
      handleEdit(row,chargeEmployState,strategyType) {
        console.log('333333333----handleEdit()')
          let parkingLotAccessTbs = this.parkingLotAccessTbs;
          console.log(parkingLotAccessTbs)
          if(parkingLotAccessTbs){
            const blueTimeData = parkingLotAccessTbs[1];
            const greenTimeData = parkingLotAccessTbs[2];
            if(blueTimeData){
                blueTimeData.forEach((item, index) =>{
                    if(index == 0){
                        this.$set(this.timeAdmission, 'blueTime', [
                            item.startTime,
                            item.endTime
                        ])
                    }

                    if(index == 1){
                        this.addblueType = 1
                        this.$set(this.timeAdmission, 'blueTimes', [
                            item.startTime,
                            item.endTime
                        ])
                    }
                })
            }
            if(greenTimeData){
                greenTimeData.forEach((item, index) =>{
                    if(index == 0){
                        this.$set(this.timeAdmission, 'greenTime', [
                            item.startTime,
                            item.endTime
                        ])
                    }

                    if(index == 1){
                        this.addgreenType = 1
                        this.$set(this.timeAdmission, 'greenTimes', [
                            item.startTime,
                            item.endTime
                        ])
                    }
                })
            }

            console.log(this.timeAdmission)
            
          }
          let {
              name,
              id,
              feesStandard,
              orderFreeTime,
              noOrderFreeTime,
              prepaidPlaceholder,
              availableTimeSlots,
              typePlaceholderCharge
          } = row
          feesStandard = feesStandard ? JSON.parse(feesStandard) : []
          if(availableTimeSlots){
            let availableTimeInfo = JSON.parse(availableTimeSlots);
            let parkAvailableTime = availableTimeInfo.parkAvailableTime ? availableTimeInfo.parkAvailableTime[0].split("-") : []
            let chargeAvailableTime = availableTimeInfo.chargeAvailableTime ? availableTimeInfo.chargeAvailableTime[0].split("-") : []
            this.availableTimeSlot = {
                parkAvailableTime,
                chargeAvailableTime
            }
          }else{
            this.availableTimeSlot = {}
          }
          
          this.setForm = {
              id,
              name,
              feesStandard,
              orderFreeTime,
              noOrderFreeTime,
              prepaidPlaceholder,
              strategyType,
              typePlaceholderCharge,
              chargeEmployState,
          }
      },
    },
  }
  </script>
  <style  lang="scss" scoped>
  .placeholder-infor{
      height: 86vh;
      overflow: auto;
  }
  .switchWhite{
    margin-left: 20px;
  }
  .time-picker{
    margin-bottom: 10px;
  }
  .parking-access-title{
    font-size: 20px;
    color: #333;
  }
  .add-item-title{
    display: flex;
    align-items: center;
    .tips{
        margin-top: 10px;
        margin-left: 4px;
        cursor: pointer;
    }
  }
  .algorConfig{
    display: flex;
    margin-bottom: 20px;
    .configTitle{
        color: #333;
        font-size: 18px;
        width: 140px;
        margin-top: 8px;
    }
    
    .configSelect{
        margin-left: 40px;
    }
    .configTips{
        color: red;
        font-size: 12px;
        margin-top: 10px;
    }
  }
  .tips{
    color: #999;
    font-size: 14px;
    background: #fff;
    line-height: 20px;
    margin: 6px 0;
    span{
        vertical-align: middle;
        margin-right: 6px;
    }
 }
 .flex-time{
    display: flex;
    /deep/.i-icon{
        margin-left: 6px;
    }
 }
  .header {
      display: inline-flex;
      position: relative;
      height: 30px;
      margin-bottom: 30px;
      align-items: center;
      display: flex;
  
      .title {
        margin-left: 8px;
        font-size: 16px;
        width: 300px;
        flex: 1;
        .setting {}
      }
  
      .title::before {
        position: absolute;
        top: 5px;
        left: 0;
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-image: linear-gradient(-170deg, #6dbfff 0%, #1355d9 100%);
      }
  }
  
  .flex-seat{
      display: flex;
      /deep/.el-input{
          width: 140px;
      }
  }

  .table-wrapper-style{
    width: 620px;
  }
  </style>
  