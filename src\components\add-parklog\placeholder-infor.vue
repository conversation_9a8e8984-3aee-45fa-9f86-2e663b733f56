<template>
    <div class="placeholder-infor">
      <div class="header" v-if="updateType == 1">
        <div class="title">车位占位</div>
      </div>
      <el-form ref="setForm" label-width="220px" :model="setForm" class="charging-plie" :rules="rules">
         <el-form-item label="启用占位">
          <el-switch 
            v-model="setForm.chargeEmployState"
            :active-value="1"
            :inactive-value="0"
            @change="handleState"
            >
          </el-switch>
          <span>启用后车主占位将进行计费</span>
         </el-form-item>
          <div v-if="updateType == 1 && setForm.chargeEmployState == 1">
            <el-form-item label="占位计费标准名称" prop="name">
              <el-input v-model.trim="setForm.name" placeholder="请填写占位计费标准名称"
                  style="width: 100%" maxlength="50" clearable :disabled="applyName !== null && empowerType !== 0"></el-input>
                  <!-- <el-select
                      v-model="setForm.name" 
                      :remote-method="seatNameSearch"
                      placeholder="请选择占位计费标准名称"
                      @change="seatNameChange"
                       :remote="true"
                       allow-create
                      default-first-option
                      filterable
                      class="shop-custom-input"
                      style="width: 100%"
                      clearable
                      @clear="seatNameSearch"
                      @blur="ruleBlurEvent($event)"
                      :disabled="applyName !== null && empowerType !== 0"
                  >
                      <el-option
                       v-for="item in seatRuleList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                      >
                      </el-option>
                </el-select> -->
          </el-form-item>
          <el-form-item label="占位费收费类型" prop="typePlaceholderCharge">
                   <!-- <el-radio :label="2" v-model="setForm.typePlaceholderCharge">后付费</el-radio> -->
              <el-radio-group v-model="setForm.typePlaceholderCharge">
                  <el-radio :label="2">后付费</el-radio>
              </el-radio-group>
          </el-form-item>
          <el-form-item label="预付占位押金" prop="prepaidPlaceholder">
              <el-input v-model.trim="setForm.prepaidPlaceholder"
                  placeholder="请填写预付占位押金" style="width: 90%" :disabled="readonly || applyName !== null && empowerType !== 0"></el-input> <span>元</span>
          </el-form-item>
          <div class="add-item-title">充电占位
            <el-tooltip class="item" effect="dark" content="有充电占位计费方式" placement="right">
                <div class="tips"><svg-help theme="outline" size="16"/></div>
            </el-tooltip>
          </div>
          <el-form-item label="免费时长" prop="orderFreeTime">
            <div class="flex-seat">
                <el-input v-model.trim="setForm.orderFreeTime"
                  placeholder="请输入免费时长" style="width: 60%" :disabled="readonly || applyName !== null && empowerType !== 0"></el-input>
                  <span>分钟内免费</span>
            </div>
              
          </el-form-item>
          <el-form-item label-width="200px" class="add-item is-required" label="占位计费">
             <div class="flex-seat">
                  <el-form-item label-width="40px" class="hide-required" label="每" :prop="'feesStandard.orderTime'" :rules="rules.time">
                      <el-input v-model.trim="setForm.feesStandard.orderTime" :prop="'feesStandard.orderTime'"
                      placeholder="请填写时间" maxlength="10" :disabled="readonly || applyName !== null && empowerType !== 0"></el-input>
                  </el-form-item>
                  <el-form-item label-width="50px" class="hide-required" label="分钟" :prop="'feesStandard.orderFee'" :rules="rules.fee">
                      <el-input v-model.trim="setForm.feesStandard.orderFee" :prop="'feesStandard.orderFee'" 
                      placeholder="请填写金额" maxlength="10" :disabled="readonly || applyName !== null && empowerType !== 0"></el-input>
                  </el-form-item>
                  <span>元</span>
             </div>
          </el-form-item>
          <el-form-item label="收费时段">
              <el-time-picker
                      is-range
                      v-model="availableTimeSlot.chargeAvailableTime"
                      value-format="HH:mm:ss"
                      range-separator="至"
                      :picker-options="{
                          selectableRange: '00:00:00 - 23:59:59'
                      }"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      placeholder="选择生效时段"
                      :disabled="readonly || applyName !== null && empowerType !== 0">
              </el-time-picker>
          </el-form-item>
          <div class="add-item-title">停车占位
            <el-tooltip class="item" effect="dark" content="只停车占位计费方式" placement="right">
                <div class="tips"><svg-help theme="outline" size="16"/></div>
            </el-tooltip></div>
          <el-form-item label="免费时长" prop="noOrderFreeTime">
            <div class="flex-seat">
                <el-input v-model.trim="setForm.noOrderFreeTime" 
                  placeholder="请输入免费时长" style="width: 60%" :disabled="readonly || applyName !== null && empowerType !== 0"></el-input>
                  <span>分钟内免费</span>
            </div>
          </el-form-item>
          
          <el-form-item label-width="200px" class="add-item is-required" label="占位计费">
              <div class="flex-seat">
                  <el-form-item label-width="50px" class="hide-required" label="每" :prop="'feesStandard.noOrderTime'" :rules="rules.time">
                      <el-input v-model.trim="setForm.feesStandard.noOrderTime"
                      placeholder="请填写时间" maxlength="10" :disabled="readonly || applyName !== null && empowerType !== 0"></el-input>
                  </el-form-item>
                  <el-form-item label-width="50px" class="hide-required" label="分钟" :prop="'feesStandard.noOrderFee'" :rules="rules.fee">
                      <el-input v-model.trim="setForm.feesStandard.noOrderFee" 
                      placeholder="请填写金额" maxlength="10" :disabled="readonly || applyName !== null && empowerType !== 0"></el-input>
                  </el-form-item>
                  <span>元</span>
              </div>
          </el-form-item>
          <el-form-item label="收费时段">
              <el-time-picker
                      is-range
                      v-model="availableTimeSlot.parkAvailableTime"
                      value-format="HH:mm:ss"
                      range-separator="至"
                      :picker-options="{
                          selectableRange: '00:00:00 - 23:59:59'
                      }"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      placeholder="选择生效时段"
                      :disabled="readonly || applyName !== null && empowerType !== 0">
              </el-time-picker>
          </el-form-item>
          </div>
          <!-- <div v-if="updateType == 2">
            <span class="parking-access-title">车位准入</span>
            <div class="tips"><svg-tips-one theme="outline" size="16"/>说明：默认所有车都是准入的，设置了占位费后，所有车都是付费准入</div>
            <el-form-item label="蓝牌车">
                <div class="flex-time">
                    <el-time-picker
                            is-range
                            v-model="timeAdmission.blueTime"
                            :disabled="setForm.chargeEmployState == 1"
                            class="time-picker"
                            value-format="HH:mm:ss"
                            range-separator="至"
                            :picker-options="{
                                selectableRange: '00:00:00 - 23:59:59'
                            }"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            placeholder="选择生效时段">
                    </el-time-picker>
                    <svg-add-one theme="outline" size="16" @click="addblueType = 1" v-if="addblueType == 0" />
                </div>
                <div class="flex-time" v-if="addblueType == 1">
                    <el-time-picker
                            is-range
                            v-model="timeAdmission.blueTimes"
                            :disabled="setForm.chargeEmployState == 1"
                            class="time-picker"
                            value-format="HH:mm:ss"
                            range-separator="至"
                            :picker-options="{
                                selectableRange: '00:00:00 - 23:59:59'
                            }"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            placeholder="选择生效时段">
                    </el-time-picker>
                    <svg-reduce-one theme="outline" size="16" @click="addblueType = 0; timeAdmission.blueTimes = ''"/>
                </div>
          </el-form-item>
          <el-form-item label="绿牌车">
            <div class="flex-time">
                    <el-time-picker
                            is-range
                            v-model="timeAdmission.greenTime"
                            :disabled="setForm.chargeEmployState == 1"
                            class="time-picker"
                            value-format="HH:mm:ss"
                            range-separator="至"
                            :picker-options="{
                                selectableRange: '00:00:00 - 23:59:59'
                            }"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            placeholder="选择生效时段">
                    </el-time-picker>
                    <svg-add-one theme="outline" size="16" @click="addgreenType = 1" v-if="addgreenType == 0" />
                </div>
                <div class="flex-time" v-if="addgreenType == 1">
                    <el-time-picker
                            is-range
                            v-model="timeAdmission.greenTimes"
                            :disabled="setForm.chargeEmployState == 1"
                            class="time-picker"
                            value-format="HH:mm:ss"
                            range-separator="至"
                            :picker-options="{
                                selectableRange: '00:00:00 - 23:59:59'
                            }"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            placeholder="选择生效时段">
                    </el-time-picker>
                    <svg-reduce-one theme="outline" size="16" @click="addgreenType = 0; timeAdmission.greenTimes = ''"/>
                </div>
            </el-form-item>
          </div> -->
          
          <div class="algorConfig" v-if="updateType == 1&& setForm.chargeEmployState==1">
            <div class="configTitle">占位识别</div>
            <div class="configSelect">
                <el-radio-group v-model="setForm.strategyType">
                    <el-radio label="1" border>相机识别算法</el-radio>
                    <el-radio label="2" border>融合算法</el-radio>
                    <el-radio label="3" border>占桩算法</el-radio>
                </el-radio-group>
                <div class="configTips">相机识别算法：根据相机识别算法判断车辆进出车位</div>
                <div class="configTips">融合算法：根据道闸杆开关到位或传感器信号等与相机融合计算，判断车辆进出车位，无道闸请勿选择本算法</div>
            </div>
          </div>
          
      </el-form>
      <footer slot="footer" class="dialog-footer">
          <el-button type="primary" v-show="!readonly" v-if="updateType == 1" class="custom-btns-style" style="margin-left: 30px"
              @click="submit" :loading="editloading" :disabled="applyName !== null && empowerType !== 0">确 定</el-button>
      </footer>
    </div>
  </template>
  <script>
  
  export default {
    name:'placeholderInfor',
    props: {
      bolinkId: {
        type: Number,
        default: 0
      },
      applyName: {
          type: String,
          default: null
      },
      empowerType: {
        type: Number,
        default: 0
      },
      updateType: {
        type: Number,
        default: 1
      }
    },
    data() {
      return {
          editloading: false,
          readonly: false,
          parkinglotList: [], //车位列表
          parkingFrom:{},
          timeAdmission:{
          },
          availableTimeSlot: {}, //收费时段
          parkingLotAccessTbs:[],
          addblueType:0,
          addgreenType:0,
          setForm: {
              typePlaceholderCharge:2,
              feesStandard: {
                  noOrder: {},
                  order: {},
              }
          },
          baseParams: {}, //初始化信息
          seatRuleList:[], //占位计费标准名称列表
          rules: {
              name: [{
                  required: true,
                  message: "请填写占位计费标准名称",
                  trigger: 'blur'
              }],
              typePlaceholderCharge: [{
              required: true,
              message: "请选择占位费收费类型",
              trigger: 'blur'
              }],
              prepaidPlaceholder:[
                  {
                      required: true,
                      message: "请填写预付占位押金",
                      trigger: 'blur'
                  },
                  {
                      pattern: /^[0-9]\d*\.?\d*$|^0\.\d*[1-9]\d*$/,
                      message: '金额只允许填写非负数,保留两位小数，长度限10个字符！',
                      trigger: 'blur'
                  }
                  ],
              orderFreeTime: [{
                      required: true,
                      message: "请填写充电结束后免费时长",
                      trigger: 'blur'
                  },
                  {
                      pattern: /^[0-9]+$/,
                      message: '充电结束后免费时长只允许填写正整数！',
                      trigger: 'blur'
                  }
              ],
              noOrderFreeTime: [{
                      required: true,
                      message: "请填写进入车位免费时长",
                      trigger: 'blur'
                  },
                  {
                      pattern: /^[0-9]+$/,
                      message: '进入车位免费时长只允许填写正整数！',
                      trigger: 'blur'
                  }
              ],
              time: [{
                      required: true,
                      message: "请填写时间",
                      trigger: 'blur'
                  },
                  {
                      pattern: /^[0-9]+$/,
                      message: '时间只允许填写正整数！',
                      trigger: 'blur'
                  }
              ],
              fee: [
                  {
                      required: true,
                      message: "请填写金额",
                      trigger: 'blur'
                  },
                  {
                      pattern: /^[0-9]\d*\.?\d*$|^0\.\d*[1-9]\d*$/,
                      message: '金额只允许填写非负数,保留两位小数，长度限10个字符！',
                      trigger: 'blur'
                  }
              ],
          },
      }
    },
    mounted() {
      this.initData()
    },
    methods: {
      handleState(e){
        this.$forceUpdate()
      },
      //关闭弹窗清除记录
      clearData(){
          this.setForm =  {
              typePlaceholderCharge:2,
              feesStandard: {
                  noOrder: {},
                  order: {},
              }
          }

          this.availableTimeSlot = {}
      },
  
      /* 初始化 */
      initData() {
          let baseParams= {}
          let loginuin = sessionStorage.getItem("loginuin"),
            cityid = sessionStorage.getItem("cityid"),
            serverId = sessionStorage.getItem("serverid"),
            comid = sessionStorage.getItem("comid")
          if (comid != '0' && comid != 'undefined') {
              baseParams.comid = comid
          } else if (serverId != 'undefined'&& serverId!==null) {
              baseParams.serverId = serverId
          } else if (cityid != 'undefined') {
              baseParams.cityid = cityid
          } else {
              baseParams.backstageId = loginuin
          }
          this.baseParams = baseParams;
      },
  
      //选择框可输入
      ruleBlurEvent(e){
          if (e.target.value) {
              // 如果是对象，要使用this.$set方法
              this.$set(this.setForm, 'name', e.target.value)
          }
      },
  
      //获取车位列表
      getParkList(parkList){
          this.parkinglotList = parkList;
      },
  
  
      //获取车位占位信息
      getPlaceholderInfor(parkingFrom){
        this.parkingFrom = parkingFrom;
        sessionStorage.setItem('parkingFromInfo',JSON.stringify(parkingFrom)|| '')
          this.$axios.get("/parkingSpaceInfo/queryBillByParkingSpaceBolinkId?bolinkId="+ parkingFrom.id).then(res => {            
              let data = res.data;
              if (data.code === 200) {
                  const occupyBasis = data.data.occupyBasis;
                  let parkingLotAccessTbs = data.data.parkingLotAccessTbs;
                  if(parkingLotAccessTbs){
                    this.parkingLotAccessTbs = parkingLotAccessTbs;
                  }
                  let chargeEmployState = data.data.chargeEmployState;
                  this.setForm.chargeEmployState = data.data.chargeEmployState;
                  const strategyType = data.data.strategyType.toString();
                  this.handleEdit(occupyBasis,chargeEmployState,strategyType)
              } 
            })
          .catch(err => {
              
          })
      },
  
      seatNameChange(val){
          if(!val){
              this.seatRuleList=[]
          }else{
              this.seatRuleList.forEach(item => {
                  if(item.id == val){
                      this.handleEdit(item)
                  }
              });
          }
      },
  
      //获取占位计费标准名称列表
      seatNameSearch(val){
          let data = {
            name: val,
            deviceType: 5,
            ...this.baseParams
          }
          
          let loginuin = sessionStorage.getItem("loginuin"),
            cityid = sessionStorage.getItem("cityid"),
            serverId = sessionStorage.getItem("serverid"),
            comid = sessionStorage.getItem("comid")
          if (comid != '0' && comid != 'undefined') {
              data.comid = comid
          } else if (serverId != 'undefined'&& serverId!==null) {
              data.serverId = serverId
          } else if (cityid != 'undefined') {
              data.cityid = cityid
          } else {
              data.backstageId = loginuin
          }
  
          this.organizationIdSearchTimer = null
          clearTimeout(this.organizationIdSearchTimer);
          this.organizationIdSearchTimer = setTimeout(() => {
            this.searchCriteria(data)
          }, 400)
      },
  
      // 查询搜索条件下拉
      searchCriteria(data){
        this.$axios.post('/billingSettings/queryBillList', data).then(res => {
            let data = res.data
             if (data.code === 200) {
              this.seatRuleList= data.data;
                          
             } else {
                 this.seatRuleList=[]         
             }
                      
             })
             .catch(err => {
                     this.seatRuleList=[]
             })
        },

        //提交车位准入信息
        accessSubmit(){
            const timeData = this.timeAdmission;
            const parkingFrom = JSON.parse(sessionStorage.getItem("parkingFromInfo"));
            let parkingLotAccessTbs = [];
            if(timeData){
                if(timeData.blueTime){
                    const blueTime = timeData.blueTime;
                    parkingLotAccessTbs.push({
                        comid: parkingFrom.comid, //车场id
                        parkingLotId: parkingFrom.id, //车位id
                        carType: 1, //车牌类型 1 蓝牌车 2 绿牌车
                        startTime: blueTime[0], //准入开始时间
                        endTime: blueTime[1] //准入结束时间
                    })
                }
                if(timeData.blueTimes){
                    const blueTimes = timeData.blueTimes;
                    parkingLotAccessTbs.push({
                        comid: parkingFrom.comid, //车场id
                        parkingLotId: parkingFrom.id, //车位id
                        carType: 1, //车牌类型 1 蓝牌车 2 绿牌车
                        startTime: blueTimes[0], //准入开始时间
                        endTime: blueTimes[1] //准入结束时间
                    })
                }
                if(timeData.greenTime){
                    const greenTime = timeData.greenTime;
                    parkingLotAccessTbs.push({
                        comid: parkingFrom.comid, //车场id
                        parkingLotId: parkingFrom.id, //车位id
                        carType: 2, //车牌类型 1 蓝牌车 2 绿牌车
                        startTime: greenTime[0], //准入开始时间
                        endTime: greenTime[1] //准入结束时间
                    })
                }
                if(timeData.greenTimes){
                    const greenTimes = timeData.greenTimes;
                    parkingLotAccessTbs.push({
                        comid: parkingFrom.comid, //车场id
                        parkingLotId: parkingFrom.id, //车位id
                        carType: 2, //车牌类型 1 蓝牌车 2 绿牌车
                        startTime: greenTimes[0], //准入开始时间
                        endTime: greenTimes[1] //准入结束时间
                    })
                }
            }
            const _this = this
            const {
                setForm,
                bolinkId
            } = _this
            const {
                id,
            } = setForm;

            const api = '/parkingSpaceInfo/updateByBolinkId'
            const selectPull = [];
            if(this.parkinglotList){
                const selectInfo = this.parkinglotList.filter(item => item.selectType == 1)
                selectInfo.forEach(item =>{
                    selectPull.push(item.id)
                })
            }
            let data = {
                ...this.baseParams,
                deviceType: 5,
                parkingLotAccessTbs,
                updateType: 2,
                occupyBasis:id || '', //占位计费模型id
            }

            if(bolinkId > 0){
                data.bolinkId = bolinkId; //车位id
            }

            if(selectPull.length > 0){
                data.bolinkIds = selectPull; //车位id数组
            }

            _this.editloading = true

            _this.$axios.post(api, data, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(res => {
                if (res.data.code == 200) {
                    _this.$message.success(res.data.message)
                    _this.$emit('closeDialog', false)
                    _this.resetForm()
                } else if (res.data.code == 500) {
                    _this.$message.error(res.data.message)
                }
                setTimeout(() => {
                    _this.editloading = false
                }, 1000)
            }).catch(err => {
                _this.editloading = false
                setTimeout(() => {
                    _this.editloading = false
                }, 1000)
            });
        },
  
      /* 提交占位计费标准 */
      submit() {
        console.log('点击了提交单个设置占位的信息0000')
          const _this = this
          const api = '/parkingSpaceInfo/updateByBolinkId'
          const {
                setForm,
                availableTimeSlot,
                bolinkId
               } = _this
          const selectPull = [];
          if(this.parkinglotList){
              const selectInfo = this.parkinglotList.filter(item => item.selectType == 1)
              selectInfo.forEach(item =>{
                  selectPull.push(item.id)
              })
          }
          if(this.setForm.chargeEmployState == 1){
          this.$refs['setForm'].validate((valid) => {
              if (valid) {
                  const {
                      id,
                      feesStandard,
                      name,
                      noOrderFreeTime,
                      orderFreeTime,
                      prepaidPlaceholder,
                      strategyType,
                      typePlaceholderCharge,
                      chargeEmployState
                  } = setForm;
                  let parkAvailableTime = '';
                  let chargeAvailableTime = '';
                  let availableTimeInfo = {};
                    parkAvailableTime = availableTimeSlot.parkAvailableTime[0] + '-' + availableTimeSlot.parkAvailableTime[1]
                    chargeAvailableTime = availableTimeSlot.chargeAvailableTime[0] + '-' + availableTimeSlot.chargeAvailableTime[1]
                    availableTimeInfo = {parkAvailableTime:[parkAvailableTime],chargeAvailableTime:[chargeAvailableTime]}
                  
                  console.log(chargeAvailableTime)
                  console.log(parkAvailableTime)
                  console.log(availableTimeInfo)
                
               
                  let data = {
                      name,
                      noOrderFreeTime,
                      orderFreeTime,
                      prepaidPlaceholder,
                      strategyType,
                      typePlaceholderCharge,
                      ...this.baseParams,
                      deviceType: 5,
                      chargeEmployState,
                      updateType: 1,
                      occupyBasis:id || '', //占位计费模型id
                      feesStandard: JSON.stringify(feesStandard),
                      availableTimeSlots: JSON.stringify(availableTimeInfo) 
                  }
                  
                  // prepaidPlaceholder: Number(data.prepaidPlaceholder),
                  if(bolinkId > 0){
                      data.bolinkId = bolinkId; //车位id
                  }
  
                  if(selectPull.length > 0){
                      data.bolinkIds = selectPull; //车位id数组
                  }
                  _this.editloading = true
                  _this.$axios.post(api, data, {
                      headers: {
                          'Content-Type': 'application/json'
                      }
                  }).then(res => {
                      if (res.data.code == 200) {
                        console.log('单个设置占位提交成功------------now');
                          _this.$message.success(res.data.message)
                          _this.$emit('closeDialog', false)
                          _this.resetForm()
                      } else if (res.data.code == 500) {
                          _this.$message.error(res.data.message)
                      }
                      setTimeout(() => {
                          _this.editloading = false
                      }, 1000)
                  }).catch(err => {
                      _this.editloading = false
                      setTimeout(() => {
                          _this.editloading = false
                      }, 1000)
                  });
              }
          })
        } else {
            let params = {
                 ...this.baseParams,
                 updateType:1,
                 chargeEmployState: 0,
            }
            if(bolinkId > 0){
              params.bolinkId = bolinkId; //车位id
            }
  
            if(selectPull.length > 0){
              params.bolinkIds = selectPull; //车位id数组
            }
            _this.editloading = true
  
            _this.$axios.post(api, params, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(res => {
                if (res.data.code == 200) {
                    _this.$message.success(res.data.message)
                    _this.$emit('closeDialog', false)
                    _this.resetForm()
                } else if (res.data.code == 500) {
                    _this.$message.error(res.data.message)
                }
                setTimeout(() => {
                    _this.editloading = false
                }, 1000)
            }).catch(err => {
                _this.editloading = false
                setTimeout(() => {
                    _this.editloading = false
                }, 1000)
            });

        }
      },
      /* 编辑计费标准 */
      handleEdit(row,chargeEmployState,strategyType) {
          let parkingLotAccessTbs = this.parkingLotAccessTbs;
          if(parkingLotAccessTbs){
            const blueTimeData = parkingLotAccessTbs[1];
            const greenTimeData = parkingLotAccessTbs[2];
            if(blueTimeData){
                blueTimeData.forEach((item, index) =>{
                    if(index == 0){
                        this.$set(this.timeAdmission, 'blueTime', [
                            item.startTime,
                            item.endTime
                        ])
                    }

                    if(index == 1){
                        this.addblueType = 1
                        this.$set(this.timeAdmission, 'blueTimes', [
                            item.startTime,
                            item.endTime
                        ])
                    }
                })
            }
            if(greenTimeData){
                greenTimeData.forEach((item, index) =>{
                    if(index == 0){
                        this.$set(this.timeAdmission, 'greenTime', [
                            item.startTime,
                            item.endTime
                        ])
                    }

                    if(index == 1){
                        this.addgreenType = 1
                        this.$set(this.timeAdmission, 'greenTimes', [
                            item.startTime,
                            item.endTime
                        ])
                    }
                })
            }
          }
          let {
              name,
              id,
              feesStandard,
              orderFreeTime,
              noOrderFreeTime,
              prepaidPlaceholder,
              availableTimeSlots,
              typePlaceholderCharge
          } = row
          feesStandard = feesStandard ? JSON.parse(feesStandard) : []
          if(availableTimeSlots){
            let availableTimeInfo = JSON.parse(availableTimeSlots);
            let parkAvailableTime = availableTimeInfo.parkAvailableTime ? availableTimeInfo.parkAvailableTime[0].split("-") : []
            let chargeAvailableTime = availableTimeInfo.chargeAvailableTime ? availableTimeInfo.chargeAvailableTime[0].split("-") : []
            this.availableTimeSlot = {
                parkAvailableTime,
                chargeAvailableTime
            }
          }else{
            this.availableTimeSlot = {}
          }
          
          this.setForm = {
              id,
              name,
              feesStandard,
              orderFreeTime,
              noOrderFreeTime,
              prepaidPlaceholder,
              strategyType,
              typePlaceholderCharge,
              chargeEmployState,
          }
      },
    },
  }
  </script>
  <style  lang="scss" scoped>
  .placeholder-infor{
      height: 86vh;
      overflow: auto;
  }
  .time-picker{
    margin-bottom: 10px;
  }
  .parking-access-title{
    font-size: 20px;
    color: #333;
  }
  .add-item-title{
    display: flex;
    align-items: center;
    .tips{
        margin-top: 10px;
        margin-left: 4px;
        cursor: pointer;
    }
  }

  .algorConfig{
    display: flex;
    margin-bottom: 20px;
    .configTitle{
        color: #333;
        font-size: 18px;
        width: 140px;
        margin-top: 8px;
    }
    
    .configSelect{
        margin-left: 40px;
    }
    .configTips{
        color: red;
        font-size: 12px;
        margin-top: 10px;
    }
  }
  .tips{
    color: #999;
    font-size: 14px;
    background: #fff;
    line-height: 20px;
    margin: 6px 0;
    span{
        vertical-align: middle;
        margin-right: 6px;
    }
 }
 .flex-time{
    display: flex;
    /deep/.i-icon{
        margin-left: 6px;
    }
 }
  .header {
      display: inline-flex;
      position: relative;
      height: 30px;
      margin-bottom: 30px;
      align-items: center;
  
      .title {
        margin-left: 8px;
        font-size: 16px;
        width: 300px;
  
        .setting {}
      }
  
      .title::before {
        position: absolute;
        top: 5px;
        left: 0;
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-image: linear-gradient(-170deg, #6dbfff 0%, #1355d9 100%);
      }
  }
  
  .flex-seat{
      display: flex;
      /deep/.el-input{
          width: 140px;
      }
  }
  </style>
  