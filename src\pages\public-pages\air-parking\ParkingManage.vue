<template>
  <section class="right-wrapper-size shop-table-wrapper" id="scrollBarDom">
    <div class="shop-custom-operation">
      <div class="shop-custom-operation">
        <header class="shop-custom-header">
          <p style="float: left">
            机场停车<span style="margin: 2px">-</span>车场管理
          </p>
          <div class="float-right">
            <el-button type="text" size="mini" @click="resetForm" icon="el-icon-refresh" style="font-size: 14px;color: #1E1E1E;">刷新</el-button>
          </div>
        </header>
      </div>

      <super-form
        :form-config="formConfig"
        :value="searchForm"
        @input="handleSearch"
      />
    </div>

    <div class="count-table-wrapper-style">
      <bl-table
        border
        ref="tableRef"
        :api="fetchTableData"
        :request-params="searchForm"
        :showToolbar="true"
        :toolbarButtons="comId == 0 ? toolbarButtons : []"
        :showPagination="true"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handlePageChange"
      >
        <el-table-column prop="id" label="ID" align="center"></el-table-column>
        <el-table-column
          prop="lotName"
          label="车场名称(编号)"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="provinceName"
          label="省份"
          align="center"
        ></el-table-column>
        <el-table-column prop="cityName" label="城市" width="120" />
        <el-table-column prop="airportName" label="机场名称" width="180" />
        <el-table-column prop="feeStandard" label="收费标准" width="200" />
        <el-table-column prop="phone" label="接驳电话" width="150" />
        <el-table-column
          prop="operate"
          label="操作"
          align="center"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="handleEdit(scope.row)"
              v-if="comId == 0">编辑</el-button
            >
            <el-button type="text" @click="handleConfig(scope.row)"
              >设置</el-button
            >
            <el-button type="text" v-if="scope.row.status == 0" @click="handleStatusChange(scope.row,1)"
              >上架</el-button
            >
            <el-button type="text" v-if="scope.row.status == 1" @click="handleStatusChange(scope.row,0)"
              >下架</el-button
            >
          </template>
        </el-table-column>
      </bl-table>
    </div>

    <!-- 车场弹窗组件 -->
    <parking-dialog
      :visible.sync="dialogVisible"
      :form-data="currentData"
      @submit="handleDialogSubmit"
    />
  </section>
</template>

<script>
import {
  fetchParkingList,
  addParking,
  updateParking,
  deleteParking,
} from "@/api/airParking";
import SuperForm from "@/components/super-form/inline-form";
import BlTable from "@/components/BlTable/index.vue";
import ParkingDialog from "./components/ParkingDialog.vue";

export default {
  name: "ParkingManage",
  components: { SuperForm, BlTable, ParkingDialog },
  data() {
    return {
      searchForm: { airportName: "", lotName:"" },
      comId: sessionStorage.getItem('comid') || '',
      formConfig: {
        first: [
          {
            label: "机场名称",
            type: "input",
            prop: "airportName",
          },
          {
            label: "车场名称",
            type: "input",
            prop: "lotName",
          },
        ],
      },
      subjectId: sessionStorage.getItem('comid') || '',
      current: 1, // 当前页码
      size: 10, // 每页显示多少条数据
      requestUrl: this.$PUBLIC_URL.AIRPORT_API, // 请求接口
      toolbarButtons: [
        {
          label: "新增",
          handler: this.handleAdd,
        },
      ],
      dialogVisible: false,
      currentData: {},
    };
  },

  mounted() {
    this.fetchTableData();
  },

  methods: {
    async fetchTableData() {
      const params = { ...this.searchForm, size: this.size, current: this.current  };
      if(this.subjectId > 0){
        params.subjectId = this.subjectId
      }
      try {
        const response = await fetchParkingList(this.requestUrl, params);
        const dataList = response? response.data.data : [];
        console.log("请求响应:", response);
        return {
          list: dataList.records || [],
          total: dataList.total || 0,
        };
      } catch (error) {
        console.error("ParkingManage fetchParkingList 请求失败:", error); // 记录错误日志
        this.$message.error("fetchParkingList 加载数据失败，请稍后重试");
        return {
          list: [],
          total: 0,
        };
      }
    },

    async handleStatusChange(row, status) {
       const actionText = status === 1 ? "上架" : "下架";
    
      console.log(row, status);
    
      try {
        await this.$confirm(`确认${actionText}该车场？`, "提示", {
          type: "warning",
        });
    
        this.$axios
          .patch(
            `${this.requestUrl}/admin/parking-lot/${row.id}/status`,
            { status }
          )
          .then((response) => {
            console.log(response);
    
            // 校验响应结构
            if (!response || !response.data) {
              this.$message.error(`${actionText}失败`);
              return;
            }
    
            const { code } = response.data;
    
            if (String(code) === '200') {
              this.$message.success("成功");
            } else {
              this.$message.error("失败");
              return;
            }
    
            this.$refs.tableRef.loadData();
          });
      } catch (error) {
        if (error !== "cancel") {
          this.$message.error("操作失败");
          console.error("操作失败详情：", error); // 增加日志输出
        }
      }
    },

    // async handleStatusChange(row) {
    //   try {
    //     await updateParkingStatus(this.requestUrl, row.id, !row.status);
    //     this.$message.success("状态更新成功");
    //     this.fetchTableData();
    //   } catch (error) {
    //     this.$message.error("操作失败");
    //   }
    // },

    handleSearch(form) {
      this.searchForm = form;
      this.currentPage = 1;
      this.$refs.tableRef.loadData();
    },

    handleSizeChange(size) {
      this.size = size;
      this.$refs.tableRef.loadData();
    },

    handlePageChange(page) {
      console.log("handlePageChange", page);
      this.current = page;
      this.$refs.tableRef.loadData();
    },

    resetForm(){
      this.searchForm = { airportName: "", lotName:"" };
      this.current = 1;
      this.$refs.tableRef.loadData();
    },


    handleAdd() {
      this.currentData = {};
      this.dialogVisible = true;
    },

    handleEdit(row) {
      this.currentData = { ...row };
      this.dialogVisible = true;
    },

    handleConfig(row) {
      // 跳转到车场配置页面，传递车场ID
      this.$router.push({
        name: "ParkingConfig",
        query: { id: row.id, name: row.name },
      });
    },

    showErrorMessage(message) {
      this.$message.error(message);
    },

    async handleDialogSubmit(formData) {
      console.log("formData", formData);
      try {
        let response;
        if (formData.id) {
          // 编辑机场
          response = await updateParking(this.requestUrl, formData.id, formData);
        } else {
          // 新增机场
          response = await addParking(this.requestUrl, formData);
        }
    
        // 校验响应结构
        if (!response || !response.data) {
          this.showErrorMessage(formData.id ? '编辑失败' : '新增失败');
          return;
        }
    
        const { code } = response.data;
    
        if (code === 200 || code === '200') {
          this.$message.success(formData.id ? '编辑成功' : '新增成功');
        } else if (code === 409 || code === '409') {
          this.showErrorMessage('车场重复');
          return;
        } else {
          this.showErrorMessage(formData.id ? '编辑失败' : '新增失败');
          return;
        }
    
        this.dialogVisible = false;
        this.$refs.tableRef.loadData();

      } catch (error) {
        this.$message.error(error.message || "操作失败");
      }
    },

    // async handleDelete(row) {
    //   try {
    //     await this.$confirm("确认删除该车场信息？", "提示", {
    //       type: "warning",
    //     });
    //     await deleteParking(row.id);
    //     this.$message.success("删除成功");
    //     this.fetchTableData();
    //   } catch (error) {
    //     if (error !== "cancel") {
    //       this.$message.error("删除失败");
    //     }
    //   }
    // },

    showSettings() {},
  },
};
</script>
<style lang="scss" scoped>
// .shop-custom-header {
//   @include flex-space-between;
//   padding: 20px;
// }
</style>