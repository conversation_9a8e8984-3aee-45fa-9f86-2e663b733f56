/**
 * API请求工具类
 * 提供统一的请求处理、错误处理和加载状态管理
 */

import axios from 'axios';
import { ErrorHandler } from './formValidation';

// 创建axios实例
const apiClient = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 添加认证token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 添加请求时间戳
    config.metadata = { startTime: new Date() };
    
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  response => {
    // 计算请求耗时
    const endTime = new Date();
    const duration = endTime - response.config.metadata.startTime;
    console.log(`API请求耗时: ${duration}ms - ${response.config.url}`);
    
    return response;
  },
  error => {
    // 统一错误处理
    if (error.response?.status === 401) {
      // 清除token并跳转到登录页
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    
    return Promise.reject(error);
  }
);

/**
 * API请求管理器
 * 提供加载状态管理和错误处理
 */
export class ApiRequestManager {
  constructor(context) {
    this.context = context; // Vue组件实例
    this.loadingStates = {}; // 加载状态管理
    this.requestQueue = new Map(); // 请求队列管理
  }

  /**
   * 设置加载状态
   * @param {string} key - 加载状态键
   * @param {boolean} loading - 是否加载中
   */
  setLoading(key, loading) {
    if (this.context && this.context.loading) {
      this.context.$set(this.context.loading, key, loading);
    }
    this.loadingStates[key] = loading;
  }

  /**
   * 获取加载状态
   * @param {string} key - 加载状态键
   * @returns {boolean}
   */
  getLoading(key) {
    return this.loadingStates[key] || false;
  }

  /**
   * 执行API请求
   * @param {Function} requestFn - 请求函数
   * @param {Object} options - 选项
   * @returns {Promise}
   */
  async executeRequest(requestFn, options = {}) {
    const {
      loadingKey = 'default',
      showLoading = true,
      showSuccess = false,
      successMessage = '操作成功',
      errorMessage = '操作失败',
      onSuccess,
      onError,
      retryCount = 0,
      retryDelay = 1000
    } = options;

    // 防止重复请求
    const requestId = `${loadingKey}_${Date.now()}`;
    if (this.requestQueue.has(loadingKey)) {
      console.warn(`请求正在进行中: ${loadingKey}`);
      return;
    }

    this.requestQueue.set(loadingKey, requestId);

    try {
      // 设置加载状态
      if (showLoading) {
        this.setLoading(loadingKey, true);
      }

      // 执行请求
      const response = await this.retryRequest(requestFn, retryCount, retryDelay);
      
      // 处理成功响应
      if (showSuccess) {
        ErrorHandler.showSuccess(successMessage, this.context);
      }

      // 执行成功回调
      if (onSuccess) {
        await onSuccess(response.data);
      }

      return response.data;

    } catch (error) {
      // 处理错误
      if (onError) {
        await onError(error);
      } else {
        ErrorHandler.handleApiError(error, this.context, errorMessage);
      }
      
      throw error;

    } finally {
      // 清除加载状态和请求队列
      if (showLoading) {
        this.setLoading(loadingKey, false);
      }
      this.requestQueue.delete(loadingKey);
    }
  }

  /**
   * 重试请求
   * @param {Function} requestFn - 请求函数
   * @param {number} retryCount - 重试次数
   * @param {number} retryDelay - 重试延迟
   * @returns {Promise}
   */
  async retryRequest(requestFn, retryCount, retryDelay) {
    let lastError;
    
    for (let i = 0; i <= retryCount; i++) {
      try {
        return await requestFn();
      } catch (error) {
        lastError = error;
        
        // 如果是最后一次尝试或者是客户端错误，直接抛出
        if (i === retryCount || (error.response && error.response.status < 500)) {
          throw error;
        }
        
        // 等待后重试
        await this.delay(retryDelay * Math.pow(2, i)); // 指数退避
        console.warn(`请求失败，正在重试 (${i + 1}/${retryCount + 1}):`, error.message);
      }
    }
    
    throw lastError;
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise}
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 取消所有请求
   */
  cancelAllRequests() {
    this.requestQueue.clear();
    Object.keys(this.loadingStates).forEach(key => {
      this.setLoading(key, false);
    });
  }
}

/**
 * 车场配置相关API
 */
export class ParkingConfigApi {
  constructor(requestManager) {
    this.requestManager = requestManager;
  }

  /**
   * 获取车场基础信息
   * @param {string} parkingId - 车场ID
   * @returns {Promise}
   */
  async getBasicInfo(parkingId) {
    return this.requestManager.executeRequest(
      () => apiClient.get(`/parking/${parkingId}/basic`),
      {
        loadingKey: 'basic',
        errorMessage: '获取车场基础信息失败'
      }
    );
  }

  /**
   * 保存车场基础信息
   * @param {string} parkingId - 车场ID
   * @param {Object} data - 基础信息数据
   * @returns {Promise}
   */
  async saveBasicInfo(parkingId, data) {
    return this.requestManager.executeRequest(
      () => apiClient.put(`/parking/${parkingId}/basic`, data),
      {
        loadingKey: 'basic',
        showSuccess: true,
        successMessage: '车场基础信息保存成功',
        errorMessage: '保存车场基础信息失败'
      }
    );
  }

  /**
   * 获取停车费率信息
   * @param {string} parkingId - 车场ID
   * @returns {Promise}
   */
  async getVehicleRate(parkingId) {
    return this.requestManager.executeRequest(
      () => apiClient.get(`/parking/${parkingId}/vehicle-rate`),
      {
        loadingKey: 'vehicle',
        errorMessage: '获取停车费率信息失败'
      }
    );
  }

  /**
   * 保存停车费率信息
   * @param {string} parkingId - 车场ID
   * @param {Object} data - 费率数据
   * @returns {Promise}
   */
  async saveVehicleRate(parkingId, data) {
    return this.requestManager.executeRequest(
      () => apiClient.put(`/parking/${parkingId}/vehicle-rate`, data),
      {
        loadingKey: 'vehicle',
        showSuccess: true,
        successMessage: '停车费率保存成功',
        errorMessage: '保存停车费率失败'
      }
    );
  }

  /**
   * 获取接送服务信息
   * @param {string} parkingId - 车场ID
   * @returns {Promise}
   */
  async getShuttleService(parkingId) {
    return this.requestManager.executeRequest(
      () => apiClient.get(`/parking/${parkingId}/shuttle-service`),
      {
        loadingKey: 'shuttle',
        errorMessage: '获取接送服务信息失败'
      }
    );
  }

  /**
   * 保存接送服务信息
   * @param {string} parkingId - 车场ID
   * @param {Object} data - 接送服务数据
   * @returns {Promise}
   */
  async saveShuttleService(parkingId, data) {
    return this.requestManager.executeRequest(
      () => apiClient.put(`/parking/${parkingId}/shuttle-service`, data),
      {
        loadingKey: 'shuttle',
        showSuccess: true,
        successMessage: '接送服务信息保存成功',
        errorMessage: '保存接送服务信息失败'
      }
    );
  }

  /**
   * 获取分账信息
   * @param {string} parkingId - 车场ID
   * @returns {Promise}
   */
  async getAccountInfo(parkingId) {
    return this.requestManager.executeRequest(
      () => apiClient.get(`/parking/${parkingId}/account-info`),
      {
        loadingKey: 'account',
        errorMessage: '获取分账信息失败'
      }
    );
  }

  /**
   * 保存分账信息
   * @param {string} parkingId - 车场ID
   * @param {Object} data - 分账数据
   * @returns {Promise}
   */
  async saveAccountInfo(parkingId, data) {
    return this.requestManager.executeRequest(
      () => apiClient.put(`/parking/${parkingId}/account-info`, data),
      {
        loadingKey: 'account',
        showSuccess: true,
        successMessage: '分账信息保存成功',
        errorMessage: '保存分账信息失败'
      }
    );
  }

  /**
   * 上传文件
   * @param {File} file - 文件对象
   * @param {string} type - 文件类型
   * @returns {Promise}
   */
  async uploadFile(file, type = 'image') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    return this.requestManager.executeRequest(
      () => apiClient.post('/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }),
      {
        loadingKey: 'upload',
        showSuccess: true,
        successMessage: '文件上传成功',
        errorMessage: '文件上传失败'
      }
    );
  }

  /**
   * 获取机场列表
   * @returns {Promise}
   */
  async getAirportList() {
    return this.requestManager.executeRequest(
      () => apiClient.get('/airports'),
      {
        loadingKey: 'airports',
        errorMessage: '获取机场列表失败'
      }
    );
  }

  /**
   * 获取地区选项
   * @returns {Promise}
   */
  async getRegionOptions() {
    return this.requestManager.executeRequest(
      () => apiClient.get('/regions'),
      {
        loadingKey: 'regions',
        errorMessage: '获取地区选项失败'
      }
    );
  }
}

/**
 * 创建API实例的工厂函数
 * @param {Object} context - Vue组件实例
 * @returns {Object} API实例对象
 */
export function createApiInstance(context) {
  const requestManager = new ApiRequestManager(context);
  const parkingConfigApi = new ParkingConfigApi(requestManager);

  return {
    requestManager,
    parkingConfigApi
  };
}

export default {
  apiClient,
  ApiRequestManager,
  ParkingConfigApi,
  createApiInstance
};
