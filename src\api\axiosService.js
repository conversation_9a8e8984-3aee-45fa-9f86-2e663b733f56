import Vue from 'vue';
import { Message, MessageBox } from 'element-ui'
import axios from "axios";
import router from "../router";
import store from '../store';
import $ from 'jquery';
import util from '../common/js/util';


function changeUrlArg(url, arg, val) {
  let pattern = arg + '=([^&]*)';
  let replaceText = arg + '=' + val;
  return url.match(pattern) ? url.replace(eval('/(' + arg + '=)([^&]*)/gi'), replaceText) : (url.match('[\?]') ? url + '&' + replaceText : url + '?' + replaceText);
}

//根据环境引入不同的请求路径
axios.defaults.baseURL = process.env.BASE_API;
let SERVER_API = util.isHttps() ? "https:" + process.env.SERVER_API : "http:" + process.env.SERVER_API;
let BOLINK_API = util.isHttps() ? "https:" + process.env.BOLINK_API : "http:" + process.env.BOLINK_API;
let PUBLIC_URL = {
  BASE_API: process.env.BASE_API,
  SERVER_API: SERVER_API,
  SOCKET_API: process.env.SOCKET_API,
  BOLINK_API: BOLINK_API,
  PAAS_API: process.env.PAAS_API,
  JM_API:   process.env.JM_API,
  AIRPORT_API:   process.env.AIRPORT_API
};
let Authority = process.env.CONTRAST == 'test' ? require('@/common/js/const_test.js') : require('@/common/js/const_yun.js');
Vue.prototype.$PUBLIC_URL = PUBLIC_URL;
Vue.prototype.$Authority = Authority;
const PathInterception = new RegExp('^' + BOLINK_API + '.*')
//所有请求头都加上token   然后验证
axios.interceptors.request.use(
  config => {
    //云平台login 过滤
    if ((config.url.indexOf("/user/dologin")) > -1) {
      return config;
    }
    // if (config.method == 'patch') {
    //   console.log(config.url,'url---')
    //   console.log(config.method,'method---')
    //   config.headers.patch['Content-Type'] = 'application/json'
    // }
    //泊链路径过滤
    if (PathInterception.test(config.url)) {
      let users = JSON.parse(sessionStorage.getItem("user"))
      $.ajax({
        type: 'post',
        url: `${PUBLIC_URL.BOLINK_API}/parkingos/gettoken`,
        data: users.token_params,
        async: false,
        success: function (res) {
          let data = JSON.parse(res);
          if (data.state === 1) {
            if (config.method == 'post') {
              if (config.data != undefined) {
                config.data.set('token', data.token)
              } else {
                config.url = changeUrlArg(config.url, 'token', data.token)
              }

            } else if (config.method == 'get') {
              config.url = changeUrlArg(config.url, 'token', data.token)
            } else {

            }
            sessionStorage.setItem('token', data.token);
          }

          return config;
        }
      })
      return config;
    }
    //泊链路径过滤
    if ((config.url.indexOf("bt1.bolink.club")) > -1 || (config.url.indexOf("bt2.bolink.club")) > -1||config.url.indexOf('/official/getdata/') > -1) {
      return config;

    }

    let token = sessionStorage.getItem('cloud_token')
    let token4G = sessionStorage.getItem('uToken')
    let tokenYun = sessionStorage.getItem('tokenYun')
    const randomNumber = Math.floor(100000000000000000000 + Math.random() * 900000000000000000000);
    const currentTimestamp = new Date().getTime();
    config.headers['traceId'] = randomNumber.toString(36) + currentTimestamp;
    if (token) {
      config.headers['token'] = token;
    }
    if (token4G) {
      config.headers['token4G'] = token4G
    }
    if (tokenYun) {
      config.headers['tokenYun'] = tokenYun
    }
    return config
  },
  error => {
    console.log(error) // for debug
    Promise.reject(error)
  }
)
// response interceptor
/**
 * @desc axios响应处理优化
 * @validate 0--登录超时 1--被挤掉 2--令牌无效 3--没有权限登录
 * @is_kicked 1--被挤掉
 * @result fail-- 登录超时
 */
axios.interceptors.response.use(response => {
  const ret = response.data;
  // console.log(router)
  let currentFullPath = router.history.current.fullPath
  let tip = '';// 提示文案
  const validate = String(ret.validate);
  const is_kicked = String(ret.is_kicked);
  const result = String(ret.result);

  if (['0', '1', '2', '3'].includes(validate)) {
    switch (validate) {
      case '0':
        tip = "登录超时,请重新登录!"
        break;
      case '1':
        tip = "当前账号在另一地点登录，您被迫下线了。如果这不是您本人的操作，那么您的密码可能已经泄露，建议您修改密码。";
        break;
      case '2':
        tip = "令牌无效，请尝试重新登录"
        break;
      case '3':
        tip = "无登录权限，请联系平台工作人员"
        break;
    }
  } else if (is_kicked === '1') {
    tip = "当前账号在另一地点登录，您被迫下线了。如果这不是您本人的操作，那么您的密码可能已经泄露，建议您修改密码。";
  } else if (result === 'fail') {
    tip = "登录超时,请重新登录!";
  } else {
    tip = '';
  }

  // 当提示文案为空时，正常返回
  if (tip === '') {
    return response;
  } else {
    if (store.state.app.gateState) {
      store.state.app.gateState = false;
      setTimeout(() => {
        // 强制清除全部定时器
        let idx = setTimeout(() => { }, 0);
        while (idx > 0) {
          window.clearTimeout(idx);
          idx--;
        }
        sessionStorage.clear();
        MessageBox.alert(tip, '提示', {
          confirmButtonText: '确定',
          type: 'warning',
          callback: action => {
            store.state.app.gateState = true;
            store.dispatch('tagsView/delAllViews').then(({ visitedViews, cachedViews }) => {
              if (currentFullPath == "/Yun_Onduty") {
                localStorage.setItem('CloudLastPath', 'Yun_Onduty')
                location.reload()
              }
              router.push({
                path: '/loginCloud'
              })
            })
          }
        })
      }, 150);
    }
    return false;
  }
}, reject => {
  Message({
    message: reject.message || '请求失败',
    type: 'error',
    duration: 5 * 1000
  })
  return Promise.reject(reject)
})
Vue.prototype.$axios = axios;


export default axios;