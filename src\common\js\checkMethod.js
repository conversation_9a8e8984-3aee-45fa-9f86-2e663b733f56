//校验正整数
var validateInteger = (rule, value, callback) => {
    if (!value) {
        return callback(new Error('请输入'));
    }
    if(!(/(^[1-9]\d*$)/.test(value))){
        callback(new Error('请输入正整数'));
    }else{
        callback();
    }
};
//校验区间范围 0.1~10
var validateOneDecimal = (rule, value, callback) => {
    if (!value) {
        return callback(new Error('请输入'));
    }
    var reg = /^(?:([1-9](?:\.[\d]{0,1})?)|(?:0\.[1-9]{1,1})|10)$/;
    if(!(reg.test(value))){
        callback(new Error('请输入折扣值0.1~10范围内的值'));
    }else{
        callback();
    }
};
//保留一位小数
var validateOneNumber = (value)=>{
    var reg = /^\d+(\.\d{1})?$/;
    if (typeof value === 'undefined' || value === '') {
        return false;
    } else if (!reg.test(value) || value === '0') {
        return false;
    } else {
        return true;
    }
};
/**
 * @description 校验金额，不能为负数
 * @param rule
 * @param value
 * @param callback
 */
const validateMoney = (rule, value, callback) => {
  const reg = /(^[0-9](\d+)?(\.\d{1,2})?$)|(^[1-9]$)|(^\d\.[1-9]{1,2}$)|(^\d\.[0]{1}[1-9]{1}$|(^\d\.[1-9]{1}[0]{1}$)$)/;
  if (typeof(value) === 'undefined' || value === '') {
    return callback(new Error('请输入正确数值'));
  } else if (!(reg.test(value))) {
    return callback(new Error('请输入正确数值'));
  } else {
    callback();
  }
}
/**
 * 校验车场手机号
 * @param rule
 * @param value
 * @param callback
 * @returns {*}
 */
const validateMobile = (rule, value, callback) => {
    if (typeof(value) === 'undefined' || value === '') {
        return callback(new Error('请输入手机号码'));
    } else if (!((/^1[3456789]\d{9}$/.test(value)))) {
        return callback(new Error('请输入正确的手机号码'));
    } else {
        callback();
    }
};
/**
 * 校验电话号码
 * @param rule
 * @param value
 * @param callback
 * @returns {*}
 */
const validateTelPhone = (rule, value, callback) => {
    if (typeof(value) === 'undefined' || value === '') {
        callback();
    } else if (!((/^800[0-9]{7}$/.test(value)) || (/^400[0-9]{7}$/.test(value)) || (/^(0[0-9]{2,3}\-)([2-9][0-9]{6,7})$/.test(value)))) {
        return callback(new Error('请输入座机(区号后加-)或400,800开头号码'));
    } else {
        callback();
    }
};

const checkNumber = (rule, value, callback) => {
    if (typeof(value) === 'undefined' || value === '') {
        return callback(new Error('请输入电话号码'));
    } else if (!(/^[0-9]*$/.test(value)) || String(value).length>20) {
        return callback(new Error('请输入正确的电话号码'));
    } else {
        callback();
    }
};

// 允许座机和移动手机号
const checkPhone = (rule, value, callback) => {
    const checkTelReg=((/^800[0-9]{7}$/.test(value)) || (/^400[0-9]{7}$/.test(value)) || (/^(0[0-9]{2,3}\-)([1-9][0-9]{6,7})$/.test(value)))
    const checkMobileReg=(/^1[3456789]\d{9}$/.test(value))
    
    if (typeof(value) === 'undefined' || value === '') {
        return callback(new Error('请输入电话号码'));
    } else if (!checkTelReg && !checkMobileReg) {
        return callback(new Error('请输入正确的手机号码或者座机号码'));
    } else {
        callback();
    }
};

//时间控件只允许选择一个月
function isNull(value) {
    if (!value && typeof value != "undefined" && value != 0) {
        return true;
    } else {
        return false;
    }
}

/**
 * 时间选择器失去焦点后，主动把变量重置为null
 * @param event
 */
const pickerReset = (event)=>{
    event.pickerOptions.onPick({
        minDate:null,
        maxDate:null
    })

}
/**
 *
 * @param limitDays 禁用时区 默认61天
 * @returns {boolean|{disabledDate(*): *, onPick: onPick}}
 */
const pickerOptionsInfo = (limitDays,e)=>{
    let choiceDate = null;
    let max_date = null;
    let limit_days = limitDays?limitDays:61;
    return {
        onPick: ({ maxDate, minDate }) => {
            if(minDate == null){
                choiceDate = '';
            }else{
                choiceDate = minDate.getTime();
            }
            if (maxDate) {
                max_date = maxDate.getTime();
                choiceDate = '';
            }
        },
        disabledDate(time){
            let tep = false;
            let currentTime = (new Date(new Date(new Date().toLocaleDateString()).getTime()).getTime())+ 3600 * 1000 * 24 * 1;
            if(!isNull(choiceDate)){
                if (e ==1){
                    tep = time.getTime() >  new Date(choiceDate).getTime()+limit_days*24*3600000
                        || time.getTime()>= (Date.now()-8.64e7)
                        || time.getTime() <  new Date(choiceDate).getTime()-limit_days*24*3600000;
                }else {
                    tep = time.getTime() >  new Date(choiceDate).getTime()+limit_days*24*3600000
                        || time.getTime()>= currentTime
                        || time.getTime() <  new Date(choiceDate).getTime()-limit_days*24*3600000;
                }
            }else{
                tep = time.getTime()>= currentTime;
            }

            return  tep;
        }
    }
};
/**
 *
 * @param limitDays 禁用时区 默认31天
 * @returns {boolean|{disabledDate(*): *, onPick: onPick}}
 */
const pickerOptions = (limitDays,e)=>{
    let choiceDate = null;
    let max_date = null;
    let limit_days = limitDays?limitDays:31;
    return {
        onPick: ({ maxDate, minDate }) => {
            if(minDate == null){
                choiceDate = '';
            }else{
                choiceDate = minDate.getTime();
            }
            if (maxDate) {
                max_date = maxDate.getTime();
                choiceDate = '';
            }
        },
        disabledDate(time){
            let tep = false;
            let currentTime = (new Date(new Date(new Date().toLocaleDateString()).getTime()).getTime())+ 3600 * 1000 * 24 * 1;
            if(!isNull(choiceDate)){
                if (e ==1){
                    tep = time.getTime() >  new Date(choiceDate).getTime()+limit_days*24*3600000
                        || time.getTime()>= (Date.now()-8.64e7)
                        || time.getTime() <  new Date(choiceDate).getTime()-limit_days*24*3600000;
                }else {
                    tep = time.getTime() >  new Date(choiceDate).getTime()+limit_days*24*3600000
                        || time.getTime()>= currentTime
                        || time.getTime() <  new Date(choiceDate).getTime()-limit_days*24*3600000;
                }
            }else{
                tep = time.getTime()>= currentTime;
            }

            return  tep;
        }
    }
};
/**
 *
 * @param limitDays 时间区间，默认一天
 * @returns {boolean|{disabledDate(*): *, onPick: onPick}}
 */
const getRange = (limitDays=1,time=new Date())=>{
    let year = time.getFullYear()
    let month = time.getMonth()
    let day = time.getDate()

    let endTime = new Date(year, month, day+1) - 1000
    let startTime = new Date(year, month, day).getTime() - (limitDays-1)*3600*24*1000
    return [ startTime ,endTime ]
}
/**
 *
 * @param limitDays 禁用时区 默认90天
 * @returns {boolean|{disabledDate(*): *, onPick: onPick}}
 */
 const chargeTimePickerOptions = (limitDays=90,e)=>{
    let choiceDate = null;
    let max_date = null;
    return {
        shortcuts: [{
            text: '最近一周',
            onClick(picker) {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 8 - 1);
                picker.$emit('pick',getRange(7));
            }
            }, {
            text: '最近一个月',
            onClick(picker) {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                picker.$emit('pick', getRange(30));
            }
            }, {
            text: '最近三个月',
            onClick(picker) {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                picker.$emit('pick',  getRange(90));
            }
        }],
        onPick: ({ maxDate, minDate }) => {
            if(minDate == null){
                choiceDate = '';
            }else{
                choiceDate = minDate.getTime();
            }
            if (maxDate) {
                max_date = maxDate.getTime();
                choiceDate = '';
            }
        },
        disabledDate:(time)=>{
            let tep = false;
            let currentTime = (new Date(new Date(new Date().toLocaleDateString()).getTime()).getTime())+ 3600 * 1000 * 24 * 1;
            if(!isNull(choiceDate)){
                if (e ==1){
                    tep = time.getTime() >  new Date(choiceDate).getTime()+limitDays*24*3600000
                        || time.getTime()>= (Date.now()-8.64e7)
                        || time.getTime() <  new Date(choiceDate).getTime()-limitDays*24*3600000;
                }else {
                    tep = time.getTime() >  new Date(choiceDate).getTime()+limitDays*24*3600000
                        || time.getTime()>= currentTime
                        || time.getTime() <  new Date(choiceDate).getTime()-limitDays*24*3600000;
                }
            }else{
                tep = time.getTime()>= currentTime;
            }
            return  tep;
        }
    }
};
const pickerFixedOptions = (limitDays)=>{
    let max_date = null;
    let startTime =null;
    let endTime =null;
    let limit_days = limitDays?limitDays:31;
    return {
        onPick: ({ maxDate, minDate }) => {
            if(minDate == null){
                startTime = '';
                endTime = '';
            }else{
                let year = minDate.getFullYear()
                let month = minDate.getMonth()
                startTime = new Date(year, month, 1).getTime()
                endTime =  new Date(year, month + 1, 0).getTime() + 3600 * 1000 * 24 * 1;
            }
            if (maxDate) {
                max_date = maxDate.getTime();
            }
        },
        disabledDate(time){
            let tep = false;
            let currentTime = (new Date(new Date(new Date().toLocaleDateString()).getTime()).getTime())+ 3600 * 1000 * 24 * 1;
            if(endTime == '' || endTime == null || endTime == undefined){
                tep = time.getTime()>= currentTime;
            }else{
                tep = time.getTime() > (endTime-1) || time.getTime() < startTime || time.getTime()>= currentTime;
            }
            return  tep;
        }
    }
};
module.exports = {
    validateInteger:validateInteger,
    validateOneDecimal:validateOneDecimal,
    validateOneNumber:validateOneNumber,
    validateMobile:validateMobile,
    validateTelPhone:validateTelPhone,
    validateMoney:validateMoney,
    checkNumber:checkNumber,
    checkPhone:checkPhone,
    PickerOptions:pickerOptions,
    PickerOptionsInfo:pickerOptionsInfo,
    PickerReset:pickerReset,
    pickerFixedOptions:pickerFixedOptions,
    chargeTimePickerOptions: chargeTimePickerOptions
}
