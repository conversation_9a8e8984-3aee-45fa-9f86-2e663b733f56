<template>
  <div :id="blMapId" :style="'height:' + height + 'px'"></div>
</template>

<script>
/**
 * 该组件基于原生高德地图封装
 * 优点：灵活支持原生Api
 * 缺点：免费次数有限
*/
import AMapLoader from "@amap/amap-jsapi-loader";

export default {
  name: "bl-map-view",
  props: {
    zoom: {
      type: Number,
      default: 10,
    },
    viewMode: {
      type: String,
      default: "2D",
    },
    center: {
      type: Array,
      default: () => {
        return [116.397428, 39.90923];
      },
    },
    height: {
      type: Number,
      default: 300, //地图容器高度
    },
    blMapId: {
      type: String,
      default: "blMapContainer",
    },
  },
  data() {
    return {
      map: null,
      placeSearch: null,
      marker: null,
    };
  },
  mounted() {
    this.initAMap();
  },
  unmounted() {
    this.map && this.map.destroy();
  },
  methods: {
    initAMap() {
      console.log(432423, this.center);

      window._AMapSecurityConfig = {
        securityJsCode: "dd424a4d1bf0d2a11ac32f09817b1d87",
      };
      AMapLoader.load({
        key: "f0b6ad80e7567f69684813b2566d5a51", // 申请好的Web端开发者Key，首次调用 load 时必填
        version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: [
          "AMap.AutoComplete",
          "AMap.PlaceSearch",
          "AMap.ToolBar",
          "AMap.MapType",
          "AMap.Geolocation",
          "AMap.Marker",
        ], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
      })
        .then((AMap) => {
          console.log("初始化地图成功!");
          this.map = new AMap.Map(this.blMapId, {
            // 设置地图容器id
            viewMode: this.viewMode, // 是否为3D地图模式
            zoom: this.zoom, // 初始化地图级别
            center: this.center, // 初始化地图中心点位置
          });
          this.$emit("init", this.map);
        })
        .catch((e) => {
          console.log("初始化地图失败!", e);
        });
    },

    setMarkerPoint(lng, lat) {
      if (this.map) {
        this.removeMarker();
        const markerContent =
          "<i class='el-icon-location' style='color:red;font-size:30px'></i>";
        const position = new AMap.LngLat(lng, lat); //Marker 经纬度
        const marker = new AMap.Marker({
          position: position,
          content: markerContent, //将 html 传给 content
        });
        this.marker = marker;
        this.map.add(marker);
        this.map.setFitView([marker]);
      }
    },

    removeMarker() {
      if (this.map && this.marker) {
        this.map.remove(this.marker);
      }
    },
  },
};
</script>

<style scoped>
#blMapContainer {
  padding: 0px;
  margin: 0px;
  width: 100%;
}
.custom-marker {
  color: red;
  font-size: 30px;
}
</style>