<template>
  <el-dialog :visible.sync="visible" class="version-update-dialog__wrapper"
             custom-class="version-update-dialog"
             :close-on-click-modal="false"
             :close-on-press-escape="false"
             :modal-append-to-body='false'
             :show-close="false"
             :destroy-on-close="true" width="364px">
    <div class="version-update-dialog--body">
      <div class="banner">
        <img src="./version-update-banner.png" class="banner-img" alt="">
        <span class="banner-close" @click="handleClick">
          <i class="el-icon-close"></i>
        </span>
      </div>
      <div class="content">
        <el-scrollbar style="height: 100%">
          <ul>
            <li v-for="(item,index) in list" :key="index">{{item}}</li>
          </ul>
        </el-scrollbar>
      </div>
      <div class="footer">
        <div class="footer-btn">
          <el-button @click="handleClick('cancel')">取消</el-button>
          <el-button type="primary" :loading="loading" @click="handleClick('confirm')">马上开启</el-button>
        </div>
        <div class="footer-btn">
          <el-checkbox v-model="checked">不再提示</el-checkbox>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
name: "versionUpdatePage",
  data() {
  return {
    visible: false,
    checked: false,
    loading: false,
    list: [],
  }
  },
  methods: {
    async getNotifyList() {
      const api = this.common.getItemDomain() + `/cominfo4g/getParkLoginMessage?comid=${sessionStorage.getItem('comid')}&userId=${sessionStorage.getItem('loginuin')}`   //数据请求路径
      await this.$axios.get(api)
        .then(res => {
          let { code, data } = res.data;
          if (code === 200 && data.type === 1) {
            this.list = data.messageList || [];
          }
          else {
            this.list = [];
          }

        }).catch(err => {
          this.list = [];
        })

    },
    async openDialog() {
      await this.getNotifyList();
      if (this.list.length > 0) {
        this.visible = false;  //【*********】纯云车场可开/关相机断网模  需求去掉弹框公告  gh 2022/10/10 10:00
      }
    },
    handleClick(action) {
      this.loading = true;
      const api = this.common.getItemDomain() +'/cominfo4g/commitOpenParkSet'    //数据请求路径
      this.$axios.get(api,{
        params: {
          comid: sessionStorage.getItem('comid'),
          userId: sessionStorage.getItem('loginuin'),
          state: action === 'confirm'?1:0,
          isLoginShow: this.checked?1:0
        }
      })
      .then(res => {
        let { code, message } = res.data;
        if (code === 200) {
          this.visible = false;
          this.$message.success(message|| '操作成功');
        }
        else {
          this.$message.error(message || '操作失败');
        }
        this.loading = false;
      }).catch(err => {
        this.loading = false;
        this.$message.error('网络错误，请重试')
      })
    }
  }
}
</script>

<style lang="scss">
  .version-update-dialog__wrapper {
    .version-update-dialog {
      border-radius: 12px !important;
      .el-dialog__header {
        display: none;
      }
      .el-dialog__body {
        padding: 0;
      }
      &--body {
        .banner {
          position: relative;
          height: 167px;
          &-img {
            width: 100%;
            height: 100%;
          }
          &-close {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 2;
            font-size: 24px;
            color: #fff;
            cursor: pointer;
          }
        }
        .content {
          padding: 20px 20px 0 20px;
          max-height: 350px;
          ul {
            li {
              list-style: inside decimal;
              margin-bottom: 16px;
            }

          }
        }
        .footer {
          padding: 20px 0;
          text-align: center;
          .footer-btn {
            margin-bottom: 16px;
          }
        }
      }
    }
  }

</style>
