<template>
  <div class="bl-area-box">
    <el-select
      v-model="provinceId"
      placeholder="请选择省"
      clearable
      filterable
      @change="changeHandler(3, provinceId)"
      @clear="clearHandler(3)"
      style="width: 33%"
    >
      <el-option
        v-for="item in provinceOptions"
        :key="item.areaCode"
        :label="item.areaName"
        :value="item.areaCode"
      >
      </el-option>
    </el-select>
    <el-select
      v-model="cityId"
      placeholder="请选择市"
      clearable
      filterable
      :disabled="!provinceId"
      @change="changeHandler(4, cityId)"
      @clear="clearHandler(4)"
      style="width: 33%"
    >
      <el-option
        v-for="item in cityOptions"
        :key="item.areaCode"
        :label="item.areaName"
        :value="item.areaCode"
      >
      </el-option>
    </el-select>
    <el-select
      v-model="countyId"
      placeholder="请选择区"
      clearable
      filterable
      :disabled="!cityId"
      @change="changeHandler"
      @clear="clearHandler"
      style="width: 34%"
    >
      <el-option
        v-for="item in countyOptions"
        :key="item.areaCode"
        :label="item.areaName"
        :value="item.areaCode"
      >
      </el-option>
    </el-select>
    <!-- <span class="clearable-regiions" @click="cleanHanlder">清空</span> -->
  </div>
</template>

<script>
export default {
  name: "BlAreaCascader",
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    value: {
      type: [Array, String], // 适配单选或多选的值类型
      default: () => [],
    },
  },
  data() {
    return {
      provinceId: "",
      cityId: "",
      countyId: "",
      provinceOptions: [],
      cityOptions: [],
      countyOptions: [],
    };
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal && Array.isArray(newVal) && newVal.length === 3) {
          this.provinceId = newVal[0] || '';
          this.cityId = newVal[1] || '';
          this.countyId = newVal[2] || '';
          if (newVal[0]) {
            this.getAreaList(3, newVal[0]);
          }
          if (newVal[1]) {
            this.getAreaList(4, newVal[1]);
          }
        }
      },
      deep: true,
    },
  },
  mounted() {
    // 获取省份
    this.getAreaList(2, "");
  },
  methods: {
    changeHandler(areaType, areaCode) {
      if (areaType === 3) {
        this.cityId = "";
        this.countyId = "";
      }
      if (areaType === 4) {
        this.countyId = "";
      }

      const { provinceId, cityId, countyId } = this;
      const regions = [provinceId, cityId, countyId];
      this.$emit("change", regions);

      if (areaType && areaCode) {
        this.getAreaList(areaType, areaCode);
      }
    },

    clearHandler(areaType) {
      if (areaType === 3) {
        this.cityId = "";
        this.countyId = "";
      }
      if (areaType === 4) {
        this.countyId = "";
      }

      const { provinceId, cityId, countyId } = this;
      const regions = [provinceId, cityId, countyId];
      this.$emit("change", regions);
    },

    cleanHanlder() {
      this.provinceId = "";
      this.cityId = "";
      this.countyId = "";
      this.$emit("change", []);
    },

    async getAreaList(areaType, areaCode) {
      const params = {
        areaType,
        areaCode,
      };
      const res = await this.$axios.post("/facearea/queryNext", params);
      const resData = res.data;
      const { code, data } = resData;
      if (code === 200) {
        if (areaType === 2) {
          this.provinceOptions = data;
        } else if (areaType === 3) {
          this.cityOptions = data;
        } else {
          this.countyOptions = data;
        }
      }
    },
  },
};
</script>
<style lang="scss"scoped>
.bl-area-box {
  display: flex;
}
.clearable-regiions {
  font-size: 12px;
  color: #909399;
  cursor: pointer;
}
</style>