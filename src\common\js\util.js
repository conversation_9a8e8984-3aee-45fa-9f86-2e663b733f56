//导入路由列表
import { constantRouters_404 } from '@/router/routes'
import adminRouter from  '@/router/modules/admin';
import shopRouter  from  '@/router/modules/shop';
import parkRouter  from  '@/router/modules/park';
import cityRouter  from  '@/router/modules/city';
import serverRouter  from  '@/router/modules/server';
import unionRouter  from  '@/router/modules/union';
import eomsRouter  from  '@/router/modules/eoms';
import { Message } from 'element-ui';
let util = {};
util.title = function (title) {
    title = title || '智慧通行云';
    window.document.title = title;
};
util.matchedLogin = function (){
    let path = '';
    let domian = document.domain;
  if(domian === "www.yitingyun.cn"){
      util.title('易停云停车管理平台');
      path = '/yt-login';
  }else{
      path = '/loginCloud'
  }
  return path;
};
//获取登录的角色
util.judgeLoginRoleAuth = function(vm,user){
    let currentAuthObj = {};
    let currentAuth = vm.$Authority;

    let roleIdObj = currentAuth.ROLE_ID;
    let roleIdItem = user.oid;
    let currentLoginRole = undefined;
    for(let item in roleIdObj){
        if(roleIdObj[item] === roleIdItem ){
            currentLoginRole = item;
        }
    }
    if(currentLoginRole === 'PARK'){
        currentAuthObj = {
            'currentLoginRole':currentLoginRole,
            'authIdObj':currentAuth.AUTH_ID,
            'showItemConst':currentAuth.showParkItem_const,
            'currentRouter':parkRouter,
        }
    }
    else if(currentLoginRole === 'UNION'){
        currentAuthObj = {
            'currentLoginRole':currentLoginRole,
            'authIdObj':currentAuth.AUTH_ID_UNION,
            'showItemConst':currentAuth.showUnionItem_const,
            'currentRouter':unionRouter,
        }
    }
    else if(currentLoginRole === 'CITY'){
        currentAuthObj = {
            'currentLoginRole':currentLoginRole,
            'authIdObj':currentAuth.AUTH_ID_CITY,
            'showItemConst':currentAuth.showCityItems_const,
            'currentRouter':cityRouter,
        }
    }
    else if(currentLoginRole === 'SHOP'){
        currentAuthObj = {
            'currentLoginRole':currentLoginRole,
            'authIdObj':currentAuth.AUTH_ID_SHOP,
            'showItemConst':currentAuth.showShopItem_const,
            'currentRouter':shopRouter,
        }
    }
    else if(currentLoginRole === 'SERVER'){
        currentAuthObj = {
            'currentLoginRole':currentLoginRole,
            'authIdObj':currentAuth.AUTH_ID_SERVER,
            'showItemConst':currentAuth.showServerItems_const,
            'currentRouter':serverRouter,
        }
    }
    else if(currentLoginRole === 'BOSS'){
        currentAuthObj = {
          'currentLoginRole':currentLoginRole,
          'authIdObj':null,
          'showItemConst':null,
          'currentRouter':JSON.parse(JSON.stringify(adminRouter)),
        }
    }
    else if(currentLoginRole === 'SUBADMIN'){
      currentAuthObj = {
        'currentLoginRole':currentLoginRole,
        'authIdObj':currentAuth.AUTH_ID_ADMIN,
        'showItemConst':currentAuth.showADMINItems_const,
        'currentRouter':JSON.parse(JSON.stringify(adminRouter)),
      }
    }
    else if(currentLoginRole === 'EOMS'){
        currentAuthObj = {
            'currentLoginRole':currentLoginRole,
            'authIdObj':currentAuth.AUTH_ID_EOMS,
            'showItemConst':currentAuth.showEomsItems_const,
            'currentRouter':eomsRouter,
        }
    }
    else{
        return false;
    }
    return currentAuthObj
};

// 页面初始化
util.initData=function(){
    try {
        let params = {},
        type='',
        oid=sessionStorage.getItem("oid"),
        loginuin = sessionStorage.getItem("loginuin"),
        cityid = sessionStorage.getItem("cityid"),
        serverId = sessionStorage.getItem("serverid"),
        comid = sessionStorage.getItem("comid"),
        userid=sessionStorage.getItem("userid"),
        name=sessionStorage.getItem("name"),
        atuhGroup = ""

          if (oid==8) {
              atuhGroup = "AUTH_ID"
              params.comid = comid
              type=3

          } else if (oid==11) {
              params.serverId = serverId
              atuhGroup = "AUTH_ID_SERVER"
              type=2
          } else if (oid==7) {
              atuhGroup = ""
              params.cityid = cityid
              type=1
          } else if(oid==15){
              params.backstageId = loginuin
              type=4
          }
          let id=Object.values(params)
          let data={
            params,
            atuhGroup,
            type,
            mainId:id[0]-0,
            userid,
            name
          }
        sessionStorage.setItem('initDataParams',JSON.stringify(data))
    } catch (err){
        console.log('util,initData',err)

    }

};
util.pageShow = function(authlist,pageNo){
    for(let i of authlist){
        if(pageNo === i.auth_id){
            return true;
        }
    }
    return false;
};
//获取当前的权限列表
util.getCurrentAuth = function(vm){
    let user = sessionStorage.getItem('user');
    if(user){
        user = JSON.parse(user);
    }else{
        alert('请先登录');
        return false;
    }
    let authList = user['authlist'];

    let currentAuthObj = util.judgeLoginRoleAuth(vm,user);
    let currentAuthIdObj = undefined;
    let currentShowItem = undefined;
    if(currentAuthObj){
        currentAuthIdObj = currentAuthObj.authIdObj;
        currentShowItem = currentAuthObj.showItemConst;
    }
    // console.log('--------->',currentAuthObj)
//  便历重置变量状态
    for(let item in currentShowItem){
        for(let authid in currentAuthIdObj){
            if(authid === item){
                currentShowItem[item] =  util.pageShow(authList,currentAuthIdObj[authid]);
            }
        }
    }

    return  currentAuthObj;
};
//更新路由json数据
util.assembleObj = function (currentShowItem,routerItem){
    let is4g = sessionStorage.getItem('is4GPark')
    //先判断单项有没有[mate]
    if(routerItem.meta){
        //便历权限列表
        for(let item in currentShowItem.showItemConst){
            //路由权限和权限列表进行对比
            if(routerItem.meta.authority && routerItem.meta.authority === item ){
                if(currentShowItem.showItemConst[item]){
                    if(routerItem.meta.is4g == 1){
                        if(is4g){
                            routerItem.meta.hidden = false;
                        }else{
                            routerItem.meta.hidden = true;
                        }
                    }
                    else if(routerItem.meta.is4g == 2){
                        if(is4g){
                            routerItem.meta.hidden = true;
                        }else{
                            routerItem.meta.hidden = false;
                        }
                    }
                    else{
                        routerItem.meta.hidden = false;
                    }
                    if(routerItem.children && routerItem.children.length>0){
                        for(let childrenItem of routerItem.children){
                            util.assembleObj(currentShowItem,childrenItem)
                        }
                    }
                }else{
                    routerItem.meta.hidden = true;
                }
            }
        }
    }
    return routerItem;
};
/**
 * 过滤要隐藏的菜单
 * @param _router
 * @param user
 * @return {*}
 */
util.routerFilterChildren = function (_router, user) {
  let n_router = []
  if (_router && _router.length > 0) {
    n_router = _router.filter(_item => {
      if (_item.children && _item.children.length > 0) {
        _item.children = util.routerFilterChildren(_item.children, user)
      }
      if (_item.meta.admin) {
        return user.is_adminUser === 1
      } else {
        return !_item.meta.hidden
      }
    })
  }
  return n_router
};
util.routerFilter = function(currentRouter){
    let currentArr = JSON.parse(JSON.stringify(currentRouter));
    let user = JSON.parse(sessionStorage.getItem('user'));
    // 过滤第一层
    let itemFilter = currentArr.filter(item=>{
        if(item.meta && item.meta.requestAuthority){
            return user.docking_type === 2 && item.meta.hidden === false;
        }
        else if(item.meta && item.meta.admin){//admin账户登录的
          return user.is_adminUser === 1;
        }
        else if (item.meta && item.meta.videoAuthority) { // 是否显示视频接口
          return user.show_unionurl === 1 && !item.meta.hidden;
        }
        else {
          return item.meta && item.meta.hidden === false;
        }
    })
    if (itemFilter && itemFilter.length > 0) {
      for(let item of itemFilter){
        item.children = util.routerFilterChildren(item.children, user)
      }
    }
    return itemFilter
    // if(itemFilter && itemFilter.length>0){
    //     for(let item of itemFilter){
    //         if(item.children && item.children.length > 0){
    //             let children = item.children.filter(_item=> {
    //                 if(_item.children && _item.children.length > 0){
    //                     let _children = _item.children.filter(__item=> {
    //                         if(__item.meta.admin){
    //                           if(user.is_adminUser === 1){
    //                             return true;
    //                           }else{
    //                             return false;
    //                           }
    //                         }
    //                         else if(__item.meta.hidden == false){
    //                             return true
    //                         }else{
    //                             return false
    //                         }
    //                     })
    //                     _item.children = _children;
    //                 }
    //
    //                 if(_item.meta.admin){
    //                   if(user.is_adminUser === 1){
    //                     return true;
    //                   }else{
    //                     return false;
    //                   }
    //                 }
    //                 else if(_item.meta.hidden == false){
    //                     return true
    //                 }else{
    //                     return false
    //                 }
    //             })
    //             item.children = children;
    //         }
    //     }
    // }else{
    //     return [];
    // }
    // return itemFilter;
};
// 初始化集团路由
util.getDutyRouter = function (vm) {
  const router = eomsRouter
  sessionStorage.setItem('menuList', JSON.stringify(router));
  vm.$store.commit('updateMenuList', router);
  return router
}
//初始化页面路由
util.initRouter = function (vm) {
    //获取当前的路由权限
    let currentShowItem = util.getCurrentAuth(vm);
    for(let routerItem of currentShowItem.currentRouter){
        util.assembleObj(currentShowItem,routerItem)
    }

    sessionStorage.setItem('showParkItem',JSON.stringify(currentShowItem.showItemConst));
    let itemFilter = util.routerFilter(currentShowItem.currentRouter);
    if(itemFilter&&itemFilter.length>0){
      console.log('itemFilter: ', itemFilter)
        sessionStorage.setItem('menuList',JSON.stringify(itemFilter));
        vm.$store.commit('updateMenuList',itemFilter);
    }else{
        // alert('无此权限');
        // return false;
      console.log('itemFilter: ', constantRouters_404)
      return constantRouters_404
    }
    return itemFilter;
};
let envN = 0;
util.addFourRouter = function (val) {
    envN++;
    if(process.env.NODE_ENV == 'other' || process.env.NODE_ENV == 'produce'){
        return val
    }else{
        return {
            path:'/path'+envN,
            name:'name'+envN,
            meta:{
                skip:true,
                title:'该隐藏的',
                authority:'abc',
                hidden:true,
            }
        }
    }
};
util.choicePageRouter = function (val,SDK,FourG) {
    let is4g = sessionStorage.getItem('is4GPark')
    if(is4g){
        val.component = FourG
    }else{
        val.component = SDK
    }
    return val;
};

/**
 * 判断当前页面是http还是https
 *
 */
util.isHttps = function () {
  return  "https:" === document.location.protocol;
}
/**
 * 判断当前时间选择期是否符合搜索要求
 * @param endTime
 * @param startTime
 * @param limit
 * @param message
 * @return {boolean}
 */
util.isAllowSearch = function (endTime, startTime,limit=32, message="时间区间不能大于31天，请重新选择") {
  let limitTime = limit * 3600 * 1000 * 24;
  let status = true;
  if (endTime === null || endTime === undefined || endTime === "") {
    return status;
  }
  if (endTime - startTime > limitTime) {
    status = false;
    Message({
      type: 'info',
      message: message,
      duration: 5000
    })
  }
  return status
}
/**
 * @description 获取当前月的第一天和当前的秒数
 * @return []
 */
util.getCurrentMonthFirstDateAndLastDate = function () {
  try {
    const _date = new Date();
    const year = _date.getFullYear()
    const month = _date.getMonth()
    let start =  new Date(year, month, 1).getTime();
    let end =   new Date().getTime();
    return  [start,end]
  } catch (e) {
    return  [null,null]
  }
}
/**
 * @desc 登录后，匹配默认页面
 * @param menu
 * @return {string}
 */
util.matchHomepage = function (menu) {
  let current_path = ''
  if (Object.prototype.toString.call(menu).slice(8,-1) === 'Array' && menu.length > 0) {
    const [currentItem] = menu
    if (Object.prototype.toString.call(currentItem.children).slice(8,-1) === 'Array' && currentItem.children.length > 0) {
      return util.matchHomepage(currentItem.children)
    } else {
      current_path = currentItem.path
    }
  } else {
    current_path = menu.path
  }
  return current_path
}
/** 存储登录的信息 */
util.storeSetItemFormat = function (u) {
  sessionStorage.setItem('user', JSON.stringify(u));
  sessionStorage.setItem('token', u.token);
  sessionStorage.setItem('comid', u.comid);
  localStorage.setItem('comid', u.comid)
  sessionStorage.setItem('groupid', u.groupid);
  localStorage.setItem('groupid', u.groupid)
  sessionStorage.setItem('channelid', u.channelid);
  sessionStorage.setItem('unionid', u.union_id);
  sessionStorage.setItem('cityid', u.cityid);
  sessionStorage.setItem('eomsid', u.eoms_id);
  sessionStorage.setItem('loginuin', u.loginuin);
  sessionStorage.setItem('oid', u.oid);
  sessionStorage.setItem('nickname', u.nickname);
  sessionStorage.setItem('ishdorder', u.ishdorder);
  sessionStorage.setItem('loginroleid', u.loginroleid);
  sessionStorage.setItem('supperadmin', u.supperadmin);
  sessionStorage.setItem('shopid', u.shopid);
  sessionStorage.setItem('nickname1', u.nickname);
  sessionStorage.setItem('serverid',u.serverid);
  sessionStorage.setItem('bolink_serverid',u.bolink_serverid);
  sessionStorage.setItem('docking_type',u.docking_type);
  sessionStorage.setItem('cloud_token',u.cloud_token);
  sessionStorage.setItem('is_new_type',u.is_new_type);
  sessionStorage.setItem('is_show_renewal',u.is_show_renewal);
  sessionStorage.setItem('buildingAdmin', u.buildingAdmin);
  u.token_params && sessionStorage.setItem('parkid', u.token_params.park_id);
  if(u.is4GPark){
    sessionStorage.setItem('is4GPark',u.is4GPark);
  }
  //切换logo
  if(u.logo1 !== undefined && u.logo1 !== null && u.logo1 !== ''){
    sessionStorage.setItem('logo1', u.logo1);
    sessionStorage.setItem('logo2', u.logo2);
  }else{
    sessionStorage.removeItem('logo1');
    sessionStorage.removeItem('logo2');
  }
}
/**
 * 驼峰转下划线
 * @param data
 * @return {[]}
 */
util.toLine = function (data) {
  if (typeof data != 'object' || !data) return data
  if (Array.isArray(data)) {
    return data.map(item => util.toLine(item))
  }
  const newData = {}
  Object.keys(data).forEach(key => {
    const nK = key.replace(/([A-Z])/g,"_$1").toLowerCase();
    newData[nK] =  util.toLine(data[key])
  })
  return newData
}
export default util;






















// var SIGN_REGEXP = /([yMdhsm])(\1*)/g;
// var DEFAULT_PATTERN = 'yyyy-MM-dd';
// function padding(s, len) {
//     var len = len - (s + '').length;
//     for (var i = 0; i < len; i++) { s = '0' + s; }
//     return s;
// };
//
// export default {
//     getQueryStringByName: function (name) {
//         var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
//         var r = window.location.search.substr(1).match(reg);
//         var context = "";
//         if (r != null)
//             context = r[2];
//         reg = null;
//         r = null;
//         return context == null || context == "" || context == "undefined" ? "" : context;
//     },
//     formatDate: {
//
//
//         format: function (date, pattern) {
//             pattern = pattern || DEFAULT_PATTERN;
//             return pattern.replace(SIGN_REGEXP, function ($0) {
//                 switch ($0.charAt(0)) {
//                     case 'y': return padding(date.getFullYear(), $0.length);
//                     case 'M': return padding(date.getMonth() + 1, $0.length);
//                     case 'd': return padding(date.getDate(), $0.length);
//                     case 'w': return date.getDay() + 1;
//                     case 'h': return padding(date.getHours(), $0.length);
//                     case 'm': return padding(date.getMinutes(), $0.length);
//                     case 's': return padding(date.getSeconds(), $0.length);
//                 }
//             });
//         },
//         parse: function (dateString, pattern) {
//             var matchs1 = pattern.match(SIGN_REGEXP);
//             var matchs2 = dateString.match(/(\d)+/g);
//             if (matchs1.length == matchs2.length) {
//                 var _date = new Date(1970, 0, 1);
//                 for (var i = 0; i < matchs1.length; i++) {
//                     var _int = parseInt(matchs2[i]);
//                     var sign = matchs1[i];
//                     switch (sign.charAt(0)) {
//                         case 'y': _date.setFullYear(_int); break;
//                         case 'M': _date.setMonth(_int - 1); break;
//                         case 'd': _date.setDate(_int); break;
//                         case 'h': _date.setHours(_int); break;
//                         case 'm': _date.setMinutes(_int); break;
//                         case 's': _date.setSeconds(_int); break;
//                     }
//                 }
//                 return _date;
//             }
//             return null;
//         }
//
//     }
//
// };
