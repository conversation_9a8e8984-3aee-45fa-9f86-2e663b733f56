<template>
  <section class="right-wrapper-size shop-table-wrapper" id="scrollBarDom">
    <div class="shop-custom-operation">
      <div class="shop-custom-operation">
        <header class="shop-custom-header">
          <p style="float: left">
            机场停车<span style="margin: 2px">-</span>机场管理
          </p>
          <div class="float-right">
          <el-button type="text" size="mini" @click="resetForm" icon="el-icon-refresh" style="font-size: 14px;color: #1E1E1E;">刷新</el-button>
        </div>
        </header>
      </div>

      <super-form
        :form-config="formConfig"
        :value="searchForm"
        @input="handleSearch"
      />
    </div>

    <div class="count-table-wrapper-style">
      <bl-table
        ref="tableRef"
        border
        :api="fetchTableData"
        :request-params="searchForm"
        :showIndex="true"
        :showToolbar="true"
        :toolbarButtons="toolbarButtons"
        :showPagination="true"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handlePageChange"
      >
        <!-- 表格列 -->
        <el-table-column
          prop="type"
          label="类型"
          align="center"
        >
          <template slot-scope="scope">
            {{scope.row.type == 0 ? '机场' : ''}}
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="名称"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="address"
          label="地址"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="longitude"
          label="经度"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="latitude"
          label="纬度"
          align="center"
        ></el-table-column>
        <el-table-column label="图片" align="center">
          <template slot-scope="scope">
            <el-image
              :src="scope.row.imageUrl"
              :preview-src-list="[scope.row.imageUrl]"
              fit="cover"
              style="width: 100px; height: 100px">
            </el-image>
          </template>
        </el-table-column>
        <el-table-column
          prop="parkingLotCount"
          label="关联车场数量"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="onlineParkingLotCount"
          label="上架车场数量"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="parkingFee"
          label="官方站内停车费"
          align="center"
          width="130"
        ></el-table-column>

        <el-table-column
          prop="operate"
          label="操作"
          align="center"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <!-- <el-button type="text" @click="handleDelete(scope.row)"
              >删除</el-button
            > -->
            <el-button
              v-if="scope.row.status === 0"
              type="text"
              @click="handleStatusChange(scope.row, 1)"
            >
              上架
            </el-button>
            <el-button
              v-else
              type="text"
              @click="handleStatusChange(scope.row, 0)"
            >
              下架
            </el-button>
          </template>
        </el-table-column>
      </bl-table>
    </div>

    <!-- 对话框组件 -->
    <airport-dialog
      :visible.sync="dialogVisible"
      :form-data="currentData"
      @submit="handleDialogSubmit"
    />
  </section>
</template>

<script lang="ts">
import {
  fetchAirportList,
  deleteAirport,
  updateAirport,
  addAirport,
} from "@/api/airParking";
import SuperForm from "@/components/super-form/inline-form";
import BlTable from "@/components/BlTable/index.vue";
import AirportDialog from "./components/AirportDialog.vue";

export default {
  name: "AirportManage",
  components: { SuperForm, BlTable, AirportDialog },
  data() {
    return {
      searchForm: {
        name: ""
      },
      current: 1, // 当前页码
      size: 10, // 每页显示多少条数据
      requestUrl: this.$PUBLIC_URL.AIRPORT_API, // 请求接口
      formConfig: {   // 表单配置
        first: [
          {
            label: "机场名称",
            type: "input",
            prop: "name",
          }
        ],
      },
      dialogVisible: false,
      toolbarButtons: [   
        {
          label: "新增",
          handler: (e) => {
            this.handleAdd();
          },
        },
      ],
      currentData: {},
    };
  },
  methods: {
    async fetchTableData() {
      const params = { ...this.searchForm, size: this.size, current: this.current };
      // 生产环境建议关闭或替换为日志系统
      if (process.env.NODE_ENV !== 'production') {
        console.log("请求参数:", params, this.requestUrl, 'this.$PUBLIC_URL.AIRPORT_API');
      }
    
      try {
        const response = await fetchAirportList(this.requestUrl, params);
        const dataList = response? response.data.data : [];
        console.log("请求响应:", response);
        return {
          list: dataList.records || [],
          total: dataList.total || 0,
        };
      } catch (error) {
        console.error("fetchTableData 请求失败:", error); // 记录错误日志
        this.$message.error("加载数据失败，请稍后重试");
        return {
          list: [],
          total: 0,
        };
      }
    },
    resetForm(){
      this.searchForm = {
        name: ""
      };
      this.current = 1;
      this.$refs.tableRef.loadData();
    },

    handleSearch(form) {
      this.searchForm = form;
      this.$refs.tableRef.loadData();
    },

    handleAdd() {
      this.currentData = {
        type:'0',
        imageUrl: "https://image.bolink.club/yima/empty1.jpg"
      };
      this.dialogVisible = true;
    },

    showErrorMessage(message) {
      this.$message.error(message);
    },


    async handleDialogSubmit(formData) {
      try {
        let response;
        if (formData.id) {
          // 编辑机场
          response = await updateAirport(this.requestUrl, formData.id, formData);
        } else {
          // 新增机场
          response = await addAirport(this.requestUrl, formData);
        }
    
        // 校验响应结构
        if (!response || !response.data) {
          this.showErrorMessage(formData.id ? '编辑失败' : '新增失败');
          return;
        }
    
        const { code } = response.data;
    
        if (code === 200 || code === '200') {
          this.$message.success(formData.id ? '编辑成功' : '新增成功');
        } else {
          this.showErrorMessage(formData.id ? '编辑失败' : '新增失败');
          return;
        }
    
        this.dialogVisible = false;
        this.$refs.tableRef.loadData();
      } catch (error) {
        console.error(error);
        this.showErrorMessage('操作失败');
      }
    },
    handleEdit(row) {
      row.type = row.type.toString();
      this.currentData = { ...row };
      this.dialogVisible = true;
    },

    // async handleDelete(row) {
    //   try {
    //     await this.$confirm("确认删除该机位信息？", "提示", {
    //       type: "warning",
    //     });

    //     await deleteAirport(row.id);
    //     this.$message.success("删除成功");
    //     this.fetchTableData();
    //   } catch (error) {
    //     if (error !== "cancel") {
    //       this.$message.error("删除失败");
    //     }
    //   }
    // },

    handleSizeChange(size) {
      this.size = size;
      this.$refs.tableRef.loadData();
    },

    handlePageChange(page) {
      console.log("handlePageChange", page);
      this.current = page;
      this.$refs.tableRef.loadData();
    },

    async handleStatusChange(row, status) {
      const actionText = status === 1 ? "上架" : "下架";
    
      console.log(row, status);
    
      try {
        await this.$confirm(`确认${actionText}该机场？`, "提示", {
          type: "warning",
        });
    
        this.$axios
          .patch(
            `${this.requestUrl}/admin/airport/${row.id}/status`,
            { status }
          )
          .then((response) => {
            console.log(response);
    
            // 校验响应结构
            if (!response || !response.data) {
              this.$message.error(`${actionText}失败`);
              return;
            }
    
            const { code } = response.data;
    
            if (String(code) === '200') {
              this.$message.success("成功");
            } else {
              this.$message.error("失败");
              return;
            }
    
            this.$refs.tableRef.loadData();
          });
      } catch (error) {
        if (error !== "cancel") {
          this.$message.error("操作失败");
          console.error("操作失败详情：", error); // 增加日志输出
        }
      }
    }
  },
  mounted() {
    
  },
};
</script>
<style lang="scss" scoped>
.header-title {
  padding: 24px 28px 0 20px;
  font-size: 18px;
  font-weight: bold;
  color: #363636;
  background: #fff;
  display: flex;

  p {
    flex: 1;
  }
}
</style>