<template>
  <el-dialog :close-on-click-modal="false" custom-class="custom-dialog" :show-close="false"
    :visible.sync="dialogVisible" width="600px">
    <header class="fixed-code__title" slot="title" style="font-size: 18px; font-weight: bold;text-align:center">
      订单结算
      <i class="el-icon-close dialog-header-iconfont" @click="handleClose"></i>
    </header>
    
    <el-form ref="form" :model="form" :rules="rules" label-width="120px" size="small">
      <el-form-item label="预约停车时间:">
        <span>{{ orderData.expectStart || '' }}</span>
      </el-form-item>
      
      <el-form-item label="预约取车时间:">
        <span>{{ orderData.expectEnd || '' }}</span>
      </el-form-item>
      
      <el-form-item label="停车时间:">
        <span>{{ parkingDuration }}</span>
      </el-form-item>
      
      <el-form-item label="订单金额:">
        <span>{{ orderData.payAmount || '0' }}元</span>
      </el-form-item>
      
      <el-form-item label="入场时间:">
        <span>{{ orderData.inTime || '' }}</span>
      </el-form-item>
      
      <el-form-item label="离场时间:" prop="outTime">
        <el-date-picker
          v-model="form.outTime"
          type="datetime"
          placeholder="请选择时间"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm"
          style="width: 100%;"
        />
      </el-form-item>
      <!-- <el-form-item label="退款金额:">
        <span>{{ refundAmount }}元</span>
      </el-form-item> -->
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'OrderSettlementDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        outTime: ''
      },
      rules: {
        outTime: [
          { required: true, message: '请选择离场时间', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    parkingDuration() {
      // 计算停车时长
      const start = new Date(this.orderData.reserveStartTime || '2025-03-01 12:00')
      const end = new Date(this.orderData.reserveEndTime || '2025-03-08 12:00')
      const diffTime = Math.abs(end - start)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return `${diffDays}天`
    },
    refundAmount() {
      // 简单的退款计算逻辑，实际应根据业务规则计算
      if (!this.form.outTime) return '0'
      
      const entryTime = new Date(this.orderData.entryTime || '2025-03-01 13:00')
      const outTime = new Date(this.form.outTime)
      const actualHours = Math.ceil((outTime - entryTime) / (1000 * 60 * 60))
      const totalAmount = parseFloat(this.orderData.amount || '180')
      
      // 假设按小时计费，这里简化计算
      const hourlyRate = 5 // 每小时5元
      const actualCost = actualHours * hourlyRate
      const refund = Math.max(0, totalAmount - actualCost)
      
      return refund.toFixed(0)
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.resetForm()
      }
    }
  },
  methods: {
    resetForm() {
      this.form = {
        outTime: ''
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate()
      }
    },
    handleClose() {
      this.$emit('cancel')
      this.dialogVisible = false
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const settlementData = {
            orderId: this.orderData.id,
            outTime: this.form.outTime
          }
          this.$emit('confirm', settlementData)
          this.dialogVisible = false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-dialog {
  .dialog-header-iconfont {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    font-size: 16px;
    color: #909399;
    
    &:hover {
      color: #409EFF;
    }
  }
}

.dialog-footer {
  text-align: center;
  
  .el-button {
    min-width: 80px;
  }
}

.el-form-item {
  margin-bottom: 20px;
  
  .el-form-item__label {
    font-weight: 500;
  }
}
</style>