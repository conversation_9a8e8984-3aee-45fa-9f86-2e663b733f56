<template>
  <!-- 只有纯云车场并且有紧急通知内容时，弹框才展示 -->
  <div
    v-if="showNotice"
    :style="{ bottom: bottomDistance, right: rightDistance }"
    class="urgent-notice-wrapper"
  >
    <div class="banner">
      <img src="./urgent-notice-banner.png" class="banner-img" alt="" />
      <i class="el-icon-close banner-close" @click="handleClick" />
    </div>
    <div class="content">
      <p v-html="message"></p>
    </div>
    <div class="footer-btn">
      <el-checkbox v-model="checked" class="check-select">不再提示</el-checkbox>
      <el-button class="cancel-btn" @click="handleClick('cancel')"
        >取消</el-button
      >
      <el-button
        type="primary"
        :loading="loading"
        :disabled="!isRenew || is4GSentry"
        @click="handleClick('confirm')"
        >{{ btnDict[btnType] }}</el-button
      >
    </div>
  </div>
</template>
<script>
export default {
  name: 'UrgentNotice',
  props: {
    bottomDistance: {
      type: String,
      default: '10px'
    },
    rightDistance: {
      type: String,
      default: '10px'
    }
  },
  data() {
    return {
      btnDict: {
        '': '默认', // 兼容没值情况
        1: '立即续费'
      },
      is4GPark: sessionStorage.getItem('oid'),
      checked: false,
      loading: false,
      isRenew: false, //是否可以跳转到纯云服务
      message: '',
      path: '',
      btnType: '',
      is4GSentry: false // 是否是纯云岗亭页面
    }
  },
  mounted() {
    this.is4GSentry = this.$route.path === '/dataCenter_4GSentryBox'
    if (sessionStorage.getItem('showUrgentNotice') === 'true') {
      if (this.is4GPark === '8') {
        this.get4GMessage()
      }
      this.isJump4G()
    }
  },
  computed: {
    showNotice() {
      return this.message !== '' && this.is4GPark === '8'
    },
    showUrgentNotice() {
      return this.$store.state.app.isShowUrgentNotice
    }
  },
  methods: {
    // 请求纯云服务提醒
    get4GMessage() {
      let para = {
        comid: Number(sessionStorage.getItem('comid')),
        userId: Number(sessionStorage.getItem('userid')),
        type: 1
      }

      const api = this.common.getItemDomain() +'/comMessage/get4GMessage'    //数据请求路径
      this.$axios
        .post(api, para)
        .then(res => {
          let data = res.data
          if (data.code === 200) {
            if (data.data !== null) {
              this.message = '<p>' + data.data.content + '</p>'
              this.path = data.data.path
              this.btnType = data.data.type
            } else {
              sessionStorage.setItem('showUrgentNotice', false)
              this.message = ''
            }
          } else {
            this.$message.error(data.message)
          }
        })
        .catch(err => {})
    },
    handleClick(val) {
      // 选择了不再提示，点X和取消和立即续费再调接口
      if (val === 'confirm') {
        this.$router.push(this.path)
      }
      if (this.checked) {
        this.handleNotNotice()
      }
      this.message = ''
      // 手动关闭弹框的时候，要做持久化存储
      sessionStorage.setItem('showUrgentNotice', false)
    },
    // 判断权限，看是否跳转到纯云服务
    isJump4G() {
      this.isRenew = false
      let authList = JSON.parse(sessionStorage.getItem('user')).authlist
      for (let i = 0; i < authList.length; i++) {
        if (authList[i].nname === '纯云服务') {
          this.isRenew = true
          return
        }
      }
    },
    // 不再提示
    handleNotNotice() {
      let para = {
        comid: Number(sessionStorage.getItem('comid')),
        userId: Number(sessionStorage.getItem('userid')),
        type: 1
      }
      const api = this.common.getItemDomain() +'/comMessage/updateIsReceiver'    //数据请求路径
      this.$axios
        .post(api, para)
        .then(res => {
          let data = res.data
          if (data.code === 200) {
            this.$message.success(data.message)
            this.message = ''
          } else {
            this.$message.error(data.message)
          }
        })
        .catch(err => {})
    }
  }
}
</script>
<style lang="scss">
.urgent-notice-wrapper {
  .el-button {
    width: 120px;
    height: 36px;
    display: inline-block;
  }
  .cancel-btn {
    margin-right: 10px;
  }
}
</style>
<style lang="scss" scoped>
.urgent-notice-wrapper {
  position: fixed;
  width: 460px;
  height: 300px;
  background: #ffffff;
  box-shadow: 0px 0px 24px 0px rgba(0, 0, 0, 0.16);
  border-radius: 16px 16px 12px 12px;
  .banner {
    height: 102px;
    position: relative;
    .banner-img {
      height: 100%;
    }
    .banner-close {
      position: absolute;
      top: 8px;
      right: 8px;
      color: #fff;
      font-size: 18px;
      cursor: pointer;
    }
  }
  .content {
    width: 412px;
    height: 64px;
    padding: 26px 24px;
    font-size: 16px;
    line-height: 32px;
    overflow: auto;
  }
  .footer-btn {
    padding: 26px 24px;
    padding: 26px 24px 0 24px;
    .check-select {
      margin-right: 60px;
    }
  }
}
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: #f9f9f9;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #fff;
}
</style>