<template>
  <el-dialog :close-on-click-modal="false" custom-class="custom-dialog" :show-close="false"
    :visible.sync="dialogVisible" width="600px">
    <header class="fixed-code__title" slot="title" style="font-size: 18px; font-weight: bold;text-align:center">
      {{ formData.id ? '编辑机场' : '新增机场' }}
      <i class="el-icon-close dialog-header-iconfont" @click="handleClose"></i>
    </header>
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="small">
      <el-form-item label="类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择机场类型" style="width: 100%;">
          <el-option label="机场" value="0" />
        </el-select>
      </el-form-item>

      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入机场名称" />
      </el-form-item>

      <el-form-item label="地址" prop="address">
        <el-input v-model="form.address" placeholder="请输入机场地址" />
      </el-form-item>

      <el-form-item label="经度" prop="longitude">
        <el-input v-model.number="form.longitude" placeholder="请输入经度" type="number" />
      </el-form-item>

      <el-form-item label="纬度" prop="latitude">
        <el-input v-model.number="form.latitude" placeholder="请输入纬度" type="number" />
      </el-form-item>

      <el-form-item label="站内停车费" prop="parkingFee">
        <el-input v-model.number="form.parkingFee" placeholder="请输入站内停车费" type="number">
          <template #append>元/小时</template>
        </el-input>
      </el-form-item>
<!-- prop="imageUrl" -->
      <el-form-item label="机场图片" >
        <el-upload
          ref="upload"
          class="image-uploader"
          :action="action"
          :file-list="fileList"
          :multiple="true"
          :limit="5"
          :on-success="handleImageSuccess"
          :before-upload="beforeImageUpload"
          :on-remove="handleImageRemove"
          :on-exceed="handleExceed"
          list-type="picture-card">
          <i class="el-icon-plus"></i>
        </el-upload>
        <div class="upload-tip">
          <p>支持多选上传，最多5张图片，每张图片不超过1MB</p>
        </div>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'AirportDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        type: '',
        name: '',
        address: '',
        longitude: '',
        latitude: '',
        parkingFee: '',
        imageUrls: []
      },
      fileList: [],
      action: this.$PUBLIC_URL.AIRPORT_API + '/common/upload',
      rules: {
        type: [{ required: true, message: '请选择机场类型', trigger: 'change' }],
        name: [{ required: true, message: '请输入机场名称', trigger: 'blur' }],
        address: [{ required: true, message: '请输入机场地址', trigger: 'blur' }],
        longitude: [
          { required: true, message: '请输入经度', trigger: 'blur' },
          { type: 'number', message: '经度必须为数字', trigger: 'blur' }
        ],
        latitude: [
          { required: true, message: '请输入纬度', trigger: 'blur' },
          { type: 'number', message: '纬度必须为数字', trigger: 'blur' }
        ],
        parkingFee: [
          { required: true, message: '请输入站内停车费', trigger: 'blur' },
          { type: 'number', message: '站内停车费必须为数字', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.form = { ...this.formData }
        // 初始化文件列表
        this.initFileList()
      }
    }
  },
  methods: {
    handleClose() {
      this.$refs.form.resetFields()
      this.fileList = []
      this.form.imageUrls = []
      this.dialogVisible = false
    },

    async handleSubmit() {
      await this.$refs.form.validate()
      try {
        // 将文件列表中的URL提取到form中
        this.form.imageUrls = this.fileList.map(file => {
          return file.url || (file.response && file.response.data && file.response.data.url)
        }).filter(Boolean)
        this.$emit('submit', this.form)
      } catch (error) {
        console.error("createAirport 请求失败:", error)
      }
    },

    // 初始化文件列表
    initFileList() {
      if (this.form.imageUrls && this.form.imageUrls.length > 0) {
        this.fileList = this.form.imageUrls.map((url, index) => ({
          name: `image-${index + 1}`,
          url: url,
          uid: Date.now() + index
        }))
      } else {
        this.fileList = []
      }
    },

    // 上传前验证
    beforeImageUpload(file) {
      const isImage = /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(file.name)
      const isLt1M = file.size / 1024 / 1024 < 1

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt1M) {
        this.$message.error('图片大小不能超过 1MB!')
        return false
      }
      return true
    },

    // 上传成功回调
    handleImageSuccess(res, file, fileList) {
      console.log('上传成功:', res)
      if (res.code == 200) {
        // 更新文件列表中对应文件的URL
        const targetFile = fileList.find(f => f.uid === file.uid)
        if (targetFile) {
          targetFile.url = res.data.url
        }
        this.fileList = fileList
        this.$message.success('图片上传成功')
      } else {
        this.$message.error('上传失败: ' + (res.message || '未知错误'))
        // 从文件列表中移除失败的文件
        this.fileList = fileList.filter(f => f.uid !== file.uid)
      }
    },

    // 移除图片
    handleImageRemove(file, fileList) {
      this.fileList = fileList
      this.$message.success('图片已移除')
    },

    // 超出限制处理
    handleExceed(files, fileList) {
      this.$message.warning(`最多只能上传5张图片，当前已选择${fileList.length}张，本次选择了${files.length}张`)
    }
  }
}
</script>

<style scoped>
/* 多图片上传样式 */
.image-uploader {
  width: 100%;
}

.image-uploader ::v-deep .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.image-uploader ::v-deep .el-upload:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}

.image-uploader ::v-deep .el-upload i {
  font-size: 28px;
  color: #8c939d;
}

.image-uploader ::v-deep .el-upload-list__item {
  width: 120px;
  height: 120px;
  margin: 0 8px 8px 0;
  border-radius: 6px;
}

.image-uploader ::v-deep .el-upload-list--picture-card .el-upload-list__item-actions {
  font-size: 20px;
}

.upload-tip {
  margin-top: 8px;
}

.upload-tip p {
  margin: 0;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

/* 对话框样式优化 */
::v-deep .custom-dialog {
  border-radius: 8px;
}

::v-deep .custom-dialog .el-dialog__header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f2f5;
}

::v-deep .custom-dialog .el-dialog__body {
  padding: 24px;
}

::v-deep .custom-dialog .el-dialog__footer {
  padding: 16px 24px 20px;
  border-top: 1px solid #f0f2f5;
}

.dialog-header-iconfont {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  font-size: 16px;
  color: #909399;
  transition: color 0.3s ease;
}

.dialog-header-iconfont:hover {
  color: #409eff;
}
</style>