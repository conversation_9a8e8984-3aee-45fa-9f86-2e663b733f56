<template>
    <div class="parking-spaceNew">
      <el-drawer
            :visible="settParkShow"
            direction="rtl"
            @close="closeDialog"
            size="680px"
            custom-class="custom-drawer">
            <div slot="title" class="header-title">设置</div>
            <div class="custom-drawer-info">
              <el-form ref="refillAddForm" label-width="68px" >
                <div class="level-title">车位类型</div>
                  <el-form-item label="新增类型">
                      <el-tag
                        v-for="tagItem in parkTagsList"
                        :key="tagItem.id"
                        :closable="parkTagsList.length > 1 && tagItem.used == 0"
                        :disable-transitions="false"
                        @close="handleClose(tagItem)">
                        {{tagItem.value1}}
                      </el-tag>
                      <el-input
                        class="input-new-tag"
                        v-if="inputVisible"
                        v-model="TagsValue"
                        ref="saveTagInput"
                        size="small"
                        @keyup.enter.native="handleInputConfirm"
                        @blur="handleInputConfirm"
                        maxlength="15"
                      ></el-input>
                      <!-- <el-button v-else class="button-new-tag" size="small" @click="showInput">新增</el-button> -->
                      <div class="tips"><svg-tips-one theme="outline" size="16"/>选填，管理新增/编辑车位类型，最大15个字符（最多新增10个）</div>
                  </el-form-item>
                <div class="level-title">超时入场</div>
                <el-form-item label="留位时间">
                  <div class="houseInfo">
                    <el-input type="number" :min="0" :max="90" v-model.trim="retentionTime" placeholder=""></el-input>
                    <span>分钟</span>
                  </div>
                  <div class="tips"><svg-tips-one theme="outline" size="16"/>默认为空不保留，当用户超出已预约时间入场时，最大保留车位时间，仅限数字最大90（超出自动取消预约）</div>
                </el-form-item>
                <div class="level-title">预约须知</div>
                <el-form-item label="编辑">
                    <el-input type="textarea" :rows="7" v-model.trim="carefulRemark" placeholder="选填，最大300字符" maxlength="300"></el-input>
                    <div class="tips"><svg-tips-one theme="outline" size="16"/>必填，用于显示在小程序用户提交预约支付时的注意事项，默认显示系统默认规则，更改后显示最新，最大300字符</div>
                </el-form-item>
              </el-form>
              <footer slot="footer" class="dialog-footer">
                  <el-button type="primary" @click="saveSetInfo">保存</el-button>
              </footer>
            </div>
      </el-drawer>
    </div>
</template>
<script>

export default {
  props: {
    // value: {
    //   type: Number,
    // },
    settParkShow: {
      type: Boolean,
      default: true
    },
    getParkSetList: {
      type: Array,
      default: [] //车位类型列表
    }
  },
  data() {
    return {
      top:'0vh',
      parkTagsList:[],
      retentionTime: '' , //留位时间
      carefulRemark: '预约与取消：已预约车位将持续保留至预约时间（超时最大15分钟），超时仍未入场的，将自动取消预约且已支付的预约费不退款（已取消的预约车入场后，不再以预约身份记录）', //预约须知
      inputVisible: false,
      TagsValue:'',
      parkingFromRules: {
          total: [
              {required: true, message: '应收金额不能为空', trigger: 'blur'}
          ],
          add_money: [
              {required: true, message: '充值金额不能为空', trigger: 'blur'},

          ],
      },
    }
  },
  activated() {
    this.getSetSeach()
    // setTimeout(() => {
    //   this.parkTagsList = JSON.parse(JSON.stringify(this.getParkSetList))
    // }, 500); 
  },
  methods: {
    closeDialog(){
      this.$emit("closeDialog",false)
    },

    //获取设置数据查询
    getSetSeach(){
        let comid = sessionStorage.getItem("comid");
        comid = comid != 'undefined' ? Number(comid) : null
        this.$axios.post('/parkingLot/config/find', {
          comid//车场ID
          }).then(res => {
          if(res.data.code == 200){
            let dataInfo = res.data.data;
            this.parkTagsList = []
            dataInfo.forEach(item =>{
              if(item.dataType == 'reservedTime'){
                this.retentionTime = item.value1
              }

              if(item.dataType == 'appointNotice'){
                this.carefulRemark = item.value1
              }

              if(item.dataType == 'parkingLotType'){
                this.parkTagsList.push(item)
              }
              
            })
          }
        })
      },

    handleClose(tag) {
      this.parkTagsList.splice(this.parkTagsList.indexOf(tag), 1);
    },

    showInput() {
      this.inputVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    //保存并返回
    saveSetInfo(){
      var parklotSet = [];
      const parkTagsList = this.parkTagsList;
      const retentionTime = this.retentionTime;
      const carefulRemark = this.carefulRemark;
      const comid = sessionStorage.getItem("comid");

      if(carefulRemark == ''){
        this.$message({
          message: '请填写预约须知',
          type: 'error'
        });
        return;
      }

      
      parkTagsList.forEach(item=>{
        parklotSet.push({
          id: item.id || null, //ID
          comid: comid, //车场ID
          dataType: "parkingLotType", //车位类型
          value1: item.value1 //数据值
        })
      });

      if(retentionTime !== ''){
        parklotSet.push({
          comid: comid, //车场ID
          dataType: "reservedTime", //预留时间
          value1: retentionTime //数据值
        })
      }

      if(carefulRemark !== ''){
        parklotSet.push({
          comid: comid, //车场ID
          dataType: "appointNotice", //预约须知
          value1: this.carefulRemark //数据值
        })
      }
      this.$emit("lotSetSubmit",parklotSet)
    },

    handleInputConfirm() {
      if(this.parkTagsList.length < 10){
        let inputValue =  this.TagsValue;
        if (inputValue) {
          this.parkTagsList.push({
            used: 0,
            value1:inputValue
          });
        }
      }else{
        this.$message({
          message: '最多新增10个',
          type: 'error'
        });
      }
      
      this.inputVisible = false;
      this.TagsValue = '';
    }
  }
}
</script>
<style  lang="scss" scoped>
.header-title{
  color: #333;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
/deep/.el-drawer__header{
  margin-bottom: 10px;
  padding: 20px 30px 0 30px;
}
.custom-drawer-info{
  margin: 10px 30px;
}
.level-title{
  font-weight: bold;
  margin-bottom: 10px;
}
.tips{
  color: #999;
  font-size: 14px;
  background: #fff;
  line-height: 20px;
  margin: 6px 0;
  span{
    vertical-align: middle;
    margin-right: 6px;
  }
}

.el-button{
  width: 120px;
}

.header-tips{
  color: #B8741A;
  margin-left: 20px;
  margin: 0 10px;
}

/deep/.el-input{
  width: 200px;
}


.houseInfo{
  display: flex;
  span{
    display: inline-block;
    width: 30px;
    margin: 0 10px;
  }
  .el-input{
    width: 70px;
  }
}

/deep/.custom-dialog .el-dialog__body{
  padding: 30px 20px;
}

/deep/.custom-dialog .el-dialog__header .el-dialog__headerbtn{
  top: 20px;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}

</style>
