// 机场停车配置主题样式文件
// 统一的设计系统变量和混合器

// ==================== 颜色系统 ====================
$primary-color: #409EFF;
$primary-light: lighten($primary-color, 20%);
$primary-dark: darken($primary-color, 10%);

$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// 背景色
$bg-primary: #ffffff;
$bg-secondary: #f8f9fa;
$bg-tertiary: #f5f7fa;
$bg-gradient-primary: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
$bg-gradient-secondary: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);

// 文字颜色
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #c0c4cc;

// 边框颜色
$border-color-base: #e4e7ed;
$border-color-light: #f0f2f5;
$border-color-lighter: #f5f7fa;

// ==================== 尺寸系统 ====================
$border-radius-small: 4px;
$border-radius-base: 6px;
$border-radius-large: 8px;
$border-radius-round: 20px;

$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-xxl: 24px;
$spacing-xxxl: 32px;

// ==================== 阴影系统 ====================
$box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.04);
$box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.06);
$box-shadow-medium: 0 2px 12px rgba(0, 0, 0, 0.1);
$box-shadow-heavy: 0 4px 20px rgba(0, 0, 0, 0.15);
$box-shadow-primary: 0 2px 8px rgba(64, 158, 255, 0.3);
$box-shadow-primary-hover: 0 6px 20px rgba(64, 158, 255, 0.4);

// ==================== 动画系统 ====================
$transition-fast: all 0.2s ease;
$transition-base: all 0.3s ease;
$transition-slow: all 0.5s ease;

$animation-duration-fast: 0.2s;
$animation-duration-base: 0.3s;
$animation-duration-slow: 0.5s;

// ==================== 混合器 (Mixins) ====================

// 卡片样式混合器
@mixin card-style($shadow: $box-shadow-base, $radius: $border-radius-large) {
  background: $bg-primary;
  border-radius: $radius;
  box-shadow: $shadow;
  border: 1px solid $border-color-light;
  transition: $transition-base;
  
  &:hover {
    box-shadow: $box-shadow-medium;
    transform: translateY(-2px);
  }
}

// 按钮样式混合器
@mixin button-style($color: $primary-color, $hover-transform: true) {
  padding: $spacing-md $spacing-xxxl;
  border-radius: $border-radius-base;
  font-weight: 500;
  font-size: 14px;
  transition: $transition-base;
  
  @if $hover-transform {
    &:hover {
      transform: translateY(-2px);
    }
  }
  
  &.primary {
    background: linear-gradient(135deg, $color 0%, lighten($color, 10%) 100%);
    border: none;
    color: white;
    box-shadow: 0 2px 8px rgba($color, 0.3);
    
    &:hover {
      box-shadow: 0 6px 20px rgba($color, 0.4);
    }
  }
}

// 表单项样式混合器
@mixin form-item-style() {
  margin-bottom: $spacing-xxl;
  
  .el-form-item__label {
    font-weight: 500;
    color: $text-primary;
    line-height: 1.6;
    padding-bottom: $spacing-sm;
  }
  
  .el-input__inner,
  .el-textarea__inner {
    border-radius: $border-radius-base;
    border: 1px solid $border-color-base;
    transition: $transition-base;
    font-size: 14px;
    
    &:focus {
      border-color: $primary-color;
      box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
    }
    
    &:hover {
      border-color: $primary-light;
    }
  }
}

// 渐变背景混合器
@mixin gradient-bg($direction: 135deg, $color1: #f8f9fa, $color2: #ffffff) {
  background: linear-gradient($direction, $color1 0%, $color2 100%);
}

// 文字截断混合器
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

// 居中对齐混合器
@mixin flex-center($direction: row) {
  display: flex;
  flex-direction: $direction;
  justify-content: center;
  align-items: center;
}

// 响应式断点混合器
@mixin respond-to($breakpoint) {
  @if $breakpoint == mobile {
    @media (max-width: 767px) { @content; }
  }
  @if $breakpoint == tablet {
    @media (min-width: 768px) and (max-width: 1023px) { @content; }
  }
  @if $breakpoint == desktop {
    @media (min-width: 1024px) { @content; }
  }
  @if $breakpoint == large-desktop {
    @media (min-width: 1200px) { @content; }
  }
}

// ==================== 工具类 ====================

// 间距工具类
@for $i from 1 through 10 {
  .m-#{$i} { margin: #{$i * 4}px !important; }
  .mt-#{$i} { margin-top: #{$i * 4}px !important; }
  .mr-#{$i} { margin-right: #{$i * 4}px !important; }
  .mb-#{$i} { margin-bottom: #{$i * 4}px !important; }
  .ml-#{$i} { margin-left: #{$i * 4}px !important; }
  
  .p-#{$i} { padding: #{$i * 4}px !important; }
  .pt-#{$i} { padding-top: #{$i * 4}px !important; }
  .pr-#{$i} { padding-right: #{$i * 4}px !important; }
  .pb-#{$i} { padding-bottom: #{$i * 4}px !important; }
  .pl-#{$i} { padding-left: #{$i * 4}px !important; }
}

// 文字工具类
.text-primary { color: $text-primary !important; }
.text-regular { color: $text-regular !important; }
.text-secondary { color: $text-secondary !important; }
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

// 显示工具类
.d-flex { display: flex !important; }
.d-block { display: block !important; }
.d-inline-block { display: inline-block !important; }
.d-none { display: none !important; }

// Flex工具类
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.align-center { align-items: center !important; }
.align-start { align-items: flex-start !important; }
.align-end { align-items: flex-end !important; }

// 边框圆角工具类
.rounded-sm { border-radius: $border-radius-small !important; }
.rounded { border-radius: $border-radius-base !important; }
.rounded-lg { border-radius: $border-radius-large !important; }
.rounded-full { border-radius: 50% !important; }

// 阴影工具类
.shadow-sm { box-shadow: $box-shadow-light !important; }
.shadow { box-shadow: $box-shadow-base !important; }
.shadow-md { box-shadow: $box-shadow-medium !important; }
.shadow-lg { box-shadow: $box-shadow-heavy !important; }

// ==================== 动画关键帧 ====================
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 动画工具类
.fade-in { animation: fadeIn $animation-duration-base ease; }
.slide-in-up { animation: slideInUp $animation-duration-base ease; }
.slide-in-down { animation: slideInDown $animation-duration-base ease; }
.scale-in { animation: scaleIn $animation-duration-base ease; }
