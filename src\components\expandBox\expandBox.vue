<template>
  <div :style="boxStyle" class="expandBox">
    <transition :name="isVertical?'boxV-collapse':'boxH-collapse'">
      <div v-show="visible" class="content-box" :style="contentStyle">
        <slot></slot>
      </div>
    </transition>
    <div v-show="showTrigger" class="fold-trigger" :style="triggerStyle" @click="switchVisible">
      <i :class="triggerIcon"></i>
    </div>
  </div>
</template>

<script>

/**
 * 伸缩盒组件
 * 注意：
 *   direction 为 right 或 left 的时候，是根据盒子的 width 属性过渡。此时，width 不能为 auto，会没有过渡效果
 *   direction 为 top 或 bottom 的时候，是根据盒子的 height 属性过渡。此时，height 不能为 auto，会没有过渡效果
 * 插槽：
 * default : 伸缩盒的内容
 * 属性：
 * visible[Boolean] : 是否可见。默认true，使用的时候需要添加.sync修饰符。
 * width[String] : 整个组件的宽度。取值同css的width。为auto或确定的值时，切换可见状态的动画效果不同。
 * height[String] : 整个组件的高度。取值同css的height。为auto或确定的值时，切换可见状态的动画效果不同。
 * direction[String] : 组件展开的方向，即切换按钮的位置。有 right、left、top、bottom 四个取值。
 * showTrigger[Boolean] : 是否显示状态切换开关，需要另外控制开关的话，可以把自带的关闭
 * 事件：
 * switchVisible 切换状态的事件
 */
export default {
  name: 'expandBox',
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    direction: {
      type: String,
      default: 'right',
      validator(value) {
        let values = {right:true,left:true,top:true,bottom:true};
        return values[value];
      }
    },
    showTrigger: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      props: { label: 'label', children: 'children' }
    };
  },
  computed: {
    isVertical() {
      return this.direction === 'top' || this.direction === 'bottom';
    },
    triggerIcon() {
      let prefix = 'el-icon-arrow-';
      let icons = {right: {hidden:'right',visible:'left'},left:{hidden:'left',visible:'right'},top:{hidden:'up',visible:'down'},bottom:{hidden:'down',visible:'up'}};
      return prefix + icons[this.direction][this.visible?'visible':'hidden'];
    },
    boxStyle() {
      let flexDirections = {right:'row',left:'row-reverse',top:'column-reverse',bottom:'column'};
      let flexDirection = flexDirections[this.direction];
      let boxStyle = {width: this.width, height: this.height, flexDirection };
      if(this.isVertical) {
        if (this.height !== 'auto') {
          boxStyle.height = this.visible ? this.height : this.showTrigger ? '20px' : '0px';
        }
      }else{
        if (this.width !== 'auto') {
          boxStyle.width = this.visible ? this.width :
            this.showTrigger ? '20px' : '0px';
        }
      }
      return boxStyle;
    },
    contentStyle() {
      if(this.isVertical) {
        return {width:'100%'};
      }else{
        return {height:'100%'};
      }
    },
    triggerStyle() {
      if(this.isVertical) {
        return {width:'100%',flexDirection: 'row'};
      } else {
        return {height:'100%',flexDirection: 'column'};
      }
    }
  },
  methods: {
    switchVisible() {
      this.$emit('update:visible', !this.visible);
      //触发切换显隐的事件
      this.$emit('switchVisible',!this.visible);
    }
  }
};
</script>

<style>
.boxV-collapse-enter-active,
.boxV-collapse-leave-active {
  transition: width 0.5s ease 0s,height 0.5s ease 0s;
  overflow: hidden;
}
.boxV-collapse-enter,
.boxV-collapse-leave-to {
  height: 0 !important;
}
.boxH-collapse-enter-active,
.boxH-collapse-leave-active {
  transition: width 0.5s ease 0s,height 0.5s ease 0s;
  overflow: hidden;
}
.boxH-collapse-enter,
.boxH-collapse-leave-to {
  width: 0 !important;
}
</style>
<style scoped>
.expandBox {
  overflow: hidden;
  transition: width 0.5s ease 0s,height 0.5s ease 0s;
  display: flex;
  align-items: center;
}
.content-box {
  display: block;
  /* overflow: auto; */
  flex: 1 1 calc(100% - 20px);
}
.fold-trigger {
  display: inline-flex;
  flex: 0 0 20px;
  justify-content: center;
  cursor: pointer;
  /* background-color: #f4faff; */
  background: #fff;
}
.fold-trigger > i {
  font-size: 20px;
  font-weight: bolder;
}
.fold-trigger:hover {
  color: #68a9f6;
}
</style>
