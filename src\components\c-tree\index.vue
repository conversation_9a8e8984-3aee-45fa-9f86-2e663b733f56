<template>
  <div class="c-tree">
    <template v-for="(item,index) in treeData" >
      <div class="c-tree-item" v-if="item.subpermission && item.subpermission.length>0" :key="index">
        <div class="c-tree-item__content">
          <el-checkbox v-model="item.ischeck" @change="_handleCheckChange(parentData,treeData,item,item.ischeck)">{{item.subname}}</el-checkbox></div>
        <div class="c-tree-item-children">
          <c-tree :tree-data="item.subpermission" :parent-data="item"></c-tree>
        </div>
      </div>
      <div class="c-tree-item" v-else-if="item.subpermission && item.subpermission.length === 0" :key="index">
        <div class="c-tree-item__content">
          <el-checkbox v-model="item.ischeck" @change="_handleCheckChange(parentData,treeData,item,item.ischeck)">{{item.subname}}</el-checkbox></div>
      </div>
      <div class="c-tree-item-flex" v-else :key="index">
        <div class="c-tree-item__content">
          <el-checkbox v-model="item.ischeck" @change="_handleCheckChange(parentData,treeData,item,item.ischeck)">{{item.subname}}</el-checkbox></div>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: 'cTree',
  props:{
    treeData:{
      type:Array,
      default:()=>{
        return [];
      }
    },
    parentData:{
      type:Object,
      default:()=>{
        return {};
      }
    }
  },
  methods:{
    _handleCheckChange2(){
      console.log('121212')
    },
    /**
     * 选择状态发生改变
     * @param parent_data 父级
     * @param data 同级
     * @param item 当前数据
     * @param checked 当前状态
     * @private
     */
    _handleCheckChange(parent_data,data,item,checked){
      // console.log('parent_data:',parent_data);
      // console.log('data:',data);
      // console.log('item:',item);
      // console.log('checked:',checked);

      let sub_data = item.subpermission;
      //改变下级
      if(sub_data){
        this._formatCheckedStatus(sub_data,checked);
      }
      //改变上级
      let all_status = data.findIndex(target=>target.ischeck === true);
      if(all_status === -1){
        // parent_data.ischeck = false;
        this.$set(parent_data,'ischeck',false);
      }else{
        // parent_data.ischeck = true;
        this.$set(parent_data,'ischeck',true);
      }
    },
    /**
     * 递归复制，跟随父节点改变
     * @param sub_data
     * @param checked
     * @private
     */
    _formatCheckedStatus(sub_data,checked){
      for(let sub_item of sub_data){
        this.$set(sub_item,'ischeck',checked);
        if(sub_item.subpermission && sub_item.subpermission.length>0){
          this._formatCheckedStatus(sub_item.subpermission,checked);
        }
      }
    },
  }
};
</script>

<style lang="scss" scoped>
  .c-tree{
    .c-tree-item{
      padding-left: 10px;
      .c-tree-item__content{
        margin-bottom: 10px;
      }
      .c-tree-item-children{
        padding-left: 10px;
        .c-tree-item{
          margin-bottom: 0;
        }
      }
    }

    .c-tree-item-flex{
      display: inline-block;
      padding-left: 10px;
    }
  }
</style>
