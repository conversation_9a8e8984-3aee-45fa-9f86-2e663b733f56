<template>
  <el-dialog
    custom-class="add-server-dialog width-520"
    :show-close="false"
    :visible.sync="buyVisible"
    @close="closeFn"
  >
    <header
      class="fixed-code__title"
      slot="title"
      style="font-size: 18px; font-weight: bold"
    >
      短信购买<i
        class="el-icon-close dialog-header-iconfont"
        @click="buyVisible = false"
      ></i>
    </header>

    <el-steps :active="activeIndex" simple style="padding: 18px 20%">
      <el-step title="数量选择" icon="iconfont icon-icon-test1"></el-step>
      <el-step title="确认支付" icon="iconfont icon-icon-test"></el-step>
    </el-steps>

    <el-form
      ref="addForm"
      v-if="buyVisible"
      label-width="80px"
      :model="buyForm"
      class="custom-form-style fiexd-code-form"
    >
      <div v-show="activeIndex == 1">
        <el-form-item label="所属集团">
          <el-select
            v-model="buyForm.groupid"
            filterable
            style="width: 292px"
            @change="changeUnionFn"
          >
            <el-option
              v-for="item in cityGroups"
              :label="item.value_name"
              :value="item.value_no"
              :key="item.value_no"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属车场">
          <el-select
            v-model="buyForm.comid"
            filterable
            style="width: 292px"
            @change="changeCom"
          >
            <el-option
              v-for="item in cityParks"
              :label="item.value_name"
              :value="item.value_no"
              :key="item.value_no"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="数量选择">
          <el-select
            v-model="buyForm.count"
            style="width: 292px"
            @change="changeCount"
          >
            <el-option
              v-for="item in countSelectData"
              :label="item.count"
              :value="item.count"
              :key="item.id"
            >
              <span style="float: left">{{ item.count }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px"
                >条</span
              >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="有效期">
          <p>{{ months }}个月</p>
        </el-form-item>
        <el-form-item label="支付费用">
          <p>{{ buyForm.money }} 元</p>
        </el-form-item>
      </div>
      <div v-show="activeIndex == 2">
        <div class="pay-method-wrapper">
          <span>支付方式：</span>
          <el-radio-group v-model="payMethod" @change="paymethodChangeFn">
            <el-radio :label="1">扣除分润余额</el-radio>
            <el-radio :label="2">扫码支付</el-radio>
            <el-radio :label="3">人工调价</el-radio>
          </el-radio-group>
        </div>
        <div class="code-wrapper" v-if="payMethod == 1 || payMethod == 3">
          <p style="line-height: 200px" v-show="payMethod == 1">
            扣除厂商分润余额支付该平台服务费用
          </p>
          <p style="line-height: 200px" v-show="payMethod == 3">
            如有特殊原因需要调价的,提交后联系泊链商务人员处理
          </p>
        </div>
        <div class="code-wrapper" v-else>
          <img :src="qrsrc" />
          <div class="pay-state" v-if="payState == 1 || payState == 0">
            <img
              class="pay-state-img"
              :src="payState == 1 ? paySuccessImg : payErrorImg"
            />
          </div>
          <p v-show="payState == 2">扫码支付</p>
          <p class="pay-success" v-show="payState == 1">
            支付成功<span style="margin: 0 3px">{{ readSecondSuccess }}</span
            >秒后<a class="back" @click="backList">返回列表</a>
          </p>
          <p class="pay-error" v-show="payState == 0">
            支付失败<span style="margin: 0 3px">{{ readSecondError }}</span
            >秒后<a class="back" @click="getCode">重新获取二维码</a>
          </p>
        </div>
      </div>
    </el-form>

    <div v-if="activeIndex === 1" class="fixed-code-btn">
      <el-button
        type="primary"
        style="width: 144px"
        @click="getCode"
        :loading="nextLoad"
        >下一步</el-button
      >
    </div>
    <div v-else class="fixed-code-btn">
      <el-button style="width: 144px" @click="upperStep">上一步</el-button>
      <el-button
        type="primary"
        style="width: 144px; margin-left: 60px"
        :loading="comfireLoad"
        @click="paySumbitFn"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "BlBuyDialog",
  data() {
    return {
      buyVisible: false,
      comfireLoad: false,
      nextLoad: false,
      buyForm: {
        groupid: "",
        comid: "",
        count: "",
        money: "",
      },
      activeIndex: 1,
      cityGroups: [],
      cityParks: [],
      cityParksOld:[],
      payMethod: 1,
      payState: 1,
      readSecondSuccess: false,
      readSecondError: false,
      paySuccessImg: "",
      payErrorImg: "",
    };
  },
  methods: {
    open() {
    // 短信购买：
    // 厂商1：所属集团 所属车场 数量选择 有效期 支付费用
    // 服务商2：
    // 车场3：车场需联系上级厂商购买？
      this.buyVisible = true;

    },

    getOptions() {
      this.$axios
        .all([
          this.$axios.get(
            "/getdata/cityparks" +
              "?cityid=" +
              sessionStorage.getItem("cityid") +
              "&t=" +
              Date.now()
          ),
          this.$axios.get(
            "/getdata/getallunion" +
              "?cityid=" +
              sessionStorage.getItem("cityid") +
              "&t=" +
              Date.now()
          ),
        ])
        .then(
          this.$axios.spread((res1, res2) => {
            that.cityParks = res1.data;
            that.cityParksOld = res1.data;
            that.cityGroups = res2.data;
          })
        )
        .catch(function (error) {
          console.log(error);
        });
    },

    closeFn() {
      this.buyVisible = false;
    },

    changeUnionFn() {},

    changeCount() {},

    paymethodChangeFn() {},

    backList() {},

    getCode() {},

    upperStep() {},

    paySumbitFn() {},
  },
};
</script>

<style lang="scss" scoped>
.fixed-code__title {
  text-align: center;
}
.fiexd-code-form {
  position: relative;
  padding: 30px 53px 0 53px;
  height: 312px;
}
.fixed-code-btn {
  margin: 0 auto;
  display: block;
  width: 372px;
  text-align: center;
}
.code-wrapper {
  position: relative;
  padding-top: 20px;
  width: 100%;
  text-align: center;
  img {
    display: inline-block;
    width: 196px;
    height: 196px;
  }
  .pay-state {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 196px;
    height: 196px;
    background: rgba(255, 255, 255, 0.9);
    z-index: 3;
    .pay-state-img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 112px;
      height: 112px;
    }
  }
  p {
    font-size: 16px;
    line-height: 30px;
    color: rgba(54, 54, 54, 1);
  }
  .back {
    margin-left: 7px;
    color: #3c75cf;
    cursor: pointer;
  }
}
</style>