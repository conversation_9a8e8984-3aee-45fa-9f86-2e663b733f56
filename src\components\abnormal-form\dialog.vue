<template>
  <el-dialog
    :close-on-click-modal="false"
    custom-class="custom-dialog" 
    :show-close="false"
    :visible="dialogVisible"
    width="950px"
    :append-to-body="true"
    >
    <header class="fixed-code__title" slot="title" style="font-size: 18px; font-weight: bold;text-align:center">
      关联场内订单<i class="el-icon-close dialog-header-iconfont" @click="dialogVisible=false"></i>
    </header>
    <div class="content">
      <el-form :model="searchForm" inline class="shop-custom-form-search">
        <el-form-item label="入场时间">
          <el-date-picker 
            class="shop-custom-datepicker"
            v-model="searchForm.date"
            type="datetimerange"
            value-format="timestamp"
            :clearable="false"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="车牌号码">
          <bl-keyboard v-model="searchForm.carNumber">
            <div slot="reference-box">
              <el-input v-model="searchForm.carNumber" class="shop-custom-input" type="text" placeholder="请输入车牌号" clearable/>
            </div>
          </bl-keyboard>
        </el-form-item>
        <el-form-item class="shop-clear-style">
          <el-button type="primary" @click="searchFn" icon="el-icon-search">搜索</el-button>
        </el-form-item>
      </el-form>
      <div class="tooltip">提示: 请搜索后【点击】表格数据任意行来进行【选择订单】或者【查看入场图片】;  完成选择后请点击【确认】按钮</div>
      <el-table :data="tableData" border stripe @row-click="rowClick" highlight-current-row>
        <el-table-column prop="orderId" label="订单号"></el-table-column>
        <el-table-column prop="carNumber" label="车牌号码" width="100px"></el-table-column>
        <el-table-column prop="inAreaName" label="区域" width="100px"></el-table-column>
        <el-table-column prop="inChannelName" label="车道名称"></el-table-column>
        <el-table-column prop="inTime" label="入场时间">
          <span slot-scope="scope">{{ scope.row.inTime ? common.dateformat(scope.row.inTime) : '-' }}</span>
        </el-table-column>
        <el-table-column prop="carCostType" label="车辆种类" width="120px">
          <span slot-scope="scope">{{ scope.row.carCostType ? carCostTypeMap[scope.row.carCostType] : '-' }}</span>
        </el-table-column>
        <el-table-column prop="carType" label="车型" width="120px"></el-table-column>
      </el-table>
      <div class="picture">
        <div class="pic-item">
          <img v-if="rowData.inPic" :src="rowData.inPic" alt="" class="pic-img">
          <img v-else :src="defaultImg" alt="">
        </div>
        <div class="pic-item">
          <img v-if="row.outPic" :src="row.outPic" alt="" class="pic-img">
          <img v-else :src="defaultImg" alt="">
        </div> 
      </div>
      <div class="footer-btn">
        <el-button type="primary" @click="confirmBtn">确认</el-button>
        <el-button plain @click="dialogVisible=false">关闭</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import BlKeyboard from '@/components/v2/keyboard/index.vue';
import common from '@/common/js/common';

const carCostTypeMap = {
  1: '临时车',
  2: '月卡车',
  3: '内部车',
  9: '储值车',
}
export default {
  components: {
    BlKeyboard
  },
  data() {
    return {
      common,
      defaultImg: require('@/pages/fourg/park/4g-sentry-box/images/image-icon.png'),
      carCostTypeMap,
      dialogVisible: false,
      searchForm: {
        date: [],
        carNumber: ''
      },
      loading: false,
      tableData: [],
      row: {},
      rowData: {},
    }
  },
  methods: {
    show(data, row) {
      this.row = row
      this.tempData = data
      this.dialogVisible = true
      // 默认查询时间为当前月
      this.searchForm.date = [
        new Date(new Date().getFullYear(), new Date().getMonth(), 1, 0, 0, 0), // 当前月第一天的 00:00:00
        new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0, 23, 59, 59) // 当前月最后一天的 23:59:59
      ];
    },
    searchFn() {
      const carNumber = this.searchForm.carNumber
      if (!carNumber || carNumber.length < 4) {
        this.$message.warning('车牌号码至少为4位')
        return
      }
      const api = this.row.businessDomain + '/zld/eomsEvent/queryOrderList';
      const params = {
        carNumber,
        comId: this.row.comId,
        beginTime: this.searchForm.date[0] / 1000,
        endTime: this.searchForm.date[1] / 1000,
        rp: 100,
        page: 1
      }
      this.loading = true
      this.$axios.post(api, params).then(res => {
        this.loading = false
        const { code, data } = res.data
        if (code === 200 && data.list && data.list.length) {  
          this.tableData = data.list
        } else {
          this.tableData = []
          this.$message.error('未查询到相关订单')
        }
      }).catch(() => {
        this.loading = false
      })
    },
    timeChange(val) {
      
    },
    rowClick(row) {
      this.rowData = row
    },
    confirmBtn() {
      this.$emit('handleConfirm', this.rowData)
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/utils.scss';
/deep/.el-dialog.custom-dialog {
  .el-dialog__body {
    // background: #10223f;
    .content {
      .el-date-editor.el-range-editor.el-input__inner.el-date-editor--datetimerange {
        width: vw(380);
      }
      .el-input__inner {
        width: vw(160);
      }
      .cell {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .picture {
        display: flex;
        justify-content: space-between;
        background: #fff;
        height: vw(250);
        margin: vw(20) 0;
        .pic-item {
          margin: vw(10);
          display: flex;
          justify-content: center;
          align-items: center;
          width: 50%;
          background: #e1e1e1;
          .pic-img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .footer-btn {
        display: flex;
        justify-content: flex-end;
        & > button {
          margin-left: vw(20);
        }
      }
      .tooltip {
        color: #000;
        font-size: vw(16);
        // font-weight: bold;
        // margin: vw(10) 0;
        margin-bottom: vw(10);
      }
    }
  }
}
/deep/.el-table {
  // background: #10223F !important;
  border: none;
  height: vh(160);
  overflow: auto;
  th {
    font-weight: bold;
    padding: 0;
    box-sizing: content-box;  
    height: vh(45);
  }
  td {
    padding: 0;
    box-sizing: content-box;
    height: vw(45);
  }
  tr {
    font-size: vw(14);
  }
}
/deep/.el-table.el-table--fit.el-table--striped.el-table--border.el-table--enable-row-transition::before {
  background: transparent !important;
}
/deep/.el-table.el-table--fit.el-table--striped.el-table--border.el-table--enable-row-transition::after {
  background: transparent !important;
}
</style>