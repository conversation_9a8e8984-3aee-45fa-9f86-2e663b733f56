/**
 * 车场配置混入
 * 提供通用的车场配置功能和状态管理
 */

import { FormValidator, PARKING_VALIDATION_RULES, ErrorHandler } from '@/utils/formValidation';
import { createApiInstance } from '@/utils/apiRequest';

export default {
  data() {
    return {
      // 加载状态管理
      loading: {
        basic: false,
        vehicle: false,
        shuttle: false,
        account: false,
        upload: false,
        airports: false,
        regions: false
      },

      // 表单数据
      basicForm: {
        lotName: '',
        airportId: '',
        region: [],
        address: '',
        longitude: '',
        latitude: '',
        phone: '',
        feeStandard: '',
        positionType: '',
        feeScreenImages: [],
        parkingPhotos: []
      },

      vehicleForm: {
        dailyRate: 0,
        minDays: 1,
        freeOvertime: 30
      },

      shuttleForm: {
        shuttlePhone: '',
        region: [],
        airportId: '',
        distance: '',
        timeSlot: '',
        duration: '',
        positionType: '',
        dropoffLocation: '',
        pickupLocation: '',
        description: '',
        notice: ''
      },

      accountInfo: {
        globalSettings: {
          settlementCycle: '',
          accountType: ''
        },
        accountList: []
      },

      // 选项数据
      airportList: [],
      regionOptions: [],
      positionTypeOptions: [
        { label: '室内停车场', value: 'indoor' },
        { label: '室外停车场', value: 'outdoor' },
        { label: '地下停车场', value: 'underground' }
      ],

      // 验证规则
      basicRules: PARKING_VALIDATION_RULES.basicInfo,
      vehicleRules: PARKING_VALIDATION_RULES.vehicleRate,
      shuttleRules: PARKING_VALIDATION_RULES.shuttleService,

      // 步骤状态
      stepStatus: {
        basic: 'process',
        vehicle: 'wait',
        shuttle: 'wait',
        account: 'wait'
      },

      // 当前车场ID
      currentParkingId: null
    };
  },

  computed: {
    // 计算已完成步骤数
    completedSteps() {
      return Object.values(this.stepStatus).filter(status => status === 'finish').length;
    },

    // 检查基础信息是否完整
    isBasicInfoComplete() {
      const required = ['lotName', 'airportId', 'region', 'address', 'longitude', 'latitude', 'phone', 'feeStandard', 'positionType'];
      return required.every(field => {
        const value = this.basicForm[field];
        return Array.isArray(value) ? value.length > 0 : !!value;
      });
    },

    // 检查费率信息是否完整
    isVehicleRateComplete() {
      return this.vehicleForm.dailyRate > 0 && this.vehicleForm.minDays > 0;
    },

    // 检查接送服务是否完整
    isShuttleServiceComplete() {
      const required = ['shuttlePhone', 'region', 'airportId', 'distance', 'timeSlot', 'duration', 'positionType', 'dropoffLocation', 'pickupLocation', 'description', 'notice'];
      return required.every(field => {
        const value = this.shuttleForm[field];
        return Array.isArray(value) ? value.length > 0 : !!value;
      });
    },

    // 检查分账信息是否完整
    isAccountInfoComplete() {
      return this.accountInfo.accountList.length > 0 && 
             this.accountInfo.globalSettings.settlementCycle &&
             this.accountInfo.globalSettings.accountType;
    }
  },

  watch: {
    // 监听表单完成状态，自动更新步骤状态
    isBasicInfoComplete(newVal) {
      this.stepStatus.basic = newVal ? 'finish' : 'process';
      if (newVal && this.stepStatus.vehicle === 'wait') {
        this.stepStatus.vehicle = 'process';
      }
    },

    isVehicleRateComplete(newVal) {
      this.stepStatus.vehicle = newVal ? 'finish' : 'process';
      if (newVal && this.stepStatus.shuttle === 'wait') {
        this.stepStatus.shuttle = 'process';
      }
    },

    isShuttleServiceComplete(newVal) {
      this.stepStatus.shuttle = newVal ? 'finish' : 'process';
      if (newVal && this.stepStatus.account === 'wait') {
        this.stepStatus.account = 'process';
      }
    },

    isAccountInfoComplete(newVal) {
      this.stepStatus.account = newVal ? 'finish' : 'process';
    }
  },

  created() {
    // 初始化API实例
    const { requestManager, parkingConfigApi } = createApiInstance(this);
    this.requestManager = requestManager;
    this.parkingConfigApi = parkingConfigApi;

    // 获取路由参数
    this.currentParkingId = this.$route.params.id;

    // 初始化数据
    this.initializeData();
  },

  beforeDestroy() {
    // 清理请求
    if (this.requestManager) {
      this.requestManager.cancelAllRequests();
    }
  },

  methods: {
    /**
     * 初始化数据
     */
    async initializeData() {
      try {
        // 并行加载基础数据
        await Promise.all([
          this.loadAirportList(),
          this.loadRegionOptions()
        ]);

        // 如果有车场ID，加载车场数据
        if (this.currentParkingId) {
          await this.loadParkingData();
        }
      } catch (error) {
        console.error('初始化数据失败:', error);
      }
    },

    /**
     * 加载机场列表
     */
    async loadAirportList() {
      try {
        const data = await this.parkingConfigApi.getAirportList();
        this.airportList = data || [];
      } catch (error) {
        console.error('加载机场列表失败:', error);
      }
    },

    /**
     * 加载地区选项
     */
    async loadRegionOptions() {
      try {
        const data = await this.parkingConfigApi.getRegionOptions();
        this.regionOptions = data || [];
      } catch (error) {
        console.error('加载地区选项失败:', error);
      }
    },

    /**
     * 加载车场数据
     */
    async loadParkingData() {
      try {
        // 并行加载所有配置数据
        const [basicData, vehicleData, shuttleData, accountData] = await Promise.allSettled([
          this.parkingConfigApi.getBasicInfo(this.currentParkingId),
          this.parkingConfigApi.getVehicleRate(this.currentParkingId),
          this.parkingConfigApi.getShuttleService(this.currentParkingId),
          this.parkingConfigApi.getAccountInfo(this.currentParkingId)
        ]);

        // 处理基础信息
        if (basicData.status === 'fulfilled' && basicData.value) {
          this.basicForm = { ...this.basicForm, ...basicData.value };
        }

        // 处理费率信息
        if (vehicleData.status === 'fulfilled' && vehicleData.value) {
          this.vehicleForm = { ...this.vehicleForm, ...vehicleData.value };
        }

        // 处理接送服务
        if (shuttleData.status === 'fulfilled' && shuttleData.value) {
          this.shuttleForm = { ...this.shuttleForm, ...shuttleData.value };
        }

        // 处理分账信息
        if (accountData.status === 'fulfilled' && accountData.value) {
          this.accountInfo = { ...this.accountInfo, ...accountData.value };
        }

      } catch (error) {
        console.error('加载车场数据失败:', error);
      }
    },

    /**
     * 保存基础信息
     */
    async saveBasicInfo() {
      const validator = new FormValidator(this.$refs.basicForm);
      
      if (await validator.validateForm()) {
        try {
          await this.parkingConfigApi.saveBasicInfo(this.currentParkingId, this.basicForm);
          this.updateStepStatus('basic', 'finish');
        } catch (error) {
          console.error('保存基础信息失败:', error);
        }
      }
    },

    /**
     * 保存费率信息
     */
    async saveVehicleRate() {
      const validator = new FormValidator(this.$refs.vehicleForm);
      
      if (await validator.validateForm()) {
        try {
          await this.parkingConfigApi.saveVehicleRate(this.currentParkingId, this.vehicleForm);
          this.updateStepStatus('vehicle', 'finish');
        } catch (error) {
          console.error('保存费率信息失败:', error);
        }
      }
    },

    /**
     * 保存接送服务
     */
    async saveShuttleService() {
      const validator = new FormValidator(this.$refs.shuttleForm);
      
      if (await validator.validateForm()) {
        try {
          await this.parkingConfigApi.saveShuttleService(this.currentParkingId, this.shuttleForm);
          this.updateStepStatus('shuttle', 'finish');
        } catch (error) {
          console.error('保存接送服务失败:', error);
        }
      }
    },

    /**
     * 保存分账信息
     */
    async saveAccountInfo() {
      try {
        await this.parkingConfigApi.saveAccountInfo(this.currentParkingId, this.accountInfo);
        this.updateStepStatus('account', 'finish');
      } catch (error) {
        console.error('保存分账信息失败:', error);
      }
    },

    /**
     * 更新步骤状态
     */
    updateStepStatus(step, status) {
      this.$set(this.stepStatus, step, status);
    },

    /**
     * 处理文件上传
     */
    async handleFileUpload(file, type = 'image') {
      try {
        const response = await this.parkingConfigApi.uploadFile(file, type);
        return response.url;
      } catch (error) {
        console.error('文件上传失败:', error);
        throw error;
      }
    },

    /**
     * 重置表单
     */
    resetForm(formName) {
      if (this.$refs[formName]) {
        this.$refs[formName].resetFields();
      }
    },

    /**
     * 清除验证
     */
    clearValidation(formName) {
      if (this.$refs[formName]) {
        this.$refs[formName].clearValidate();
      }
    },

    /**
     * 显示确认对话框
     */
    showConfirmDialog(message, title = '确认操作') {
      return this.$confirm(message, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });
    },

    /**
     * 返回上一页
     */
    goBack() {
      this.$router.go(-1);
    }
  }
};
