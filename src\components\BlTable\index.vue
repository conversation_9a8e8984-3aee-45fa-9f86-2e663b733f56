<template>
  <div class="bl-table-container" ref="tableContainer">
    <!-- 工具栏区域 -->
    <div v-if="showToolbar" class="table-toolbar">
      <el-button
        v-for="(btn, index) in toolbarButtons"
        :key="index"
        :icon="btn.icon"
        @click="btn.handler"
        :type="btn.type || 'primary'"
        :size="btn.size"
        >{{ btn.label }}</el-button
      >
      <div class="table-title" v-if="title">{{ title }}</div>
    </div>

    <!-- 表格主体 -->
    <el-table
      ref="elTable"
      v-bind="$attrs"
      v-on="$listeners"
      :data="tableData"
      :height="tableHeight"
      v-loading="loading"
      :show-summary="showSummary"
      :summary-method="getSummaries"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <el-table-column v-if="showExpand" type="expand">
        <template slot-scope="scope">
          <slot name="expand" :row="scope.row"></slot>
        </template>
      </el-table-column>
      <el-table-column v-if="showSelection" type="selection" width="55">
      </el-table-column>
      <el-table-column v-if="showIndex" label="序号" type="index" width="55">
      </el-table-column>

      <slot></slot>

      <!-- 操作列插槽 -->
      <el-table-column
        v-if="showOperation"
        label="操作"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <slot name="operation" :row="scope.row"></slot>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="table-pagination">
      <el-pagination
        v-if="showPagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        :page-sizes="pageSizes"
        :layout="layout"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: "BlTable",
  props: {
    // 数据请求配置
    api: { type: Function },
    requestParams: { type: Object, default: () => ({}) },

    // 表格配置
    title: String,
    showToolbar: { type: Boolean, default: false },
    showExpand: { type: Boolean, default: false },
    showSelection: { type: Boolean, default: false },
    showIndex: { type: Boolean, default: false },
    showOperation: { type: Boolean, default: false },
    toolbarButtons: { type: Array, default: () => [] },

    // 分页配置
    showPagination: { type: Boolean, default: false },
    pageSizes: { type: Array, default: () => [10, 20, 50, 100] },
    layout: {
      type: String,
      default: "total, sizes, prev, pager, next, jumper",
    },

    // 自适应
    autoHeight: { type: Boolean, default: true },
    minHeight: { type: Number, default: 300 },

    // 合计行配置
    showSummary: { type: Boolean, default: false },
    summaryMethod: { type: Function },
    summaryFields: { type: Array, default: () => [] },
  },

  data() {
    return {
      loading: false,
      tableData: [],
      tableHeight: null,
      selectedRows: [],
      sortParams: {},
      total: 0,
      pageSize: 10,
      currentPage: 1,
    };
  },

  methods: {
    async loadData() {
      try {
        this.loading = true;
        const params = {
          ...this.sortParams,
          ...this.requestParams,
        };
        console.log("params", params);
        const resData = await this.api(params);

        console.log("resData", resData);
        if (!resData) {
          this.tableData = [];
          this.total = 0;
          return;
        }

        this.tableData = resData.list;
        this.total = resData.total;
      } finally {
        this.loading = false;
      }
    },

    handleSelectionChange(selection) {
      this.selectedRows = selection;
      this.$emit("selection-change", selection);
    },

    handleSortChange({ column, prop, order }) {
      this.sortParams = {
        sortField: prop,
        sortOrder: order === "ascending" ? "asc" : "desc",
      };
      this.loadData();
    },

    handleSizeChange(size) {
      this.$emit("handleSizeChange", size);
    },

    handleCurrentChange(page) {
      this.$emit("handleCurrentChange", page);
    },

    calcTableHeight() {
      const container = this.$refs.tableContainer; // ← 此处获取的 DOM 实例
      // 计算容器高度 - 分页高度 - 安全边距
      this.tableHeight = Math.max(container.offsetHeight - 52 - 20, 500);
    },

    getSummaries(param) {
      // 如果用户提供了自定义的合计方法，使用用户的方法
      if (this.summaryMethod) {
        return this.summaryMethod(param);
      }

      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        
        const values = data.map(item => Number(item[column.property]));
        
        // 如果指定了需要合计的字段，只对这些字段进行合计
        if (this.summaryFields.length > 0) {
          if (this.summaryFields.includes(column.property)) {
            if (!values.every(value => isNaN(value))) {
              sums[index] = values.reduce((prev, curr) => {
                const value = Number(curr);
                if (!isNaN(value)) {
                  return prev + curr;
                } else {
                  return prev;
                }
              }, 0);
            } else {
              sums[index] = '';
            }
          } else {
            sums[index] = '';
          }
        } else {
          // 如果没有指定字段，对所有数字字段进行合计
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
          } else {
            sums[index] = '';
          }
        }
      });

      return sums;
    },
  },

  mounted() {
    this.autoHeight && this.calcTableHeight();
    window.addEventListener("resize", this.handleResize);
    this.api && this.loadData();
  },
};
</script>

<style scoped>
/* 新增工具栏样式 */
.table-toolbar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  font-size: 16px;
  font-weight: 500;
  line-height: 32px;
}

.table-pagination {
  display: flex;
  justify-content: flex-end; /* 将分页组件放在右侧 */
  align-items: center;
  margin-top: 16px; /* 添加顶部间距 */
}
</style>