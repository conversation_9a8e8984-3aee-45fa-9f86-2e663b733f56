const destroyView = {
    data() {
        return {
            routePath: ''
        }
    },
    mounted() {
        this.routePath = this.$route.path
    },
    computed: {
        cachedViews() {
            return this.$store.state.tagsView.cachedViews
        }
    },
    watch: {
        cachedViews(value) {
            if(!value.includes('dataCenter_RemoteOpening') && !value.includes('dataCenter_RemoteOpeningGroup') && !value.includes('eomsEventManage_choosePark')){
                this.$destroy(this.$options.name)
            }
        }
    }
}

export default destroyView
