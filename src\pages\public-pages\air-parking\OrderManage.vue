<template>
  <section class="right-wrapper-size shop-table-wrapper" id="scrollBarDom">
    <div class="shop-custom-operation">
      <div class="shop-custom-operation">
        <header class="shop-custom-header">
          <p style="float: left">
            机场停车<span style="margin: 2px">-</span>预约订单管理
          </p>
          <div class="float-right">
          <el-button type="text" size="mini" @click="resetForm" icon="el-icon-refresh" style="font-size: 14px;color: #1E1E1E;">刷新</el-button>
        </div>
        </header>
      </div>
      <super-form
        :form-config="formConfig"
        :value="searchForm"
        @input="handleSearch"
      />
    </div>

    <div class="count-table-wrapper-style">
      <bl-table
        border
        ref="tableRef"
        :api="fetchTableData"
        :currentPage="current"
        :request-params="searchForm"
        :showExpand="true"
        :showToolbar="true"
        :showPagination="true"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handlePageChange"
      >
        <template slot="expand" slot-scope="{ row }">
          <div class="foldInfo">
            <div class="folditem">预约停车时间: {{ row.expectStart }}</div>
            <div class="folditem">入场时间: {{ row.inTime }}</div>
          </div>
          <div class="foldInfo">
            <div class="folditem">预约取车时间: {{ row.expectEnd }}</div>
            <div class="folditem">离场时间: {{ row.outTime }}</div>
          </div>
          <div class="foldInfo">
            <div class="folditem">车场联系电话: {{ row.userPhone }}</div>
            <div class="folditem">停车时长: {{ row.days }}</div>
          </div>
          <div class="foldInfo">
            <div class="folditem">接驳司机电话: {{ row.shuttlePhone }}</div>
            <div class="folditem">停车费: {{ row.payAmount }}</div>
          </div>
          <!-- <div class="foldInfo">
            <div class="folditem">退款金额: {{ row.refundAmount }}</div>
            <div class="folditem">退款时间: {{ row.refundTime }}</div>
          </div> -->
          <div class="foldInfo">
            <div class="folditem">结算状态: 
              <span v-if="row.settleStatus === 0" type="danger">未结算</span>
              <span v-else-if="row.settleStatus === 1" type="success">已结算</span>
              <span v-else>已分账</span>
            </div>
          </div>
        </template>
        <el-table-column
          prop="parkingLotName"
          label="车场名称"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="airportName"
          label="机场"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="plateNo"
          label="车牌号"
          align="center"
        ></el-table-column>
        <el-table-column prop="userPhone" label="手机号" />
        <el-table-column prop="orderTime" label="下单时间" width="180" />
        <el-table-column prop="passengerCnt" label="时长" />
        <el-table-column prop="payAmount" label="金额" />
        <el-table-column prop="shuttlePhone" label="接驳电话" />
        <el-table-column prop="orderStatus" label="订单状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.orderStatus === 0" type="danger">已预约</el-tag>
            <el-tag v-else-if="scope.row.orderStatus === 1" type="success">已取消</el-tag>
            <el-tag v-else-if="scope.row.orderStatus === 2" type="success">已过期</el-tag>
            <el-tag v-else>已完成</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="shuttlePhone" label="在场状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.parkStatus === 0" type="danger">未入场</el-tag>
            <el-tag v-else-if="scope.row.parkStatus === 1" type="success">已入场</el-tag>
            <el-tag v-else type="success">已离场</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="operate"
          label="操作"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button v-if="scope.row.parkStatus === 1 && scope.row.orderStatus === 0" type="text" @click="handleEdit(scope.row)"
              >人工结算</el-button>
          </template>
        </el-table-column>
      </bl-table>
    </div>

    <!-- 订单结算弹窗 -->
    <order-settlement-dialog
      :visible.sync="settlementDialogVisible"
      :order-data="currentOrderData"
      @confirm="handleSettlementConfirm"
      @cancel="handleSettlementCancel"
    />
  </section>
</template>

<script>
import { reserveOrderList, settleOrderList, dropdownAirport, dropdownParking } from "@/api/airParking";
import SuperForm from "@/components/super-form/inline-form";
import BlTable from "@/components/BlTable/index.vue";
import OrderSettlementDialog from "./components/OrderSettlementDialog.vue";

export default {
  name: "OrderManage",
  components: { SuperForm, BlTable, OrderSettlementDialog },
  data() {
    return {
      searchForm: {
        parkStatus:'',
        orderStatus:'',
        settleStatus:'',
        plateNo:'',
        userPhone:'',
        airportId:'',
        parkingLotId:'',
      },
      settlementDialogVisible: false,
      currentOrderData: {},
      airportList:[], //机场列表
      dropParkingList:[], //车场列表
      current: 1, // 当前页码
      size: 10, // 每页显示多少条数据
      requestUrl: this.$PUBLIC_URL.AIRPORT_API, // 请求地址
      formConfig: {
        first: [
          {
              label:'在场状态',
              type:'select',
              prop:'parkStatus',
              options:[{
                value_name:'未入场',
                value_no:'0'
              },
              {
                value_name:'已入场',
                value_no:'1'
              },
              {
                value_name:'已离场',
                value_no:'2'
              }]
          },
          {
              label:'订单状态',
              type:'select',
              prop:'orderStatus',
              options:[{
                value_name:'已预约',
                value_no:'0'
              },
              {
                value_name:'已取消',
                value_no:'1'
              },
              {
                value_name:'已过期',
                value_no:'2'
              },
              {
                value_name:'已完成',
                value_no:'3'
              }]
          },
          {
              label:'结算状态',
              type:'select',
              prop:'settleStatus',
              options:[{
                value_name:'未结算',
                value_no:'0'
              },
              {
                value_name:'已结算',
                value_no:'1'
              },
              {
                value_name:'已分账',
                value_no:'2'
              }]
          },
          {
            label: "手机号",
            type: "input",
            prop: "userPhone",
          },
          {
            label: "车牌号",
            type: "input",
            prop: "plateNo",
          },
          {
            label: "机场",
            type: "select",
            prop: "airportId",
            options:this.airportList
          },
          {
            label: "车场名称",
            type: "select",
            prop: "parkingLotId",
            options: this.dropParkingList
          },
        ],
      }
    };
  },

  mounted() {
   
  },
  watch: {
      airportList:function(val,oldVal){
          this.formConfig.first[5].options = val;
      },
      dropParkingList:function(val,oldVal){
          this.formConfig.first[6].options = val;
      },
  },
  
  activated(){
    let that = this;
    that.loadAirportList();
    that.loadParkingList();
    that.resetForm();
  },

  methods: {
    async fetchTableData() {
      const params = { ...this.searchForm, size: this.size, current: this.current };
      console.log("请求参数:", params);
      try {
        const response = await reserveOrderList(this.requestUrl, params);
        const dataList = response? response.data.data : [];
        console.log("请求响应:", response);
        return {
          list: dataList.records || [],
          total: dataList.total || 0,
        };
      } catch (error) {
        console.error('订单数据加载失败:', error);

        let errorMessage = "数据加载失败";
        if (error.response) {
          const { status } = error.response;
          if (status === 401) {
            errorMessage = "登录已过期，请重新登录";
          } else if (status === 403) {
            errorMessage = "没有权限查看订单数据";
          } else if (status === 500) {
            errorMessage = "服务器错误，请稍后重试";
          }
        } else if (error.request) {
          errorMessage = "网络连接失败，请检查网络";
        }

        this.$message.error(errorMessage);
        return { list: [], total: 0 };
      }
    },

    // 加载机场列表
    async loadAirportList() {
      try {
        // 这里应该调用实际的API
        const response = await dropdownAirport(this.requestUrl);

        // 防止 response 或 response.data 不存在导致解构失败
        if (!response || !response.data) {
          throw new Error('响应数据为空');
        }

        const { code, data } = response.data;
        if (code == 200) {
          // 转换数据格式为下拉列表需要的格式
          this.airportList = data.map(item => ({
            value_name: item.name || item.airportName || item.label,
            value_no: item.id || item.airportId || item.value
          }));
        } else {
          this.airportList = [];
        }
      } catch (error) {
        console.error('加载机场列表失败:', error);
        this.airportList = [];

        // 只在用户主动操作时显示错误提示
        if (error.response && error.response.status !== 401) {
          this.$message.warning('机场列表加载失败，请刷新页面重试');
        }
      }
    },

    // 加载车场列表
    async loadParkingList() {
      try {
        // 这里应该调用实际的API
        const response = await dropdownParking(this.requestUrl);

        // 防止 response 或 response.data 不存在导致解构失败
        if (!response || !response.data) {
          throw new Error('响应数据为空');
        }

        const { code, data } = response.data;
        if (code == 200) {
          // 转换数据格式为下拉列表需要的格式
          this.dropParkingList = data.map(item => ({
            value_name: item.name || item.parkingName || item.label,
            value_no: item.id || item.parkingId || item.value
          }));
        } else {
          this.dropParkingList = [];
        }
      } catch (error) {
        console.error('加载停车场列表失败:', error);
        this.dropParkingList = [];

        // 只在用户主动操作时显示错误提示
        if (error.response && error.response.status !== 401) {
          this.$message.warning('停车场列表加载失败，请刷新页面重试');
        }
      }
    },

    handleSearch(form) {
      this.searchForm = form;
      this.current = 1;
      this.$refs.tableRef.loadData();
    },

    handleSizeChange(size) {
      this.size = size;
      this.$refs.tableRef.loadData();
    },

    handlePageChange(page) {
      console.log("handlePageChange", page);
      this.current = page;
      this.$refs.tableRef.loadData();
    },

    resetForm(){
      this.searchForm = {
        parkStatus:'',
        orderStatus:'',
        settleStatus:'',
        plateNo:'',
        userPhone:'',
        airportId:'',
        parkingLotId:'',
      };
      this.current = 1;
      this.$refs.tableRef.loadData();
    },
    // 人工结算
    handleEdit(row) {
      this.currentOrderData = row;
      this.settlementDialogVisible = true;
    },

    async handleSettlementConfirm(settlementData) {
      console.log("结算数据:", settlementData);
      // 这里可以调用结算API
      try {
        let response = await settleOrderList(this.requestUrl, settlementData.orderId, {outTime: settlementData.outTime + ':00'});
        console.log(response, 'response===');

        // 校验响应结构
        if (!response || !response.data) {
          this.$message.error('结算失败：服务器响应异常');
          return;
        }

        const { code, message, msg } = response.data;

        if (code === 200 || code === '200') {
          this.$message.success('结算成功');
          this.settlementDialogVisible = false;
          this.$refs.tableRef.loadData();
        } else {
          // 显示具体的错误信息
          let errorMessage = '结算失败';
          if (message) {
            errorMessage = message;
          } else if (msg) {
            errorMessage = msg;
          } else if (code) {
            errorMessage = `结算失败 (错误码: ${code})`;
          }

          this.$message.error(errorMessage);
          return;
        }

      } catch (error) {
        console.error('结算接口调用失败:', error);

        // 处理不同类型的错误
        let errorMessage = '操作失败';

        if (error.response) {
          // 服务器返回了错误状态码
          const { status, data } = error.response;

          if (status === 400) {
            // 400错误，显示具体的业务错误信息
            if (data && data.message) {
              errorMessage = data.message;
            } else if (data && data.msg) {
              errorMessage = data.msg;
            } else {
              errorMessage = '请求参数错误';
            }
          } else if (status === 401) {
            errorMessage = '登录已过期，请重新登录';
          } else if (status === 403) {
            errorMessage = '没有权限执行此操作';
          } else if (status === 404) {
            errorMessage = '订单不存在或已被删除';
          } else if (status === 500) {
            errorMessage = '服务器内部错误，请稍后重试';
          } else {
            errorMessage = `请求失败 (状态码: ${status})`;
          }
        } else if (error.request) {
          // 网络错误
          errorMessage = '网络连接失败，请检查网络后重试';
        } else {
          // 其他错误
          errorMessage = error.message || '未知错误';
        }

        this.$message.error(errorMessage);
      }
    },

    handleSettlementCancel() {
      this.settlementDialogVisible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
// .shop-custom-header {
//   @include flex-space-between;
//   padding: 20px;
// }
.foldInfo{
  display: flex;
  margin: 0 30px;
  .folditem{
    width: 300px;
    margin: 4px 0;
  }  
}
</style>