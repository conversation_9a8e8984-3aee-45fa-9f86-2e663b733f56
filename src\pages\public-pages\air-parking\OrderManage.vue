<template>
  <section class="right-wrapper-size shop-table-wrapper" id="scrollBarDom">
    <div class="shop-custom-operation">
      <div class="shop-custom-operation">
        <header class="shop-custom-header">
          <p style="float: left">
            机场停车<span style="margin: 2px">-</span>预约订单管理
          </p>
          <div class="float-right">
          <el-button type="text" size="mini" @click="resetForm" icon="el-icon-refresh" style="font-size: 14px;color: #1E1E1E;">刷新</el-button>
        </div>
        </header>
      </div>

      <super-form
        :form-config="formConfig"
        :value="searchForm"
        @input="handleSearch"
      />
    </div>

    <div class="count-table-wrapper-style">
      <bl-table
        border
        ref="tableRef"
        :api="fetchTableData"
        :request-params="searchForm"
        :showExpand="true"
        :showToolbar="true"
        :showPagination="true"
      >
        <template slot="expand" slot-scope="{ row }">
          <div class="foldInfo">
            <div class="folditem">预约停车时间: {{ row.orderTime }}</div>
            <div class="folditem">入场时间: {{ row.inTime }}</div>
          </div>
          <div class="foldInfo">
            <div class="folditem">预约取车时间: {{ row.id }}</div>
            <div class="folditem">离场时间: {{ row.outTime }}</div>
          </div>
          <div class="foldInfo">
            <div class="folditem">车场联系电话: {{ row.shuttlePhone }}</div>
            <div class="folditem">停车时长: {{ row.passengerCnt }}</div>
          </div>
          <div class="foldInfo">
            <div class="folditem">接驳司机电话: {{ row.userPhone }}</div>
            <div class="folditem">停车费: {{ row.payAmount }}</div>
          </div>
          <div class="foldInfo">
            <div class="folditem">退款金额: {{ row.refundAmount }}</div>
            <div class="folditem">退款时间: {{ row.refundTime }}</div>
          </div>
          <div class="foldInfo">
            <div class="folditem">结算状态: 
              <span v-if="row.settleStatus === 0" type="danger">未结算</span>
              <span v-else-if="row.settleStatus === 1" type="success">已结算</span>
              <span v-else>已分账</span>
            </div>
          </div>
        </template>
        <el-table-column
          prop="parkingLotName"
          label="车场名称"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="airportName"
          label="机场"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="plateNo"
          label="车牌号"
          align="center"
        ></el-table-column>
        <el-table-column prop="userPhone" label="手机号" />
        <el-table-column prop="orderTime" label="下单时间" width="180" />
        <el-table-column prop="passengerCnt" label="时长" />
        <el-table-column prop="payAmount" label="金额" />
        <el-table-column prop="shuttlePhone" label="接驳电话" />
        <el-table-column prop="orderStatus" label="订单状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.orderStatus === 0" type="danger">已预约</el-tag>
            <el-tag v-else-if="scope.row.orderStatus === 1" type="success">已取消</el-tag>
            <el-tag v-else-if="scope.row.orderStatus === 2" type="success">已过期</el-tag>
            <el-tag v-else>已完成</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="shuttlePhone" label="在场状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.parkStatus === 0" type="danger">未入场</el-tag>
            <el-tag v-else-if="scope.row.parkStatus === 1" type="success">已入场</el-tag>
            <el-tag v-else type="success">已离场</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="operate"
          label="操作"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button v-if="scope.row.settleStatus === 0" type="text" @click="handleEdit(scope.row)"
              >人工结算</el-button>
          </template>
        </el-table-column>
      </bl-table>
    </div>

    <!-- 订单结算弹窗 -->
    <order-settlement-dialog
      :visible.sync="settlementDialogVisible"
      :order-data="currentOrderData"
      @confirm="handleSettlementConfirm"
      @cancel="handleSettlementCancel"
    />
  </section>
</template>

<script>
import { reserveOrderList, settleOrderList  } from "@/api/airParking";
import SuperForm from "@/components/super-form/inline-form";
import BlTable from "@/components/BlTable/index.vue";
import OrderSettlementDialog from "./components/OrderSettlementDialog.vue";

export default {
  name: "OrderManage",
  components: { SuperForm, BlTable, OrderSettlementDialog },
  data() {
    return {
      searchForm: {
        parkStatus:'',
        orderStatus:'',
        settleStatus:'',
        plateNo:'',
        userPhone:'',
        airportId:'',
        parkingLotId:'',
      },
      settlementDialogVisible: false,
      currentOrderData: {},
      current: 1, // 当前页码
      size: 10, // 每页显示多少条数据
      requestUrl: this.$PUBLIC_URL.AIRPORT_API, // 请求地址
      formConfig: {
        first: [
          {
              label:'在场状态',
              type:'select',
              prop:'parkStatus',
              options:[{
                value_name:'未入场',
                value_no:'0'
              },
              {
                value_name:'已入场',
                value_no:'1'
              },
              {
                value_name:'已离场',
                value_no:'2'
              }]
          },
          {
              label:'订单状态',
              type:'select',
              prop:'orderStatus',
              options:[{
                value_name:'已预约',
                value_no:'0'
              },
              {
                value_name:'已取消',
                value_no:'1'
              },
              {
                value_name:'已过期',
                value_no:'2'
              },
              {
                value_name:'已完成',
                value_no:'3'
              }]
          },
          {
              label:'结算状态',
              type:'select',
              prop:'settleStatus',
              options:[{
                value_name:'未结算',
                value_no:'0'
              },
              {
                value_name:'已结算',
                valvalue_noue:'1'
              },
              {
                value_name:'已分账',
                value_no:'2'
              }]
          },
          {
            label: "手机号",
            type: "input",
            prop: "userPhone",
          },
          {
            label: "车牌号",
            type: "input",
            prop: "plateNo",
          },
          {
            label: "机场",
            type: "select",
            prop: "airportId",
            options:[]
          },
          {
            label: "车场名称",
            type: "select",
            prop: "parkingLotId",
            options:[]
          },
        ],
      }
    };
  },

  mounted() {
    this.$refs.tableRef.loadData();
  },

  methods: {
    async fetchTableData() {
      const params = { ...this.searchForm, size: this.size, current: this.current };
      console.log("请求参数:", params);
      try {
        const response = await reserveOrderList(this.requestUrl, params);
        const dataList = response? response.data.data : [];
        console.log("请求响应:", response);
        return {
          list: dataList.records || [],
          total: dataList.total || 0,
        };
      } catch (error) {
        this.$message.error("数据加载失败");
        return { list: [], total: 0 };
      }
    },

    handleSearch(form) {
      this.searchForm = form;
      this.current = 1;
      this.$refs.tableRef.loadData();
    },

    resetForm(){
      this.searchForm = {};
      this.current = 1;
      this.$refs.tableRef.loadData();
    },
    // 人工结算
    handleEdit(row) {
      this.currentOrderData = row;
      this.settlementDialogVisible = true;
    },

    async handleSettlementConfirm(settlementData) {
      console.log("结算数据:", settlementData);
      // 这里可以调用结算API
      try {
        let response = await settleOrderList(this.requestUrl, settlementData.orderId, {outTime: settlementData.outTime + ':00'});
        console.log(response, 'response===');
        // 校验响应结构
        if (!response || !response.data) {
          this.$message.error('结算失败');
          return;
        }
    
        const { code } = response.data;
    
        if (code === 200 || code === '200') {
          this.$message.success('结算成功');
        } else {
          this.$message.error('结算失败');
          return;
        }
    
        this.settlementDialogVisible = false;
        this.$refs.tableRef.loadData();
      } catch (error) {
        console.error(error);
        this.$message.error('操作失败');
      }
    },

    handleSettlementCancel() {
      this.settlementDialogVisible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
// .shop-custom-header {
//   @include flex-space-between;
//   padding: 20px;
// }
.foldInfo{
  display: flex;
  margin: 0 30px;
  .folditem{
    width: 300px;
    margin: 4px 0;
  }  
}
</style>