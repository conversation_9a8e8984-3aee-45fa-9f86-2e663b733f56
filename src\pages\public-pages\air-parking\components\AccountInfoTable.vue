<template>
  <div class="account-info-table">
    <div class="table-header">
      <h4 class="header-title">
        <i class="el-icon-pie-chart"></i>
        分账信息
      </h4>
      <el-button type="primary" size="medium" @click="showAddDialog" icon="el-icon-plus">
        添加
      </el-button>
    </div>

    <!-- 分账规则设置 -->
    <div class="global-settings">
      <div class="settings-header">
        <h5 class="settings-title">
          <i class="el-icon-setting"></i>
          分账规则
        </h5>
      </div>
      <el-form :model="ruleSettings" label-width="200px" size="medium" class="aligned-form">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="结算周期">
              <el-select v-model="ruleSettings.settlementPeriod" placeholder="请选择结算周期" style="width: 100%">
                <el-option label="日结" :value="1" />
                <el-option label="周结" :value="7" />
                <el-option label="月结" :value="30" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="提现延迟天数">
              <el-input v-model="ruleSettings.withdrawDelayDays" placeholder="请输入延迟天数">
                <template slot="append">天</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 分账主体表格 -->
    <div class="account-table">
      <el-table :data="accountList" border style="width: 100%">
        <el-table-column prop="subjectId" label="编号" width="100" align="center">
        </el-table-column>

        <el-table-column prop="subjectName" label="名称" width="200" align="center">
        </el-table-column>

        <el-table-column prop="shareRatio" label="比例" width="120" align="center">
          <template slot-scope="scope">
            {{ scope.row.shareRatio }}%
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="form-actions">
      <el-button type="primary" size="small" @click="saveRule">保存</el-button>
    </div>

    <!-- 添加分账主体弹窗 -->
    <el-dialog
      title="添加"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="dialogForm" label-width="120px" size="medium">
        <el-form-item label="提现时间">
          <el-select v-model="dialogForm.withdrawTime" placeholder="请选择" style="width: 100%">
            <el-option label="周一" value="monday" />
            <el-option label="周二" value="tuesday" />
            <el-option label="周三" value="wednesday" />
            <el-option label="周四" value="thursday" />
            <el-option label="周五" value="friday" />
            <el-option label="周六" value="saturday" />
            <el-option label="周日" value="sunday" />
          </el-select>
        </el-form-item>

        <el-form-item label="车辆离场后分账等待时长">
          <el-input v-model="dialogForm.waitDays" placeholder="请输入">
            <template slot="append">天</template>
          </el-input>
        </el-form-item>

        <el-form-item label="分账设置">
          <div class="subject-list">
            <div v-for="(subject, index) in dialogForm.subjects" :key="index" class="subject-item">
              <el-form-item label="分账主体" style="margin-bottom: 10px;">
                <el-row :gutter="10">
                  <el-col :span="8">
                    <el-input v-model="subject.name" placeholder="名称" />
                  </el-col>
                  <el-col :span="8">
                    <el-input v-model="subject.company" placeholder="公司名称" />
                  </el-col>
                  <el-col :span="6">
                    <el-input v-model="subject.ratio" placeholder="比例">
                      <template slot="append">%</template>
                    </el-input>
                  </el-col>
                  <el-col :span="2">
                    <el-button type="text" icon="el-icon-plus" @click="addSubject" v-if="index === 0"></el-button>
                    <el-button type="text" icon="el-icon-minus" @click="removeSubject(index)" v-else></el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialog">取消</el-button>
        <el-button type="primary" @click="confirmDialog">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  shuttleRuleDetails,
  shuttleRuleUpdate,
  shuttleRuleAdd
} from "@/api/airParking";

export default {
  name: 'AccountInfoTable',
  props: {
    parkingLotId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      // 分账规则设置
      ruleSettings: {
        parkingLotId: null,
        settlementPeriod: 1, // 结算周期：1-日结，7-周结，30-月结
        withdrawDelayDays: 7 // 提现延迟天数
      },
      // 分账主体列表 - 使用假数据
      accountList: [
        {
          ruleId: 1,
          subjectType: 0,
          subjectId: 202489,
          subjectName: "广东",
          shareRatio: 20,
          isOperator: 1
        },
        {
          ruleId: 2,
          subjectType: 1,
          subjectId: 202489,
          subjectName: "深圳市场地下车场服务有限公司",
          shareRatio: 80,
          isOperator: 0
        }
      ],
      // 弹窗相关
      dialogVisible: false,
      dialogForm: {
        withdrawTime: 'friday',
        waitDays: '7',
        subjects: [
          {
            name: '广东',
            company: '北京软件公司',
            ratio: '80'
          },
          {
            name: '车场',
            company: '车场A',
            ratio: '20'
          }
        ]
      }
    }
  },
  computed: {
    totalRatio() {
      return this.accountList.reduce((total, account) => {
        return total + (parseFloat(account.shareRatio) || 0);
      }, 0);
    }
  },
  watch: {
    parkingLotId: {
      handler(newVal) {
        if (newVal) {
          this.ruleSettings.parkingLotId = newVal;
          this.loadRuleData();
        }
      },
      immediate: true
    }
  },
  mounted() {
    if (this.parkingLotId) {
      this.ruleSettings.parkingLotId = this.parkingLotId;
      this.loadRuleData();
    }
  },
  methods: {
    // 加载分账规则数据
    async loadRuleData() {
      try {
        console.log('加载分账规则数据...');
        // 这里可以调用API获取真实数据
        // const response = await shuttleRuleDetails(this.parkingLotId);
        // if (response && response.data) {
        //   this.ruleSettings = { ...response.data };
        //   this.accountList = response.data.subjects || [];
        // }
      } catch (error) {
        console.error('加载分账规则失败:', error);
        this.$message.error('加载分账规则失败');
      }
    },

    // 显示添加弹窗
    showAddDialog() {
      this.dialogVisible = true;
    },

    // 取消弹窗
    cancelDialog() {
      this.dialogVisible = false;
      this.resetDialogForm();
    },

    // 确认弹窗
    confirmDialog() {
      // 验证表单
      if (!this.dialogForm.withdrawTime || !this.dialogForm.waitDays) {
        this.$message.warning('请填写完整信息');
        return;
      }

      // 验证分账主体
      const totalRatio = this.dialogForm.subjects.reduce((sum, subject) => {
        return sum + (parseFloat(subject.ratio) || 0);
      }, 0);

      if (totalRatio !== 100) {
        this.$message.warning('分账比例总和必须为100%');
        return;
      }

      this.dialogVisible = false;
      this.$message.success('保存成功，不支持主体变更');
      this.resetDialogForm();
    },

    // 重置弹窗表单
    resetDialogForm() {
      this.dialogForm = {
        withdrawTime: 'friday',
        waitDays: '7',
        subjects: [
          {
            name: '广东',
            company: '北京软件公司',
            ratio: '80'
          },
          {
            name: '车场',
            company: '车场A',
            ratio: '20'
          }
        ]
      };
    },

    // 添加分账主体
    addSubject() {
      this.dialogForm.subjects.push({
        name: '',
        company: '',
        ratio: ''
      });
    },

    // 删除分账主体
    removeSubject(index) {
      if (this.dialogForm.subjects.length > 1) {
        this.dialogForm.subjects.splice(index, 1);
      }
    },

    // 保存分账规则
    async saveRule() {
      try {
        const ruleData = {
          parkingLotId: this.ruleSettings.parkingLotId,
          settlementPeriod: this.ruleSettings.settlementPeriod,
          withdrawDelayDays: parseInt(this.ruleSettings.withdrawDelayDays)
        };

        console.log('保存分账规则:', ruleData);

        // 这里可以调用API保存数据
        // await shuttleRuleAdd(ruleData);
        // 或者
        // await shuttleRuleUpdate(this.ruleSettings.ruleId, ruleData);

        this.$message.success('分账规则保存成功');
      } catch (error) {
        console.error('保存分账规则失败:', error);
        this.$message.error('保存分账规则失败');
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.account-info-table {
  // 表格头部
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 0;

    .header-title {
      margin: 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;

      i {
        margin-right: 8px;
        color: #409EFF;
        font-size: 18px;
      }
    }

    .el-button {
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 14px;

      &.el-button--primary {
        background: #409EFF;
        border-color: #409EFF;

        &:hover {
          background: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }

  // 分账规则设置区域
  .global-settings {
    background: #fff;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin-bottom: 20px;

    .settings-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;

      .settings-title {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        display: flex;
        align-items: center;

        i {
          margin-right: 6px;
          color: #409EFF;
          font-size: 16px;
        }
      }
    }

    .aligned-form {
      ::v-deep .el-form-item {
        margin-bottom: 16px;

        .el-form-item__label {
          font-weight: 500;
          color: #303133;
          line-height: 1.6;
          text-align: right;
          padding-right: 12px;
        }

        .el-select,
        .el-input {
          .el-input__inner {
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            height: 36px;
            line-height: 36px;

            &:focus {
              border-color: #409EFF;
            }

            &:hover {
              border-color: #c0c4cc;
            }
          }
        }

        .el-input-group__append {
          background: #f5f7fa;
          border-color: #dcdfe6;
          color: #909399;
          padding: 0 12px;
          border-radius: 0 4px 4px 0;
        }
      }
    }
  }

  // 分账表格
  .account-table {
    margin-bottom: 20px;

    ::v-deep .el-table {
      border: 1px solid #ebeef5;
      border-radius: 4px;

      .el-table__header-wrapper {
        .el-table__header {
          th {
            background: #fafafa;
            color: #303133;
            font-weight: 500;
            border-bottom: 1px solid #ebeef5;
            padding: 12px 0;
            text-align: center;

            .cell {
              font-size: 14px;
            }
          }
        }
      }

      .el-table__body-wrapper {
        .el-table__body {
          tr {
            &:hover {
              background: #f5f7fa;
            }

            td {
              padding: 12px 0;
              border-bottom: 1px solid #ebeef5;
              text-align: center;

              .cell {
                font-size: 14px;
                line-height: 1.6;
              }
            }
          }
        }
      }
    }
  }

  // 表单操作区域
  .form-actions {
    text-align: center;
    padding: 20px 0;

    .el-button {
      padding: 8px 20px;
      border-radius: 4px;
      font-size: 14px;

      &.el-button--primary {
        background: #409EFF;
        border-color: #409EFF;

        &:hover {
          background: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }

  // 弹窗样式
  ::v-deep .el-dialog {
    border-radius: 4px;

    .el-dialog__header {
      padding: 20px 20px 10px;
      border-bottom: 1px solid #e4e7ed;

      .el-dialog__title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .el-dialog__body {
      padding: 20px;

      .subject-list {
        .subject-item {
          margin-bottom: 16px;
          padding: 16px;
          background: #f9f9f9;
          border-radius: 4px;

          .el-form-item {
            margin-bottom: 0;

            .el-form-item__label {
              font-weight: 500;
              color: #303133;
            }
          }

          .el-input {
            .el-input__inner {
              height: 32px;
              line-height: 32px;
              border-radius: 4px;
            }
          }

          .el-button {
            padding: 6px 8px;
            font-size: 12px;
            border-radius: 4px;
          }
        }
      }
    }

    .el-dialog__footer {
      padding: 10px 20px 20px;
      text-align: right;

      .el-button {
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .table-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .el-button {
        align-self: stretch;
      }
    }

    .global-settings {
      padding: 20px 16px;

      ::v-deep .el-form {
        .el-row .el-col {
          margin-bottom: 16px;
        }
      }
    }

    .account-table {
      ::v-deep .el-table {
        font-size: 13px;

        .el-table__header-wrapper th,
        .el-table__body-wrapper td {
          padding: 12px 8px;
        }
      }
    }

    .form-actions {
      padding: 20px 16px;

      .el-button {
        margin: 8px 4px;
        padding: 10px 20px;
        font-size: 13px;
      }
    }
  }
}

// 全局样式覆盖
::v-deep .el-message-box {
  border-radius: 8px;

  .el-message-box__header {
    padding: 20px 24px 16px;

    .el-message-box__title {
      font-weight: 600;
      color: #303133;
    }
  }

  .el-message-box__content {
    padding: 0 24px 20px;

    .el-message-box__message {
      color: #606266;
      line-height: 1.6;
    }
  }

  .el-message-box__btns {
    padding: 16px 24px 20px;

    .el-button {
      border-radius: 6px;
      font-weight: 500;

      &.el-button--primary {
        background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
        border: none;
      }
    }
  }
}
</style>