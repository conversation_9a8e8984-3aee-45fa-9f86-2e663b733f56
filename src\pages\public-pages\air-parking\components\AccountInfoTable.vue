<template>
  <div class="account-info-table">
    <div class="table-header">
      <h4 class="header-title">
        <i class="el-icon-pie-chart"></i>
        分账信息
      </h4>
      <el-button type="primary" size="medium" @click="showAddDialog" icon="el-icon-plus">
        设置
      </el-button>
    </div>

    <!-- 分账规则设置 -->
    <div class="global-settings">
      <div class="settings-header">
        <h5 class="settings-title">
          <i class="el-icon-setting"></i>
          分账规则
        </h5>
      </div>
      <el-form :model="ruleSettings" label-width="200px" size="medium" class="aligned-form">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="提现时间">
              <el-select v-model="ruleSettings.settlementPeriod" placeholder="请选择结算周期" style="width: 100%">
                <el-option label="周一" value="1" />
                <el-option label="周二" value="2" />
                <el-option label="周三" value="3" />
                <el-option label="周四" value="4" />
                <el-option label="周五" value="5" />
                <el-option label="周六" value="6" />
                <el-option label="周日" value="7" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="提现延迟天数">
              <el-input v-model="ruleSettings.withdrawDelayDays" placeholder="请输入延迟天数">
                <template slot="append">天</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 分账主体表格 -->
    <div class="account-table">
      <el-table :data="accountList" border style="width: 100%">
        <el-table-column prop="subjectId" label="编号" width="100" align="center">
        </el-table-column>
        <el-table-column prop="subjectType" label="类型" width="200" align="center">
          <template slot-scope="scope">
            {{ scope.row.subjectType == 0 ? '厂商' : scope.row.subjectType == 1 ? '服务商' : '车场' }}
          </template>
        </el-table-column>
        <el-table-column prop="isOperator" label="是否运营主体" width="200" align="center">
          <template slot-scope="scope">
            {{ scope.row.isOperator == 1 ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column prop="subjectName" label="名称" align="center">
        </el-table-column>
        <el-table-column prop="shareRatio" label="比例" align="center">
          <template slot-scope="scope">
            {{ scope.row.shareRatio }}%
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 添加分账主体弹窗 -->
    <el-dialog
      title="设置"
      :visible.sync="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
      class="account-dialog"
    >
      <div class="dialog-content">
        <el-form :model="ruleSettings" label-width="140px" size="medium" class="dialog-form">
          <div class="form-section">
            <h4 class="section-title">
              <i class="el-icon-time"></i>
              提现设置
            </h4>
            <el-form-item label="提现时间">
              <el-select v-model="ruleSettings.settlementPeriod" placeholder="请选择提现时间" style="width: 100%">
                <el-option label="周一" value="1" />
                <el-option label="周二" value="2" />
                <el-option label="周三" value="3" />
                <el-option label="周四" value="4" />
                <el-option label="周五" value="5" />
                <el-option label="周六" value="6" />
                <el-option label="周日" value="7" />
              </el-select>
            </el-form-item>
            <el-form-item label="车辆离场后分账等待时长">
              <el-input v-model="ruleSettings.withdrawDelayDays" placeholder="请输入等待天数" style="width: 200px;">
                <template slot="append">天</template>
              </el-input>
            </el-form-item>
          </div>
          <div class="dialog-footer">
            <el-button type="primary" @click="confirmDialog" size="medium">确认</el-button>
          </div>
          <div class="form-section">
            <h4 class="section-title">
              <i class="el-icon-pie-chart"></i>
              分账设置
            </h4>
            <div class="subject-list">
              <div v-for="(subject, index) in subjects" :key="index" class="subject-item">
                <div class="subject-header">
                  <span class="subject-label">分账主体 {{ index + 1 }}</span>
                  <div class="subject-actions">
                    <el-button
                      v-if="index === 0"
                      type="text"
                      icon="el-icon-plus"
                      @click="addSubject"
                      class="add-btn"
                    >
                      添加
                    </el-button>
                    <el-button
                      v-else
                      type="text"
                      icon="el-icon-delete"
                      @click="removeSubject(index)"
                      class="remove-btn"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
                <!-- 第一行：主体类型、主体ID、主体名称 -->
                <el-row :gutter="16" class="subject-inputs">
                  <el-col :span="8">
                    <div class="form-item">
                      <label class="form-label">主体类型</label>
                      <el-select v-model="subject.subjectType" placeholder="请选择主体类型" style="width: 100%">
                        <el-option label="厂商" value="0" />
                        <el-option label="服务商" value="1" />
                        <el-option label="车场" value="2" />
                      </el-select>
                    </div>
                  </el-col>

                  <el-col :span="8">
                    <div class="form-item">
                      <label class="form-label">主体ID</label>
                      <el-input
                        v-model="subject.subjectId"
                        placeholder="请输入主体ID"
                        @blur="subjectSeach(subject.subjectId, index)"
                      />
                    </div>
                  </el-col>

                  <el-col :span="8">
                    <div class="form-item">
                      <label class="form-label">主体名称</label>
                      <el-input
                        v-model="subject.subjectName"
                        placeholder="主体名称"
                        :readonly="!!subject.subjectName"
                        :style="subject.subjectName ? 'background-color: #f5f7fa;' : ''"
                      />
                    </div>
                  </el-col>
                </el-row>

                <!-- 第二行：分账比例、是否运营主体 -->
                <el-row :gutter="16" class="subject-inputs">
                  <el-col :span="8">
                    <div class="form-item">
                      <label class="form-label">分账比例</label>
                      <el-input
                        v-model="subject.shareRatio"
                        placeholder="请输入分账比例"
                      >
                        <template slot="append">%</template>
                      </el-input>
                    </div>
                  </el-col>

                  <el-col :span="8">
                    <div class="form-item">
                      <label class="form-label">是否运营主体</label>
                      <el-select
                        v-model="subject.isOperator"
                        placeholder="请选择"
                        style="width: 100%"
                      >
                        <el-option label="是" :value="1" />
                        <el-option label="否" :value="0" />
                      </el-select>
                    </div>
                  </el-col>

                  <el-col :span="8">
                    <!-- 空列，保持对齐 -->
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialog" size="medium">取消</el-button>
        <el-button type="primary" @click="saveSubject" size="medium">保存</el-button>       
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  shuttleRuleDetails,
  shuttleRuleUpdate,
  shuttleRuleAdd,
  shuttleSubjectDetails,
  shuttleSubjectAdd,
  shuttleSubjectUpdate,
  shuttleSubjectDelete,
  fetchParkSettlement,
  fetchServerSettlement,
  fetchCitySettlement
} from "@/api/airParking";
export default {
  name: 'AccountInfoTable',
  props: {
    parkingLotId: {
      type: [String, Number],
      required: false,
      default: null
    }
  },
  data() {
    return {
      // 分账规则设置
      ruleSettings: {
        ruleId: null, // 规则ID，用于区分新增还是编辑
        parkingLotId: null,
        settlementPeriod: 1, // 结算周期：1-日结，7-周结，30-月结
        withdrawDelayDays: 7 // 提现延迟天数
      },
      accountList:[],
      // 弹窗相关
      dialogVisible: false,
      requestUrl: this.$PUBLIC_URL.AIRPORT_API, // 请求接口基础URL
      subjects: []
    }
  },
  computed: {
    totalRatio() {
      return this.subjects.reduce((total, account) => {
        return total + (parseFloat(account.shareRatio) || 0);
      }, 0);
    }
  },
  watch: {
    parkingLotId: {
      handler(newVal) {
        if (newVal) {
          this.ruleSettings.parkingLotId = newVal;
          this.loadRuleSettings();
        }
      },
      immediate: true
    }
  },
  mounted() {
    if (this.parkingLotId) {
      this.ruleSettings.parkingLotId = this.parkingLotId;
      this.loadRuleSettings();
    }
  },
  methods: {
    // 加载分账规则设置
    async loadRuleSettings() {
      try {
        if (!this.parkingLotId) {
          return;
        }

        // 调用API获取分账规则详情
        const response = await shuttleRuleDetails(this.requestUrl, this.parkingLotId);

        if (response && response.data) {
          console.log('分账规则详情:', response.data);
          const {data} = response.data;
          // 更新分账规则设置
          this.ruleSettings = {
            parkingLotId: this.parkingLotId,
            settlementPeriod: data.settlementPeriod.toString() || '',
            withdrawDelayDays: data.withdrawDelayDays || '',
            ruleId: data.id // 保存规则ID用于更新
          };
          this.loadSubjectList(data.id);
          console.log('加载分账规则成功:', this.ruleSettings);
        }
      } catch (error) {
        console.error('加载分账规则失败:', error);
        // 如果是404错误，说明还没有创建规则，使用默认值
        if (error.response && error.response.status === 404) {
          console.log('分账规则不存在，使用默认设置');
        } else {
          this.$message.error('加载分账规则失败');
        }
      }
    },

    // 加载分账主体列表
    async loadSubjectList(id) {
      try {
        if (!id) {
          this.$message.error('请填写分账规则');
          return;
        }

        const response = await shuttleSubjectDetails(this.requestUrl, id);
        console.log('分账主体列表:', response.data);
        let {code, data} = response.data;
        if (code == 200 && data.length > 0) {
          // 更新弹窗中的subjects数据
          data.forEach(item => {
            item.subjectType = item.subjectType.toString();
          });
          this.subjects = data;
          this.accountList = data;
        } else {
          this.subjects = [
            {
              subjectType: '',
              subjectId: '',
              subjectName: '',
              shareRatio: '',
              isOperator: ''
            }
          ];
          this.accountList = [];
        }
      } catch (error) {
        console.error('加载分账主体列表失败:', error);
        this.$message.error('加载分账主体列表失败');
        this.subjects = [
          {
            subjectType: '',
            subjectId: '',
            subjectName: '',
            shareRatio: '',
            isOperator: ''
          }
        ];
        this.accountList = [];
      }
    },
    // 根据主体ID查询主体名称
    async subjectSeach(subjectId, subjectIndex) {
      try {
        if (!subjectId) {
          return;
        }

        const subject = this.subjects[subjectIndex];
        if (!subject || subject.subjectType === undefined) {
          this.$message.warning('请先选择主体类型');
          return;
        }

        let apiFunction;
        let typeName;

        console.log('主体ID:', subject.subjectType);

        // 根据主体类型选择对应的API
        if (subject.subjectType == 0) {
          apiFunction = fetchCitySettlement;
          typeName = '厂商';
        }else if (subject.subjectType == 1) {
          apiFunction = fetchServerSettlement;
          typeName = '服务商';
        }else if (subject.subjectType == 2) {
          apiFunction = fetchParkSettlement;
          typeName = '车场';
        }else {
          this.$message.warning('请选择主体类型');
          return;
        }

        // 调用对应的API查询主体名称
        const response = await apiFunction(this.requestUrl, subjectId);

        if (response && response.data) {
          // 更新主体名称
          this.$set(this.subjects[subjectIndex], 'subjectName', response.data.data || '');
          console.log(`查询${typeName}信息成功:`, response.data);
        } else {
          this.$message.warning(`未找到对应的${typeName}信息`);
        }

      } catch (error) {
        console.error('查询主体信息失败:', error);
        this.$message.error('查询主体信息失败');
      }
    },

    // 显示添加弹窗
    showAddDialog() {
      this.dialogVisible = true;
      this.loadRuleSettings(); // 加载分账规则设置
    },

    // 取消弹窗
    cancelDialog() {
      this.dialogVisible = false;
      this.resetDialogForm();
    },

    // 确认弹窗
    async confirmDialog() {
      try {
        // 验证分账规则设置
        if (!this.ruleSettings.withdrawDelayDays || !this.ruleSettings.settlementPeriod) {
          this.$message.warning('请填写完整的提现设置信息');
          return;
        }
        // 保存分账规则
        await this.saveRule();
      } catch (error) {
        console.error('保存设置失败:', error);
        this.$message.error('保存设置失败');
      }
    },

    // 重置弹窗表单
    resetDialogForm() {
      this.subjects= [
        {
          subjectType: '',
          subjectId: '',
          subjectName: '',
          shareRatio: '',
          isOperator: ''
        }
      ];
    },

    // 添加分账主体
    addSubject() {
      // 直接在界面上添加新的分账主体项
      this.subjects.push({
        subjectType: '',
        subjectId: '',
        subjectName: '',
        shareRatio: '',
        isOperator: ''
      });
    },

    // 删除分账主体
    async removeSubject(index) {
      try {
        if (this.subjects.length <= 1) {
          this.$message.warning('至少需要保留一个分账主体');
          return;
        }
        this.subjects.splice(index, 1);
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除分账主体失败:', error);
          this.$message.error('删除失败');
        }
      }
    },

    // 保存分账主体
    async saveSubject() {
      // 验证分账主体
      const totalRatio = this.subjects.reduce((sum, subject) => {
        return sum + (parseFloat(subject.shareRatio) || 0);
      }, 0);

      if (totalRatio !== 100) {
        this.$message.warning('分账比例总和必须为100%');
        return;
      }

      let ruleId = this.ruleSettings.ruleId;
      if(!ruleId){
        this.$message.warning('请填写分账规则');
        return;
      }
      try {
        const subjectList = this.subjects;
        subjectList.forEach((item, index) => { 
          item.ruleId = ruleId;
        });

        // 调用API更新 - 这里需要subjectId，暂时用index代替
        await shuttleSubjectUpdate(this.requestUrl, subjectList);

        this.dialogVisible = false;
        this.$message.success('设置保存成功');
        this.loadRuleSettings();
        this.resetDialogForm();

      } catch (error) {
        console.error('更新分账主体失败:', error);
        // 不显示错误消息，避免频繁提示
      }
    },

    // 保存分账规则
    async saveRule() {
      try {
        const ruleData = {
          parkingLotId: this.ruleSettings.parkingLotId,
          settlementPeriod: parseInt(this.ruleSettings.settlementPeriod),
          withdrawDelayDays: parseInt(this.ruleSettings.withdrawDelayDays)
        };

        console.log('保存分账规则:', ruleData);

        let response;
        if (this.ruleSettings.ruleId) {
          // 如果有ruleId，说明是编辑操作
          response = await shuttleRuleUpdate(this.requestUrl, this.ruleSettings.ruleId, ruleData);
          this.$message.success('分账规则更新成功');
        } else {
          // 如果没有ruleId，说明是新增操作
          response = await shuttleRuleAdd(this.requestUrl, ruleData);
          this.$message.success('分账规则创建成功');
          // 保存返回的ruleId
          if (response && response.data && response.data.ruleId) {
            this.ruleSettings.ruleId = response.data.ruleId;
          }
        }
        console.log('分账规则保存成功:', response);

      } catch (error) {
        console.error('保存分账规则失败:', error);
        this.$message.error('保存分账规则失败');
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.account-info-table {
  // 表格头部
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 0;

    .header-title {
      margin: 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;

      i {
        margin-right: 8px;
        color: #409EFF;
        font-size: 18px;
      }
    }

    .el-button {
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 14px;

      &.el-button--primary {
        background: #409EFF;
        border-color: #409EFF;

        &:hover {
          background: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }

  // 分账规则设置区域
  .global-settings {
    background: #fff;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin-bottom: 20px;

    .settings-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;

      .settings-title {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        display: flex;
        align-items: center;

        i {
          margin-right: 6px;
          color: #409EFF;
          font-size: 16px;
        }
      }
    }

    .aligned-form {
      ::v-deep .el-form-item {
        margin-bottom: 16px;

        .el-form-item__label {
          font-weight: 500;
          color: #303133;
          line-height: 1.6;
          text-align: right;
          padding-right: 12px;
        }

        .el-select,
        .el-input {
          .el-input__inner {
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            height: 36px;
            line-height: 36px;

            &:focus {
              border-color: #409EFF;
            }

            &:hover {
              border-color: #c0c4cc;
            }
          }
        }

        .el-input-group__append {
          background: #f5f7fa;
          border-color: #dcdfe6;
          color: #909399;
          padding: 0 12px;
          border-radius: 0 4px 4px 0;
        }
      }
    }
  }

  // 分账表格
  .account-table {
    margin-bottom: 20px;

    ::v-deep .el-table {
      border: 1px solid #ebeef5;
      border-radius: 4px;

      .el-table__header-wrapper {
        .el-table__header {
          th {
            background: #fafafa;
            color: #303133;
            font-weight: 500;
            border-bottom: 1px solid #ebeef5;
            padding: 12px 0;
            text-align: center;

            .cell {
              font-size: 14px;
            }
          }
        }
      }

      .el-table__body-wrapper {
        .el-table__body {
          tr {
            &:hover {
              background: #f5f7fa;
            }

            td {
              padding: 12px 0;
              border-bottom: 1px solid #ebeef5;
              text-align: center;

              .cell {
                font-size: 14px;
                line-height: 1.6;
              }
            }
          }
        }
      }
    }
  }

  // 表单操作区域
  .form-actions {
    text-align: center;
    padding: 20px 0;

    .el-button {
      padding: 8px 20px;
      border-radius: 4px;
      font-size: 14px;

      &.el-button--primary {
        background: #409EFF;
        border-color: #409EFF;

        &:hover {
          background: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }

  // 弹窗样式优化
  ::v-deep .account-dialog {
    .el-dialog {
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .el-dialog__header {
      padding: 24px 24px 16px;
      border-bottom: 1px solid #ebeef5;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        display: flex;
        align-items: center;

        &::before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 18px;
          background: #409EFF;
          margin-right: 8px;
          border-radius: 2px;
        }
      }
    }

    .el-dialog__body {
      padding: 0;
      max-height: 60vh;
      overflow-y: auto;

      .dialog-content {
        padding: 24px;
      }

      .dialog-form {
        .form-section {
          margin-bottom: 32px;

          &:last-child {
            margin-bottom: 0;
          }

          .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin: 0 0 20px 0;
            padding-bottom: 12px;
            border-bottom: 2px solid #f0f2f5;
            display: flex;
            align-items: center;

            i {
              margin-right: 8px;
              color: #409EFF;
              font-size: 18px;
            }
          }

          .el-form-item {
            margin-bottom: 20px;

            .el-form-item__label {
              font-weight: 500;
              color: #606266;
              line-height: 1.6;
            }

            .el-input,
            .el-select {
              .el-input__inner {
                height: 36px;
                line-height: 36px;
                border-radius: 6px;
                border: 1px solid #dcdfe6;
                transition: all 0.3s ease;

                &:focus {
                  border-color: #409EFF;
                  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
                }

                &::placeholder {
                  color: #c0c4cc;
                }
              }
            }
          }
        }

        .subject-list {
          .subject-item {
            margin-bottom: 28px;
            padding: 28px;
            background: #fafbfc;
            border: 1px solid #ebeef5;
            border-radius: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);

            &:hover {
              border-color: #c6e2ff;
              background: #f0f8ff;
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
            }

            .subject-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 24px;
              padding-bottom: 16px;
              border-bottom: 1px solid #ebeef5;

              .subject-label {
                font-size: 14px;
                font-weight: 600;
                color: #303133;
              }

              .subject-actions {
                .add-btn {
                  color: #67C23A;
                  font-size: 13px;

                  &:hover {
                    background: rgba(103, 194, 58, 0.1);
                  }
                }

                .remove-btn {
                  color: #F56C6C;
                  font-size: 13px;

                  &:hover {
                    background: rgba(245, 108, 108, 0.1);
                  }
                }
              }
            }

            .subject-inputs {
              margin-bottom: 24px;

              &:last-child {
                margin-bottom: 0;
              }

              .form-item {
                margin-bottom: 0;

                .form-label {
                  display: block;
                  font-size: 13px;
                  font-weight: 500;
                  color: #606266;
                  margin-bottom: 8px;
                  line-height: 1.4;
                }
              }

              .el-input, .el-select {
                .el-input__inner {
                  height: 36px;
                  line-height: 36px;
                  font-size: 13px;
                  border-radius: 6px;
                  transition: all 0.3s ease;
                  border: 1px solid #dcdfe6;

                  &:focus {
                    border-color: #409eff;
                    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
                  }

                  &:hover {
                    border-color: #c0c4cc;
                  }
                }
              }

              .el-select {
                .el-input__inner {
                  cursor: pointer;
                }
              }

              .el-input-group__append {
                background-color: #f5f7fa;
                border-color: #dcdfe6;
                color: #909399;
                font-size: 13px;
                padding: 0 12px;
              }
            }
          }
        }
      }
    }

    .el-dialog__footer {
      padding: 16px 24px 24px;
      text-align: right;
      border-top: 1px solid #ebeef5;
      background: #fafbfc;

      .el-button {
        padding: 10px 20px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        margin-left: 12px;

        &:first-child {
          margin-left: 0;
        }

        &.el-button--primary {
          background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
          border: none;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
          }
        }

        &:not(.el-button--primary) {
          border: 1px solid #dcdfe6;
          color: #606266;

          &:hover {
            border-color: #409EFF;
            color: #409EFF;
          }
        }
      }
    }
  }

}
</style>