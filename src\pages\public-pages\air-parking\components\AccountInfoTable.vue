<template>
  <div class="account-info-table">
    <div class="table-header">
      <el-button type="primary" size="small" @click="addAccount">添加分账主体</el-button>
    </div>
    
    <!-- 全局设置 -->
    <div class="global-settings">
      <el-form :model="globalSettings" label-width="150px" size="small">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="提现时间设置">
              <el-select v-model="globalSettings.withdrawalDay" placeholder="请选择提现日">
                <el-option label="周一" value="monday" />
                <el-option label="周二" value="tuesday" />
                <el-option label="周三" value="wednesday" />
                <el-option label="周四" value="thursday" />
                <el-option label="周五" value="friday" />
                <el-option label="周六" value="saturday" />
                <el-option label="周日" value="sunday" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车辆离场后分账等待时长">
              <el-input v-model="globalSettings.waitingHours" placeholder="请输入等待时长">
                <template slot="append">小时</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 分账主体表格 -->
    <div class="account-table">
      <el-table :data="accountList" border style="width: 100%">
        <el-table-column prop="name" label="分账主体名称" width="200">
          <template slot-scope="scope">
            <el-input 
              v-if="scope.row.editing" 
              v-model="scope.row.name" 
              placeholder="请输入分账主体名称"
              size="small"
            />
            <span v-else>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="type" label="主体类型" width="150">
          <template slot-scope="scope">
            <el-select 
              v-if="scope.row.editing" 
              v-model="scope.row.type" 
              placeholder="请选择类型"
              size="small"
            >
              <el-option label="平台方" value="platform" />
              <el-option label="车场方" value="parking" />
              <el-option label="服务商" value="service" />
              <el-option label="其他" value="other" />
            </el-select>
            <span v-else>{{ getTypeLabel(scope.row.type) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="ratio" label="分账比例" width="120">
          <template slot-scope="scope">
            <el-input 
              v-if="scope.row.editing" 
              v-model="scope.row.ratio" 
              placeholder="请输入比例"
              size="small"
            >
              <template slot="append">%</template>
            </el-input>
            <span v-else>{{ scope.row.ratio }}%</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="settlementCycle" label="结算周期" width="120">
          <template slot-scope="scope">
            <el-select 
              v-if="scope.row.editing" 
              v-model="scope.row.settlementCycle" 
              placeholder="请选择周期"
              size="small"
            >
              <el-option label="日结" value="daily" />
              <el-option label="周结" value="weekly" />
              <el-option label="月结" value="monthly" />
            </el-select>
            <span v-else>{{ getCycleLabel(scope.row.settlementCycle) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="accountInfo" label="收款账户信息" min-width="200">
          <template slot-scope="scope">
            <el-input 
              v-if="scope.row.editing" 
              v-model="scope.row.accountInfo" 
              placeholder="请输入收款账户信息"
              size="small"
            />
            <span v-else>{{ scope.row.accountInfo }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-switch 
              v-if="scope.row.editing" 
              v-model="scope.row.status" 
              active-text="启用" 
              inactive-text="禁用"
            />
            <el-tag :type="scope.row.status ? 'success' : 'danger'" size="small">
              {{ scope.row.status ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template slot-scope="scope">
            <div v-if="scope.row.editing">
              <el-button type="primary" size="mini" @click="saveAccount(scope.$index)">保存</el-button>
              <el-button size="mini" @click="cancelEdit(scope.$index)">取消</el-button>
            </div>
            <div v-else>
              <el-button type="text" size="mini" @click="editAccount(scope.$index)">编辑</el-button>
              <el-button type="text" size="mini" @click="deleteAccount(scope.$index)" style="color: #f56c6c;">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分账比例汇总 -->
    <div class="ratio-summary">
      <span>总分账比例：{{ totalRatio }}%</span>
      <span v-if="totalRatio !== 100" style="color: #f56c6c; margin-left: 10px;">（注意：总比例应为100%）</span>
    </div>
    
    <div class="form-actions">
      <el-button type="primary" size="small" @click="saveAllAccounts">保存分账配置</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AccountInfoTable',
  props: {
    value: {
      type: Object,
      default: () => ({
        globalSettings: {
          withdrawalDay: 'friday',
          waitingHours: '24'
        },
        accountList: []
      })
    }
  },
  data() {
    return {
      globalSettings: {
        withdrawalDay: 'friday',
        waitingHours: '24'
      },
      accountList: [],
      originalAccountList: []
    }
  },
  computed: {
    totalRatio() {
      return this.accountList.reduce((total, account) => {
        return total + (parseFloat(account.ratio) || 0);
      }, 0);
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal) {
          this.globalSettings = { ...newVal.globalSettings } || {
            withdrawalDay: 'friday',
            waitingHours: '24'
          };
          this.accountList = newVal.accountList ? JSON.parse(JSON.stringify(newVal.accountList)) : [];
        }
      },
      immediate: true,
      deep: true
    },
    globalSettings: {
      handler() {
        this.emitChange();
      },
      deep: true
    },
    accountList: {
      handler() {
        this.emitChange();
      },
      deep: true
    }
  },
  methods: {
    addAccount() {
      // const newAccount = {
      //   id: Date.now(),
      //   name: '',
      //   type: 'platform',
      //   ratio: '',
      //   settlementCycle: 'weekly',
      //   accountInfo: '',
      //   status: true,
      //   editing: true
      // };
      // this.accountList.push(newAccount);
    },
    
    editAccount(index) {
      // this.originalAccountList[index] = JSON.parse(JSON.stringify(this.accountList[index]));
      // this.$set(this.accountList[index], 'editing', true);
    },
    
    saveAccount(index) {
      const account = this.accountList[index];
      if (!account.name || !account.ratio) {
        this.$message.warning('请填写完整的分账主体信息');
        return;
      }
      this.$set(this.accountList[index], 'editing', false);
      this.$message.success('分账主体保存成功');
    },
    
    cancelEdit(index) {
      if (this.originalAccountList[index]) {
        this.$set(this.accountList, index, this.originalAccountList[index]);
      } else {
        this.accountList.splice(index, 1);
      }
    },
    
    deleteAccount(index) {
      this.$confirm('确定要删除这个分账主体吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // this.accountList.splice(index, 1);
        // this.$message.success('删除成功');
      }).catch(() => {});
    },
    
    saveAllAccounts() {
      if (this.totalRatio !== 100) {
        this.$message.warning('总分账比例必须为100%');
        return;
      }
      
      const hasEditing = this.accountList.some(account => account.editing);
      if (hasEditing) {
        this.$message.warning('请先保存正在编辑的分账主体');
        return;
      }
      
      this.$emit('save', {
        globalSettings: this.globalSettings,
        accountList: this.accountList
      });
      this.$message.success('分账配置保存成功');
    },
    
    getTypeLabel(type) {
      const typeMap = {
        platform: '平台方',
        parking: '车场方',
        service: '服务商',
        other: '其他'
      };
      return typeMap[type] || type;
    },
    
    getCycleLabel(cycle) {
      const cycleMap = {
        daily: '日结',
        weekly: '周结',
        monthly: '月结'
      };
      return cycleMap[cycle] || cycle;
    },
    
    emitChange() {
      this.$emit('input', {
        globalSettings: this.globalSettings,
        accountList: this.accountList
      });
    }
  }
}
</script>

<style lang="scss" scoped>
// 变量定义
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

$bg-color: #f8f9fa;
$card-bg: #ffffff;
$border-color: #e4e7ed;
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;

$border-radius: 8px;
$box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
$transition: all 0.3s ease;

.account-info-table {
  // 表格头部
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: $border-radius;
    border: 1px solid #f0f2f5;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    h4 {
      margin: 0;
      color: $text-primary;
      font-size: 18px;
      font-weight: 600;
      display: flex;
      align-items: center;

      &::before {
        content: '💰';
        margin-right: 8px;
        font-size: 20px;
      }
    }

    .el-button {
      padding: 10px 20px;
      border-radius: 6px;
      font-weight: 500;
      transition: $transition;

      &.el-button--primary {
        background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
        border: none;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
        }
      }
    }
  }

  // 全局设置区域
  .global-settings {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    padding: 24px;
    border-radius: $border-radius;
    margin-bottom: 24px;
    border: 1px solid #f0f2f5;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, $primary-color 0%, lighten($primary-color, 20%) 100%);
      border-radius: $border-radius $border-radius 0 0;
    }

    ::v-deep .el-form {
      .el-form-item {
        margin-bottom: 16px;

        .el-form-item__label {
          font-weight: 500;
          color: $text-primary;
          line-height: 1.6;
        }

        .el-select,
        .el-input {
          .el-input__inner {
            border-radius: 6px;
            border: 1px solid #e4e7ed;
            transition: $transition;

            &:focus {
              border-color: $primary-color;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }

            &:hover {
              border-color: lighten($primary-color, 20%);
            }
          }
        }
      }
    }
  }

  // 分账表格
  .account-table {
    margin-bottom: 24px;

    ::v-deep .el-table {
      border-radius: $border-radius;
      overflow: hidden;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

      .el-table__header-wrapper {
        .el-table__header {
          th {
            background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
            color: $text-primary;
            font-weight: 600;
            border-bottom: 2px solid #e4e7ed;
            padding: 16px 12px;

            .cell {
              font-size: 14px;
            }
          }
        }
      }

      .el-table__body-wrapper {
        .el-table__body {
          tr {
            transition: $transition;

            &:hover {
              background: rgba(64, 158, 255, 0.05);
            }

            td {
              padding: 16px 12px;
              border-bottom: 1px solid #f5f7fa;

              .cell {
                font-size: 14px;
                line-height: 1.6;
              }

              // 输入框样式
              .el-input,
              .el-select {
                .el-input__inner {
                  border-radius: 4px;
                  border: 1px solid #e4e7ed;
                  font-size: 13px;
                  height: 32px;
                  line-height: 32px;

                  &:focus {
                    border-color: $primary-color;
                    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
                  }
                }
              }

              // 开关样式
              .el-switch {
                .el-switch__core {
                  border-radius: 12px;

                  &::after {
                    border-radius: 50%;
                  }
                }

                &.is-checked .el-switch__core {
                  background-color: $success-color;
                }
              }

              // 操作按钮
              .el-button {
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 12px;
                margin: 0 4px;
                transition: $transition;

                &.el-button--text {
                  &.edit-btn {
                    color: $primary-color;

                    &:hover {
                      background: rgba(64, 158, 255, 0.1);
                    }
                  }

                  &.save-btn {
                    color: $success-color;

                    &:hover {
                      background: rgba(103, 194, 58, 0.1);
                    }
                  }

                  &.cancel-btn {
                    color: $info-color;

                    &:hover {
                      background: rgba(144, 147, 153, 0.1);
                    }
                  }

                  &.delete-btn {
                    color: $danger-color;

                    &:hover {
                      background: rgba(245, 108, 108, 0.1);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // 比例汇总
  .ratio-summary {
    text-align: right;
    margin-bottom: 24px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
    border-radius: $border-radius;
    border-left: 4px solid $success-color;
    font-size: 15px;
    font-weight: 500;
    color: $text-primary;

    .total-ratio {
      font-size: 18px;
      font-weight: 600;
      color: $success-color;
      margin-left: 8px;
    }

    &.error {
      background: linear-gradient(135deg, #fef0f0 0%, #fdf2f2 100%);
      border-left-color: $danger-color;

      .total-ratio {
        color: $danger-color;
      }
    }
  }

  // 表单操作区域
  .form-actions {
    text-align: center;
    padding: 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: $border-radius;
    border: 1px solid #f0f2f5;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .el-button {
      padding: 12px 32px;
      border-radius: 6px;
      font-weight: 500;
      margin: 0 8px;
      transition: $transition;

      &.el-button--primary {
        background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
        border: none;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
        }
      }

      &:not(.el-button--primary) {
        border: 1px solid #e4e7ed;
        color: $text-regular;

        &:hover {
          border-color: $primary-color;
          color: $primary-color;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .table-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .el-button {
        align-self: stretch;
      }
    }

    .global-settings {
      padding: 20px 16px;

      ::v-deep .el-form {
        .el-row .el-col {
          margin-bottom: 16px;
        }
      }
    }

    .account-table {
      ::v-deep .el-table {
        font-size: 13px;

        .el-table__header-wrapper th,
        .el-table__body-wrapper td {
          padding: 12px 8px;
        }
      }
    }

    .form-actions {
      padding: 20px 16px;

      .el-button {
        margin: 8px 4px;
        padding: 10px 20px;
        font-size: 13px;
      }
    }
  }
}

// 全局样式覆盖
::v-deep .el-message-box {
  border-radius: $border-radius;

  .el-message-box__header {
    padding: 20px 24px 16px;

    .el-message-box__title {
      font-weight: 600;
      color: $text-primary;
    }
  }

  .el-message-box__content {
    padding: 0 24px 20px;

    .el-message-box__message {
      color: $text-regular;
      line-height: 1.6;
    }
  }

  .el-message-box__btns {
    padding: 16px 24px 20px;

    .el-button {
      border-radius: 6px;
      font-weight: 500;

      &.el-button--primary {
        background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
        border: none;
      }
    }
  }
}
</style>