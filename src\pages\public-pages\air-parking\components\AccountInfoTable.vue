<template>
  <div class="account-info-table">
    <div class="table-header">
      <h4 class="header-title">
        <i class="el-icon-pie-chart"></i>
        分账信息
      </h4>
      <el-button type="primary" size="medium" @click="showAddDialog" icon="el-icon-plus">
        设置
      </el-button>
    </div>

    <!-- 分账规则设置 -->
    <div class="global-settings">
      <div class="settings-header">
        <h5 class="settings-title">
          <i class="el-icon-setting"></i>
          分账规则
        </h5>
      </div>
      <el-form :model="ruleSettings" label-width="200px" size="medium" class="aligned-form">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="结算周期">
              <el-select v-model="ruleSettings.settlementPeriod" placeholder="请选择结算周期" style="width: 100%">
                <el-option label="日结" :value="1" />
                <el-option label="周结" :value="7" />
                <el-option label="月结" :value="30" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="提现延迟天数">
              <el-input v-model="ruleSettings.withdrawDelayDays" placeholder="请输入延迟天数">
                <template slot="append">天</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 分账主体表格 -->
    <div class="account-table">
      <el-table :data="accountList" border style="width: 100%">
        <el-table-column prop="subjectId" label="编号" width="100" align="center">
        </el-table-column>
        <el-table-column prop="subjectType" label="类型" width="200" align="center">
          <template slot-scope="scope">
            {{ scope.row.subjectType == 0 ? '厂商' : scope.row.subjectType == 1 ? '服务商' : '车场' }}
          </template>
        </el-table-column>
        <el-table-column prop="isOperator" label="是否运营主体" width="200" align="center">
          <template slot-scope="scope">
            {{ scope.row.isOperator == 1 ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column prop="subjectName" label="名称" width="200" align="center">
        </el-table-column>
        <el-table-column prop="shareRatio" label="比例" width="120" align="center">
          <template slot-scope="scope">
            {{ scope.row.shareRatio }}%
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="form-actions">
      <el-button type="primary" size="small" @click="saveRule">保存</el-button>
    </div>

    <!-- 添加分账主体弹窗 -->
    <el-dialog
      title="设置"
      :visible.sync="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
      class="account-dialog"
    >
      <div class="dialog-content">
        <el-form :model="dialogForm" label-width="140px" size="medium" class="dialog-form">
          <div class="form-section">
            <h4 class="section-title">
              <i class="el-icon-time"></i>
              提现设置
            </h4>
            <el-form-item label="提现时间">
              <el-select v-model="dialogForm.withdrawTime" placeholder="请选择提现时间" style="width: 100%">
                <el-option label="周一" value="monday" />
                <el-option label="周二" value="tuesday" />
                <el-option label="周三" value="wednesday" />
                <el-option label="周四" value="thursday" />
                <el-option label="周五" value="friday" />
                <el-option label="周六" value="saturday" />
                <el-option label="周日" value="sunday" />
              </el-select>
            </el-form-item>

            <el-form-item label="车辆离场后分账等待时长">
              <el-input v-model="dialogForm.waitDays" placeholder="请输入等待天数" style="width: 200px;">
                <template slot="append">天</template>
              </el-input>
            </el-form-item>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="confirmDialog" size="medium">确认</el-button>
          </div>

          <div class="form-section">
            <h4 class="section-title">
              <i class="el-icon-pie-chart"></i>
              分账设置
            </h4>
            <div class="subject-list">
              <div v-for="(subject, index) in dialogForm.subjects" :key="index" class="subject-item">
                <div class="subject-header">
                  <span class="subject-label">分账主体 {{ index + 1 }}</span>
                  <div class="subject-actions">
                    <el-button
                      v-if="index === 0"
                      type="text"
                      icon="el-icon-plus"
                      @click="addSubject"
                      class="add-btn"
                    >
                      添加
                    </el-button>
                    <el-button
                      v-else
                      type="text"
                      icon="el-icon-delete"
                      @click="removeSubject(index)"
                      class="remove-btn"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
                <el-row :gutter="12" class="subject-inputs">
                  <el-col :span="8">
                    <el-input v-model="subject.name" placeholder="主体名称" />
                  </el-col>
                  <el-col :span="8">
                    <el-input v-model="subject.company" placeholder="公司名称" />
                  </el-col>
                  <el-col :span="8">
                    <el-input v-model="subject.ratio" placeholder="分账比例">
                      <template slot="append">%</template>
                    </el-input>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialog" size="medium">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  shuttleRuleDetails,
  shuttleRuleUpdate,
  shuttleRuleAdd,
  shuttleSubjectDetails,
  shuttleSubjectAdd,
  shuttleSubjectUpdate,
  shuttleSubjectDelete
} from "@/api/airParking";

export default {
  name: 'AccountInfoTable',
  props: {
    parkingLotId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      // 分账规则设置
      ruleSettings: {
        parkingLotId: null,
        settlementPeriod: 1, // 结算周期：1-日结，7-周结，30-月结
        withdrawDelayDays: 7 // 提现延迟天数
      },
      // 分账主体列表 - 使用假数据
      accountList: [
        {
          ruleId: 1,
          subjectType: 0,
          subjectId: 202489,
          subjectName: "广东",
          shareRatio: 20,
          isOperator: 1
        },
        {
          ruleId: 2,
          subjectType: 1,
          subjectId: 202489,
          subjectName: "深圳市场地下车场服务有限公司",
          shareRatio: 80,
          isOperator: 0
        }
      ],
      // 弹窗相关
      dialogVisible: false,
      dialogForm: {
        withdrawTime: 'friday',
        waitDays: '7',
        subjects: [
          {
            name: '广东',
            company: '北京软件公司',
            ratio: '80'
          },
          {
            name: '车场',
            company: '车场A',
            ratio: '20'
          }
        ]
      }
    }
  },
  computed: {
    totalRatio() {
      return this.accountList.reduce((total, account) => {
        return total + (parseFloat(account.shareRatio) || 0);
      }, 0);
    }
  },
  watch: {
    parkingLotId: {
      handler(newVal) {
        if (newVal) {
          this.ruleSettings.parkingLotId = newVal;
          this.loadRuleData();
        }
      },
      immediate: true
    }
  },
  mounted() {
    if (this.parkingLotId) {
      this.ruleSettings.parkingLotId = this.parkingLotId;
      this.loadRuleData();
    }
  },
  methods: {
    // 加载分账规则数据
    async loadRuleData() {
      try {
        console.log('加载分账规则数据...');
        // 这里可以调用API获取真实数据
        // const response = await shuttleRuleDetails(this.parkingLotId);
        // if (response && response.data) {
        //   this.ruleSettings = { ...response.data };
        //   this.accountList = response.data.subjects || [];
        // }
      } catch (error) {
        console.error('加载分账规则失败:', error);
        this.$message.error('加载分账规则失败');
      }
    },

    // 显示添加弹窗
    showAddDialog() {
      this.dialogVisible = true;
    },

    // 取消弹窗
    cancelDialog() {
      this.dialogVisible = false;
      this.resetDialogForm();
    },

    // 确认弹窗
    confirmDialog() {
      // 验证表单
      if (!this.dialogForm.withdrawTime || !this.dialogForm.waitDays) {
        this.$message.warning('请填写完整信息');
        return;
      }

      // 验证分账主体
      const totalRatio = this.dialogForm.subjects.reduce((sum, subject) => {
        return sum + (parseFloat(subject.ratio) || 0);
      }, 0);

      if (totalRatio !== 100) {
        this.$message.warning('分账比例总和必须为100%');
        return;
      }

      this.dialogVisible = false;
      this.$message.success('保存成功，不支持主体变更');
      this.resetDialogForm();
    },

    // 重置弹窗表单
    resetDialogForm() {
      this.dialogForm = {
        withdrawTime: 'friday',
        waitDays: '7',
        subjects: [
          {
            name: '广东',
            company: '北京软件公司',
            ratio: '80'
          },
          {
            name: '车场',
            company: '车场A',
            ratio: '20'
          }
        ]
      };
    },

    // 添加分账主体
    addSubject() {
      this.dialogForm.subjects.push({
        name: '',
        company: '',
        ratio: ''
      });
    },

    // 删除分账主体
    removeSubject(index) {
      if (this.dialogForm.subjects.length > 1) {
        this.dialogForm.subjects.splice(index, 1);
      }
    },

    // 保存分账规则
    async saveRule() {
      try {
        const ruleData = {
          parkingLotId: this.ruleSettings.parkingLotId,
          settlementPeriod: this.ruleSettings.settlementPeriod,
          withdrawDelayDays: parseInt(this.ruleSettings.withdrawDelayDays)
        };

        console.log('保存分账规则:', ruleData);

        // 这里可以调用API保存数据
        // await shuttleRuleAdd(ruleData);
        // 或者
        // await shuttleRuleUpdate(this.ruleSettings.ruleId, ruleData);

        this.$message.success('分账规则保存成功');
      } catch (error) {
        console.error('保存分账规则失败:', error);
        this.$message.error('保存分账规则失败');
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.account-info-table {
  // 表格头部
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 0;

    .header-title {
      margin: 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;

      i {
        margin-right: 8px;
        color: #409EFF;
        font-size: 18px;
      }
    }

    .el-button {
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 14px;

      &.el-button--primary {
        background: #409EFF;
        border-color: #409EFF;

        &:hover {
          background: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }

  // 分账规则设置区域
  .global-settings {
    background: #fff;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin-bottom: 20px;

    .settings-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;

      .settings-title {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        display: flex;
        align-items: center;

        i {
          margin-right: 6px;
          color: #409EFF;
          font-size: 16px;
        }
      }
    }

    .aligned-form {
      ::v-deep .el-form-item {
        margin-bottom: 16px;

        .el-form-item__label {
          font-weight: 500;
          color: #303133;
          line-height: 1.6;
          text-align: right;
          padding-right: 12px;
        }

        .el-select,
        .el-input {
          .el-input__inner {
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            height: 36px;
            line-height: 36px;

            &:focus {
              border-color: #409EFF;
            }

            &:hover {
              border-color: #c0c4cc;
            }
          }
        }

        .el-input-group__append {
          background: #f5f7fa;
          border-color: #dcdfe6;
          color: #909399;
          padding: 0 12px;
          border-radius: 0 4px 4px 0;
        }
      }
    }
  }

  // 分账表格
  .account-table {
    margin-bottom: 20px;

    ::v-deep .el-table {
      border: 1px solid #ebeef5;
      border-radius: 4px;

      .el-table__header-wrapper {
        .el-table__header {
          th {
            background: #fafafa;
            color: #303133;
            font-weight: 500;
            border-bottom: 1px solid #ebeef5;
            padding: 12px 0;
            text-align: center;

            .cell {
              font-size: 14px;
            }
          }
        }
      }

      .el-table__body-wrapper {
        .el-table__body {
          tr {
            &:hover {
              background: #f5f7fa;
            }

            td {
              padding: 12px 0;
              border-bottom: 1px solid #ebeef5;
              text-align: center;

              .cell {
                font-size: 14px;
                line-height: 1.6;
              }
            }
          }
        }
      }
    }
  }

  // 表单操作区域
  .form-actions {
    text-align: center;
    padding: 20px 0;

    .el-button {
      padding: 8px 20px;
      border-radius: 4px;
      font-size: 14px;

      &.el-button--primary {
        background: #409EFF;
        border-color: #409EFF;

        &:hover {
          background: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }

  // 弹窗样式优化
  ::v-deep .account-dialog {
    .el-dialog {
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .el-dialog__header {
      padding: 24px 24px 16px;
      border-bottom: 1px solid #ebeef5;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        display: flex;
        align-items: center;

        &::before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 18px;
          background: #409EFF;
          margin-right: 8px;
          border-radius: 2px;
        }
      }
    }

    .el-dialog__body {
      padding: 0;
      max-height: 60vh;
      overflow-y: auto;

      .dialog-content {
        padding: 24px;
      }

      .dialog-form {
        .form-section {
          margin-bottom: 32px;

          &:last-child {
            margin-bottom: 0;
          }

          .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin: 0 0 20px 0;
            padding-bottom: 12px;
            border-bottom: 2px solid #f0f2f5;
            display: flex;
            align-items: center;

            i {
              margin-right: 8px;
              color: #409EFF;
              font-size: 18px;
            }
          }

          .el-form-item {
            margin-bottom: 20px;

            .el-form-item__label {
              font-weight: 500;
              color: #606266;
              line-height: 1.6;
            }

            .el-input,
            .el-select {
              .el-input__inner {
                height: 36px;
                line-height: 36px;
                border-radius: 6px;
                border: 1px solid #dcdfe6;
                transition: all 0.3s ease;

                &:focus {
                  border-color: #409EFF;
                  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
                }

                &::placeholder {
                  color: #c0c4cc;
                }
              }
            }
          }
        }

        .subject-list {
          .subject-item {
            margin-bottom: 20px;
            padding: 20px;
            background: #fafbfc;
            border: 1px solid #ebeef5;
            border-radius: 8px;
            transition: all 0.3s ease;

            &:hover {
              border-color: #c6e2ff;
              background: #f0f8ff;
            }

            .subject-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 16px;

              .subject-label {
                font-size: 14px;
                font-weight: 600;
                color: #303133;
              }

              .subject-actions {
                .add-btn {
                  color: #67C23A;
                  font-size: 13px;

                  &:hover {
                    background: rgba(103, 194, 58, 0.1);
                  }
                }

                .remove-btn {
                  color: #F56C6C;
                  font-size: 13px;

                  &:hover {
                    background: rgba(245, 108, 108, 0.1);
                  }
                }
              }
            }

            .subject-inputs {
              .el-input {
                .el-input__inner {
                  height: 34px;
                  line-height: 34px;
                  font-size: 13px;
                }
              }
            }
          }
        }
      }
    }

    .el-dialog__footer {
      padding: 16px 24px 24px;
      text-align: right;
      border-top: 1px solid #ebeef5;
      background: #fafbfc;

      .el-button {
        padding: 10px 20px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        margin-left: 12px;

        &:first-child {
          margin-left: 0;
        }

        &.el-button--primary {
          background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
          border: none;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
          }
        }

        &:not(.el-button--primary) {
          border: 1px solid #dcdfe6;
          color: #606266;

          &:hover {
            border-color: #409EFF;
            color: #409EFF;
          }
        }
      }
    }
  }

}
</style>