<template>
  <div class="parking-config-optimized">
    <!-- 页面头部 -->
    <div class="page-header fade-in">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <i class="el-icon-setting"></i>
            机场停车配置管理
          </h1>
          <div class="breadcrumb">
            <span class="breadcrumb-item">机场停车</span>
            <i class="el-icon-arrow-right"></i>
            <span class="breadcrumb-item">车场管理</span>
            <i class="el-icon-arrow-right"></i>
            <span class="breadcrumb-item current">车场配置</span>
          </div>
          <div v-if="currentParkingName" class="parking-name-tag slide-in-up">
            <el-tag type="info" size="medium">
              <i class="el-icon-location-outline"></i>
              {{ currentParkingName }}
            </el-tag>
          </div>
        </div>
        <div class="header-right">
          <el-button type="primary" plain @click="goBack" icon="el-icon-back">
            返回车场管理
          </el-button>
        </div>
      </div>
    </div>

    <!-- 配置内容区域 -->
    <div class="config-container">
      <!-- 进度指示器 -->
      <div class="progress-card slide-in-down">
        <div class="progress-header">
          <h3><i class="el-icon-s-operation"></i> 配置进度</h3>
          <div class="progress-stats">
            <span class="completed">{{ completedSteps }}/4 已完成</span>
          </div>
        </div>
        <el-steps :active="getStepIndex(activeTab)" align-center class="custom-steps">
          <el-step 
            title="车场信息" 
            description="基础信息配置" 
            icon="el-icon-info"
            :status="stepStatus.basic"
          ></el-step>
          <el-step 
            title="停车费率" 
            description="费率标准设置" 
            icon="el-icon-money"
            :status="stepStatus.vehicle"
          ></el-step>
          <el-step 
            title="接送服务" 
            description="接送服务配置" 
            icon="el-icon-truck"
            :status="stepStatus.shuttle"
          ></el-step>
          <el-step 
            title="分账信息" 
            description="分账规则设置" 
            icon="el-icon-pie-chart"
            :status="stepStatus.account"
          ></el-step>
        </el-steps>
      </div>

      <!-- 配置标签页 -->
      <div class="config-tabs-wrapper scale-in">
        <el-tabs 
          v-model="activeTab" 
          type="card" 
          @tab-click="handleTabChange" 
          class="enhanced-tabs"
        >
          <!-- 车场信息标签页 -->
          <el-tab-pane label="车场信息" name="basic">
            <div class="tab-content-wrapper">
              <div class="section-header">
                <div class="section-title">
                  <h3><i class="el-icon-info"></i> 车场基础信息</h3>
                  <el-tag v-if="stepStatus.basic === 'finish'" type="success" size="mini">
                    <i class="el-icon-check"></i> 已完成
                  </el-tag>
                </div>
                <p class="section-desc">配置车场的基本信息，包括位置、联系方式等关键信息</p>
              </div>
              
              <div class="form-container">
                <ParkingBasicInfo
                  :airportList="airportList"
                  :regionOptions="regionOptions"
                  v-model="basicForm" 
                  @save="handleBasicInfoSave"
                  :loading="loading.basic"
                />
              </div>
            </div>
          </el-tab-pane>

          <!-- 停车费率标签页 -->
          <el-tab-pane label="停车费率" name="vehicle">
            <div class="tab-content-wrapper">
              <div class="section-header">
                <div class="section-title">
                  <h3><i class="el-icon-money"></i> 停车费率设置</h3>
                  <el-tag v-if="stepStatus.vehicle === 'finish'" type="success" size="mini">
                    <i class="el-icon-check"></i> 已完成
                  </el-tag>
                </div>
                <p class="section-desc">设置预约停车的费率标准和相关规则，确保价格合理透明</p>
              </div>
              
              <div class="form-container">
                <el-card class="rate-config-card" shadow="never">
                  <div slot="header" class="card-header">
                    <span><i class="el-icon-tickets"></i> 费率配置</span>
                    <div class="header-actions">
                      <el-tooltip content="费率设置说明" placement="top">
                        <el-button type="text" icon="el-icon-question" size="mini"></el-button>
                      </el-tooltip>
                    </div>
                  </div>
                  
                  <el-form 
                    ref="vehicleForm" 
                    :model="vehicleForm" 
                    :rules="vehicleRules" 
                    label-width="140px" 
                    size="medium"
                    class="enhanced-form"
                  >
                    <el-row :gutter="24">
                      <el-col :span="8">
                        <el-form-item label="预约停车费率" prop="dailyRate">
                          <el-input-number 
                            v-model="vehicleForm.dailyRate" 
                            :min="0" 
                            :precision="2"
                            :step="1"
                            placeholder="请输入费率"
                            style="width: 100%"
                          />
                          <span class="unit-label">元/天</span>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="最少停车天数" prop="minDays">
                          <el-input-number 
                            v-model="vehicleForm.minDays" 
                            :min="1" 
                            :max="30"
                            placeholder="请输入天数"
                            style="width: 100%"
                          />
                          <span class="unit-label">天</span>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="超时免费时长" prop="freeOvertime">
                          <el-input-number 
                            v-model="vehicleForm.freeOvertime" 
                            :min="0" 
                            :max="1440"
                            placeholder="请输入时长"
                            style="width: 100%"
                          />
                          <span class="unit-label">分钟</span>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    
                    <!-- 费率预览 -->
                    <div class="rate-preview" v-if="vehicleForm.dailyRate">
                      <h4><i class="el-icon-view"></i> 费率预览</h4>
                      <div class="preview-content">
                        <div class="preview-item">
                          <span class="label">日费率：</span>
                          <span class="value">{{ vehicleForm.dailyRate }}元/天</span>
                        </div>
                        <div class="preview-item">
                          <span class="label">3天费用：</span>
                          <span class="value">{{ (vehicleForm.dailyRate * 3).toFixed(2) }}元</span>
                        </div>
                        <div class="preview-item">
                          <span class="label">7天费用：</span>
                          <span class="value">{{ (vehicleForm.dailyRate * 7).toFixed(2) }}元</span>
                        </div>
                      </div>
                    </div>
                  </el-form>
                  
                  <div class="form-actions">
                    <el-button 
                      type="primary" 
                      :loading="loading.vehicle"
                      :disabled="!isVehicleFormValid"
                      @click="saveVehicleInfo"
                      size="medium"
                      class="save-btn"
                    >
                      <i class="el-icon-check"></i>
                      保存费率配置
                    </el-button>
                    <el-button @click="resetVehicleForm" size="medium">
                      <i class="el-icon-refresh"></i>
                      重置
                    </el-button>
                  </div>
                </el-card>
              </div>
            </div>
          </el-tab-pane>

          <!-- 接送服务标签页 -->
          <el-tab-pane label="接送服务" name="shuttle">
            <div class="tab-content-wrapper">
              <div class="section-header">
                <div class="section-title">
                  <h3><i class="el-icon-truck"></i> 接送服务配置</h3>
                  <el-tag v-if="stepStatus.shuttle === 'finish'" type="success" size="mini">
                    <i class="el-icon-check"></i> 已完成
                  </el-tag>
                </div>
                <p class="section-desc">配置车场的接送服务信息，包括接驳时间、地点等服务详情</p>
              </div>
              
              <div class="form-container">
                <!-- 接送服务表单内容将在这里展示 -->
                <div class="service-placeholder">
                  <i class="el-icon-truck"></i>
                  <p>接送服务配置功能开发中...</p>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 分账信息标签页 -->
          <el-tab-pane label="分账信息" name="account">
            <div class="tab-content-wrapper">
              <div class="section-header">
                <div class="section-title">
                  <h3><i class="el-icon-pie-chart"></i> 分账信息配置</h3>
                  <el-tag v-if="stepStatus.account === 'finish'" type="success" size="mini">
                    <i class="el-icon-check"></i> 已完成
                  </el-tag>
                </div>
                <p class="section-desc">设置分账规则和结算周期，确保收益分配透明合理</p>
              </div>
              
              <div class="form-container">
                <AccountInfoTable 
                  v-model="accountInfo" 
                  @save="handleAccountSave"
                />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
// 这里保持原有的JavaScript逻辑不变
// 只是为了展示优化后的模板结构
export default {
  name: 'ParkingConfigOptimized',
  data() {
    return {
      activeTab: 'basic',
      currentParkingName: '示例车场',
      stepStatus: {
        basic: 'process',
        vehicle: 'wait',
        shuttle: 'wait',
        account: 'wait'
      },
      vehicleForm: {
        dailyRate: 0,
        minDays: 1,
        freeOvertime: 30
      },
      loading: {
        basic: false,
        vehicle: false,
        shuttle: false,
        account: false
      }
    }
  },
  computed: {
    completedSteps() {
      return Object.values(this.stepStatus).filter(status => status === 'finish').length;
    },
    isVehicleFormValid() {
      return this.vehicleForm.dailyRate > 0 && this.vehicleForm.minDays > 0;
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    getStepIndex(tab) {
      const tabMap = { basic: 0, vehicle: 1, shuttle: 2, account: 3 };
      return tabMap[tab] || 0;
    },
    handleTabChange(tab) {
      this.activeTab = tab.name;
    },
    saveVehicleInfo() {
      // 保存逻辑
    },
    resetVehicleForm() {
      this.vehicleForm = {
        dailyRate: 0,
        minDays: 1,
        freeOvertime: 30
      };
    },
    handleBasicInfoSave() {
      // 保存逻辑
    },
    handleAccountSave() {
      // 保存逻辑
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/parking-config-theme.scss';

.parking-config-optimized {
  min-height: 100vh;
  @include gradient-bg(135deg, #f5f7fa, #c3cfe2);

  // 页面头部
  .page-header {
    @include card-style($box-shadow-base, $border-radius-large);
    margin-bottom: $spacing-xxl;

    .header-content {
      @include flex-center(row);
      justify-content: space-between;
      padding: $spacing-xxl $spacing-xxxl;
      max-width: 1400px;
      margin: 0 auto;

      .header-left {
        flex: 1;

        .page-title {
          font-size: 24px;
          font-weight: 600;
          color: $text-primary;
          margin: 0 0 $spacing-sm 0;
          @include flex-center(row);
          justify-content: flex-start;

          i {
            margin-right: $spacing-md;
            color: $primary-color;
            font-size: 28px;
          }
        }

        .breadcrumb {
          @include flex-center(row);
          justify-content: flex-start;
          font-size: 14px;
          color: $text-secondary;
          margin-bottom: $spacing-md;

          .breadcrumb-item {
            &.current {
              color: $primary-color;
              font-weight: 500;
            }
          }

          i {
            margin: 0 $spacing-sm;
            font-size: 12px;
          }
        }

        .parking-name-tag {
          .el-tag {
            font-size: 14px;
            padding: $spacing-sm $spacing-lg;
            border-radius: $border-radius-round;

            i {
              margin-right: $spacing-xs;
            }
          }
        }
      }

      .header-right {
        .el-button {
          @include button-style($primary-color);
        }
      }
    }
  }

  // 配置容器
  .config-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 $spacing-xxxl $spacing-xxxl;

    // 进度卡片
    .progress-card {
      @include card-style($box-shadow-medium, $border-radius-large);
      padding: $spacing-xxxl;
      margin-bottom: $spacing-xxl;

      .progress-header {
        @include flex-center(row);
        justify-content: space-between;
        margin-bottom: $spacing-xl;

        h3 {
          font-size: 18px;
          font-weight: 600;
          color: $text-primary;
          margin: 0;
          @include flex-center(row);
          justify-content: flex-start;

          i {
            margin-right: $spacing-sm;
            color: $primary-color;
          }
        }

        .progress-stats {
          .completed {
            font-size: 14px;
            color: $success-color;
            font-weight: 500;
            padding: $spacing-xs $spacing-md;
            background: rgba($success-color, 0.1);
            border-radius: $border-radius-round;
          }
        }
      }

      .custom-steps {
        ::v-deep .el-step__title {
          font-size: 16px;
          font-weight: 500;
        }

        ::v-deep .el-step__description {
          font-size: 14px;
          color: $text-secondary;
        }

        ::v-deep .el-step__icon {
          width: 40px;
          height: 40px;

          &.is-text {
            border-width: 2px;
          }
        }
      }
    }

    // 标签页包装器
    .config-tabs-wrapper {
      @include card-style($box-shadow-medium, $border-radius-large);
      overflow: hidden;

      .enhanced-tabs {
        ::v-deep .el-tabs__header {
          margin: 0;
          @include gradient-bg(90deg, #f8f9fa, #e9ecef);

          .el-tabs__nav-wrap {
            padding: 0 $spacing-xxxl;
          }

          .el-tabs__item {
            padding: $spacing-xl $spacing-xxxl;
            font-size: 16px;
            font-weight: 500;
            color: $text-regular;
            border: none;
            transition: $transition-base;

            &:hover {
              color: $primary-color;
            }

            &.is-active {
              color: $primary-color;
              background: $bg-primary;
              border-radius: $border-radius-large $border-radius-large 0 0;
              position: relative;

              &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(90deg, $primary-color, $primary-light);
              }
            }
          }
        }

        ::v-deep .el-tabs__content {
          padding: 0;
        }
      }
    }
  }
}

// 标签页内容
.tab-content-wrapper {
  padding: $spacing-xxxl;

  .section-header {
    margin-bottom: $spacing-xxxl;
    padding-bottom: $spacing-xl;
    border-bottom: 2px solid $border-color-lighter;

    .section-title {
      @include flex-center(row);
      justify-content: space-between;
      margin-bottom: $spacing-sm;

      h3 {
        font-size: 20px;
        font-weight: 600;
        color: $text-primary;
        margin: 0;
        @include flex-center(row);
        justify-content: flex-start;

        i {
          margin-right: $spacing-md;
          color: $primary-color;
          font-size: 22px;
        }
      }
    }

    .section-desc {
      font-size: 14px;
      color: $text-secondary;
      margin: 0;
      line-height: 1.6;
    }
  }

  .form-container {
    .rate-config-card {
      @include card-style($box-shadow-light, $border-radius-large);

      .card-header {
        @include flex-center(row);
        justify-content: space-between;
        font-size: 16px;
        font-weight: 600;
        color: $text-primary;

        i {
          margin-right: $spacing-sm;
          color: $primary-color;
        }

        .header-actions {
          .el-button {
            color: $text-secondary;

            &:hover {
              color: $primary-color;
            }
          }
        }
      }

      ::v-deep .el-card__header {
        @include gradient-bg(90deg, #f8f9fa, #ffffff);
        border-bottom: 1px solid $border-color-lighter;
        padding: $spacing-xl $spacing-xxl;
      }

      ::v-deep .el-card__body {
        padding: $spacing-xxl;
      }
    }

    .enhanced-form {
      @include form-item-style();

      .unit-label {
        margin-left: $spacing-md;
        color: $text-secondary;
        font-size: 14px;
        font-weight: 500;
        padding: $spacing-xs $spacing-sm;
        background: $bg-tertiary;
        border-radius: $border-radius-small;
      }

      .rate-preview {
        margin-top: $spacing-xxl;
        padding: $spacing-xl;
        @include gradient-bg(135deg, #e8f5e8, #f0f9f0);
        border-radius: $border-radius-base;
        border-left: 4px solid $success-color;

        h4 {
          margin: 0 0 $spacing-lg 0;
          color: $text-primary;
          font-size: 16px;
          font-weight: 600;
          @include flex-center(row);
          justify-content: flex-start;

          i {
            margin-right: $spacing-sm;
            color: $success-color;
          }
        }

        .preview-content {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: $spacing-lg;

          .preview-item {
            @include flex-center(row);
            justify-content: space-between;
            padding: $spacing-sm $spacing-md;
            background: rgba(white, 0.7);
            border-radius: $border-radius-small;

            .label {
              color: $text-regular;
              font-size: 14px;
            }

            .value {
              color: $success-color;
              font-weight: 600;
              font-size: 16px;
            }
          }
        }
      }
    }

    .service-placeholder {
      @include flex-center(column);
      padding: $spacing-xxxl * 2;
      color: $text-secondary;

      i {
        font-size: 48px;
        margin-bottom: $spacing-lg;
        color: $info-color;
      }

      p {
        font-size: 16px;
        margin: 0;
      }
    }
  }
}

// 表单操作区域
.form-actions {
  @include flex-center(row);
  margin-top: $spacing-xxxl;
  padding: $spacing-xxl;
  @include gradient-bg(135deg, #f8f9fa, #ffffff);
  border-radius: $border-radius-base;
  border: 1px solid $border-color-lighter;

  .el-button {
    @include button-style($primary-color);
    margin: 0 $spacing-sm;

    &.save-btn {
      background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
      border: none;
      color: white;
      box-shadow: $box-shadow-primary;

      &:hover {
        box-shadow: $box-shadow-primary-hover;
      }

      &:disabled {
        background: $info-color;
        transform: none;
        box-shadow: none;
      }
    }
  }
}

// 响应式设计
@include respond-to(mobile) {
  .parking-config-optimized {
    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-lg;
      padding: $spacing-xl $spacing-xxl;

      .header-right {
        align-self: stretch;

        .el-button {
          width: 100%;
        }
      }
    }

    .config-container {
      padding: 0 $spacing-lg $spacing-lg;

      .progress-card {
        padding: $spacing-xl;

        .custom-steps {
          ::v-deep .el-step__title {
            font-size: 14px;
          }
        }
      }
    }
  }

  .tab-content-wrapper {
    padding: $spacing-xl;

    .section-header {
      .section-title h3 {
        font-size: 18px;
      }
    }

    .form-container {
      .enhanced-form {
        .rate-preview {
          .preview-content {
            grid-template-columns: 1fr;
          }
        }
      }
    }
  }
}
</style>
