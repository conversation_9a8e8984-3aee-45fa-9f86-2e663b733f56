<template>
  <div class="bl-line-chart">
    <div ref="blLine<PERSON>hart" style="height:100%"></div>
  </div>
</template>

<script>
import echarts from 'echarts';

export default {
  name: "BlLine<PERSON><PERSON>",
  props: {
    propsData: [Object,Array],
    title: String,
  },
  data() {
    return {
      blChart: null,
    }
  },
  computed: {

  },
  mounted() {

  },
  activated() {
    const self = this;
    if(this.blChart){
      setTimeout(()=>{
        self.blChart.resize();
        window.addEventListener('resize', () => {
          self.blChart.resize();
        });
      },60)
    }
  },
  methods: {
    eventListener() {
      const self = this;
      if(this.blChart){
        setTimeout(()=>{
          self.blChart.resize();
          window.addEventListener('resize', () => {
            self.blChart.resize();
          });
        },60)
      }else {
        window.removeEventListener('resize',()=> {

        })
      }
    },
    chartOption() {
      const inner_data = this.propsData;
      if (inner_data && inner_data.length > 0) {
        const inner_series = JSON.parse(JSON.stringify(inner_data));
        inner_series.forEach((item)=> {
          item.type = 'line';
        });
        const inner_legend = inner_data.map(data=> data.name);
        const inner_axis_data = inner_data[0].data.map(data=> data.date);

        const options = {
          title: {
            text: this.title
          },
          tooltip: {
            trigger: 'axis',
          },
          legend: {
            data: inner_legend
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: inner_axis_data
          },
          yAxis: {
            type: 'value'
          },
          series: inner_series
        };
        if (this.blChart) {
          this.blChart.setOption(options);
        }
        else {
          let blLineChart = this.$refs.blLineChart;
          this.blChart = echarts.init(blLineChart);
          this.blChart.setOption(options);
        }
      }else {
        return {}
      }
    }
  },
  watch: {
    propsData: {
      handler(newVal,oldVal){
        this.chartOption();
        this.eventListener();
      },
      deep:true
    }
  }
}
</script>

<style lang="scss" scoped>
.bl-line-chart {
  width: 100%;
  height: 100%;
}
</style>
