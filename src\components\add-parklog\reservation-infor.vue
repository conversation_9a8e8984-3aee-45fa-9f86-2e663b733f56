<template>
    <div class="reservation-infor">
      <div class="header">
        <div class="title">充电预约</div>
        <!-- <div class="setting">
          <el-switch v-model="setForm.releaseStatus" active-value="1" inactive-value="0" active-text="发布" inactive-text="未发布" :disabled="applyName !== null && empowerType !== 0">
          </el-switch>
        </div> -->
      </div>
      <el-form ref="setForm" label-width="110px" :model="setForm" class="charging-plie" :rules="rules">
        <el-form-item label-width="200px" label="预约规则名称" prop="name">
            <el-input v-model.trim="setForm.name" :disabled="applyName !== null && empowerType !== 0" placeholder="请填写预约规则名称"
                style="width: 100%" clearable maxlength="50"></el-input>
                <!-- <el-select
                      v-model="setForm.name" 
                      :remote-method="appointRuleNameSearch"
                      placeholder="请选择预约规则名称"
                      @change="appointRuleNameChange"
                       :remote="true"
                      allow-create
                      default-first-option
                      filterable
                      class="shop-custom-input"
                      style="width: 100%"
                      clearable
                      @clear="appointRuleNameSearch"
                      @blur="ruleBlurEvent($event)"
                      :disabled="applyName !== null && empowerType !== 0"
                  >
                      <el-option
                       v-for="item in appointRuleList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                      >
                      </el-option>
                </el-select> -->
        </el-form-item>
        <el-form-item label-width="200px" label="同一车主单日预约次数限制" prop="numberOfAppointments">
            <el-input v-model.trim="setForm.numberOfAppointments" :disabled="readonly || applyName !== null && empowerType !== 0"
                placeholder="请填写同一车主单日预约次数限制" style="width: 100%" maxlength="2" ></el-input>
        </el-form-item>
        <p>
            <span
                style="display: inline-flex; width: 94px; padding-right: 12px;justify-content: flex-end;margin-bottom: 15px">
                <span style="color: #F56C6C;padding-right: 4px">*</span>
                <span>收费标准</span>
            </span>
            <el-button size="small" :disabled="setForm.feesStandard.length == 1 || applyName !== null && empowerType !== 0" type="primary" @click="addFeesStandard">添加</el-button>
        </p>
        <div class="tip-box">
        </div>
        <el-form-item>
            <el-row style="background-color: #efefef;text-align: center; margin: 10px 0 0">
                <el-col :span="readonly ? 11 : 10"><span
                        style="color: #F56C6C;padding-right: 4px">*</span>可预约时长（分钟）</el-col>
                <el-col :span="readonly ? 11 : 10"><span
                        style="color: #F56C6C;padding-right: 4px">*</span>预约费用（元）</el-col>
                <el-col :span="3" v-show="!readonly">操作</el-col>
            </el-row>
        </el-form-item>
        <el-form-item v-for="(item,index) in setForm.feesStandard" :key="index">
            <el-row>
                <el-col :span="readonly ? 11 : 10">
                    <el-form-item style="margin: 0 10px" :prop="'feesStandard.'+index+'.time'"
                        :rules="rules.time">
                        <el-input v-model="item.time" :value="item.time"
                            oninput="if(value.length>10)value=value.slice(0,5)" type="number"
                            :disabled="true" placeholder="请填写可预约时长（分钟）" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="readonly ? 11 : 10">
                    <el-form-item style="margin: 0 10px" :prop="'feesStandard.'+index+'.fee'"
                        :rules="rules.fee">
                        <el-input v-model="item.fee" :value="item.fee"
                            oninput="if(value.length>10)value=value.slice(0,5)" type="number"
                            :disabled="applyName !== null && empowerType !== 0" placeholder="请填写预约费用（元）" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="3" v-show="!readonly" style="text-align: center; color: red"><span
                        @click="applyName !== null && empowerType !== 0 ? '' : subParamData(index)">删除</span></el-col>
            </el-row>
        </el-form-item>
      </el-form>
      <footer slot="footer" class="dialog-footer">
          <el-button type="primary" v-show="!readonly" class="custom-btns-style" style="margin-left: 30px"
              @click="submit" :loading="editloading" :disabled="applyName !== null && empowerType !== 0">确 定</el-button>
      </footer>
    </div>
  </template>
  <script>
  
  export default {
    name:'reservatInfor',
    props: {
      bolinkId: {
        type: Number,
        default: 0
      },
      applyName: {
          type: String,
          default: null
      },
      empowerType: {
        type: Number,
        default: 0
      },
    },
    data() {
      return {
        setForm: {
            feesStandard: [],
        },
        appointRuleList:[], //预约规则名称列表
        parkinglotList: [], //车位列表
        baseParams: {}, //初始化信息
        readonly: false,
        editloading: false,
        rules: {
            name: [{
                required: true,
                message: "请填写预约规则名称",
                trigger: 'blur'
            }],
            numberOfAppointments: [{
                    required: true,
                    message: "请填写同一车主单日预约次数限制",
                    trigger: 'blur'
                },
                {
                    pattern: /^[0-9]+$/,
                    message: '同一车主单日预约次数限制只允许填写正整数！',
                    trigger: 'blur'
                }
            ],
        },
      }
    },
    mounted() {
      this.initData()
    },
    methods: {
      //关闭弹窗清除记录
      clearData(){
          this.setForm =  {
            feesStandard: []
        }
      },
  
      /* 初始化 */
      initData() {
          let baseParams= {}
          let loginuin = sessionStorage.getItem("loginuin"),
            cityid = sessionStorage.getItem("cityid"),
            serverId = sessionStorage.getItem("serverid"),
            comid = sessionStorage.getItem("comid")
          if (comid != '0' && comid != 'undefined') {
              baseParams.comid = comid
          } else if (serverId != 'undefined'&& serverId!==null) {
              baseParams.serverId = serverId
          } else if (cityid != 'undefined') {
              baseParams.cityid = cityid
          } else {
              baseParams.backstageId = loginuin
          }
          this.baseParams = baseParams;
          this.appointRuleNameSearch()
      },
  
      //选择框可输入
      ruleBlurEvent(e){
        if (e.target.value) {
              // 如果是对象，要使用this.$set方法
              this.$set(this.setForm, 'name', e.target.value)
          }
      },
  
      //获取车位列表
      getParkList(parkList){
          console.log(parkList)
          this.parkinglotList = parkList;
      },
  
      //获取车位预约信息
      getReservationInfor(parkingFrom){
          this.$axios.get("/parkingSpaceInfo/queryBillByParkingSpaceBolinkId?bolinkId="+ parkingFrom.id).then(res => {            
              let data = res.data
              console.log(data)
              if (data.code === 200) {
                  let {
                      name,
                      feesStandard,
                      id,
                      numberOfAppointments,
                  } = data.data.appointmentBasis;
                  feesStandard = feesStandard ? JSON.parse(feesStandard) : []
                  // const releaseStatus = parkingFrom.chargeAppointState.toString();
                  this.setForm = {
                      id,
                      name,
                      feesStandard,
                      numberOfAppointments
                  } 
                  console.log(this.setForm) 
              } 
            })
              .catch(err => {
                 
              })
      },
  
      appointRuleNameChange(val){
          console.log(val)
          if(!val){
              this.appointRuleList=[]
          }else{
              this.appointRuleList.forEach(item => {
                  if(item.id == val){
                      let {
                          name,
                          feesStandard,
                          id,
                          numberOfAppointments
                      } = item
                      feesStandard = feesStandard ? JSON.parse(feesStandard) : []
                      this.setForm = {
                          id,
                          name,
                          feesStandard,
                          numberOfAppointments
                      }
                  }
              });
          }
      },
  
      //获取预约规则列表
      appointRuleNameSearch(val){
          let data = {
            name: val,
            deviceType: 4,
            ...this.baseParams
          }
  
          this.organizationIdSearchTimer = null
          clearTimeout(this.organizationIdSearchTimer);
          this.organizationIdSearchTimer = setTimeout(() => {
            this.searchCriteria(data)
          }, 400)
      },
  
      // 查询搜索条件下拉
      searchCriteria(data){
        this.$axios.post('/billingSettings/queryBillList', data).then(res => {
            let data = res.data
            console.log(data)
             if (data.code === 200) {
              this.appointRuleList= data.data;
                          
             } else {
                 this.appointRuleList=[]         
             }
                      
             })
             .catch(err => {
                     this.appointRuleList=[]
             })
        },
  
      /* 提交新增计费标准 */
      submit() {
          const _this = this
          this.$refs['setForm'].validate((valid) => {
              if (valid) {
                  const {
                      setForm,
                      bolinkId,
                  } = _this
                  const {
                      name,
                      feesStandard,
                      id,
                      numberOfAppointments
                  } = setForm
                  if (setForm.feesStandard.length == 0) {
                      _this.$message.error('请填写收费标准')
                  } else {
                      const api = '/parkingSpaceInfo/updateByBolinkId'
                      const selectPull = [];
                      if(this.parkinglotList){
                          const selectInfo = this.parkinglotList.filter(item => item.selectType == 1)
                          selectInfo.forEach(item =>{
                              selectPull.push(item.id)
                          })
                      }
                      let data = {
                          name,
                          feesStandard: JSON.stringify(feesStandard),
                          deviceType: 4,
                          // releaseStatus: releaseStatus || '0', //预约 0未发布 1已发布
                          appointmentBasis:id || '', //预约计费模型id
                          numberOfAppointments,
                          ...this.baseParams
                      }
                      console.log(data)
                      
                      if(bolinkId > 0){
                          data.bolinkId = bolinkId; //车位id
                      }
  
                      if(selectPull.length > 0){
                          data.bolinkIds = selectPull; //车位id数组
                      }
                      console.log(data)
                      _this.editloading = true
                      _this.$axios.post(api, data, {
                          headers: {
                              'Content-Type': 'application/json'
                          }
                      }).then(res => {
                          if (res.data.code == 200) {
                              _this.$message.success(res.data.message)
                          } else if (res.data.code == 500) {
                              _this.$message.error(res.data.message)
                          }
                          setTimeout(() => {
                              _this.editloading = false
                          }, 1000)
                      }).catch(err => {
                          _this.editloading = false
                          setTimeout(() => {
                              _this.editloading = false
                          }, 1000)
                      });
                  }
              }
          })
      },
      /* 删除计费标准（行） */
      subParamData(i) {
          this.setForm.feesStandard.splice(i, 1)
      },
      /* 添加计费规则 */
      addFeesStandard() {
          let feesStandard = JSON.parse(JSON.stringify(this.setForm.feesStandard))
          let tempData = {}
          for (let i = 0; i < feesStandard.length; i++) {
              if (feesStandard[i].type == 2) {
                  tempData = feesStandard[i]
                  break
              }
          }
          let fee, time = '30'
          if (tempData && tempData.type) {
              fee = tempData.fee
              time = tempData.time
          }
          this.setForm.feesStandard.push({
              time,
              fee,
          })
          // this.$refs['setForm'].validate((valid) => {
          //     if (valid) {
                  
          //     }
          // })
      },
    },
  }
  </script>
  <style  lang="scss" scoped>
  .header {
      display: inline-flex;
      position: relative;
      height: 30px;
      margin-bottom: 30px;
      align-items: center;
  
      .title {
        margin-left: 8px;
        font-size: 16px;
        width: 300px;
  
        .setting {}
      }
  
      .title::before {
        position: absolute;
        top: 5px;
        left: 0;
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-image: linear-gradient(-170deg, #6dbfff 0%, #1355d9 100%);
      }
  }
  </style>
  