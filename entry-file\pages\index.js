//引入全局样式
import '@/styles/index.scss'
import '@/styles/Home.scss'
import '@/styles/common-style.scss'
import "@/styles/mixin.scss";
//全局引入自定义iconfont
import '@/assets/iconfont.css'
import '@/assets/iconfont/iconfont.css'
import '@/assets/shop-iconfont/iconfont.css'

import { adminComponents, adminComponentsObj } from './adminIndex';
import { cityComponents, cityComponentsObj } from './cityIndex';
import { parkComponents, parkComponentsObj } from './parkIndex';
import { serviceComponents, serviceComponentsObj } from './serviceIndex';
import { shopComponents, shopComponentsObj } from './shopIndex';
import { unionComponents, unionComponentsObj } from './unionIndex';

/**
 * 拓展类的页面 全屏显示要跳转的
 */

import MonitorApp from '@/MonitorApp'

/****************************/

const components = [
    MonitorApp,
    ...adminComponents,
    ...cityComponents,
    ...parkComponents,
    ...serviceComponents,
    ...shopComponents,
    ...unionComponents
]

const install = function (Vue,opts = {}) {
    components.forEach(component => {
        Vue.component(component.name, component);
    });
}

/* istanbul ignore if */
if (typeof window !== 'undefined' && window.Vue) {
    install(window.Vue);
}
const otherObj = {
    install,MonitorApp,
}
export default Object.assign(otherObj,
    adminComponentsObj,
    cityComponentsObj,
    parkComponentsObj,
    serviceComponentsObj,
    shopComponentsObj,
    unionComponentsObj
)

