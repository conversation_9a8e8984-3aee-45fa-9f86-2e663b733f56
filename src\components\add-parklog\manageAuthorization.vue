<template>
    <div class="parking-spaceNew">
      <el-drawer
            :visible="authorShow"
            direction="rtl"
            @close="closeDialog"
            size="680px"
            custom-class="custom-drawer">
            <div slot="title" class="header-title">授权管理</div>
            <div class="custom-drawer-info">
              <div class="tips"><svg-tips-one theme="outline" size="16"/>接收/管理来自充电运营方的车位授权申请</div>
              <el-table
                :data="tableData"
                style="width: 100%">
                <el-table-column
                  type="index"
                  label="序号"
                  width="50">
                </el-table-column>
                <el-table-column
                  align="left"
                  prop="out_park_count"
                  label="申请方">
                    <template slot-scope="scope">
                       <p class="ellipsis">名称：{{scope.row.roleName}}</p>   
                       <p>操作人：{{scope.row.userName}}</p> 
                       <p class="ellipsis">申请时间：{{common.dateformat(scope.row.ctime)}}</p>      
                   </template>
                </el-table-column>
                <el-table-column
                  align="left"
                  label="状态">
                    <template slot-scope="scope">
                      <div style="color: #B8741A;" v-if="scope.row.status == 0">等待授权</div>
                      <div style="color: #B8741A;" v-else-if="scope.row.status == 2">取消已授权</div>
                      <div v-else>
                        <p style="color: #02830F;">已授权</p>   
                        <p>授权人：{{scope.row.approveUserName}}</p> 
                        <p class="ellipsis">授权时间：{{common.dateformat(scope.row.approveTime)}}</p>  
                      </div>  
                   </template>
                </el-table-column>
                <el-table-column
                  align="left"
                  label="管理"
                  width="100">
                  <template slot-scope="scope">
                    <el-button type="text" v-if="scope.row.status == 1" @click="authorManage(scope.row.id,2)">取消已授权</el-button>
                    <div v-if="scope.row.status == 0">
                      <el-button type="text" @click="authorManage(scope.row.id,1)">同意</el-button>
                      <el-button style="color: #A30014;" type="text" @click="authorManage(scope.row.id,2)">否</el-button>
                    </div>  
                   </template>
                </el-table-column>
                
              </el-table>
              <div class="pagination-info">
                <el-pagination
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="page"
                  :page-size="pageSize"
                  layout="prev, pager, next"
                  background
                  :total="total"></el-pagination>
              </div>
              
            </div>
      </el-drawer>
    </div>
</template>
<script>
import common from '@/common/js/common';
export default {
  props: {
    authorShow: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      top:'0vh', 
      isBolink: true,
      pageSize: 10,
      page: 1,
      authorList: [], //授权管理列表
      total:0, //总个数
      tableData: [
        
      ]
    }
  },
  mounted() {
    this.getAuthorList();
  },
  methods: {
    closeDialog(){
      this.$emit("closeDialog",false)
    },

    //授权处理
    authorManage(id,status){
      const tag = status == 2 ? '是否取消授权？': '是否授权？'
      this.$confirm(tag, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let data = {
            parkingLotApplyId: id,
            status: status
          }
          this.$axios.post('/parkingLot/apply', data).then(res => {
            if(res.data.code == 200){
              this.getAuthorList()
            }else{
              this.$message({
                message: res.data.message,
                type: 'error'
              });
            }
          })
        }).catch(() => {
                
        });
      
    },

    //获取授权管理列表
    getAuthorList(){
      const comid = sessionStorage.getItem("comid");
      let data = {
        page: this.page,
        rp: this.pageSize,
        comid: comid, //车场ID
      }
      this.$axios.post('/parkingLot/apply/find', data).then(res => {
        if(res.data.code == 200){
          this.total = res.data.data.total;
          this.tableData = res.data.data.list;
        }else{
          this.tableData = [];
        }
      })
    },

    //分页器相关
    handleSizeChange(val){
        this.pageSize = val;
        this.page = 1;
        this.getAuthorList()
    },

    handleCurrentChange(val){
        this.page = val;
        this.getAuthorList()
    },
  }
}
</script>
<style  lang="scss" scoped>
.header-title{
  color: #333;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
/deep/.el-drawer__header{
  margin-bottom: 10px;
  padding: 20px 30px 0 30px;
}
.custom-drawer-info{
  margin: 10px 30px;
}
.level-title{
  font-weight: bold;
  margin-bottom: 10px;
}

.headerInfo .el-button{
  width: 120px;
  height: 40px;
}

.tips{
  color: #999;
  font-size: 14px;
  background: #fff;
  line-height: 20px;
  margin: 6px 0;
  span{
    vertical-align: middle;
    margin-right: 6px;
  }
}

.header-tips{
  color: #B8741A;
  margin-left: 20px;
  margin: 0 10px;
}

/deep/.el-input{
  width: 200px;
}

.houseInfo{
  display: flex;
  span{
    display: inline-block;
    width: 30px;
    margin: 0 10px;
  }
  .el-input{
    width: 90px;
    margin-right: 10px;
  }
}

.ellipsis{
  white-space:nowrap;
  overflow:hidden;
  text-overflow:ellipsis;
}

.headerInfo{
  display: flex;
  margin: 20px 0;
  span{
    margin-left: 10px;
    color: #B8741A;
  }
}

/deep/.custom-dialog .el-dialog__body{
  padding: 30px 20px;
}

/deep/.custom-dialog .el-dialog__header .el-dialog__headerbtn{
  top: 20px;
}
.import-input-wrapper{
    position: relative;
    height: 32px;
    border: 1px solid #3C75CF;
    border-radius: 6px;
    width: 350px;
    .upload-demo{
      position: absolute;
      top: 0;
      right: 0;
    }
}

.pagination-info{
  margin: 10px 0;
  text-align: center;
}

/deep/.el-form-item__content{
  line-height: 24px;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}

/deep/.el-upload-list{
  position: absolute;
  top: -5px;
  left: -224px;
}

</style>
