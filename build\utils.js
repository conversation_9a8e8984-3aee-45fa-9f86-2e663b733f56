'use strict'
const path = require('path')
const config = require('../config')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const packageConfig = require('../package.json')

// const cariablesFlis = ((process.env.NODE_ENV == 'dev') || (process.env.NODE_ENV == 'production') || (process.env.NODE_ENV == 'test')||(process.env.NODE_ENV == 'test2'))?'common-variables.scss':'other-variables.scss'

const themeFiles = {
  common: "common-variables.scss",
}


exports.fontIconOption = function (dev) {
  let config = {};
  if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'other' || process.env.NODE_ENV === 'pages') {
    config = {
      test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
      loader: 'url-loader',
      options: {
        limit: 10000,
        name: exports.assetsPath('fonts/[name].[hash:7].[ext]')
      }
    }
  } else {
    config = {
      limit: 10000,
      name: '[name].[hash:7].[ext]',
      publicPath: '../fonts/',              //打包时替换的路径
      outputPath: exports.assetsPath('fonts/')  //文件输出路径
    }
  }
  return config
}

exports.assetsPath = function (_path) {
  const assetsSubDirectory =
    process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'pages'
      ? config.dev.assetsSubDirectory
      : config.build.assetsSubDirectory

  return path.posix.join(assetsSubDirectory, _path)
}

exports.cssLoaders = function (options) {
  options = options || {}

  const cssLoader = {
    loader: 'css-loader',
    options: {
      sourceMap: options.sourceMap
    }
  }

  const postcssLoader = {
    loader: 'postcss-loader',
    options: {
      sourceMap: options.sourceMap
    }
  }

  function resolveResource(name) {
    return path.resolve(__dirname, '../src/styles/' + name);
  }

  function resolveResources() {
    return Object.keys(themeFiles).map(key => {
      return resolveResource(themeFiles[key])
    })
  }

  // generate loader string to be used with extract text plugin
  function generateLoaders(loader, loaderOptions) {
    const loaders = []

    // Extract CSS when that option is specified
    // (which is the case during production build)
    if (options.extract) {
      loaders.push(MiniCssExtractPlugin.loader)
    } else {
      loaders.push('vue-style-loader')
    }

    loaders.push(cssLoader)

    if (options.usePostCSS) {
      loaders.push(postcssLoader)
    }

    if (loader) {
      if (loader === 'sass-resources') {
        loaders.push({
          loader: 'sass-loader'
        })
      }

      loaders.push({
        loader: loader + '-loader',
        options: Object.assign({}, loaderOptions, {
          sourceMap: options.sourceMap
        })
      })
    }

    return loaders
  }
  // https://vue-loader.vuejs.org/en/configurations/extract-css.html
  return {
    css: generateLoaders(),
    postcss: generateLoaders(),
    less: generateLoaders('less'),
    sass: generateLoaders('sass', {
      indentedSyntax: true
    }),
    scss: generateLoaders('sass-resources', {
      resources: resolveResources()
    }),
    stylus: generateLoaders('stylus'),
    styl: generateLoaders('stylus')
  }
}

// Generate loaders for standalone style files (outside of .vue)
exports.styleLoaders = function (options) {
  const output = []
  const loaders = exports.cssLoaders(options)

  for (const extension in loaders) {
    const loader = loaders[extension]
    output.push({
      test: new RegExp('\\.' + extension + '$'),
      use: loader
    })
  }

  return output
}

exports.createNotifierCallback = () => {
  const notifier = require('node-notifier')

  return (severity, errors) => {
    if (severity !== 'error') return

    const error = errors[0]
    const filename = error.file && error.file.split('!').pop()

    notifier.notify({
      title: packageConfig.name,
      message: severity + ': ' + error.name,
      subtitle: filename || '',
      icon: path.join(__dirname, 'logo.png')
    })
  }
}
