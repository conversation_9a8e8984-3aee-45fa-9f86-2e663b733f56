export default class WebSocketClient {
  constructor(url, protocols = null, reconnectDelay = 5000) {
    this.url = url //连接地址
    this.protocols = protocols //可选协议
    this.socket = null //当前实例
    this.isConnected = false //是否连接
    this.heartbeatInterval = null; // 心跳定时器
    this.reconnectTimer = null; // 重连定时器
    this.reconnectDelay = reconnectDelay; // 重连时间间隔

    this.onOpen = null; // 连接成功回调
    this.onMessage = null; // 消息接收回调
    this.onClose = null; // 连接关闭回调
    this.onError = null; // 错误回调
  }

  // 初始化 WebSocket
  connect() {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      console.warn('WebSocket 已连接');
      return;
    }
    this.socket = new WebSocket(this.url, this.protocols);

    // WebSocket 打开事件
    this.socket.onopen = () => {
      console.log('WebSocket 连接成功');
      this.isConnected = true;
      this.startHeartbeat(); // 开启心跳检测
      if (this.onOpen) this.onOpen();
    };

    // WebSocket 消息接收事件
    this.socket.onmessage = (event) => {
      console.log('收到消息:', event.data);
      if (this.onMessage) this.onMessage(event.data);
    };

    // WebSocket 关闭事件
    this.socket.onclose = (event) => {
      console.warn('WebSocket 连接关闭', event.reason);
      this.isConnected = false;
      this.stopHeartbeat(); // 停止心跳检测
      if (this.onClose) this.onClose(event);

      // 自动重连
      this.reconnect();
    }
    // WebSocket 错误事件
    this.socket.onerror = (error) => {
      console.error('WebSocket 错误:', error);
      if (this.onError) this.onError(error);
    };
  }

  // 发送消息
  send(message) {
    if (this.isConnected) {
      this.socket.send(message);
    } else {
      console.warn('WebSocket 未连接，无法发送消息:', message);
    }
  }

  // 关闭连接
  close() {
    if (this.socket) {
      this.socket.close();
    }
  }

  // 心跳检测
  startHeartbeat(interval = 30000) {
    this.stopHeartbeat(); // 确保不重复启动
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send('ping');
        console.log('发送心跳包: ping');
      }
    }, interval);
  }

  // 停止心跳检测
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  // 自动重连
  reconnect() {
    if (this.reconnectTimer) return;

    console.log('尝试重连 WebSocket...');
    this.reconnectTimer = setTimeout(() => {
      this.reconnectTimer = null;
      this.connect();
    }, this.reconnectDelay);
  }
}