{"name": "heradoc", "version": "1.0.0", "description": "doc website for hera", "main": "index.js", "scripts": {"dev": "webpack-dev-server --progress --inline --config config/webpack.dev-website.config.js --host 0.0.0.0", "build": "NODE_ENV=production webpack --progress --config config/webpack.build-website.config.js --display-modules --sort-modules-by size", "deploy:gh-pages": "npm run build && git checkout gh-pages && cd ../../ && cp -r temp/* . && git add . && git commit -m 'chore: update doc' --no-verify", "lint": "eslint --fix  ./website/**/*.js ./website/**/*.vue", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"babel-core": "^6.21.0", "babel-eslint": "^8.0.2", "babel-helper-vue-jsx-merge-props": "^2.0.2", "babel-loader": "^6.2.10", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.3.0", "babel-preset-latest": "^6.16.0", "css-loader": "^0.28.7", "es6-promise": "^4.0.5", "eslint": "^3.19.0", "eslint-config-wdfe": "^0.3.2", "eslint-loader": "^1.7.1", "eslint-plugin-vue": "^2.0.1", "file-loader": "^0.11.0", "html-loader": "^0.5.1", "html-webpack-plugin": "^2.28.0", "markdown-loader": "^2.0.1", "node-sass": "^4.5.2", "raw-loader": "^0.5.1", "sass-loader": "^6.0.3", "style-loader": "^0.19.0", "url-loader": "^0.5.7", "vue": "^2.2.6", "vue-loader": "^11.3.4", "vue-router": "^2.3.1", "vue-template-compiler": "^2.2.6", "webpack": "^2.3.2", "webpack-dev-server": "^2.2.0"}, "keywords": ["doc", "hera"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT"}