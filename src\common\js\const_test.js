const AUTH_ID = {
    dataCenter:69,//数据中心
    dataCenter_Park: 406,
    my_account:949,//我的账户
    my_park_vehicle: 960, //新能源车概况
    parkingLot: 1078, // 车位
    Data_SIM_Management:1089,//SIM卡管理
    Data_SIM_Card:1090,//流量卡
    dataCenter_4GSentryBox:449,
    Yun_Onduty:9565,
    Onduty_Setting:9566,
    dataCenter_RemoteOpening:405,
    orderManage: 2, //订单管理
    orderManage_NowOrders:453,//在场订单
    orderManage_Orders: 83, //订单记录
    orderManage_Poles: 84, //抬杆记录
    orderManage_Income:353,//交易记录
    orderManage_Discount:507,//抵扣记录
    orderManage_Expense:354,//支出记录
    orderManage_Record:355,//减免记录
    orderManage_Abnormal:364,//异常订单
    orderManage_PayAfterDeparture: 955, // 欠费订单
    monthMember: 7, //月卡会员-大菜单
    monthMember_Refill: 297, //月卡续费记录
    relationSet:616,//会员设置
    monthMember_VIP: 298, //月卡会员-小菜单
    park_white_list:362,//内部车管理
    camera_white_list:504,//下发记录
    park_appoint_manage: 859, // 预约车管理
    prepay_card:339,
    prepay_card_trade:340,
    prepay_card_use:341,
    orderStatistics: 11, //统计分析
    orderStatistics_DailyReport: 12, //统计分析-时租订单统计-日报
    orderStatistics_MonthReport: 318, //统计分析-时租订单统计-日报
    orderStatistics_CollectorReport:321,//统计分析-收费员统计
    orderStatistics_CollectorMonthReport:608,//统计分析-收费员月报
    flowStatistics_DailyReport:625,//统计分析-车流量日报
    flowStatistics_MonthReport: 626, //统计分析-车流量月报

    // shopManage: 71, //商户管理-大菜单
    shopManageA:71,//商户A
    shopAnalysis: 413, //商户统计
    newParkAnalysis: 9686, //商户统计
    shopManage_Shop:299 , //商户管理-小菜单
    shopManage_QueryAccount: 300, //流水查询
    shopManage_Coupon: 305, //优惠券管理

    shopManageB:444,
    shopAnalysis_B: 510, //商户统计
    shopManage_CouponSettings:445,
    shopManage_Shop_B:446, //买券商户
    shopManage_QueryAccount_B:447,
    shopManage_Coupon_B:448,
    shopManage_CouponList: 9632, //发券设置
    shopManage_ShopList: 9635, //商户管理

    systemManage_AddedService_Sms:338,//短信服务
    systemManage_AddedService_Screen:345,//数据大屏
	systemManage_AddServices_Public:356,//商户公众号
    systemManage_AddServices_Program:352, //车场小程序
    systemManage_AddServices_ParkPublic:410, //车场公众号
    systemManage_AddServices_Video:488,//语音呼叫
    systemManage_AddServices_FourService:521,//纯云服务
    systemManage_AddedService_Cam_Video:704,//视频VIP服务
    systemManage_AddedService_Car_Distinguish:757,//手动车牌识别
    systemManage_AddedService:343, //平台服务
    equipmentManage: 346, //设备管理
    equipmentManage_Charging: 407, //计费管理
    equipmentManage_ParkCamera: 408, //相机管理
	equipmentManage_Watchhouse:357, //岗亭管理
    equipmentManage_Monitor: 951, //监控管理
    equipmentManage_DataUpdate: 953, //数据同步
    equipmentManage_Intercom: 348, //对讲管理
    equipmentManage_WorkStation: 349,//工作站管理
    equipmentManage_Channel: 350, //通道管理
    equipmentManage_QrCodeManage:607,// 二维码管理
    equipmentManage_Camera: 19, //摄像头管理
    equipmentManage_LED: 20, //LED屏管理
    employeePermission: 21, //账号管理
    employeePermission_Role: 22, //账号角色
    employeePermission_Manage: 23, //员工管理
    employeePermission_MessageNtification:482,//消息通知
    onlineAssistance: 9634,//在线协助
    systemManage: 24, //系统管理
    parkManage:593,//车厂管理
    parkManage_Set:433,//车场设置
    systemManage_AdvanceSet:409,//高级设置
    systemManage_ParkPublic:411,//车场公众号
    systemManage_BlackList: 303, //黑名单管理
    systemManage_specialVehicles: 966, // 特殊车辆管理
    systemManage_Commute: 380, //上下班记录
    systemManage_Account: 770, //账户管理
    systemManage_Params: 26, //参数设置
    systemManage_FreeReason: 648, //免费原因
    systemManage_CarManage_BindType: 260, //绑定车型
    systemManage_CarManage_CarType: 9923, //车型设定
    systemManage_CarManage: 27, //车型管理
    systemManage_Price: 28, //时租价格管s理
    systemManage_MonthCard: 29, //月卡套餐管理
    systemManage_Logs: 80, //系统日志
    car_Info_Gather: 677, //车辆信息采集
    centerMonitor: 342, //中央监控
    onlinePay: 8, //电子支付
    onlinePay_Income: 10, //电子收款
    onlinePay_CashManage: 9, //提现管理
	vistorManage_VistorMember:324,
    vistorManage_homeOwner:336,
    vistorManage:323,
    operatorOnDuty: 9570,

    faceDetectionManage:492, // 门禁梯控
    faceDetectionManage_FaceProject:497, // 项目管理
    faceDetectionManage_Base: 498, // 基本数据
    faceDetectionManage_Village: 877, // 区域管理
    faceDetectionManage_Building: 878, // 楼宇管理
    faceDetectionManage_Houses: 880, // 房屋管理
    faceDetectionManage_FaceInfomation: 881, // 人员管理
    faceDetectionManage_Caller: 499, // 访客管理
    faceDetectionManage_VisitRegistration: 882, // 来访登记
    faceDetectionManage_DepartureRegistration: 883, // 离开登记
    faceDetectionManage_ElevatorRideRecord:1181, //乘梯记录
    faceDetectionManage_FeeCollectingManagement:1177,//收费管理
    faceDetectionManage_LadderCharge:1178,//乘梯计费
    faceDetectionManage_MonthlyRecord:1179,//包月记录
    faceDetectionManage_AccountRecords:1180,//账户记录
    faceDetectionManage_InoutRecords: 500, // 出入记录
    faceDetectionManage_Service: 501, // 服务管理
    faceDetectionManage_Report: 884, // 报修管理
    faceDetectionManage_Phone: 885, // 服务电话
    faceDetectionManage_Notice: 886, // 公告管理
    faceDetectionManage_Device: 505, // 设备管理
    faceDetectionManage_Devices: 887, // 门禁设备
    faceDetectionManage_DoorsGroup: 888, // 门禁设备组
    faceDetectionManage_TimeGroup: 889, // 门禁时间组
    faceDetectionManage_FaceLaddelEquipment: 890, // 梯控设备
    faceDetectionManage_FaceLaddelPowerGroup: 891, // 梯控权限组
    faceDetectionManage_FaceManageCardIssuer:933,//发卡器
    faceDetectionManage_AccessCtrlPassenger:1193,//门禁通道
    faceDetectionManage_DownloadRecords: 509, // 门禁同步
    faceDetectionManage_ElevatorCtrlSync: 1192, // 梯控同步
    faceDetectionManage_publicParams: 673, // 参数配置
    faceDetectionManage_appletSet: 715, //  小程序设置
    faceDetectionManage__ConsumptionManage: 771, //人脸消费
    faceDetectionManage_ConsumerPersonnel: 1075, //人脸消费-人员管理
    faceDetectionManage_RecordsConsumption: 775, //人脸消费-消费记录
    faceDetectionManage_OperationRecord: 776, //人脸消费-操作记录


    Public_CarCharge: 659, // 汽车运营管理
    Public_CarCharge_CalculaterManage: 662, // 计费管理
    Public_CarCharge_Rule: 663, // 充电费
    Public_CarCharge_OrderRule: 849, // 预约费
    Public_CarCharge_SeatRule: 850, // 占位费
    Public_CarCharge_StationManage: 792, // 电站管理
    Public_CarCharge_Station: 793, // 电站
    Public_CarCharge_Area: 794, // 区域
    Public_CarCharge_ParkingLot: 795, // 车位
    Public_CarCharge_Device: 660, // 设备管理
    Public_CarCharge_Pile: 748, // 充电桩
    Public_CarCharge_Camera: 790, // 相机
    Public_CarCharge_GroundLock: 791, // 地锁
    Public_CarCharge_Monitor:1070,//监控
    Public_CarCharge_Order: 664, // 订单管理
    Public_CarCharge_Order_Charge: 665, // 在充订单
    Public_CarCharge_Order_Record: 666, // 订单记录
    Public_CarCharge_Order_Reduction: 927, // 减免订单
    Public_CarCharge_Order_ReductionSummary: 928, // 减免汇总
    Public_CarCharge_Order_Share: 765, // 分账订单
    Public_CarCharge_Order_Order: 865, // 占位订单
    Public_CarCharge_Order_Arrearage:900,//欠费记录
    Public_CarCharge_Order_Seat: 864, // 预约订单
    Public_CarCharge_Statistics: 675, // 统计分析
    Public_CarCharge_Statistics_Charge: 676, // 充电统计
    Public_CarCharge_Statistics_Share: 694, // 分账统计
    Public_CarCharge_Statistics_ShareArrival: 705, // 分账到账统计
    Public_CarCharge_Store: 762, // 库存管理
    Public_CarCharge_Store_Operate: 763, // 设备出库
    Public_CarCharge_Store_FlowRecord: 764, // 流转记录
    Public_CarCharge_UserManage:1045,//用户管理
    Public_CarCharge_User:1046,//用户管理
    Public_CarCharge_ElectronicsCard:1047, //电子卡管理
    Public_CarCharge_PhysicalCard:1048,//实体卡管理
    Public_CarCharge_PhysicalVIN:1202,//VIN码
    Public_CarCharge_cardRecord:1203,//卡记录
    Public_CarCharge_User_ChargingActivities:1049, //充电活动
    Public_CarCharge_User_ActivitiesRecord:1050,//活动记录
    //有序能源
    orderlyEnergy:923, //有序能源
    orderlyEnergy_Home:924, //首页
    orderlyEnergy_SchedulingModel:925,//调度模型

    chargeConnect: 1122, // 充电互联
    Public_connect_auth: 1123, // 互联授权
    connectPark_authorizedStation: 1124, // 授权电站
    connectPark_authPile: 1125, // 授权桩
    connectPark_differenceOrder: 1126, // 异常订单

    // 单车充电
    Public_BicycleCharge: 778, // 两轮车充电
    Public_BicycleCharge_StationManage: 779, // 站点管理
    Public_BicycleCharge_Station: 782, // 充电站
    Public_BicycleCharge_Area: 914, // 充电区
    Public_BicycleCharge_Pile: 783, // 充电桩
    Public_BicycleCharge_Door: 934, // 门禁设备
    Public_BicycleCharge_BillingManage: 780, // 计费管理
    Public_BicycleCharge_ChargingSet: 784, // 计费标准
    Public_BicycleCharge_MonthRuleSet: 906, // 包月计费
    Public_BicycleCharge_Rule_Park: 935, // 停车计费
    Public_BicycleCharge_OrderManage: 781, // 订单管理
    Public_BicycleCharge_ChargeOrder: 785, // 在充订单
    Public_BicycleCharge_OrderRecord: 786, // 订单记录
    Public_BicycleCharge_Order_Park: 936, // 停车订单
    Public_BicycleCharge_AccountSplitting:930,//分账订单
    Public_BicycleCharge_FinancialStatistics:836,//统计分析
    Public_bicycleCharge_chargeDaily: 837,//充电统计
    Public_bicycleCharge_shareDaily:838,//分账统计
    Public_bicycleCharge_shareArrivalDaily:839,//分账到账统计
    Public_BicycleCharge_UserManage: 893,// 用户管理
    Public_BicycleCharge_User: 894,// 用户管理
    Public_BicycleCharge_User_Park: 937,// 车辆管理
    Public_BicycleCharge_MonthCardRecord: 895,// 包月订单
    Public_BicycleCharge_PhysicalCard: 901,// 充电卡管理
    Public_BicycleCharge_ElectronicsCard: 1031,// 电子卡管理
    Public_BicycleCharge_User_AccessRecord: 938,// 出入记录
    park_videoPort: 834, // 视频接口
    park_Order_Picture_Storage: 876, //订单图片存储
    Public_BicycleCharge_AlarmInformation: 1150, // 告警消息
    Public_BicycleCharge_deviceMsg: 1151, // 告警记录

    // parkCarportManage: 9653,  // 车位管理
    // parkCarportManage_carportManage: 9654, // 车位管理
    // parkCarportOrder_carportOrder: 9655, // 车位订单
    // parkCarportRepport_carportReport: 9656, // 统计报表
    // parkCarportUsers_carportUsers: 9657, // 用户管理


};

const showParkItem_const = {
    dataCenter:false,//数据中心
    dataCenter_Park: false,
    my_account:false,//我的账户
    Data_SIM_Management:false,//SIM卡管理
    Data_SIM_Card:false,//流量卡
    dataCenter_RemoteOpening:false,
    dataCenter_4GSentryBox:false,
    Yun_Onduty:false,
    Onduty_Setting:false,
    orderManage: false,
    my_park_vehicle: false, //新能源车概况
    parkingLot: false, // 车位
    orderManage_NowOrders:false,
    orderManage_Orders: false,
    orderManage_Poles: false,
    orderManage_Income:false,//交易记录
    orderManage_Expense:false,//支出记录
    orderManage_Record:false,//减免记录
    orderManage_Abnormal:false,//异常订单
    orderManage_Discount:false,//抵扣记录
    orderManage_PayAfterDeparture: false, // 欠费订单
    monthMember: false,
    relationSet:false,//会员设置
    monthMember_Refill: false,
    monthMember_VIP: false,
    park_white_list:false,
    camera_white_list:false,
    park_appoint_manage: false,
    prepay_card:false,
    prepay_card_trade:false,
    prepay_card_use:false,
    orderStatistics: false,
    orderStatistics_DailyReport: false,
    orderStatistics_MonthReport: false,
    flowStatistics_DailyReport: false,
    flowStatistics_MonthReport: false,
    orderStatistics_CollectorReport:false,//统计分析-收费员日报
    orderStatistics_CollectorMonthReport:false,//统计分析-收费员月报
    onlinePay: false,
    onlinePay_Income: false,
    onlinePay_CashManage: false,

    shopManageA:false,//商户A
    shopAnalysis: false, //商户统计
    newParkAnalysis: false, //商户统计
    shopManage_Shop:false , //商户管理-小菜单
    shopManage_QueryAccount: false, //流水查询
    shopManage_Coupon: false, //优惠券管理

    shopManageB:false,
    shopAnalysis_B:false,
    shopManage_CouponSettings:false,
    shopManage_Shop_B:false,
    shopManage_QueryAccount_B:false,
    shopManage_Coupon_B:false,
    shopManage_CouponList:false,
    shopManage_ShopList:false,

    equipmentManage: false,
    equipmentManage_Charging:false,
    equipmentManage_ParkCamera:false,
	equipmentManage_Watchhouse:false,
    equipmentManage_Monitor: false,
    equipmentManage_Intercom: false,
    equipmentManage_DataUpdate: false,
    equipmentManage_WorkStation: false,
    equipmentManage_Channel: false,
    equipmentManage_QrCodeManage:false,
    equipmentManage_Camera: false,
    equipmentManage_LED: false,
    employeePermission: false,
    employeePermission_Role: false,
    employeePermission_Manage: false,
    employeePermission_MessageNtification: false,
    onlineAssistance: false,
    systemManage: false,
    parkManage:false,
    parkManage_Set: false,
    systemManage_AdvanceSet:false,
    systemManage_ParkPublic:false,
    systemManage_BlackList: false,
    systemManage_specialVehicles: false,
    systemManage_Commute: false,
    systemManage_Account: false,
    systemManage_Params: false,
    systemManage_FreeReason: false,
    systemManage_CarManage_CarType: false,
    systemManage_CarManage_BindType: false,
    systemManage_CarManage: false,
    systemManage_Price: false,
    systemManage_MonthCard: false,
    systemManage_Logs: false,
    car_Info_Gather: false,
	systemManage_AddServices_Public:false,
    systemManage_AddedService_Sms:false,
    systemManage_AddedService_Screen:false,
    systemManage_AddServices_Program:false,
    systemManage_AddServices_ParkPublic:false,
    systemManage_AddServices_Video:false,
    systemManage_AddServices_FourService:false,
    systemManage_AddedService_Cam_Video:false,//视频VIP服务
    systemManage_AddedService_Car_Distinguish:false,//手动车牌识别
    systemManage_AddedService:false,
    vistorManage_VistorMember:false,
    vistorManage_homeOwner:false,
    vistorManage:false,
    centerMonitor: false,
    operatorOnDuty: false, // 值班人员

    faceDetectionManage:false, // 门禁梯控
    faceDetectionManage_FaceProject:false, // 项目管理
    faceDetectionManage_Base: false, // 基本数据
    faceDetectionManage_Village: false, // 区域管理
    faceDetectionManage_Building: false, // 楼宇管理
    faceDetectionManage_Houses: false, // 房屋管理
    faceDetectionManage_FaceInfomation: false, // 人员管理
    faceDetectionManage_Caller: false, // 访客管理
    faceDetectionManage_VisitRegistration: false, // 来访登记
    faceDetectionManage_DepartureRegistration: false, // 离开登记
    faceDetectionManage_InoutRecords: false, // 出入记录
    faceDetectionManage_ElevatorRideRecord:false,//乘梯记录
    faceDetectionManage_FeeCollectingManagement:false,//收费管理
    faceDetectionManage_LadderCharge:false,//乘梯计费
    faceDetectionManage_MonthlyRecord:false,//包月记录
    faceDetectionManage_AccountRecords:false,//账户记录
    faceDetectionManage_Service: false, // 服务管理
    faceDetectionManage_Report: false, // 报修管理
    faceDetectionManage_Phone: false, // 服务电话
    faceDetectionManage_Notice: false, // 公告管理
    faceDetectionManage_Device: false, // 设备管理
    faceDetectionManage_Devices: false, // 门禁设备
    faceDetectionManage_DoorsGroup: false, // 门禁设备组
    faceDetectionManage_TimeGroup: false, // 门禁时间组
    faceDetectionManage_FaceLaddelEquipment: false, // 梯控设备
    faceDetectionManage_FaceLaddelPowerGroup: false, // 梯控权限组
    faceDetectionManage_FaceManageCardIssuer:false,//发卡器
    faceDetectionManage_DownloadRecords: false, // 门禁同步
    faceDetectionManage_ElevatorCtrlSync:false, // 梯控同步
    faceDetectionManage_publicParams: false, // 参数配置
    faceDetectionManage_appletSet: false, //  小程序设置
    faceDetectionManage__ConsumptionManage: false, //人脸消费
    faceDetectionManage_ConsumerPersonnel: false, //人脸消费-人员管理
    faceDetectionManage_RecordsConsumption: false, //人脸消费-消费记录
    faceDetectionManage_OperationRecord: false, //人脸消费-操作记录

    // 汽车充电
    Public_CarCharge: false, // 汽车运营管理
    Public_CarCharge_CalculaterManage: false, // 计费管理
    Public_CarCharge_Rule: false, // 充电费
    Public_CarCharge_OrderRule: false, // 预约费
    Public_CarCharge_SeatRule: false, // 占位费
    Public_CarCharge_StationManage: false, // 电站管理
    Public_CarCharge_Station: false, // 电站
    Public_CarCharge_Area: false, // 区域
    Public_CarCharge_ParkingLot: false, // 车位
    Public_CarCharge_Device: false, // 设备管理
    Public_CarCharge_Pile: false, // 充电桩
    Public_CarCharge_Camera: false, // 相机
    Public_CarCharge_GroundLock: false, // 地锁
    Public_CarCharge_Monitor:false,//监控
    Public_CarCharge_Order: false, // 订单管理
    Public_CarCharge_Order_Charge: false, // 在充订单
    Public_CarCharge_Order_Record: false, // 订单记录
    Public_CarCharge_Order_Reduction: false, // 减免订单
    Public_CarCharge_Order_ReductionSummary: false, // 减免汇总
    Public_CarCharge_Order_Share: false, // 分账订单
    Public_CarCharge_Order_Order: false, // 占位订单
    Public_CarCharge_Order_Arrearage:false,//欠费记录
    Public_CarCharge_Order_Seat: false, // 预约订单
    Public_CarCharge_Statistics: false, // 统计分析
    Public_CarCharge_Statistics_Charge: false, // 充电统计
    Public_CarCharge_Statistics_Share: false, // 分账统计
    Public_CarCharge_Statistics_ShareArrival: false, // 分账到账统计
    Public_CarCharge_Store: false, // 库存管理
    Public_CarCharge_Store_Operate: false, // 设备出库
    Public_CarCharge_Store_FlowRecord: false, // 流转记录
    Public_CarCharge_UserManage:false,
    Public_CarCharge_User:false,//用户管理
    Public_CarCharge_ElectronicsCard:false, //电子卡管理
    Public_CarCharge_PhysicalCard:false,//实体卡管理
    Public_CarCharge_PhysicalVIN:false,//VIN码
    Public_CarCharge_cardRecord:false,//卡记录
    Public_CarCharge_User_ChargingActivities:false, //充电活动
    Public_CarCharge_User_ActivitiesRecord:false,//活动记录

    // 有序能源
    orderlyEnergy:false, //有序能源
    orderlyEnergy_Home:false, //首页
    orderlyEnergy_SchedulingModel:false,//调度模型

    chargeConnect: false, // 充电互联
    Public_connect_auth: false, // 互联授权
    connectPark_authorizedStation: false, // 授权电站
    connectPark_authPile: false, // 授权桩
    connectPark_differenceOrder: false, // 异常订单

    // 单车充电
    Public_BicycleCharge: false, // 两轮车充电
    Public_BicycleCharge_StationManage: false, // 站点管理
    Public_BicycleCharge_Station: false, // 充电站
    Public_BicycleCharge_Area: false, // 充电区
    Public_BicycleCharge_Pile: false, // 充电桩
    Public_BicycleCharge_Door: false, // 门禁设备
    Public_BicycleCharge_BillingManage: false, // 计费管理
    Public_BicycleCharge_ChargingSet: false, // 计费标准
    Public_BicycleCharge_MonthRuleSet: false, // 包月计费
    Public_BicycleCharge_Rule_Park: false, // 停车计费
    Public_BicycleCharge_OrderManage: false, // 订单管理
    Public_BicycleCharge_ChargeOrder: false, // 在充订单
    Public_BicycleCharge_OrderRecord: false, // 订单记录
    Public_BicycleCharge_Order_Park: false, // 停车订单
    Public_BicycleCharge_AccountSplitting:false,//分账订单
    Public_BicycleCharge_FinancialStatistics:false, //统计分析
    Public_bicycleCharge_chargeDaily:false, //充电统计
    Public_bicycleCharge_shareDaily:false,//分账统计
    Public_bicycleCharge_shareArrivalDaily:false,//分账到账统计
    Public_BicycleCharge_UserManage: false,// 用户管理
    Public_BicycleCharge_User: false,// 用户管理
    Public_BicycleCharge_User_Park: false,// 车辆管理
    Public_BicycleCharge_MonthCardRecord: false,// 包月订单
    Public_BicycleCharge_PhysicalCard: false,// 实体卡管理
    Public_BicycleCharge_ElectronicsCard: false,// 电子卡管理
    Public_BicycleCharge_User_AccessRecord: false,// 出入记录
    park_videoPort: false,
    park_Order_Picture_Storage: false, // 订单图片存储，
    Public_BicycleCharge_AlarmInformation: false, // 告警消息
    Public_BicycleCharge_deviceMsg: false, // 告警记录


    // parkCarportManage: false,  // 车位管理
    // parkCarportManage_carportManage: false, // 车位管理
    // parkCarportOrder_carportOrder: false, // 车位订单
    // parkCarportRepport_carportReport: false, // 统计报表
    // parkCarportUsers_carportUsers: false, // 用户管理
};

//集团权限
const AUTH_ID_UNION = {
    my_park_vehicle: 959, //新能源车概况
	businessOrder: 105, //业务订单
	data_Center: 114, //概况-数据中心
    data_Center_Child: 828, // 数据中心子菜单
	dataCenter_RemoteOpeningGroup: 670, //车道监控
	businessOrder_Cars: 208, //在场车辆
	businessOrder_Orders: 104, //订单记录
	businessOrder_Poles: 106, //抬杆记录
	businessOrder_Income: 358, //交易记录
	businessOrder_Expense: 359, //支出记录
	businessOrder_Reduce: 361, //减免记录
	businessOrder_Discount: 508, //抵扣记录
	member: 212, //会员
	member_MonthVIP: 214, //月卡会员
	member_Refill: 520, //月卡续费记录
	member_BlackList: 215, //黑名单管理
	member_WhiteList: 360, //内部车管理
	member_PrepayCardVIP: 363, //车场储值卡会员
	unionPrepayCard_VIP: 622, //集团储值卡
	unionPrepayCard_Trade: 623, //集团储值卡余额变更
	unionPrepayCard_Use: 624, //集团储值卡使用记录
	systemSetting: 238, //系统设置
	systemSetting_Company: 240, //企业信息
	systemSetting_Account: 247, //账户信息
    systemSetting_Park:243,//车场管理 一级菜单
    systemSetting_Park_Page:969,//车场管理 二级菜单
    systemParkManage_parkManagePage_4g: 970, //纯云车场管理
	systemSetting_DevelopmentConfig: 611, //开发配置
	systemSetting_HR: 239, //账号管理
	systemSetting_EmployeeManage: 246, //员工管理
	systemSetting_MessageNtification: 483, //消息通知
	systemSetting_RoleManage: 245, //账号角色
	systemSetting_LogsManage: 244, //日志管理
	systemSetting_LogsOperates: 284, //操作日志管理
	strategicAnalysis: 219, //决策分析
	strategicAnalysis_DailyReport: 319, //封闭车场订单统计-车场日报
	strategicAnalysis_MonthReport: 320,
	strategicAnalysis_CollectorDailyReport: 610, //集团收费员日报
	strategicAnalysis_CollectorMonthReport: 609, //集团收费员月报
	UnionPrepayCardRenew: 636, //储值卡续费统计
	strategicAnalysis_DailyParkReport: 322, //车场日报
	strategicAnalysis_ShopReport: 617, //商户统计
	strategicAnalysis_MonthParkReport: 618, //车场月报
	strategicAnalysis_MonthRenewDetail: 619, //月卡续费明细
	flow_DailyReport: 627, // 车场流量日报
	flow_MonthReport: 628, // 车场流量月报
	centerMonitor: 310, //中央监控
	unionValueAddedService: 381, //平台服务
	unionValueAddedService_Sms: 382, //短信服务
	unionValueAddedService_Screen: 423, //大屏
	unionValueAddedService_ShopApp: 424, //商户公众号
	unionValueAddedService_Program: 425, //小程序收费
	unionValueAddedService_ParkApp: 426, //车场公众号
	unionValueAddedService_Video: 491, //语音
	unionValueAddedService_Cam_Video: 703, //视频VIP
	unionValueAddedService_Car_Distinguish: 756, //手动车牌识别
  union_videoPort: 833, // 视频接口
  union_Order_Picture_Storage: 875, //订单图片存储
}
const showUnionItem_const = {
	businessOrder: false,
	data_Center: false, //概况-数据中心
    data_Center_Child: true,
    my_park_vehicle: false, //新能源车概况
    systemSetting_Park:false,//车场管理 一级菜单
    systemSetting_Park_Page:false,//车场管理 二级菜单
    systemParkManage_parkManagePage_4g:false,//纯云车场管理
	dataCenter_RemoteOpeningGroup: false, //车道监控
	businessOrder_Cars: false,
	businessOrder_Orders: false,
	businessOrder_Poles: false,
	businessOrder_Income: false, //交易记录
	businessOrder_Expense: false, //支出记录
	businessOrder_Reduce: false, //减免记录
	businessOrder_Discount: false, //抵扣记录
	member: false,
	member_MonthVIP: false,
	member_Refill: false, //月卡续费记录
	member_BlackList: false,
	member_WhiteList: false,
	member_PrepayCardVIP: false,
	unionPrepayCard_VIP: false,
	unionPrepayCard_Trade: false, //集团储值卡余额变更
	unionPrepayCard_Use: false, //集团储值卡使用记录
	systemSetting: false,
	systemSetting_Account: false,
	systemSetting_Company: false,
	systemSetting_DevelopmentConfig: false,
	systemSetting_EmployeeManage: false,
	systemSetting_RoleManage: false,
	systemSetting_MessageNtification: false,
	systemSetting_HR: false,
	systemSetting_LogsOperates: false,
	systemSetting_LogsManage: false,
	strategicAnalysis: false,
	strategicAnalysis_DailyReport: false,
	strategicAnalysis_MonthReport: false,
	strategicAnalysis_DailyParkReport: false, //车场日报
	strategicAnalysis_ShopReport: false, //商户统计
	strategicAnalysis_MonthParkReport: false, //车场月报
	strategicAnalysis_MonthRenewDetail: false, //月卡续费明细
	strategicAnalysis_CollectorDailyReport: false, //集团收费员日报
	strategicAnalysis_CollectorMonthReport: false, //集团收费员月报
	UnionPrepayCardRenew: false, //储值卡续费统计
	flow_DailyReport: false,
	flow_MonthReport: false,
	centerMonitor: false,
	unionValueAddedService: false, //平台服务
	unionValueAddedService_Sms: false, //短信服务
	unionValueAddedService_Screen: false, //大屏
	unionValueAddedService_ShopApp: false, //商户公众号
	unionValueAddedService_Program: false, //小程序收费
	unionValueAddedService_ParkApp: false, //车场公众号
	unionValueAddedService_Video: false, //语音
	unionValueAddedService_Cam_Video: false, //视频VIP
	unionValueAddedService_Car_Distinguish: false, //手动车牌识别
  union_videoPort: false,
  union_Order_Picture_Storage: false, //订单图片存储
}

const showShopItem_const = {
	shop: false,
	fixCode: false,
	ticket: false,
	ticketManage: false,
	shopRecharge: false,
	shopManagementShopAnalysis: false,
    shopMerchantAuthor:false,
	member: false,
	shopRole: false,
	shopMember: false,
	purchaseManage: false,
	shopPrintManage: false,
}

const AUTH_ID_SHOP = {
	shop: 325,
	fixCode: 331,
	ticket: 327,
	ticketManage: 330,
	shopManagementShopAnalysis: 487,
    shopMerchantAuthor: 9627,
	// shopRecharge: 328,
	shopRecharge: 329,
	member: 333,
	shopRole: 335,
	shopMember: 334,
	purchaseManage: 452,
	shopPrintManage: 514,
}

const showBossItem_const = {
	systemSetting_UnionManage: true,
	systemSetting_ParkManage: true,
}

/**
 * @date:********
 * @description:服务商权限
 *
 */
const AUTH_ID_SERVER = {
    my_account:379,
    operator_manage:376,
    park_manage:367,
    serveParkManage_parkManagePage_4g: 680,
    server_park_manage:377,
    server_4gPark_manage:680,
    Data_SIM_Management:1091,//SIM卡管理
    Data_SIM_Card:1092,//流量卡
    my_park_vehicle:957, //新能源车概况
    Public_CarCharge: 684, // 汽车运营管理
    Public_CarCharge_CalculaterManage: 685, // 计费管理
    Public_CarCharge_Rule: 686, // 充电费
    Public_CarCharge_OrderRule: 851, // 预约费
    Public_CarCharge_SeatRule: 852, // 占位费
    Public_CarCharge_StationManage: 796, // 电站管理
    Public_CarCharge_Station: 797, // 电站
    Public_CarCharge_Area: 798, // 区域
    Public_CarCharge_ParkingLot: 799, // 车位
    Public_CarCharge_Device: 687, // 设备管理
    Public_CarCharge_DeviceList: 688, // 设备列表
    Public_CarCharge_Pile: 689, // 充电桩
    Public_CarCharge_Camera: 800, // 相机
    Public_CarCharge_GroundLock: 801, // 地锁
    Public_CarCharge_Monitor:1072,//监控
    Public_CarCharge_Order: 690, // 订单管理
    Public_CarCharge_Order_Charge: 691, // 在充订单
    Public_CarCharge_Order_Record: 692, // 订单记录
    Public_CarCharge_Order_Share: 853, // 分账订单
    Public_CarCharge_Order_Order: 866, // 占位订单
    Public_CarCharge_Order_Arrearage:898, //欠费记录
    Public_CarCharge_Order_Seat: 867, // 预约订单
    Public_CarCharge_Order_Reduction: 712, // 减免订单
    Public_CarCharge_Order_ReductionSummary: 713, // 减免汇总
    Public_CarCharge_Statistics: 695, // 统计分析
    Public_CarCharge_Statistics_Charge: 696, // 充电统计
    Public_CarCharge_Statistics_Share: 710, // 分账统计
    Public_CarCharge_Statistics_ShareArrival: 711, // 分账到账统计
    Public_CarCharge_Store: 749, // 库存管理
    Public_CarCharge_Store_Operate: 750, // 设备出库
    Public_CarCharge_Store_FlowRecord: 751, // 流转记录
    Public_CarCharge_UserManage:1051,//用户管理
    Public_CarCharge_User:1052,//用户管理
    Public_CarCharge_ElectronicsCard:1053, //电子卡管理
    Public_CarCharge_PhysicalCard:1054,//实体卡管理
    Public_CarCharge_PhysicalVIN:1204,//VIN码
    Public_CarCharge_cardRecord:1205,//卡记录
    Public_CarCharge_User_ChargingActivities:1055, //充电活动
    Public_CarCharge_User_ActivitiesRecord:1056,//活动记录

    server_chargeOperate_manage: 684, // 充电运营管理
    server_chargeOperate_calculaterManage: 685, // 计费管理
    server_chargeOperate_chargingSet: 686, // 充电计费设置
    server_chargeOperate_equipment: 687, // 电站设备设置
    server_chargeOperate_stationData: 688, // 充电站
    server_chargeOperate_pileData: 689, // 充电桩
    server_chargeOperate_orderManage: 690, // 订单管理
    server_chargeOperate_chargeOrder: 691, // 在充订单
    server_chargeOperate_orderRecord: 692, // 订单记录
    server_chargeOperate_shareOrder: 773, // 分账订单
    server_chargeOperate_reductionOrder: 712, // 减免订单
    server_chargeOperate_reductionSummary: 713, // 减免汇总
    server_chargeOperate_FinancialStatistics: 695, // 统计分析
    server_chargeOperate_chargeDaily: 696, // 充电统计
    server_chargeOperate_ShareDaily: 710, // 分账统计
    server_chargeOperate_ShareArrivalDaily: 711, // 分账到账统计

    server_chargeOperate_Store: 749, // 库存管理
    server_chargeOperate_StoreOperate: 750, // 设备出库
    server_chargeOperate_FlowRecord: 751, // 流转记录

    // 有序能源
    orderlyEnergy:920, //有序能源
    orderlyEnergy_Home:921, //首页
    orderlyEnergy_SchedulingModel:922,//调度模型

    chargeConnect: 1117, // 充电互联
    Public_connect_auth: 1118, // 互联授权
    connectServer_authorizedStation: 1119, // 授权电站
    connectServer_authPile: 1120, // 授权充电桩
    connectServer_differenceOrder: 1121, // 异常订单


    // 单车充电
    Public_BicycleCharge: 819, // 两轮车充电
    Public_BicycleCharge_StationManage: 820, // 站点管理
    Public_BicycleCharge_Station: 821, // 充电站
    Public_BicycleCharge_Area: 915, // 充电区
    Public_BicycleCharge_Pile: 822, // 充电桩
    Public_BicycleCharge_Door: 939, // 门禁设备
    Public_BicycleCharge_BillingManage: 823, // 计费管理
    Public_BicycleCharge_ChargingSet: 824, // 计费标准
    Public_BicycleCharge_MonthRuleSet: 907, // 包月计费
    Public_BicycleCharge_Rule_Park: 940, // 停车计费
    Public_BicycleCharge_OrderManage: 825, // 订单管理
    Public_BicycleCharge_ChargeOrder: 826, // 在充订单
    Public_BicycleCharge_OrderRecord: 827, // 订单记录
    Public_BicycleCharge_AccountSplitting:931,//分账订单
    Public_BicycleCharge_Order_Park: 941, // 停车订单
    Public_BicycleCharge_FinancialStatistics:840,//统计分析
    Public_bicycleCharge_chargeDaily:841, //充电统计
    Public_bicycleCharge_shareDaily:842,//分账统计
    Public_bicycleCharge_shareArrivalDaily:843,//分账到账统计
    Public_BicycleCharge_UserManage: 902,// 用户管理
    Public_BicycleCharge_User: 908,// 用户管理
    Public_BicycleCharge_User_Park: 942,// 车辆管理
    Public_BicycleCharge_MonthCardRecord: 910,// 包月订单
    Public_BicycleCharge_PhysicalCard: 903,// 充电卡管理
    Public_BicycleCharge_ElectronicsCard: 1032,// 电子卡管理
	Public_BicycleCharge_User_AccessRecord: 943,// 出入记录
    Public_BicycleCharge_AlarmInformation: 1148, // 告警信息
    Public_BicycleCharge_deviceMsg: 1149, // 设备消息



    // server_chargeOperate_dailyReportManagement: 714, // 财务管理
    // server_chargeOperate_DailyReportManagement: 715, // 日报管理
    // server_chargeOperate_WithdrawalDetails: 716, // 提现明细
    // server_chargeOperate_AccountDetails: 717, // 账户明细

    subservice_manage:378,
    serverTrade:369,
    serverTrade_MoneyRecord:374,
    serverStatistics:370,
    serverStatistics_NewUnionProfit:375,
    serverResources:371,
    serverResources_RoleManage:373,
    serverResources_EmployeeManage:372,
    serverResources_MessageNtification:484,
    serverValueAddedService:427,//平台服务
    Public_AddServices_MarketingAuthorization:9621, //销售授权
    Public_AddServices_ServicePrice:9622,//服务价格
    serverValueAddedService_Sms:428, //短信服务
    serverValueAddedService_Screen:429, //大屏
    serverValueAddedService_ShopApp:430, //商户公众号
    serverValueAddedService_Program:431, //小程序收费
    serverValueAddedService_ParkApp:432, //车场公众号
    Services_Cam_Video:702, //视频VIP
    Services_Car_Distinguish:755, //手动车牌识别
    serverValueAddedService_Video:490, //语音
    serve_videoPort: 832, // 视频接口
    server_Order_Picture_Storage: 874, // 订单图片存储
}
const showServerItems_const = {
    my_account:false,
    operator_manage:false,
    park_manage:false,
    Data_SIM_Management:false,//SIM卡管理
    Data_SIM_Card:false,//流量卡
    server_park_manage:false,
    server_4gPark_manage:false,
    my_park_vehicle: false,  //新能源车概况
    Public_CarCharge: false, // 汽车运营管理
    Public_CarCharge_CalculaterManage: false, // 计费管理
    Public_CarCharge_Rule: false, // 充电费
    Public_CarCharge_OrderRule: false, // 预约费
    Public_CarCharge_SeatRule: false, // 占位费
    Public_CarCharge_StationManage: 796, // 电站管理
    Public_CarCharge_Station: false, // 电站
    Public_CarCharge_Area: false, // 区域
    Public_CarCharge_ParkingLot: false, // 车位
    Public_CarCharge_Device: false, // 设备管理
    Public_CarCharge_DeviceList: false, // 设备列表
    Public_CarCharge_Pile: false, // 充电桩
    Public_CarCharge_Camera: false, // 相机
    Public_CarCharge_GroundLock: false, // 地锁
    Public_CarCharge_Monitor:false,//监控
    Public_CarCharge_Order: false, // 订单管理
    Public_CarCharge_Order_Charge: false, // 在充订单
    Public_CarCharge_Order_Record: false, // 订单记录
    Public_CarCharge_Order_Share: false, // 分账订单
    Public_CarCharge_Order_Order: false, // 占位订单
    Public_CarCharge_Order_Arrearage:false, //欠费记录
    Public_CarCharge_Order_Seat: false, // 预约订单
    Public_CarCharge_Order_Reduction: false, // 减免订单
    Public_CarCharge_Order_ReductionSummary: false, // 减免汇总
    Public_CarCharge_Statistics: false, // 统计分析
    Public_CarCharge_Statistics_Charge: false, // 充电统计
    Public_CarCharge_Statistics_Share: false, // 分账统计
    Public_CarCharge_Statistics_ShareArrival: false, // 分账到账统计
    Public_CarCharge_Store: false, // 库存管理
    Public_CarCharge_Store_Operate: false, // 设备出库
    Public_CarCharge_Store_FlowRecord: false, // 流转记录
    Public_CarCharge_UserManage:false,
    Public_CarCharge_User:false,//用户管理
    Public_CarCharge_ElectronicsCard:false, //电子卡管理
    Public_CarCharge_PhysicalCard:false,//实体卡管理
    Public_CarCharge_PhysicalVIN:false,//VIN码
    Public_CarCharge_cardRecord:false,//卡记录
    Public_CarCharge_User_ChargingActivities:false, //充电活动
    Public_CarCharge_User_ActivitiesRecord:false,//活动记录

    //有序能源
    orderlyEnergy:false, //有序能源
    orderlyEnergy_Home:false, //首页
    orderlyEnergy_SchedulingModel:false,//调度模型

    chargeConnect: false, // 充电互联
    Public_connect_auth: false, // 互联授权
    connectServer_authorizedStation: false, // 授权电站
    connectServer_authPile: false, // 授权充电桩
    connectServer_differenceOrder: false, // 异常订单

    // 单车充电
    Public_BicycleCharge: false, // 两轮车充电
    Public_BicycleCharge_StationManage: false, // 站点管理
    Public_BicycleCharge_Station: false, // 充电站
    Public_BicycleCharge_Area: false, // 充电区
    Public_BicycleCharge_Pile: false, // 充电桩
	Public_BicycleCharge_Door: false, // 门禁设备
    Public_BicycleCharge_BillingManage: false, // 计费管理
    Public_BicycleCharge_ChargingSet: false, // 计费标准
    Public_BicycleCharge_MonthRuleSet: false, // 包月计费
	Public_BicycleCharge_Rule_Park: false, // 停车计费
    Public_BicycleCharge_OrderManage: false, // 订单管理
    Public_BicycleCharge_ChargeOrder: false, // 在充订单
    Public_BicycleCharge_OrderRecord: false, // 订单记录
    Public_BicycleCharge_AccountSplitting:false,//分账订单
	Public_BicycleCharge_Order_Park: false, // 停车订单
    Public_BicycleCharge_FinancialStatistics:false,//统计分析
    Public_bicycleCharge_chargeDaily:false,//充电统计
    Public_bicycleCharge_shareDaily:false,//分账统计
    Public_bicycleCharge_shareArrivalDaily:false,//分账到账统计
    Public_BicycleCharge_UserManage: false,// 用户管理
    Public_BicycleCharge_User: false,// 用户管理
	Public_BicycleCharge_User_Park: false,// 车辆管理
    Public_BicycleCharge_MonthCardRecord: false,// 包月订单
    Public_BicycleCharge_PhysicalCard: false,// 充电卡管理
    Public_BicycleCharge_ElectronicsCard: false,// 电子卡管理
	Public_BicycleCharge_User_AccessRecord: false,// 出入记录
    Public_BicycleCharge_AlarmInformation: false, // 告警信息
    Public_BicycleCharge_deviceMsg: false, // 设备消息

    subservice_manage:false,
    serverTrade:false,
    serverTrade_MoneyRecord:false,
    serverStatistics:false,
    serverStatistics_NewUnionProfit:false,
    serverResources:false,
    serverResources_RoleManage:false,
    serverResources_EmployeeManage:false,
    serverResources_MessageNtification:false,
    serverValueAddedService:false,//平台服务
    Public_AddServices_MarketingAuthorization:false,//销售授权
    Public_AddServices_ServicePrice:false,//服务价格
    serverValueAddedService_Sms:false, //短信服务
    serverValueAddedService_Screen:false, //大屏
    serverValueAddedService_ShopApp:false, //商户公众号
    serverValueAddedService_Program:false, //小程序收费
    serverValueAddedService_ParkApp:false, //车场公众号
    serverValueAddedService_Video:false, //音视频
    Services_Cam_Video:false, //视频VIP
    Services_Car_Distinguish:false, //手动车牌识别
    serve_videoPort: false,
    server_Order_Picture_Storage: false, //订单图片存储
}
/*----------end-------------*/

/**
 * @date:********
 * @description:厂商权限
 *
 */
const AUTH_ID_CITY = {
	// city_account: 383,
    my_account:383,
	citySerManage: 384,
	citySerManage_serManagePage: 392,
	cityUnionManage: 385,
    my_park_vehicle: 958, //新能源车概况
    Data_SIM_Management:1093,//SIM卡管理
    Data_SIM_Card:1094,//流量卡
	cityUnionManage_unionManagePage: 393,
	cityParkManage: 386,
	cityParkManage_parkManagePage: 394,
	cityParkManage_parkManagePage_4g: 414,
	cityParkManage_materielPage: 399,
	city4GCameraManage_cameraPage: 481,
    cityDeviceManage_equipmentModel:1037,//设备型号
    cityDeviceManage_productModel:1035,//产品型号
    city_DeviceManage_carPile:1137,//汽车充电桩
    city_DeviceManage_unbind:1138,//蓝牙桩解绑
	cityTrade: 387,
	cityTrade_MoneyRecord: 415,
	cityTrade_MoneyPay: 416,
	cityStatistics_NewUnionProfit: 388,
	cityResources: 389,
	cityResources_RoleManage: 396,
	cityResources_EmployeeManage: 395,
	cityResources_MessageNtification: 485,
	citySettingManage: 390,
	citySettingManage_settingPage: 398,
	citySettingManage_authPage: 404,
	citySettingYunPrivate_Config: 620,
	city_systemMange: 391,
	cityAddService: 417,
	cityAddService_sms: 418,
    Public_AddServices_MarketingAuthorization:9619, //销售授权
    Public_AddServices_ServicePrice:9620,//服务价格
	cityAddService_screen: 419,
	cityAddService_shopApp: 420,
	cityAddService_program: 421,
	cityAddService_parkApp: 422,
	cityAddService_openPlatform: 457,
	cityAddService_video: 489,
	cityAddService_face: 512,
	city_AddService_Cam_Video: 700,
	city_AddService_Car_Distinguish: 754, //手动车牌识别
	cityDeviceManage: 502,
	cityDeviceManage_deviceManage: 503,
	cityfour_serve: 519,
	cityDeviceManage_smartScreen: 591,
    cityAdManage_http_Manage: 1194,//联网模块
    city_DeviceManage_bicyclePileUpdate:1195,//升级包
	// cityCharging_Operation: 716,
	// cityCharging_Operation_repertory: 717,

	// 充电桩
    Public_CarCharge: 716, // 汽车运营管理
    Public_CarCharge_CalculaterManage: 734, // 计费管理
    Public_CarCharge_Rule: 736, // 充电费
    Public_CarCharge_OrderRule: 854, // 预约费
    Public_CarCharge_SeatRule: 855, // 占位费
    Public_CarCharge_StationManage: 804, // 电站管理
    Public_CarCharge_Station: 805, // 电站
    Public_CarCharge_Area: 806, // 区域
    Public_CarCharge_ParkingLot: 807, // 车位
    Public_CarCharge_Device: 735, // 设备管理
    Public_CarCharge_DeviceList: 737, // 设备列表
    Public_CarCharge_Pile: 738, // 充电桩
    Public_CarCharge_Camera: 802, // 相机
    Public_CarCharge_GroundLock: 803, // 地锁
    Public_CarCharge_Monitor:1071,//监控
    Public_CarCharge_Order: 739, // 订单管理
    Public_CarCharge_Order_Charge: 740, // 在充订单
    Public_CarCharge_Order_Record: 741, // 订单记录
    Public_CarCharge_Order_Share: 856, // 分账订单
    Public_CarCharge_Order_Order: 868, // 占位订单
    Public_CarCharge_Order_Arrearage:897, //欠费记录
    Public_CarCharge_Order_Seat: 869, // 预约订单
    Public_CarCharge_Order_Reduction: 742, // 减免订单
    Public_CarCharge_Order_ReductionSummary: 747, // 减免汇总
    Public_CarCharge_Statistics: 743, // 统计分析
    Public_CarCharge_Statistics_Charge: 744, // 充电统计
    Public_CarCharge_Statistics_Share: 745, // 分账统计
    Public_CarCharge_Statistics_ShareArrival: 746, // 分账到账统计
    Public_CarCharge_Store: 717, // 库存管理
    Public_CarCharge_Store_Operate: 767, // 设备出库
    Public_CarCharge_Store_FlowRecord: 768, // 流转记录
    Public_CarCharge_UserManage:1057,//用户管理
    Public_CarCharge_User:1058,//用户管理
    Public_CarCharge_ElectronicsCard:1059, //电子卡管理
    Public_CarCharge_PhysicalCard:1060,//实体卡管理
    Public_CarCharge_PhysicalVIN:1196,//VIN码
    Public_CarCharge_cardRecord:1197,//卡记录
    Public_CarCharge_User_ChargingActivities:1061, //充电活动
    Public_CarCharge_User_ActivitiesRecord:1062,//活动记录
    Public_BicycleCharge_AlarmInformation: 1146, // 告警信息
    Public_BicycleCharge_deviceMsg: 1147, // 设备消息
    //有序能源
    orderlyEnergy:917, //有序能源
    orderlyEnergy_Home:918, //首页
    orderlyEnergy_SchedulingModel:919,//调度模型

    chargeConnect: 1112, //充电互联
    Public_connect_auth: 1113, //互联授权
    connectCity_authorizedStation: 1114, //授权电站
    connectCity_authPile: 1115, //授权充电桩
    connectCity_differenceOrder: 1116, //差异订单

    // 单车充电
    Public_BicycleCharge: 809, // 两轮车充电
    Public_BicycleCharge_StationManage: 810, // 站点管理
    Public_BicycleCharge_Station: 811, // 充电站
    Public_BicycleCharge_Area: 916, // 充电区
    Public_BicycleCharge_Pile: 812, // 充电桩
    Public_BicycleCharge_Door: 944, // 门禁设备
    Public_BicycleCharge_BillingManage: 813, // 计费管理
    Public_BicycleCharge_ChargingSet: 814, // 计费标准
    Public_BicycleCharge_MonthRuleSet: 911, // 包月计费
    Public_BicycleCharge_Rule_Park: 945, // 停车计费
    Public_BicycleCharge_OrderManage: 815, // 订单管理
    Public_BicycleCharge_ChargeOrder: 816, // 在充订单
    Public_BicycleCharge_OrderRecord: 817, // 订单记录
    Public_BicycleCharge_AccountSplitting:929,//分账订单
    Public_BicycleCharge_Order_Park: 946, // 停车订单
    Public_BicycleCharge_FinancialStatistics: 845,//统计分析
    Public_bicycleCharge_chargeDaily:846,//充电统计
    Public_bicycleCharge_shareDaily:847,//分账统计
    Public_bicycleCharge_shareArrivalDaily:848,//分账到账统计
    Public_BicycleCharge_UserManage: 904,// 用户管理
    Public_BicycleCharge_User: 912,// 用户管理
    Public_BicycleCharge_User_Park: 947,// 车辆管理
    Public_BicycleCharge_MonthCardRecord: 913,// 包月订单
    Public_BicycleCharge_PhysicalCard: 905,// 充电卡管理
    Public_BicycleCharge_ElectronicsCard: 1033,// 电子卡管理
	Public_BicycleCharge_User_AccessRecord: 948,// 出入记录
    managementOfJointStation: 1076, // 电站投建
    managementOfJointStation_depotManagement: 1077, // 电站投资
    testingOfEquipment:1086,//设备测试
    testingOfEquipment_chargingTest:1087,//充电测试
    testingOfEquipment_testRecord:1088,//测试记录

    city_videoPort: 831, // 视频接口
    city_Order_Picture_Storage: 873, // 订单图片存储
  development: 1032, // 开发配置
}
const showCityItems_const = {
	// city_account: false,
    my_account:false,
	citySerManage: false,
	citySerManage_serManagePage: false,
	cityUnionManage: false,
	cityUnionManage_unionManagePage: false,
	cityParkManage: false,
    my_park_vehicle: false, //新能源车概况
    Data_SIM_Management:false,//SIM卡管理
    Data_SIM_Card:false,//流量卡
	cityParkManage_parkManagePage: false,
	cityParkManage_parkManagePage_4g: false,
	cityParkManage_materielPage: false,
	city4GCameraManage_cameraPage: false,
    cityDeviceManage_productModel:false,//产品型号
    cityDeviceManage_equipmentModel:false,//设备型号
    city_DeviceManage_carPile:false,//汽车充电桩
    city_DeviceManage_unbind:false,//蓝牙桩解绑
    city_DeviceManage_bicyclePileUpdate:false,
	cityTrade: false,
	cityTrade_MoneyRecord: false,
	cityTrade_MoneyPay: false,
	cityStatistics_NewUnionProfit: false,
	cityResources: false,
	cityResources_RoleManage: false,
	cityResources_EmployeeManage: false,
	cityResources_MessageNtification: false,
	citySettingManage: false,
	citySettingManage_settingPage: false,
	citySettingManage_authPage: false,
	citySettingYunPrivate_Config: false,
	city_systemMange: false,
	cityAddService: false,
	cityAddService_sms: false,
    Public_AddServices_MarketingAuthorization:false, //销售授权
    Public_AddServices_ServicePrice:false,//服务价格
	cityAddService_screen: false,
	cityAddService_shopApp: false,
	cityAddService_program: false,
	cityAddService_parkApp: false,
	cityAddService_openPlatform: false,
	cityAddService_video: false,
	cityAddService_face: false,
	city_AddService_Cam_Video: false,
	city_AddService_Car_Distinguish: false, //手动车牌识别
	cityDeviceManage: false,
	cityDeviceManage_deviceManage: false,
    cityfour_serve: false,
	cityDeviceManage_smartScreen: false,
    cityAdManage_http_Manage:false,

    Public_CarCharge: false, // 汽车运营管理
    Public_CarCharge_CalculaterManage: false, // 计费管理
    Public_CarCharge_Rule: false, // 充电费
    Public_CarCharge_OrderRule: false, // 预约费
    Public_CarCharge_SeatRule: false, // 占位费
    Public_CarCharge_StationManage: false, // 电站管理
    Public_CarCharge_Station: false, // 电站
    Public_CarCharge_Area: false, // 区域
    Public_CarCharge_ParkingLot: false, // 车位
    Public_CarCharge_Device: false, // 设备管理
    Public_CarCharge_DeviceList: false, // 设备列表
    Public_CarCharge_Pile: false, // 充电桩
    Public_CarCharge_Camera: false, // 相机
    Public_CarCharge_GroundLock: false, // 地锁
    Public_CarCharge_Monitor:false,//监控
    Public_CarCharge_Order: false, // 订单管理
    Public_CarCharge_Order_Charge: false, // 在充订单
    Public_CarCharge_Order_Record: false, // 订单记录
    Public_CarCharge_Order_Share: false, // 分账订单
    Public_CarCharge_Order_Order: false, // 占位订单
    Public_CarCharge_Order_Arrearage:false,//欠费记录
    Public_CarCharge_Order_Seat: false, // 预约订单
    Public_CarCharge_Order_Reduction: false, // 减免订单
    Public_CarCharge_Order_ReductionSummary: false, // 减免汇总
    Public_CarCharge_Statistics: false, // 统计分析
    Public_CarCharge_Statistics_Charge: false, // 充电统计
    Public_CarCharge_Statistics_Share: false, // 分账统计
    Public_CarCharge_Statistics_ShareArrival: false, // 分账到账统计
    Public_CarCharge_Store: false, // 库存管理
    Public_CarCharge_Store_Operate: false, // 设备出库
    Public_CarCharge_Store_FlowRecord: false, // 流转记录
    Public_CarCharge_UserManage:false,
    Public_CarCharge_User:false,//用户管理
    Public_CarCharge_ElectronicsCard:false, //电子卡管理
    Public_CarCharge_PhysicalCard:false,//实体卡管理
    Public_CarCharge_PhysicalVIN:false,//VIN码
    Public_CarCharge_cardRecord:false,//卡记录
    Public_CarCharge_User_ChargingActivities:false, //充电活动
    Public_CarCharge_User_ActivitiesRecord:false,//活动记录
    Public_BicycleCharge_AlarmInformation: false, // 告警信息
    Public_BicycleCharge_deviceMsg: false, // 设备消息
    //有序能源
    orderlyEnergy:false, //有序能源
    orderlyEnergy_Home:false, //首页
    orderlyEnergy_SchedulingModel:false,//调度模型

    chargeConnect: false, //充电互联
    Public_connect_auth: false, //互联授权
    connectCity_authorizedStation: false, //授权电站
    connectCity_authPile: false, //授权充电桩
    connectCity_differenceOrder: false, //差异订单

    // 单车充电
    Public_BicycleCharge: false, // 两轮车充电
    Public_BicycleCharge_StationManage: false, // 站点管理
    Public_BicycleCharge_Station: false, // 充电站
    Public_BicycleCharge_Area: false, // 充电区
    Public_BicycleCharge_Pile: false, // 充电桩
	Public_BicycleCharge_Door: false, // 门禁设备
    Public_BicycleCharge_BillingManage: false, // 计费管理
    Public_BicycleCharge_ChargingSet: false, // 计费标准
    Public_BicycleCharge_MonthRuleSet: false, // 包月计费
	Public_BicycleCharge_Rule_Park: false, // 停车计费
    Public_BicycleCharge_OrderManage: false, // 订单管理
    Public_BicycleCharge_ChargeOrder: false, // 在充订单
    Public_BicycleCharge_OrderRecord: false, // 订单记录
    Public_BicycleCharge_AccountSplitting:false,//分账订单
    Public_BicycleCharge_Order_Park: false, // 停车订单
    Public_BicycleCharge_FinancialStatistics:false, //统计分析
    Public_bicycleCharge_chargeDaily:false,//充电统计
    Public_bicycleCharge_shareDaily:false,//分账统计
    Public_bicycleCharge_shareArrivalDaily:false,//分账到账统计
    Public_BicycleCharge_UserManage: false,// 用户管理
    Public_BicycleCharge_User: false,// 用户管理
	Public_BicycleCharge_User_Park: false,// 车辆管理
    Public_BicycleCharge_MonthCardRecord: false,// 包月订单
    Public_BicycleCharge_PhysicalCard: false,// 充电卡管理
    Public_BicycleCharge_ElectronicsCard: false,// 电子卡管理
	Public_BicycleCharge_User_AccessRecord: false,// 出入记录

    managementOfJointStation: false, // 电站投建
    managementOfJointStation_depotManagement: false, // 电站投资

    city_videoPort: false, //视频接口
    city_Order_Picture_Storage: false, // 订单图片存储
  development: false,
  testingOfEquipment:false,//设备测试
  testingOfEquipment_chargingTest:false,//充电测试
  testingOfEquipment_testRecord:false,//测试记录
}
/*---------end------------------*/

/**
 * 运维平台权限
 * EOMS
 * oid:12
 */
const AUTH_ID_EOMS = {
	eomsParkManage: 515,
	eomsEventManage: 516,
	eomsEventManage_choosePark: 517,
	eomsEmployeeManage: 612,
  eomsRoleManage: 964,
}
const showEomsItems_const = {
	eomsParkManage: false,
	eomsEventManage: false,
	eomsEventManage_choosePark: false,
	eomsEmployeeManage: false,
  eomsRoleManage: false
}

/**
 * 总后台下属的权限
 * ADMIN
 * oid:15
 */
const AUTH_ID_ADMIN = {
  adminMyAccount: 954, // 首页
  adminCityManage:570,//厂商管理
  adminCityManage_cityManage:571,//厂商管理
  adminServiceProviderManage: 760,
  adminServiceProviderManage_ServiceProviderManage: 761,// 工程服务商
  cityGroupManage:634,//运营集团
  upgradeExplain:647,//升级说明
  adminCityManage_privateCloudManage:572,//私有云管理
  adminEomsManage:525,//运维平台
  adminEomsManage_eomsManage:526,//运维平台
  Data_SIM_Management:1083,//SIM卡管理
  Data_SIM_Card:1084,//流量卡
  SIM_Statistics:1095,//流量卡统计
  adminParkManage:527,//车场管理
  adminParkManage_parkManage:528,//车场管理
  adminParkManage_ActivationCode: 769,// 车场激活码
  adminParkManage_blacklist:529,//黑名单管理
  adminParkManage_whitelist:530,//白名单
  adminParkManage_depotEmployee:531,//车场员工

//   adminYunOnDuty: 9567, // 云值守
//   SecretKeyConfig: 9568, // 秘钥配置
//   Provisioning: 9569, // 服务开通

  adminAddServices:534,//平台服务
  adminAddService_sms:535,//短信服务
  adminAddService_screen:536,//大屏服务
  adminAddService_shopApp:537,//商户公众号
  adminAddService_program:539,//小程序收费
  adminAddService_parkApp:538,//车场公众号
  adminAddService_openPlatform:541,//开放接口
  adminAddService_video:542,//语音购买
  adminAddService_fourG:540,//纯云服务
  adminAddService_faceAccess:543,//人脸门禁
  adminCamVideoSever:701,//视频VIP服务
  adminCarDistinguish:753,//手动车牌识别
  adminAuthSet:544,//权限设置
  adminAuthSet_editAuth:545,//权限设置
  adminAuthSet_authManage:546,//权限管理
  adminAuthSet_authManage_city:547,//权限管理-厂商
  adminAuthSet_authManage_service:548,//权限管理-服务商
  adminAuthSet_authManage_union:549,//权限管理-集团
  adminAuthSet_authManage_park:550,//权限管理-车场
  adminAuthSet_authManage_shop:551,//权限管理-商户
  adminAuthSet_authManage_eoms:552,//权限管理-运维
  adminDeviceManage:553,//设备管理
  admin_DeviceManage_productType:1127,//产品类型
  admin_DeviceManage_productModel:1040,//产品型号
  admin_DeviceManage_equipmentModel:1041,//设备型号
  admin_DeviceManage_carPile:1128,//汽车充电桩
  admin_DeviceManage_bicyclePile:1129,//单车充电桩
  adminDeviceManage_deviceManage:554,//设备管理
  adminAdManage_smartScreen:592,//智慧屏管理
  device_manage_admin:555,//人脸设备管理
  adminAbnormalWatch:556,//异常监控
  adminAbnormalWatch_park:557,//设备管理-车场
  adminAbnormalWatch_timer:558,//设备管理-定时任务
  adminAbnormalWatch_log:560,//设备管理-日志
  adminAbnormalWatch_androidLog: 615,// 异常监控-安卓日志
  adminAbnormalWatch_shieldCarnumber: 9732,// 异常监控-屏蔽车牌查询
  statisticalAnalysis: 598,// 统计分析
  statisticalAnalysis_parkAlive: 599,// 统计分析-车场日活
  statisticalAnalysis_parkUse: 650,// 统计分析-车场使用日活
  statisticalAnalysis_parkOrder: 1030,// 统计分析-车场订单统计
  statisticalAnalysis_merchantsAlive: 600,// 统计分析-商户日活
  admin_Order_Picture_Storage: 872, // 订单图片存储
  Public_AddServices_MarketingAuthorization:9625, //销售授权
  Public_AddServices_ServicePrice:9626,//服务价格
  Public_CarCharge_Manage:954, // 新能源充电
  Public_CarCharge_ChargeData:1005,//数据仪表
  Public_CarCharge_CalculaterManage: 957,// 计费管理
  Public_CarCharge_ChargeRule: 964, // 充电费
  Public_CarCharge_OrderRule: 965, // 预约费
  Public_CarCharge_SeatRule: 966, //站位费
  Public_CarCharge_StationManage:958, //电站管理
  Public_Station: 967, //电站
  Public_CarCharge_Area: 968, // 区域
  Public_CarCharge_ParkingLot: 969, //车位
  Public_CarCharge_Device: 959, // 设备管理
  Admin_CarCharge_EuquipmentType: 970, // 设备类型
  Public_CarCharge_DeviceList: 971, // 设备列表
  Public_CarCharge_Pile: 972, //充电桩
  Public_CarCharge_Camera: 973, //相机
  Public_CarCharge_GroundLock: 974, //地锁
  Public_CarCharge_Monitor:1073,//监控
  Public_CarCharge_Order: 960, // 订单管理
  Public_CarCharge_Order_Charge: 975, // 在充订单
  Public_CarCharge_Order_Record: 976, // 订单记录
  Public_CarCharge_Order_Reduction: 977, // 减免记录
  Public_CarCharge_Order_ReductionSummary: 978, // 减免汇总
  Public_CarCharge_Order_Share: 979, // 分账订单
  Public_CarCharge_Order_Order: 980, // 预约订单
  Public_CarCharge_Order_Seat: 981, //占位订单
  Public_CarCharge_Order_Arrearage: 1006, //欠费记录
  Public_CarCharge_Statistics: 961, // 统计分析
  Public_CarCharge_Statistics_Charge: 982, // 充电统计
  Public_CarCharge_Statistics_Share: 983, // 分账统计
  Public_CarCharge_Statistics_ShareArrival: 984, // 分账到账统计
  Public_CarCharge_Store: 962, // 库存管理
  Public_CarCharge_Store_Operate: 985, // 库存运维
  Public_CarCharge_Store_FlowRecord: 986, // 流转记录
  Public_CarCharge_euquipment: 963, // 设备运维
  Public_CarCharge_Store_FirmwareManagement:1139,//固件管理
  Public_CarCharge_pilePrivManage: 1130, //私桩管理
  Public_CarCharge_UserManage:1063,//用户管理
  Public_CarCharge_User:1064,//用户管理
  Public_CarCharge_ElectronicsCard:1065, //电子卡管理
  Public_CarCharge_PhysicalCard:1066,//实体卡管理
  Public_CarCharge_User_ChargingActivities:1067, //充电活动
  Public_CarCharge_User_ActivitiesRecord:1068,//活动记录

  Public_BicycleCharge: 956, // 两轮车充电
  Public_BicycleCharge_StationManage: 991, // 站点管理
  Public_BicycleCharge_Station: 997, // 充电站
  Public_BicycleCharge_Area: 1007, // 充电区
  Public_BicycleCharge_Pile: 998, // 充电桩
  Public_BicycleCharge_Door: 1008, // 门禁设备
  Public_BicycleCharge_BillingManage: 992, // 计费管理
  Public_BicycleCharge_ChargingSet: 999, // 计费标准
  Public_BicycleCharge_MonthRuleSet: 1009, // 包月计费
  Public_BicycleCharge_Rule_Park: 1010, // 停车计费
  Public_BicycleCharge_OrderManage: 993, // 订单管理
  Public_BicycleCharge_ChargeOrder: 1000, // 在充订单
  Public_BicycleCharge_OrderRecord: 1001, // 订单记录
  Public_BicycleCharge_AccountSplitting: 1011, // 分账订单
  Public_BicycleCharge_Order_Park: 1012, // 停车订单
  Public_BicycleCharge_FinancialStatistics: 994, // 统计分析
  Public_bicycleCharge_chargeDaily: 1002, // 充电统计
  Public_bicycleCharge_shareDaily: 1003, // 分账统计
  Public_bicycleCharge_shareArrivalDaily: 1004, // 分账到账统计
  Public_BicycleCharge_UserManage: 1013, // 用户管理
  Public_BicycleCharge_User: 1014, // 用户管理 - 用户管理
  Public_BicycleCharge_User_Park: 1015, //车辆管理
  Public_BicycleCharge_PhysicalCard: 1016, // 实体卡管理
  Public_BicycleCharge_ElectronicsCard: 1034,// 电子卡管理
  Public_BicycleCharge_MonthCardRecord: 1017, // 包月订单
  Public_BicycleCharge_User_AccessRecord: 1018, // 出入记录
  Public_BicycleCharge_AlarmInformation: 1142, // 告警消息
  Public_BicycleCharge_deviceMsg: 1145, // 设备消息

  admin_interconnection: 1101, // 充电互联
  admin_interconnection_Company: 1102, //互联企业
  admin_interconnection_auth: 1103, // 互联授权
  admin_interconnection_authorizedStation: 1104, // 授权电站
  admin_interconnection_authPile: 1105, // 授权桩
  admin_interconnection_differenceOrder: 1106, // 差异订单
  admin_interconnection_connectOrderManage: 1107, // 互联接入
  admin_interconnection_stationData: 1108, // 互联电站
  admin_interconnection_pileData: 1109, // 互联充电桩
  admin_interconnection_chargeOrder: 1110, // 在充订单
  admin_interconnection_orderRecord: 1111, // 订单记录

  managementOfJointStation: 1019, // 电站投建
  managementOfJointStation_depotManagement: 1020, // 场站管理
  managementOfJointStation_contractManagement: 1021, // 合同管理
  managementOfJointStation_outboundDeliveryOrder: 1022, // 出库单
  managementOfJointStation_checkAndAccept: 1079, // 场站验收
  managementOfJointStation_deliveryStatistics: 1080, // 发货统计

  orderlyEnergy:1027, // 有序能源
  orderlyEnergy_Home: 1028, // 首页
  orderlyEnergy_SchedulingModel: 1029, //调度模型

  adminResourceManagement: 1081, // 资源管理
  adminResourceManagement_downloadCenter: 1082, // 下载中心

  healthBoard: 9730, //健康看板
  healthBoard_serveList: 9731, //服务列表
};
const showADMINItems_const = {
  adminMyAccount: false, // 首页
  adminCityManage:false,//厂商管理
  adminCityManage_cityManage:false,//厂商管理
  adminServiceProviderManage: false,
  adminServiceProviderManage_ServiceProviderManage: false,
  cityGroupManage:false,//运营集团
  upgradeExplain:false,//升级说明
  adminCityManage_privateCloudManage:false,//私有云管理
  adminEomsManage:false,//运维平台
  adminEomsManage_eomsManage:false,//运维平台
  Data_SIM_Management:false,//SIM卡管理
  Data_SIM_Card:false,//流量卡
  adminParkManage:false,//车场管理
  adminParkManage_parkManage:false,//车场管理
  adminParkManage_ActivationCode: false,// 车场激活码
  adminParkManage_blacklist:false,//黑名单管理
  adminParkManage_whitelist:false,//白名单
  adminParkManage_depotEmployee:false,//车场员工
  Public_CarCharge_Store_FirmwareManagement:false,//固件管理
//   adminYunOnDuty: false, // 云值守
//   SecretKeyConfig: false, // 秘钥配置
//   Provisioning: false, // 服务开通

  adminAddServices:false,//平台服务
  adminAddService_sms:false,//短信服务
  adminAddService_screen:false,//大屏服务
  adminAddService_shopApp:false,//商户公众号
  adminAddService_program:false,//小程序收费
  adminAddService_parkApp:false,//车场公众号
  adminAddService_openPlatform:false,//开放接口
  adminAddService_video:false,//语音购买
  adminAddService_fourG:false,//纯云服务
  adminAddService_faceAccess:false,//人脸门禁
  adminCamVideoSever:false,//视频VIP服务
  adminCarDistinguish:false,//手动车牌识别
  adminAuthSet:false,//权限设置
  adminAuthSet_editAuth:false,//权限设置
  adminAuthSet_authManage:false,//权限管理
  adminAuthSet_authManage_city:false,//权限管理-厂商
  adminAuthSet_authManage_service:false,//权限管理-服务商
  adminAuthSet_authManage_union:false,//权限管理-集团
  adminAuthSet_authManage_park:false,//权限管理-车场
  adminAuthSet_authManage_shop:false,//权限管理-商户
  adminAuthSet_authManage_eoms:false,//权限管理-运维
  adminDeviceManage:false,//设备管理
  admin_DeviceManage_productType:false,//产品类型
  admin_DeviceManage_productModel:false,//产品型号
  admin_DeviceManage_equipmentModel:false,//设备型号
  admin_DeviceManage_carPile:false,//汽车充电桩
  admin_DeviceManage_bicyclePile:false,//单车充电桩
  adminDeviceManage_deviceManage:false,//设备管理
  adminAdManage_smartScreen:false,//广告屏
  device_manage_admin:false,//人脸设备管理
  adminAbnormalWatch:false,//异常监控
  adminAbnormalWatch_park:false,//设备管理-车场
  adminAbnormalWatch_timer:false,//设备管理-定时任务
  adminAbnormalWatch_log:false,//设备管理-日志
  adminAbnormalWatch_androidLog: false,// 异常监控 - 安卓日志
  adminAbnormalWatch_shieldCarnumber: false,//
  statisticalAnalysis: false,// 统计分析
  statisticalAnalysis_parkAlive: false,// 统计分析-车场日活
  statisticalAnalysis_parkUse: false,// 统计分析-车场使用日活
  statisticalAnalysis_merchantsAlive: false,// 统计分析-商户日活
  statisticalAnalysis_parkOrder: false,// 统计分析 - 车场订单统计
  admin_Order_Picture_Storage: false, // 订单图片存储
  Public_AddServices_MarketingAuthorization:false, //销售授权
  Public_AddServices_ServicePrice:false,//服务价格
  Public_CarCharge_Manage:false, // 新能源充电
  Public_CarCharge_ChargeData:false,//数据仪表
  Public_CarCharge_CalculaterManage: false,// 计费管理
  Public_CarCharge_ChargeRule: false, // 充电费
  Public_CarCharge_OrderRule: false, // 预约费
  Public_CarCharge_SeatRule: false, //站位费
  Public_CarCharge_StationManage:false, //电站管理
  Public_Station: false, //电站
  Public_CarCharge_Area: false, // 区域
  Public_CarCharge_ParkingLot: false, //车位
  Public_CarCharge_Device: false, // 设备管理
  Admin_CarCharge_EuquipmentType: false, // 设备类型
  Public_CarCharge_DeviceList: false, // 设备列表
  Public_CarCharge_Pile: false, //充电桩
  Public_CarCharge_Camera: false, //相机
  Public_CarCharge_GroundLock: false, //地锁
  Public_CarCharge_Monitor:false,//监控
  Public_CarCharge_Order: false, // 订单管理
  Public_CarCharge_Order_Charge: false, // 在充订单
  Public_CarCharge_Order_Record: false, // 订单记录
  Public_CarCharge_Order_Reduction: false, // 减免记录
  Public_CarCharge_Order_ReductionSummary: false, // 减免汇总
  Public_CarCharge_Order_Share: false, // 分账订单
  Public_CarCharge_Order_Order: false, // 预约订单
  Public_CarCharge_Order_Seat: false, //占位订单
  Public_CarCharge_Order_Arrearage: false, //欠费记录
  Public_CarCharge_Statistics: false, // 统计分析
  Public_CarCharge_Statistics_Charge: false, // 充电统计
  Public_CarCharge_Statistics_Share: false, // 分账统计
  Public_CarCharge_Statistics_ShareArrival: false, // 分账到账统计
  Public_CarCharge_Store: false, // 库存管理
  Public_CarCharge_Store_Operate: false, // 库存运维
  Public_CarCharge_Store_FlowRecord: false, // 流转记录
  Public_CarCharge_euquipment: false, // 设备运维
  Public_CarCharge_pilePrivManage: false, // 私桩管理
  Public_CarCharge_UserManage:false,
  Public_CarCharge_User:false,//用户管理
  Public_CarCharge_ElectronicsCard:false, //电子卡管理
  Public_CarCharge_PhysicalCard:false,//实体卡管理
  Public_CarCharge_User_ChargingActivities:false, //充电活动
  Public_CarCharge_User_ActivitiesRecord:false,//活动记录
  Public_BicycleCharge: false, // 两轮车充电
  Public_BicycleCharge_StationManage: false, // 站点管理
  Public_BicycleCharge_Station: false, // 充电站
  Public_BicycleCharge_Area: false, // 充电区
  Public_BicycleCharge_Pile: false, // 充电桩
  Public_BicycleCharge_Door: false, // 门禁设备
  Public_BicycleCharge_BillingManage: false, // 计费管理
  Public_BicycleCharge_ChargingSet: false, // 计费标准
  Public_BicycleCharge_MonthRuleSet: false, // 包月计费
  Public_BicycleCharge_Rule_Park: false, // 停车计费
  Public_BicycleCharge_OrderManage: false, // 订单管理
  Public_BicycleCharge_ChargeOrder: false, // 在充订单
  Public_BicycleCharge_OrderRecord: false, // 订单记录
  Public_BicycleCharge_AccountSplitting: false, // 分账订单
  Public_BicycleCharge_Order_Park: false, // 停车订单
  Public_BicycleCharge_FinancialStatistics: false, // 统计分析
  Public_bicycleCharge_chargeDaily: false, // 充电统计
  Public_bicycleCharge_shareDaily: false, // 分账统计
  Public_bicycleCharge_shareArrivalDaily: false, // 分账到账统计
  Public_BicycleCharge_UserManage: false, // 用户管理
  Public_BicycleCharge_User: false, // 用户管理 - 用户管理
  Public_BicycleCharge_User_Park: false, //车辆管理
  Public_BicycleCharge_PhysicalCard: false, // 实体卡管理
  Public_BicycleCharge_ElectronicsCard: false,// 电子卡管理
  Public_BicycleCharge_MonthCardRecord: false, // 包月订单
  Public_BicycleCharge_User_AccessRecord: false, // 出入记录
  Public_BicycleCharge_AlarmInformation: false, // 告警消息
  Public_BicycleCharge_deviceMsg: false, // 设备消息

  admin_interconnection: false, // 充电互联
  admin_interconnection_Company: false, //互联企业
  admin_interconnection_auth: false, // 互联授权
  admin_interconnection_authorizedStation: false, // 授权电站
  admin_interconnection_authPile: false, // 授权桩
  admin_interconnection_differenceOrder: false, // 差异订单
  admin_interconnection_connectOrderManage: false, // 互联接入
  admin_interconnection_stationData: false, // 互联电站
  admin_interconnection_pileData: false, // 互联充电桩
  admin_interconnection_chargeOrder: false, // 在充订单
  admin_interconnection_orderRecord: false, // 订单记录


  managementOfJointStation: false, // 电站投建
  managementOfJointStation_depotManagement: false, // 场站管理
  managementOfJointStation_contractManagement: false, // 合同管理
  managementOfJointStation_outboundDeliveryOrder: false, // 出库单
  managementOfJointStation_checkAndAccept: false, // 场站验收
  managementOfJointStation_deliveryStatistics: false, // 发货统计

  orderlyEnergy:false, // 有序能源
  orderlyEnergy_Home: false, // 首页
  orderlyEnergy_SchedulingModel: false, //调度模型

  adminResourceManagement: false, // 资源管理
  adminResourceManagement_downloadCenter: false, // 下载中心

  healthBoard: false, //健康看板
  healthBoard_serveList: false, //服务列表
}

const ROLE_ID = {
	// 30 车场,26集团,,,27渠道,,28联盟,,,29城市
	//2018.2.6修改 2 集团...............8 车场 ........   7城市
	//2019.05.28    11服务商
	GROUP: 222,
	CHANNEL: 27,
	UNION: 2,
	CITY: 7,
	PARK: 8,
	BOSS: 5,
	CITYREGIS: 1001,
	SHOP: 10,
	SERVER: 11,
	EOMS: 12,
	SUBADMIN: 15,
}

module.exports = {
	AUTH_ID,
	AUTH_ID_ADMIN,
	showADMINItems_const,
	showParkItem_const,
	AUTH_ID_UNION,
	showUnionItem_const,
	showShopItem_const,
	AUTH_ID_SHOP,
	showBossItem_const,
	AUTH_ID_SERVER,
	showServerItems_const,
	AUTH_ID_CITY,
	showCityItems_const,
	ROLE_ID,
	AUTH_ID_EOMS,
	showEomsItems_const,
}
