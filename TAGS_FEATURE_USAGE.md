# 车场标签功能使用说明

## 功能概述
在服务信息部分添加了车场标签字段，支持数组格式的标签管理，能够处理服务器返回的逗号分隔字符串并转换为标签数组进行回显。

## 字段信息
- **字段名**: `tags`
- **数据类型**: `Array<string>`
- **显示位置**: 服务信息区域
- **功能**: 支持添加、删除、编辑车场标签

## 主要功能

### 1. 标签显示和管理
- 以标签形式显示已添加的车场标签
- 每个标签都有删除按钮，可以单独删除
- 支持动态添加新标签

### 2. 数据格式转换
- **输入**: 服务器返回逗号分隔的字符串 (如: "室外停车,24小时,免费WiFi")
- **处理**: 自动转换为数组格式 `["室外停车", "24小时", "免费WiFi"]`
- **输出**: 提交时可转换回逗号分隔字符串

### 3. 用户交互
- 点击"+ 添加标签"按钮显示输入框
- 输入标签内容后按回车或失焦确认
- 点击标签的 × 按钮删除标签
- 防重复添加相同标签

## 代码实现

### 模板部分
```vue
<el-form-item label="车场标签" prop="tags">
  <div class="tags-container">
    <el-tag
      v-for="(tag, index) in formData.tags"
      :key="index"
      closable
      @close="removeTag(index)"
      class="tag-item"
    >
      {{ tag }}
    </el-tag>
    <el-input
      v-if="inputVisible"
      ref="saveTagInput"
      v-model="inputValue"
      size="small"
      class="input-new-tag"
      @keyup.enter.native="handleInputConfirm"
      @blur="handleInputConfirm"
    />
    <el-button v-else class="button-new-tag" size="small" @click="showInput">
      + 添加标签
    </el-button>
  </div>
</el-form-item>
```

### 数据定义
```javascript
data() {
  return {
    inputVisible: false,
    inputValue: '',
    formData: {
      // ... 其他字段
      tags: []  // 标签数组
    }
  }
}
```

### 核心方法
```javascript
// 删除标签
removeTag(index) {
  this.formData.tags.splice(index, 1);
},

// 显示输入框
showInput() {
  this.inputVisible = true;
  this.$nextTick(() => {
    this.$refs.saveTagInput.$refs.input.focus();
  });
},

// 确认输入
handleInputConfirm() {
  const inputValue = this.inputValue;
  if (inputValue && !this.formData.tags.includes(inputValue)) {
    this.formData.tags.push(inputValue);
  }
  this.inputVisible = false;
  this.inputValue = '';
},

// 字符串转数组
parseTagsFromString(tagsString) {
  if (!tagsString || typeof tagsString !== 'string') {
    return [];
  }
  return tagsString.split(',').map(tag => tag.trim()).filter(tag => tag);
},

// 数组转字符串
formatTagsToString() {
  return this.formData.tags.join(',');
}
```

### 数据处理
在 watch 中自动处理服务器返回的字符串格式：
```javascript
watch: {
  value: {
    handler(newVal) {
      if (newVal) {
        // 处理标签字段：如果是字符串则转换为数组
        if (newVal.tags) {
          if (typeof newVal.tags === 'string') {
            newVal.tags = this.parseTagsFromString(newVal.tags);
          }
        }
        this.formData = { ...formData, ...newVal }
      }
    }
  }
}
```

## 样式特点
- 标签采用 Element UI 的 el-tag 组件
- 支持响应式布局，标签自动换行
- 添加按钮采用虚线边框设计
- 输入框大小适中，用户体验良好

## 使用场景
适用于需要为车场添加多个特征标签的场景，如：
- 停车位置：室外停车、室内停车、地下停车
- 服务特色：24小时、免费WiFi、充电桩
- 价格特点：免费停车、优惠价格、会员折扣
- 其他特征：安全监控、便民服务等

## 数据流转
1. **接收数据**: 服务器返回 `"室外停车,24小时,免费WiFi"`
2. **自动转换**: 组件自动转换为 `["室外停车", "24小时", "免费WiFi"]`
3. **用户编辑**: 可以添加、删除标签
4. **提交数据**: 调用 `formatTagsToString()` 转换为逗号分隔字符串提交
