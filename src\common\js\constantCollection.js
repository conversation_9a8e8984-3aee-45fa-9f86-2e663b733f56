/**
 *定义常规常量，多页面复用
 *  不经常改的
 */
// 异常事件来源
export const PassType = [
  {'value_name':'入场','value_no': 0},
  {'value_name':'出场','value_no': 1},
 ];
// 确认类型
export const ConfirmType = [
  {'value_name': '未处理','value_no':'-1'},
  {'value_name': '取消' ,'value_no': 0},
  {'value_name': '确认入场' ,'value_no': 1}, 
  {'value_name': '确认追缴' ,'value_no': 2}, 
  {'value_name': '确认返场' ,'value_no': 3},
];
// 异常类型
export const ExceptionType = [
  {'value_name': '重复出场', 'value_no': 1},
  {'value_name': '人工车道放行', 'value_no': 2},
  {'value_name': '进出场车牌不一致，模糊匹配', 'value_no': 3},
  {'value_name': '未知——忽略', 'value_no': 4},
  {'value_name': '重复入场', 'value_no': 5},
  {'value_name': '出口无订单，人工计费结算', 'value_no': 6},
  {'value_name': '高峰模式，自动放行', 'value_no': 7},
  {'value_name': '疑似掉头', 'value_no': 8},
  {'value_name': '疑似跟车-入场', 'value_no': 9},
  {'value_name': '疑似返场', 'value_no': 10},
  {'value_name': '疑似跟车-出场', 'value_no': 11},
  {'value_name': '疑似逃费', 'value_no': 12},

]
// 能源类型
const EnergyType = [
  {'value_name': '全部', 'value_no': 0},
  {'value_name': '汽油车', 'value_no': 1},
  {'value_name': '新能源车', 'value_no': 2},
]
// 电站场景
const sceneList=[
  { 'value_no': 1,'value_name': '住宅'},
  { 'value_no': 2,'value_name': '商超'},
  { 'value_no': 3,'value_name': '办公楼'},
  { 'value_no': 4,'value_name': '医院'},
  {' value_no': 5,'value_name': '酒店'},
  { 'value_no': 6,'value_name': '工业园'},
  { 'value_no': 7,'value_name': '交通枢纽'},
  { 'value_no': 8,'value_name': '事业单位'},
  { 'value_no': 9,'value_name': '其他'},
]
// 停车收费类型
const feeTypeList=[
  { 'value_no': 1,'value_name': '充电减免'},
  { 'value_no': 2,'value_name': '限时免费'},
  { 'value_no': 3,'value_name': '停车收费'},
  { 'value_no': 4,'value_name': '停车免费'},
  { 'value_no': 5,'value_name': '其他'},
]
// 车辆类型
export const VehicleType = [
  {'value_no': 0, 'value_name': '全部'},
  {'value_no': 1, 'value_name': '临停车'},
  {'value_no': 2, 'value_name': '月卡车'},
  {'value_no': 3, 'value_name': '白名单车'},
  {'value_no': 5, 'value_name': '贵宾车'},
  {'value_no': 6, 'value_name': 'VIP'},
  {'value_no': 7, 'value_name': '内部车'},
  {'value_no': 9, 'value_name': '储值车'},
];
const SuperimposedType = [
  {'value_name': '限制一张', 'value_no': '0'},
  {'value_name': '限制两张', 'value_no': '2'},
  {'value_name': '限制三张', 'value_no': '3'},
  {'value_name': '限制四张', 'value_no': '4'},
  {'value_name': '限制五张', 'value_no': '5'},
  {'value_name': '限制六张', 'value_no': '6'},
  {'value_name': '限制七张', 'value_no': '7'},
  {'value_name': '限制八张', 'value_no': '8'},
  {'value_name': '限制九张', 'value_no': '9'},
  {'value_name': '不限制', 'value_no': '1'}
]
// 限制时刻
const LimitTimeList = [
  {'value_no':1,'value_name':'次日'},
  {'value_no':2,'value_name':'2天后'},
  {'value_no':3,'value_name':'3天后'},
  {'value_no':4,'value_name':'4天后'},
  {'value_no':5,'value_name':'5天后'},
  {'value_no':6,'value_name':'6天后'},
  {'value_no':7,'value_name':'7天后'}
];
//支付方式
const PaymentMethod = [
  {'value_no':0,"value_name":"主扫"},
  {'value_no':1,"value_name":"被扫"},
  {'value_no':2,"value_name":"免密"},
  {'value_no':3,"value_name":"现金"},
  {'value_no':4,"value_name":"公众号"},
  {'value_no':5,"value_name":"补缴"},
  {'value_no':6,"value_name":"无感"},
  {'value_no':7,"value_name":"ETC"}
];
//支付通道
const PayChannelList=[
  {'value_no':0,"value_name":"微信"},
  {'value_no':1,"value_name":"支付宝"},
  {'value_no':2,"value_name":"余额"},
  {'value_no':3,"value_name":"银联"},
  {'value_no':4,"value_name":"建行"},
  {'value_no':5,"value_name":"银盛"},
  {'value_no':6,"value_name":"云闪付"},
  {'value_no':7,"value_name":"汇付天下"},
  {'value_no':10,"value_name":"工行"},
  {'value_no':15,"value_name":"杭州城市大脑"},
  {'value_no':18,"value_name":"常州政府"},
  {'value_no':19,"value_name":"银联金融部"},
  {'value_no':17,"value_name":"农业银行"},
  {'value_no':23,"value_name":"临沂智慧停车"},
  {'value_no':27,"value_name":"萧山"},
  {'value_no':36,"value_name":"工商银行V1"},
  {'value_no':39,"value_name":"丰收互联"},
  {'value_no':43,"value_name":"ETC"},
  {'value_no':45,"value_name":"贵州银行"},
  {'value_no':50,"value_name":"中国银行个人金融部"},
  {'value_no':61,"value_name":"黔农云"},
  {'value_no':93,"value_name":"交行智慧停车"},
]
//账务类型
const ParkTypeList=[
  {'value_no':26,'value_name':'手续费补贴'},
  {'value_no':0,'value_name':'停车费电子收入'},
  {'value_no':1,'value_name':"提现"},
  {'value_no':2,'value_name':"月卡续费收入"},
  {'value_no':3,'value_name':"手续费"},
  {'value_no':4,'value_name':"话费支出"},
  {'value_no':5,'value_name':"通用支付"},
  {'value_no':6,'value_name':"红包找零"},
  {'value_no':7,'value_name':"退款"},
  {'value_no':8,'value_name':"分账支出"},
  {'value_no':12,'value_name':"追缴"}
]
// 民族
const NationList=[
  {'value_no':0,'value_name':'汉族'},
  {'value_no':1,'value_name':"蒙古族"},
  {'value_no':2,'value_name':"回族"},
  {'value_no':3,'value_name':"藏族"},
  {'value_no':4,'value_name':"维吾尔族"},
  {'value_no':5,'value_name':"苗族"},
  {'value_no':6,'value_name':"彝族"},
  {'value_no':7,'value_name':"壮族"},
  {'value_no':8,'value_name':"布依族"},
  {'value_no':9,'value_name':"朝鲜族"},
  {'value_no':10,'value_name':"满族"},
  {'value_no':11,'value_name':"侗族"},
  {'value_no':12,'value_name':"瑶族"},
  {'value_no':13,'value_name':'白族'},
  {'value_no':14,'value_name':"土家族"},
  {'value_no':15,'value_name':"哈尼族"},
  {'value_no':16,'value_name':"哈萨克族"},
  {'value_no':17,'value_name':"傣族"},
  {'value_no':18,'value_name':"黎族"},
  {'value_no':19,'value_name':"傈僳族"},
  {'value_no':20,'value_name':'佤族'},
  {'value_no':21,'value_name':"畲族"},
  {'value_no':22,'value_name':"高山族"},
  {'value_no':23,'value_name':"拉祜族"},
  {'value_no':24,'value_name':"水族"},
  {'value_no':25,'value_name':"东乡族"},
  {'value_no':26,'value_name':"纳西族"},
  {'value_no':27,'value_name':"景颇族"},
  {'value_no':28,'value_name':"柯尔克孜族"},
  {'value_no':29,'value_name':"土族"},
  {'value_no':30,'value_name':'达斡尔族'},
  {'value_no':31,'value_name':"仫佬族"},
  {'value_no':32,'value_name':"羌族"},
  {'value_no':33,'value_name':"布朗族"},
  {'value_no':34,'value_name':"撒拉族"},
  {'value_no':35,'value_name':"毛南族"},
  {'value_no':36,'value_name':"仡佬族"},
  {'value_no':37,'value_name':"锡伯族"},
  {'value_no':38,'value_name':"阿昌族"},
  {'value_no':39,'value_name':"普米族"},
  {'value_no':40,'value_name':'塔吉克族'},
  {'value_no':41,'value_name':"怒族"},
  {'value_no':42,'value_name':"乌孜别克族"},
  {'value_no':43,'value_name':"俄罗斯族"},
  {'value_no':44,'value_name':"鄂温克族"},
  {'value_no':45,'value_name':"崩龙族"},
  {'value_no':46,'value_name':"保安族"},
  {'value_no':47,'value_name':"裕固族"},
  {'value_no':48,'value_name':"京族"},
  {'value_no':49,'value_name':"塔塔尔族"},
  {'value_no':50,'value_name':"独龙族"},
  {'value_no':51,'value_name':"鄂伦春族"},
  {'value_no':52,'value_name':"赫哲族"},
  {'value_no':53,'value_name':"门巴族"},
  {'value_no':54,'value_name':"珞巴族"},
  {'value_no':55,'value_name':"基诺族"},
  {'value_no':56,'value_name':"穿青人"},
  {'value_no':57,'value_name':"亻革家人"},
  {'value_no':58,'value_name':"外国血统"},
  {'value_no':59,'value_name':"其它"},
]
// 充电枪类型（充电桩）
const GunTypeList = [
  {'value_no': 1,'value_name':'单枪'},
  {'value_no': 2,'value_name':'双枪'},
]
// 充电桩类型（充电桩）
const PileTypeList = [
  {'value_no': 0,'value_name':'汽车交流桩'},
  {'value_no': 1,'value_name':'汽车直流桩'},
  {'value_no': 2,'value_name':'电单车交流桩'},
]
// 充电状态（充电桩）
const ChargeTypeList = [
  {'value_no': 0,'value_name':'初始化'},
  {'value_no': 1,'value_name':'空闲'},
  {'value_no': 2,'value_name':'充电'},
  {'value_no': 3,'value_name':'停止'},
  {'value_no': 4,'value_name':'故障'},
  {'value_no': 5,'value_name':'预约'},
  {'value_no': 6,'value_name':'维护'},
  {'value_no': 7,'value_name':'启动中'},
  {'value_no': 8,'value_name':'充电暂停'},
  {'value_no': 9,'value_name':'禁用'},
  {'value_no': 10,'value_name':'已连接'},
  {'value_no': 11,'value_name':'定时启动状态'},
  {'value_no': 12,'value_name':'启动失败'},
  {'value_no': 13,'value_name':'有充电器但未充电（用户未启动）'},
  {'value_no': 14,'value_name':'有充电器但未充电（已充满电）'},
  {'value_no': 15,'value_name':'浮充'},
  {'value_no': 16,'value_name':'存储器损坏'},
]
// 汽车、单车充电状态（充电桩）归类为4种     充电状态  0初始化，1空闲，2充电 3停止 4故障 5预约 6维护 7启动中8充电暂停9禁用10已连接 11定时启动状态 12 启动失败      13 有充电器但未充电（用户未启动） 14 有充电器但未充电（已充满电）  15 浮充  16 存储器损坏 17 重连 18等待接受bsd  19 未支付卡状态 20 充电完成状态  21 试用期到。停止服务状态 22结算状态
const classifyChargeTypeList = [
  {'value_no': 0,'value_name':'其它'},
  {'value_no': 1,'value_name':'空闲'},
  {'value_no': 2,'value_name':'占用'},
  {'value_no': 3,'value_name':'占用'},
  {'value_no': 4,'value_name':'故障'},
  {'value_no': 5,'value_name':'占用'},
  {'value_no': 6,'value_name':'其它'},
  {'value_no': 7,'value_name':'其它'},
  {'value_no': 8,'value_name':'其它'},
  {'value_no': 9,'value_name':'其它'},
  {'value_no': 10,'value_name':'占用'},
  {'value_no': 11,'value_name':'其它'},
  {'value_no': 12,'value_name':'其它'},
  {'value_no': 13,'value_name':'占用'},
  {'value_no': 14,'value_name':'占用'},
  {'value_no': 15,'value_name':'占用'},
  {'value_no': 16,'value_name':'故障'},
  {'value_no': 17,'value_name':'其它'},
  {'value_no': 18,'value_name':'其它'},
  {'value_no': 19,'value_name':'其它'},
  {'value_no': 20,'value_name':'占用'},
  {'value_no': 21,'value_name':'其它'},
  {'value_no': 22,'value_name':'其它'},
]
// 连接状态（充电桩）
const OnLineTypeList = [
  {'value_no': 0,'value_name':'在线'},
  {'value_no': 1,'value_name':'离线'},
]
// 运营状态（充电桩）
const OperateState = [
  {'value_no': 0,'value_name':'启用'},
  {'value_no': 1,'value_name':'禁用'},
]
// 运营状态（充电桩）
const OperateStateBicycle = [
  {'value_no': 0,'value_name':'启用'},
  {'value_no': 1,'value_name':'禁用'},
]
// 桩分类
const pileList = [
  { value_no: 0, value_name: "公共桩" },
  { value_no: 1, value_name: "私有桩" },
]
// 连接状态（充电桩）
const NetworkingTypeList = [
  {'value_no': 1,'value_name':'以太网'},
  {'value_no': 2,'value_name':'4G'},
  {'value_no': 3,'value_name':'脱机'},
]
// 项目类型
const ProjectTypeList = [
  {'value_no': '1','value_name':'小区'},
  {'value_no': '2','value_name':'学校'},
  {'value_no': '3','value_name':'医院'},
  {'value_no': '4','value_name':'写字楼'},
  {'value_no': '5','value_name':'单位'},
  {'value_no': '6','value_name':'其他'},
]
// 互联企业充电桩类型
const InPileEquipList = [
  {'value_no': '0','value_name':'交流桩'},
  {'value_no': '1','value_name':'直流桩'},
  {'value_no': '2','value_name':'电单车交流桩'},
  {'value_no': '3','value_name':'交直流一体设备'},
  {'value_no': '4','value_name':'无线设备'},
  {'value_no': '5','value_name':'其他'},
]
// 桩状态
const GunStatusList = [
  {'value_no': '0','value_name':'初始化'},
  {'value_no': '1','value_name':'空闲'},
  {'value_no': '2','value_name':'充电'},
  {'value_no': '3','value_name':'停止'},
  {'value_no': '4','value_name':'故障'},
  {'value_no': '5','value_name':'预约'},
  {'value_no': '6','value_name':'维护'},
  {'value_no': '7','value_name':'启动中'},
  {'value_no': '8','value_name':'充电暂停'},
  {'value_no': '9','value_name':'禁用'},
  {'value_no': '10','value_name':'已连接'},
  {'value_no': '11','value_name':'定时启动状态'},
  {'value_no': '12','value_name':'启动失败'},
  {'value_no': '13','value_name':'有充电器但未充电（用户未启动）'},
  {'value_no': '14','value_name':'有充电器但未充电（已充满电）'},
  {'value_no': '15','value_name':'浮充'},
  {'value_no': '16','value_name':'存储器损坏'},
]
const openModeList = [
  {'value_no': '1','value_name':'app启动'},
  {'value_no': '2','value_name':'卡启动'},
  {'value_no': '4','value_name':'离线卡启动'},
  {'value_no': '5','value_name':'vin码启动充电'},
]
// 充电桩类型（充电桩:两轮车）
const BycyclePileTypeList = [
  {'value_no': 5,'value_name': 'YM'},
]
// 充电桩类型（充电桩:汽车）
const SceneTypeList = [
  {'value_no': 1,'value_name': '标准充电桩'},
  {'value_no': 2,'value_name': '车位闸充电'},
  {'value_no': 4,'value_name': '相机+地锁+充电桩'},
  {'value_no': 5,'value_name': '视频充电桩+地锁'},
  {'value_no': 6,'value_name': '车位闸仅开闸'},
]
// 设备型号（充电桩:两轮车）
const BycyclePileModelType = [
  {'value_no': "1",'value_name': 'YM-M6-12SW'}, // 12路
  {'value_no': "2",'value_name': 'YM-G5W-02SW'}, // 2路从机
  {'value_no': "3",'value_name': 'YM-4G-02SW'}, // 2路主机
  {'value_no': "4",'value_name': 'YM-M5-12SW'}, // 10路
  {'value_no': "5",'value_name': 'YM-M5-16SW'}, // 16路
]
// 设备标识（充电桩:两轮车）
const BycyclePileIdentification = [
  {'value_no': '03','value_name': '1路插座'},
  {'value_no': '04','value_name': '2路插座'},
  {'value_no': '05','value_name': '10路充电桩'},
  {'value_no': '06','value_name': '16路充电桩'},
  {'value_no': '07','value_name': '12路充电桩'},
]
// 通用状态
const OnLineTypePublicList = [
  {'value_no': 1,'value_name':'在线'},
  {'value_no': 2,'value_name':'离线'},
]
// 设备类型
const DeviceTypeList = [
  {'value_no': 1,'value_name':'小蓝'},
  {'value_no': 2,'value_name':'BL'},
  {'value_no': 3,'value_name':'互联互通充电设备'},
  {'value_no': 7,'value_name':'KH'},
  {'value_no': 8,'value_name':'RH'},
  {'value_no': 9,'value_name':'YD'},
  {'value_no': 10,'value_name':'YX'},
  {'value_no': 11,'value_name':'RP'},
  {'value_no': 12,'value_name':'YKC'},
]
// 车位状态
const ParkingSpaceStatusList = [
  {'value_no': 1,'value_name':'入场'},
  {'value_no': 2,'value_name':'在场'},
  {'value_no': 4,'value_name':'出场'},
  {'value_no': 8,'value_name':'空场'},
  {'value_no': 16,'value_name':'车位异常'},
  {'value_no': 32,'value_name':'延迟上报出场'},
  {'value_no': 64,'value_name':'合并出入场'},
  {'value_no': 128,'value_name':'预入场'},
  {'value_no': 256,'value_name':'预出场'},
  {'value_no': 512,'value_name':'入场修正'},
]

// 房屋管理-房屋装修类型
const decorationList=[
  {'value_no': '1','value_name': '简单装修'},
  {'value_no': '2','value_name': '精装修'},
  {'value_no': '3','value_name': '豪华装修'},
  {'value_no': '4','value_name': '毛坯房'}
]
// 房屋管理-朝向
const directionList= [
  {'value_no': 1,'value_name': '朝东'},
  {'value_no': 2,'value_name': '朝南'},
  {'value_no': 3,'value_name': '朝西'},
  {'value_no': 4,'value_name': '朝北'}
]
const stateList= [
  {'value_no': 1,'value_name': '空闲'},
  {'value_no': 2,'value_name': '自住'},
  {'value_no': 3,'value_name': '出租'},
  {'value_no': 4,'value_name': '商用'},
  {'value_no': 5,'value_name': '其他'}
]
module.exports = {
  LimitTimeList,SuperimposedType,EnergyType,VehicleType,PaymentMethod,PayChannelList,ParkTypeList,NationList,GunTypeList,PileTypeList,pileList,
  ChargeTypeList,OnLineTypeList,OperateState,NetworkingTypeList,ProjectTypeList,InPileEquipList,GunStatusList,BycyclePileTypeList,OperateStateBicycle,
  openModeList,BycyclePileModelType,BycyclePileIdentification,OnLineTypePublicList,DeviceTypeList,ParkingSpaceStatusList,
  SceneTypeList,decorationList,directionList,stateList,ExceptionType,sceneList,classifyChargeTypeList,feeTypeList
}
