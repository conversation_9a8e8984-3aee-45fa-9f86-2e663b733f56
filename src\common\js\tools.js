const initData = function () {
  try {
    let params = {},
      type='',
      oid=sessionStorage.getItem("oid"),
      loginuin = sessionStorage.getItem("loginuin"),
      cityid = sessionStorage.getItem("cityid"),
      serverId = sessionStorage.getItem("serverid"),
      comid = sessionStorage.getItem("comid"),
      userid=sessionStorage.getItem("userid"),
      name=sessionStorage.getItem("name"),
      atuhGroup = ""

    if (oid==8) {
      atuhGroup = "AUTH_ID"
      params.comid = comid
      type=3

    } else if (oid==11) {
      params.serverId = serverId
      atuhGroup = "AUTH_ID_SERVER"
      type=2
    } else if (oid==7) {
      atuhGroup = ""
      params.cityid = cityid
      type=1
    } else if(oid==15){
      params.backstageId = loginuin
      type=4
    }
    let id=Object.values(params)
    let data={
      params,
      atuhGroup,
      type,
      mainId:id[0]-0,
      userid,
      name
    }
    sessionStorage.setItem('initDataParams',JSON.stringify(data))
  } catch (err){
    console.log('util,initData',err)

  }
}
const tools = {}
/***
 * 获取页面元素， 兼容qiankun shadowRoot
 * @param dom
 * @return {*}
 */
tools.getElement = function (dom) {
  return document.querySelector(dom)
  // let element  = null
  // // 开启沙箱才可以
  // if (window.__POWERED_BY_QIANKUN__) {
  //   const microDocument = document.getElementById('__qiankun_microapp_wrapper_for_parking_os__').shadowRoot
  //   element = microDocument.querySelector(dom)
  // } else {
  //   element = document.querySelector(dom)
  // }
  // return element
}
tools.loginSession = function (u) {
  sessionStorage.setItem('user', JSON.stringify(u));
  sessionStorage.setItem('token', u.token);
  sessionStorage.setItem('comid', u.comid);
  localStorage.setItem('comid', u.comid)
  sessionStorage.setItem('groupid', u.groupid);
  localStorage.setItem('groupid', u.groupid)
  sessionStorage.setItem('channelid', u.channelid);
  sessionStorage.setItem('unionid', u.union_id);
  sessionStorage.setItem('cityid', u.cityid);
  sessionStorage.setItem('eomsid', u.eoms_id);
  sessionStorage.setItem('loginuin', u.loginuin);
  sessionStorage.setItem('oid', u.oid);
  sessionStorage.setItem('nickname', u.nickname);
  sessionStorage.setItem('ishdorder', u.ishdorder);
  sessionStorage.setItem('loginroleid', u.loginroleid);
  sessionStorage.setItem('supperadmin', u.supperadmin);
  sessionStorage.setItem('shopid', u.shopid);
  sessionStorage.setItem('nickname1', u.nickname);
  sessionStorage.setItem('serverid',u.serverid);
  sessionStorage.setItem('bolink_serverid',u.bolink_serverid);
  sessionStorage.setItem('docking_type',u.docking_type);
  sessionStorage.setItem('cloud_token',u.cloud_token);
  sessionStorage.setItem('is_new_type',u.is_new_type);
  sessionStorage.setItem('is_show_renewal',u.is_show_renewal);
  sessionStorage.setItem('buildingAdmin', u.buildingAdmin);
  sessionStorage.setItem('houseAdmin', u.houseAdmin);
  sessionStorage.setItem('userid', u.userid);
  sessionStorage.setItem('name', u.name);
  u.token_params && sessionStorage.setItem('parkid', u.token_params.park_id);
  initData()

  if(u.is4GPark){
    sessionStorage.setItem('is4GPark',u.is4GPark);
  }
  //切换logo
  if(u.logo1 != undefined && u.logo1 != null && u.logo1 != ''){
    sessionStorage.setItem('logo1', u.logo1);
    sessionStorage.setItem('logo2', u.logo2);
  }else{
    sessionStorage.removeItem('logo1');
    sessionStorage.removeItem('logo2');
  }
}
export default tools
