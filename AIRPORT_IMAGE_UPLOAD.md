# 机场图片上传功能实现说明

## 功能概述
实现了机场图片的多选上传功能，支持一次性上传多张图片，并与指定的上传接口进行联调。

## 接口配置

### 上传接口地址
```javascript
uploadAction: 'https://ts.bolink.club/zld/airport-parking-server/common/upload'
```

### 请求头配置
```javascript
uploadHeaders: {
  'token': ''  // 从 sessionStorage 或 localStorage 获取
}
```

## 核心功能实现

### 1. Token 自动设置
```javascript
mounted() {
  // 设置上传请求头中的 token
  const token = sessionStorage.getItem('token') || localStorage.getItem('token');
  if (token) {
    this.uploadHeaders.token = token;
  }
}
```

### 2. 自定义上传方法
```javascript
customUpload(options) {
  const { file, onSuccess, onError, onProgress } = options;
  
  // 创建 FormData
  const formData = new FormData();
  formData.append('file', file);
  
  // 创建 XMLHttpRequest
  const xhr = new XMLHttpRequest();
  
  // 设置上传进度
  xhr.upload.addEventListener('progress', (e) => {
    if (e.lengthComputable) {
      const percentComplete = (e.loaded / e.total) * 100;
      onProgress({ percent: percentComplete });
    }
  });
  
  // 设置请求完成回调
  xhr.addEventListener('load', () => {
    if (xhr.status === 200) {
      try {
        const response = JSON.parse(xhr.responseText);
        onSuccess(response, file);
      } catch (error) {
        onError(error);
      }
    } else {
      onError(new Error(`上传失败: ${xhr.status}`));
    }
  });
  
  // 发送请求
  xhr.open('POST', this.uploadAction);
  
  // 设置请求头
  if (this.uploadHeaders.token) {
    xhr.setRequestHeader('token', this.uploadHeaders.token);
  }
  
  xhr.send(formData);
}
```

### 3. 上传成功处理
```javascript
handleImageSuccess(res, file, fileList) {
  if (res.code === 200 || res.code === '200') {
    // 更新文件列表中对应文件的URL
    const targetFile = fileList.find(f => f.uid === file.uid);
    if (targetFile) {
      targetFile.url = res.data || res.data?.url || res.url;
      targetFile.response = res;
      targetFile.status = 'success';
    }
    
    // 更新表单数据中的图片URL数组
    this.form.imageUrls = fileList
      .filter(f => f.status === 'success' && (f.url || f.response?.data))
      .map(f => f.url || f.response?.data || f.response?.data?.url);
    
    this.fileList = fileList;
    this.$message.success('图片上传成功');
  }
}
```

## 组件配置

### Element UI Upload 组件配置
```vue
<el-upload
  ref="upload"
  class="image-uploader"
  :action="uploadAction"
  :headers="uploadHeaders"
  :file-list="fileList"
  :multiple="true"
  :limit="5"
  :on-success="handleImageSuccess"
  :before-upload="beforeImageUpload"
  :on-remove="handleImageRemove"
  :on-exceed="handleExceed"
  :http-request="customUpload"
  list-type="picture-card">
  <i class="el-icon-plus"></i>
</el-upload>
```

### 关键配置说明
- `:action="uploadAction"`: 上传接口地址
- `:headers="uploadHeaders"`: 请求头，包含 token
- `:multiple="true"`: 支持多选
- `:limit="5"`: 最多上传5张图片
- `:http-request="customUpload"`: 自定义上传方法
- `list-type="picture-card"`: 卡片式图片列表

## 数据流程

### 1. 文件选择
用户选择多个图片文件 → 触发 `beforeImageUpload` 验证

### 2. 文件上传
通过 `customUpload` 方法逐个上传文件 → 显示上传进度

### 3. 上传成功
调用 `handleImageSuccess` → 更新文件列表 → 更新 `form.imageUrls` 数组

### 4. 文件移除
调用 `handleImageRemove` → 更新文件列表 → 同步更新 `form.imageUrls` 数组

## 数据结构

### 表单数据结构
```javascript
form: {
  type: '',
  name: '',
  address: '',
  longitude: '',
  latitude: '',
  parkingFee: '',
  imageUrls: []  // 存储上传成功的图片URL数组
}
```

### 文件列表结构
```javascript
fileList: [
  {
    uid: 'unique-id',
    name: 'image.jpg',
    status: 'success',
    url: 'https://example.com/image.jpg',
    response: { code: 200, data: 'image-url' }
  }
]
```

## 错误处理

### 1. 文件类型验证
```javascript
const isImage = /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(file.name);
if (!isImage) {
  this.$message.error('只能上传图片文件!');
  return false;
}
```

### 2. 文件大小验证
```javascript
const isLt1M = file.size / 1024 / 1024 < 1;
if (!isLt1M) {
  this.$message.error('上传图片大小不能超过 1MB!');
  return false;
}
```

### 3. 上传数量限制
```javascript
handleExceed(files, fileList) {
  this.$message.warning(`最多只能上传5张图片，当前已选择${fileList.length}张，本次选择了${files.length}张`);
}
```

### 4. 网络错误处理
```javascript
xhr.addEventListener('error', () => {
  onError(new Error('网络错误'));
});
```

## 功能特点

1. **多图上传**: 支持一次选择多张图片进行上传
2. **进度显示**: 实时显示每个文件的上传进度
3. **Token 认证**: 自动添加 token 到请求头
4. **错误处理**: 完善的文件验证和错误提示
5. **数据同步**: 上传成功后自动更新表单数据
6. **预览功能**: 支持图片预览和删除操作
7. **限制控制**: 支持文件数量和大小限制

## 使用示例

### 上传流程
1. 用户点击上传区域选择图片
2. 系统验证文件类型和大小
3. 逐个上传文件到服务器
4. 显示上传进度和结果
5. 更新表单中的图片URL数组

### 数据提交
```javascript
// 表单提交时，imageUrls 包含所有上传成功的图片URL
const submitData = {
  ...this.form,
  imageUrls: ['url1', 'url2', 'url3']
};
```

这样实现了完整的多图上传功能，支持与指定接口的联调，并提供了良好的用户体验。
