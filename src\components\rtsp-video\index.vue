<template>
  <div class="player-container" ref="playerDom">
    <canvas id="livecanvas"></canvas>
  </div>
</template>

<script>
import util from '@/common/js/util';

export default {
  name: "PlayerMjpeg",
  props: {
    url: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      player: null
    }
  },
  watch: {
    url: {
      handler: function (val) {
        if (val) {
          this.play();
        } else {
          if (this.player) {
            this.player.close()
          }
        }
      }
    }
  },
  mounted() {
    this.play();
  },
  methods: {
    play() {
      if (this.player) {
        this.player.close();
      }
      var can = document.getElementById('livecanvas');
      var rtspURL = 'rtsp://' + this.url + ':554/cam/realmonitor?channel=1&subtype=0&proto=Private3'
      var options = {
        wsURL: 'ws://' + this.url + ':80/rtspoverwebsocket',
        rtspURL: rtspURL,
        username: 'bolink',
        password: 'bolink@dh2023'
      };
      // if (util.isHttps()) {
      //   options.wsURL = 'wss://' + this.url + ':443/rtspoverwebsocket';
      // }
      this.player = new PlayerControl(options);
      this.player.on('WorkerReady', () => {
        this.player.connect();
      });

      this.player.on('ResolutionChanged', (e) => {
        debug.log(e)
      });
      this.player.on('PlayStart', (e) => {
        debug.log(e)
      });
      this.player.on('DecodeStart', (e) => {

      });
      this.player.on('UpdateCanvas', (e) => {

      });
      this.player.on('GetFrameRate', (e) => {
        debug.log('GetFrameRate: ' + e)
      });
      this.player.on('FrameTypeChange', (e) => {
        debug.log('FrameTypeChange: ' + e)
      });
      this.player.on('Error', (e) => {
        debug.log('Error: ' + JSON.stringify(e))
      });
      this.player.on('IvsDraw', () => {

      });
      this.player.init(can, null);
    }
  }
}
</script>

<style lang="scss" scoped>
.player-container {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  background: #000;

  canvas {
    display: inline-block;
    max-height: 100%;
    max-width: 100%;
    width: 100%;
    height: 100%;
  }
}
</style>
